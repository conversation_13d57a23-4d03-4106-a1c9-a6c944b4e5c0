[Unit]
Description=GENT Frontend Vue.js Application
After=network.target
Wants=gent-api.service

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/opt/gent
Environment=NODE_ENV=production
# Environment=VITE_API_URL=http://localhost:8001
ExecStart=/bin/bash /opt/gent/run_frontend.sh
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=gent-frontend

[Install]
WantedBy=multi-user.target
