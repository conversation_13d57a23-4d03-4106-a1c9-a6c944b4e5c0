"""
GENT v10 - API endpointy pro přímý přístup k LLM konfiguracím v DB

Tento modul poskytuje API endpointy pro přímý přístup k LLM konfiguracím
v PostgreSQL databázi bez ukládání do RAM cache.
Optimalizované pro spolehlivost a konzistenci dat.
"""

from fastapi import APIRouter, HTTPException
from typing import Dict, List, Any, Optional
from pydantic import BaseModel
import logging
import json
import uuid
import aiohttp
import asyncio
from datetime import datetime

from gent.db.llm_db_service import LlmDirectDbService

# Nastavení loggeru
logger = logging.getLogger("gent.api.app.routes.llm_direct_db_routes")

# Vytvoření routeru pro LLM Direct DB API
router = APIRouter(
    prefix="/api/db/llm",
    tags=["llm-direct-db"],
    responses={404: {"description": "Not found"}},
)

# Modely pro validaci vstupních dat

class ProviderBase(BaseModel):
    """Základní model pro poskytovatele LLM."""
    name: str
    model: str
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    timeout: int = 60
    retry_count: int = 3
    is_default: bool = False


class ProviderCreate(ProviderBase):
    """Model pro vytvoření poskytovatele LLM."""
    models: Optional[Dict[str, Any]] = None


class ProviderUpdate(ProviderBase):
    """Model pro aktualizaci poskytovatele LLM."""
    id: str
    models: Optional[Dict[str, Any]] = None


class CacheConfig(BaseModel):
    """Model pro konfiguraci cache."""
    enabled: bool = True
    ttl: int = 3600
    backend: str = "redis"
    namespace: str = "llm_cache"
    similarity_threshold: float = 0.95
    connection_string: Optional[str] = None
    id: Optional[str] = None


class EmbeddingModel(BaseModel):
    """Model pro embedding model."""
    provider: str
    model: str
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    dimension: int = 1536
    normalized: bool = True
    batch_size: int = 32
    timeout: int = 60
    is_default: bool = False
    id: Optional[str] = None


class DbServiceStatus(BaseModel):
    """Model pro stav DB service."""
    providers_count: int
    has_cache_config: bool
    embedding_count: int
    db_connected: bool = False


# API endpointy

@router.get("/status", response_model=DbServiceStatus)
async def get_db_service_status():
    """
    Získá aktuální stav připojení k databázi a počty konfigurací.
    """
    try:
        service = LlmDirectDbService()

        # Ověření připojení k DB
        conn = service._get_connection()
        db_connected = conn is not None and not conn.closed

        # Získání dat z DB
        providers = service.get_providers()
        cache_config = service.get_cache_config()
        embedding_models = service.get_embedding_models()

        # Vytvoření odpovědi
        status = DbServiceStatus(
            providers_count=len(providers),
            has_cache_config=cache_config is not None,
            embedding_count=len(embedding_models),
            db_connected=db_connected
        )

        # Uzavření připojení
        if hasattr(service, "_close_connection"):
            service._close_connection()

        return status

    except Exception as e:
        logger.error(f"Chyba při získávání stavu DB service: {str(e)}")
        return DbServiceStatus(
            providers_count=0,
            has_cache_config=False,
            embedding_count=0,
            db_connected=False
        )


@router.get("/providers", response_model=List[Dict[str, Any]])
async def get_providers():
    """
    Získá seznam poskytovatelů LLM přímo z databáze.
    """
    try:
        service = LlmDirectDbService()
        providers = service.get_providers()

        # Maskování API klíčů
        for provider in providers:
            if "api_key" in provider and provider["api_key"]:
                provider["api_key"] = "********"

        # Uzavření připojení
        if hasattr(service, "_close_connection"):
            service._close_connection()

        return providers

    except Exception as e:
        logger.error(f"Chyba při získávání poskytovatelů z DB: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Nepodařilo se získat poskytovatele z DB: {str(e)}")


@router.get("/models", response_model=List[Dict[str, Any]])
async def get_all_models():
    """
    Získá seznam všech modelů ze všech poskytovatelů přímo z databáze.
    """
    try:
        service = LlmDirectDbService()
        conn = service._get_connection()
        cursor = conn.cursor()

        # Získání všech aktivních modelů s informacemi o poskytovatelích
        cursor.execute("""
            SELECT
                m.model_id, m.provider_id, m.model_name, m.model_identifier,
                m.context_length, m.max_tokens_output, m.default_temperature,
                m.capabilities, m.is_default, m.is_active,
                p.provider_name, p.api_base_url, p.api_key_required
            FROM
                llm_models m
            JOIN
                llm_providers p ON m.provider_id = p.provider_id
            WHERE
                m.is_active = TRUE AND p.is_active = TRUE
            ORDER BY
                p.provider_name, m.model_name
        """)

        models = []
        for row in cursor.fetchall():
            # Parsování capabilities
            capabilities = row[7]
            if isinstance(capabilities, str):
                try:
                    capabilities = json.loads(capabilities)
                except json.JSONDecodeError:
                    capabilities = {}

            model = {
                "id": f"{row[1]}_{row[3]}",  # provider_id_model_identifier
                "model_id": row[0],
                "provider_id": row[1],
                "name": row[2],  # model_name
                "model_identifier": row[3],
                "provider_name": row[10],  # provider_name
                "context_length": row[4],
                "max_tokens": row[5],
                "temperature": float(row[6]) if row[6] else 0.7,
                "capabilities": capabilities,
                "is_default": row[8],
                "is_active": row[9],
                "api_key_required": row[12]
            }
            models.append(model)

        cursor.close()

        # Uzavření připojení
        if hasattr(service, "_close_connection"):
            service._close_connection()

        logger.info(f"Načteno {len(models)} modelů ze všech poskytovatelů")
        return models

    except Exception as e:
        logger.error(f"Chyba při získávání všech modelů z DB: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Nepodařilo se získat modely z DB: {str(e)}")


@router.post("/test-llm")
async def test_llm_model(request_data: Dict[str, Any]):
    """
    Testuje LLM model s jednoduchým dotazem.
    Podporuje všechny poskytovatele: OpenAI, Anthropic, Google, OpenRouter, Ollama.
    """
    import time
    start_time = time.time()

    try:
        # Extrahujeme data z požadavku
        model_id = request_data.get("model_id")
        message = request_data.get("message", "Hello, how are you?")

        if not model_id:
            raise HTTPException(status_code=400, detail="model_id je povinný")

        # Načteme informace o modelu z databáze
        service = LlmDirectDbService()
        conn = service._get_connection()
        cursor = conn.cursor()

        # Najdeme model podle ID (formát: "provider_id_model_identifier")
        if '_' in model_id:
            provider_id, model_identifier = model_id.split('_', 1)
            cursor.execute("""
                SELECT
                    m.model_id, m.provider_id, m.model_name, m.model_identifier,
                    m.context_length, m.max_tokens_output, m.default_temperature,
                    p.provider_name, p.api_base_url, p.api_key
                FROM
                    llm_models m
                JOIN
                    llm_providers p ON m.provider_id = p.provider_id
                WHERE
                    p.provider_id = %s AND m.model_identifier = %s
                    AND m.is_active = TRUE AND p.is_active = TRUE
            """, (provider_id, model_identifier))
        else:
            # Fallback pro starý formát
            cursor.execute("""
                SELECT
                    m.model_id, m.provider_id, m.model_name, m.model_identifier,
                    m.context_length, m.max_tokens_output, m.default_temperature,
                    p.provider_name, p.api_base_url, p.api_key
                FROM
                    llm_models m
                JOIN
                    llm_providers p ON m.provider_id = p.provider_id
                WHERE
                    m.model_identifier = %s AND m.is_active = TRUE AND p.is_active = TRUE
            """, (model_id,))

        model_row = cursor.fetchone()
        if not model_row:
            raise HTTPException(status_code=404, detail=f"Model s ID {model_id} nebyl nalezen")

        # Extrahujeme informace o modelu
        model_name = model_row[2]
        model_identifier = model_row[3]
        max_tokens = model_row[5] or 1000
        temperature = float(model_row[6]) if model_row[6] else 0.7
        provider_name = model_row[7].lower()
        api_base_url = model_row[8]
        api_key = model_row[9]

        cursor.close()

        logger.info(f"Testování modelu: {provider_name}/{model_identifier}")
        logger.info(f"API Base URL: {api_base_url}")
        logger.info(f"API Key prefix: {api_key[:10] if api_key else 'NONE'}...")

        # Volání podle poskytovatele
        if provider_name == "openai":
            response = await call_openai_api(api_base_url, api_key, model_identifier, message, temperature, max_tokens)
        elif provider_name == "anthropic":
            response = await call_anthropic_api(api_base_url, api_key, model_identifier, message, temperature, max_tokens)
        elif provider_name == "google":
            response = await call_google_api(api_base_url, api_key, model_identifier, message, temperature, max_tokens)
        elif provider_name == "openrouter":
            response = await call_openrouter_api(api_base_url, api_key, model_identifier, message, temperature, max_tokens)
        elif provider_name == "ollama":
            response = await call_ollama_api(api_base_url, model_identifier, message, temperature, max_tokens)
        elif provider_name == "ollama2":
            response = await call_ollama_api(api_base_url, model_identifier, message, temperature, max_tokens)
        elif provider_name == "lmstudio" or provider_name == "lm studio":
            response = await call_lmstudio_api(api_base_url, model_identifier, message, temperature, max_tokens)
        else:
            raise HTTPException(status_code=400, detail=f"Nepodporovaný poskytovatel: {provider_name}")

        # Měření času odpovědi
        end_time = time.time()
        latency = (end_time - start_time) * 1000  # v milisekundách

        # Automatické zaznamenání metriky do databáze
        try:
            from gent.api.app.routes.llm_metrics_routes import save_llm_request_metric, LLMRequestMetric

            # Extrakce tokenů z response
            tokens_used = 0
            if isinstance(response, dict) and "usage" in response:
                usage = response["usage"]
                if isinstance(usage, dict):
                    tokens_used = usage.get("total_tokens", 0) or usage.get("totalTokens", 0)

            # Vytvoření metriky
            metric = LLMRequestMetric(
                model_id=model_row[0],  # skutečné model_id z databáze
                model_name=model_name,
                provider=provider_name,
                prompt=message,
                response=response.get("text", "") if isinstance(response, dict) else str(response),
                tokens_used=tokens_used,
                latency=latency,
                status="completed",
                error_message=None,
                extra_data={
                    "chat_test": True,
                    "model_identifier": model_identifier,
                    "temperature": temperature,
                    "max_tokens": max_tokens,
                    "api_base_url": api_base_url[:50] if api_base_url else None
                }
            )

            # Uložení metriky
            save_llm_request_metric(metric)
            logger.info(f"LLM metrika uložena: {provider_name}/{model_identifier} - {latency:.1f}ms - {tokens_used} tokenů")

        except Exception as e:
            logger.error(f"Chyba při ukládání LLM metriky: {e}")
            # Pokračujeme i při chybě ukládání metriky

        return {
            "success": True,
            "model": f"{provider_name}/{model_identifier}",
            "response": response,
            "latency": latency
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Chyba při testování LLM modelu: {str(e)}")

        # Měření času i při chybě
        end_time = time.time()
        latency = (end_time - start_time) * 1000  # v milisekundách

        # Zaznamenání failed metriky
        try:
            from gent.api.app.routes.llm_metrics_routes import save_llm_request_metric, LLMRequestMetric

            model_id = request_data.get("model_id", "unknown")
            message = request_data.get("message", "Hello, how are you?")

            metric = LLMRequestMetric(
                model_id=None,
                model_name=model_id,
                provider="unknown",
                prompt=message,
                response=None,
                tokens_used=0,
                latency=latency,
                status="failed",
                error_message=str(e),
                extra_data={
                    "chat_test": True,
                    "error_type": type(e).__name__
                }
            )

            save_llm_request_metric(metric)
            logger.info(f"LLM error metrika uložena: {model_id} - {latency:.1f}ms - FAILED")

        except Exception as metric_error:
            logger.error(f"Chyba při ukládání error metriky: {metric_error}")

        raise HTTPException(status_code=500, detail=f"Chyba při testování LLM: {str(e)}")


# Pomocné funkce pro volání API jednotlivých poskytovatelů

async def call_openai_api(api_base_url: str, api_key: str, model: str, message: str, temperature: float, max_tokens: int):
    """Volá OpenAI API s podporou pro o1-preview modely."""
    if api_base_url:
        # Pokud URL už obsahuje /v1, nepřidáváme to znovu
        if api_base_url.endswith('/v1'):
            url = f"{api_base_url}/chat/completions"
        else:
            url = f"{api_base_url}/v1/chat/completions"
    else:
        url = "https://api.openai.com/v1/chat/completions"

    logger.info(f"OpenAI API URL: {url}")

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    # Kontrola, zda je to speciální model (má specifické požadavky)
    is_special_model = model in ["o1-preview", "o1-mini", "o3-mini", "gpt-4.1", "gpt-4.1-mini", "gpt-4.1-nano"]

    payload = {
        "model": model,
        "messages": [{"role": "user", "content": message}],
    }

    # Speciální modely nepodporují temperature parametr
    if not is_special_model:
        payload["temperature"] = temperature

    # Speciální modely používají max_completion_tokens místo max_tokens
    if max_tokens and max_tokens > 0:
        if is_special_model:
            payload["max_completion_tokens"] = max_tokens
        else:
            payload["max_tokens"] = max_tokens

    async with aiohttp.ClientSession() as session:
        async with session.post(url, headers=headers, json=payload) as response:
            if response.status == 200:
                data = await response.json()
                return {
                    "text": data["choices"][0]["message"]["content"],
                    "usage": data.get("usage", {})
                }
            else:
                error_text = await response.text()
                raise HTTPException(status_code=response.status, detail=f"OpenAI API error: {error_text}")


async def call_anthropic_api(api_base_url: str, api_key: str, model: str, message: str, temperature: float, max_tokens: int):
    """Volá Anthropic API."""
    url = f"{api_base_url}/v1/messages" if api_base_url else "https://api.anthropic.com/v1/messages"

    headers = {
        "x-api-key": api_key,
        "anthropic-version": "2023-06-01",
        "Content-Type": "application/json"
    }

    payload = {
        "model": model,
        "max_tokens": max_tokens,
        "temperature": temperature,
        "messages": [{"role": "user", "content": message}]
    }

    async with aiohttp.ClientSession() as session:
        async with session.post(url, headers=headers, json=payload) as response:
            if response.status == 200:
                data = await response.json()
                return {
                    "text": data["content"][0]["text"],
                    "usage": data.get("usage", {})
                }
            else:
                error_text = await response.text()
                raise HTTPException(status_code=response.status, detail=f"Anthropic API error: {error_text}")


async def call_google_api(api_base_url: str, api_key: str, model: str, message: str, temperature: float, max_tokens: int):
    """Volá Google Gemini API."""
    if api_base_url and not api_base_url.endswith('/openai/'):
        # Standardní Google API
        url = f"{api_base_url}/v1beta/models/{model}:generateContent?key={api_key}"
    else:
        # Výchozí Google API URL
        url = f"https://generativelanguage.googleapis.com/v1beta/models/{model}:generateContent?key={api_key}"

    logger.info(f"Google API URL: {url}")

    headers = {
        "Content-Type": "application/json"
    }

    payload = {
        "contents": [{"parts": [{"text": message}]}],
        "generationConfig": {
            "temperature": temperature,
            "maxOutputTokens": max_tokens
        }
    }

    async with aiohttp.ClientSession() as session:
        async with session.post(url, headers=headers, json=payload) as response:
            if response.status == 200:
                data = await response.json()
                return {
                    "text": data["candidates"][0]["content"]["parts"][0]["text"],
                    "usage": data.get("usageMetadata", {})
                }
            else:
                error_text = await response.text()
                raise HTTPException(status_code=response.status, detail=f"Google API error: {error_text}")


async def call_openrouter_api(api_base_url: str, api_key: str, model: str, message: str, temperature: float, max_tokens: int):
    """Volá OpenRouter API."""
    if api_base_url:
        # Pokud URL už obsahuje /api/v1, nepřidáváme to znovu
        if api_base_url.endswith('/api/v1'):
            url = f"{api_base_url}/chat/completions"
        else:
            url = f"{api_base_url}/api/v1/chat/completions"
    else:
        url = "https://openrouter.ai/api/v1/chat/completions"

    logger.info(f"OpenRouter API URL: {url}")

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }

    payload = {
        "model": model,
        "messages": [{"role": "user", "content": message}],
        "temperature": temperature,
        "max_tokens": max_tokens
    }

    async with aiohttp.ClientSession() as session:
        async with session.post(url, headers=headers, json=payload) as response:
            if response.status == 200:
                data = await response.json()
                return {
                    "text": data["choices"][0]["message"]["content"],
                    "usage": data.get("usage", {})
                }
            else:
                error_text = await response.text()
                raise HTTPException(status_code=response.status, detail=f"OpenRouter API error: {error_text}")


async def call_ollama_api(api_base_url: str, model: str, message: str, temperature: float, max_tokens: int):
    """Volá Ollama API."""
    url = f"{api_base_url}/api/chat" if api_base_url else "http://localhost:11434/api/chat"

    headers = {
        "Content-Type": "application/json"
    }

    payload = {
        "model": model,
        "messages": [{"role": "user", "content": message}],
        "stream": False,
        "options": {
            "temperature": temperature,
            "num_predict": max_tokens
        }
    }

    # Zvýšený timeout pro Ollama (300 sekund = 5 minut)
    timeout = aiohttp.ClientTimeout(total=300)

    async with aiohttp.ClientSession(timeout=timeout) as session:
        async with session.post(url, headers=headers, json=payload) as response:
            if response.status == 200:
                data = await response.json()
                message_content = data.get("message", {}).get("content", "")

                # Odhad tokenů pro Ollama (nevrací usage info)
                estimated_tokens = len(message.split()) + len(message_content.split())

                return {
                    "text": message_content,
                    "usage": {"total_tokens": estimated_tokens}
                }
            else:
                error_text = await response.text()
                raise HTTPException(status_code=response.status, detail=f"Ollama API error: {error_text}")


async def call_lmstudio_api(api_base_url: str, model: str, message: str, temperature: float, max_tokens: int):
    """Volá LM Studio API (kompatibilní s OpenAI API)."""
    if api_base_url:
        url = f"{api_base_url}/v1/chat/completions"
    else:
        url = "http://localhost:1234/v1/chat/completions"

    logger.info(f"LM Studio API URL: {url}")

    headers = {
        "Content-Type": "application/json"
    }

    payload = {
        "model": model,
        "messages": [{"role": "user", "content": message}],
        "temperature": temperature,
        "max_tokens": max_tokens if max_tokens > 0 else -1,
        "stream": False
    }

    # Zvýšený timeout pro LM Studio (300 sekund = 5 minut)
    # LM Studio potřebuje čas na unload/load modelů
    timeout = aiohttp.ClientTimeout(total=300)

    async with aiohttp.ClientSession(timeout=timeout) as session:
        async with session.post(url, headers=headers, json=payload) as response:
            if response.status == 200:
                data = await response.json()
                return {
                    "text": data["choices"][0]["message"]["content"],
                    "usage": data.get("usage", {})
                }
            else:
                error_text = await response.text()
                raise HTTPException(status_code=response.status, detail=f"LM Studio API error: {error_text}")




@router.get("/providers/{provider_id}", response_model=Dict[str, Any])
async def get_provider_detail(provider_id: str):
    """
    Získá detail poskytovatele podle ID přímo z databáze.
    """
    try:
        service = LlmDirectDbService()
        provider = service.get_provider_by_id(provider_id)

        if not provider:
            raise HTTPException(status_code=404, detail=f"Poskytovatel s ID {provider_id} nebyl nalezen v DB")

        # Vytvoříme kopii, aby nedošlo k modifikaci originálních dat
        provider_copy = json.loads(json.dumps(provider))

        # Maskování API klíče
        if "api_key" in provider_copy and provider_copy["api_key"]:
            provider_copy["api_key"] = "********"

        # Uzavření připojení
        if hasattr(service, "_close_connection"):
            service._close_connection()

        return provider_copy

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Chyba při získávání poskytovatele z DB: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Nepodařilo se získat poskytovatele z DB: {str(e)}")


@router.get("/models", response_model=List[Dict[str, Any]])
async def get_all_models():
    """
    Získá seznam všech modelů ze všech poskytovatelů přímo z databáze.
    """
    try:
        service = LlmDirectDbService()
        conn = service._get_connection()
        cursor = conn.cursor()

        # Získání všech aktivních modelů s informacemi o poskytovatelích
        cursor.execute("""
            SELECT
                m.model_id, m.provider_id, m.model_name, m.model_identifier,
                m.context_length, m.max_tokens_output, m.default_temperature,
                m.capabilities, m.is_default, m.is_active,
                p.provider_name, p.api_base_url, p.api_key_required
            FROM
                llm_models m
            JOIN
                llm_providers p ON m.provider_id = p.provider_id
            WHERE
                m.is_active = TRUE AND p.is_active = TRUE
            ORDER BY
                p.provider_name, m.model_name
        """)

        models = []
        for row in cursor.fetchall():
            # Parsování capabilities
            capabilities = row[7]
            if isinstance(capabilities, str):
                try:
                    capabilities = json.loads(capabilities)
                except json.JSONDecodeError:
                    capabilities = {}

            model = {
                "id": f"{row[1]}_{row[3]}",  # provider_id_model_identifier
                "model_id": row[0],
                "provider_id": row[1],
                "name": row[2],  # model_name
                "model_identifier": row[3],
                "provider_name": row[10],  # provider_name
                "context_length": row[4],
                "max_tokens": row[5],
                "temperature": float(row[6]) if row[6] else 0.7,
                "capabilities": capabilities,
                "is_default": row[8],
                "is_active": row[9],
                "api_key_required": row[12]
            }
            models.append(model)

        cursor.close()

        # Uzavření připojení
        if hasattr(service, "_close_connection"):
            service._close_connection()

        logger.info(f"Načteno {len(models)} modelů ze všech poskytovatelů")
        return models

    except Exception as e:
        logger.error(f"Chyba při získávání všech modelů z DB: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Nepodařilo se získat modely z DB: {str(e)}")


@router.put("/providers/{provider_id}", response_model=Dict[str, Any])
async def update_provider(
    provider_id: str,
    provider_data: Dict[str, Any]
):
    """
    Aktualizuje poskytovatele přímo v databázi.
    """
    try:
        service = LlmDirectDbService()

        # Kontrola, zda poskytovatel existuje
        existing_provider = service.get_provider_by_id(provider_id)
        if not existing_provider:
            raise HTTPException(status_code=404, detail=f"Poskytovatel s ID {provider_id} nebyl nalezen v DB")

        # Přidáme ID do dat
        provider_data["id"] = provider_id

        # Aktualizujeme poskytovatele v DB
        success = service.save_provider(provider_data)

        # Uzavření připojení
        if hasattr(service, "_close_connection"):
            service._close_connection()

        if not success:
            raise HTTPException(status_code=500, detail="Nepodařilo se aktualizovat poskytovatele v DB")

        # Vytvoření odpovědi
        return {
            "success": True,
            "message": "Poskytovatel byl úspěšně aktualizován v DB",
            "provider": provider_data
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Chyba při aktualizaci poskytovatele v DB: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Nepodařilo se aktualizovat poskytovatele v DB: {str(e)}")


@router.post("/providers", response_model=Dict[str, Any])
async def create_provider(
    provider_data: Dict[str, Any]
):
    """
    Vytvoří nového poskytovatele přímo v databázi.
    """
    try:
        service = LlmDirectDbService()

        # Vygenerujeme ID, pokud nebylo poskytnuto
        if "id" not in provider_data or not provider_data["id"]:
            provider_data["id"] = str(uuid.uuid4())

        provider_id = provider_data["id"]

        # Uložíme poskytovatele do DB
        success = service.save_provider(provider_data)

        # Uzavření připojení
        if hasattr(service, "_close_connection"):
            service._close_connection()

        if not success:
            raise HTTPException(status_code=500, detail="Nepodařilo se vytvořit poskytovatele v DB")

        # Vytvoření odpovědi
        return {
            "success": True,
            "message": "Poskytovatel byl úspěšně vytvořen v DB",
            "provider": provider_data
        }

    except Exception as e:
        logger.error(f"Chyba při vytváření poskytovatele v DB: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Nepodařilo se vytvořit poskytovatele v DB: {str(e)}")


@router.get("/cache", response_model=Dict[str, Any])
async def get_cache_config():
    """
    Získá konfiguraci LLM cache přímo z databáze.
    """
    try:
        service = LlmDirectDbService()
        cache_config = service.get_cache_config()

        # Uzavření připojení
        if hasattr(service, "_close_connection"):
            service._close_connection()

        if not cache_config:
            # Vrátíme výchozí hodnoty, pokud konfigurace neexistuje
            return {
                "enabled": True,
                "ttl": 3600,
                "backend": "redis",
                "namespace": "llm_cache",
                "similarity_threshold": 0.95
            }

        return cache_config

    except Exception as e:
        logger.error(f"Chyba při získávání konfigurace cache z DB: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Nepodařilo se získat konfiguraci cache z DB: {str(e)}")


@router.put("/cache", response_model=Dict[str, Any])
async def update_cache_config(
    cache_data: Dict[str, Any]
):
    """
    Aktualizuje konfiguraci LLM cache přímo v databázi.
    """
    try:
        service = LlmDirectDbService()

        # Aktualizujeme konfiguraci cache v DB
        success = service.save_cache_config(cache_data)

        # Uzavření připojení
        if hasattr(service, "_close_connection"):
            service._close_connection()

        if not success:
            raise HTTPException(status_code=500, detail="Nepodařilo se aktualizovat konfiguraci cache v DB")

        # Vytvoření odpovědi
        return {
            "success": True,
            "message": "Konfigurace cache byla úspěšně aktualizována v DB",
            "cache": cache_data
        }

    except Exception as e:
        logger.error(f"Chyba při aktualizaci konfigurace cache v DB: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Nepodařilo se aktualizovat konfiguraci cache v DB: {str(e)}")


@router.get("/embedding", response_model=Dict[str, Dict[str, Any]])
async def get_embedding_models():
    """
    Získá embedding modely přímo z databáze.
    """
    try:
        service = LlmDirectDbService()
        embedding_models = service.get_embedding_models()

        # Uzavření připojení
        if hasattr(service, "_close_connection"):
            service._close_connection()

        result_models = {}

        for model_id, model in embedding_models.items():
            # Vytvoříme kopii, aby nedošlo k modifikaci originálních dat
            model_copy = json.loads(json.dumps(model))

            # Maskování API klíče
            if "api_key" in model_copy and model_copy["api_key"]:
                model_copy["api_key"] = "********"

            # Použijeme provider jako klíč pro kompatibilitu s frontendem
            key = f"{model_copy.get('provider', 'unknown')}-embedding"
            result_models[key] = model_copy

        # Pokud nemáme žádné modely, vrátíme výchozí hodnoty
        if not result_models:
            return {
                "openai-embedding": {
                    "id": "openai-embedding",
                    "provider": "openai",
                    "model": "text-embedding-3-large",
                    "dimension": 1536,
                    "batch_size": 100,
                    "timeout": 60,
                    "is_default": True
                },
                "local-embedding": {
                    "id": "local-embedding",
                    "provider": "local",
                    "model": "all-MiniLM-L6-v2",
                    "dimension": 384,
                    "batch_size": 32,
                    "timeout": 30,
                    "is_default": False
                }
            }

        return result_models

    except Exception as e:
        logger.error(f"Chyba při získávání embedding modelů z DB: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Nepodařilo se získat embedding modely z DB: {str(e)}")


@router.put("/embedding/{model_id}", response_model=Dict[str, Any])
async def update_embedding_model(
    model_id: str,
    model_data: Dict[str, Any]
):
    """
    Aktualizuje embedding model přímo v databázi.
    """
    try:
        service = LlmDirectDbService()

        # Přidáme ID do dat
        model_data["id"] = model_id

        # Aktualizujeme embedding model v DB
        success = service.save_embedding_model(model_data)

        # Uzavření připojení
        if hasattr(service, "_close_connection"):
            service._close_connection()

        if not success:
            raise HTTPException(status_code=500, detail="Nepodařilo se aktualizovat embedding model v DB")

        # Vytvoření odpovědi
        return {
            "success": True,
            "message": "Embedding model byl úspěšně aktualizován v DB",
            "model": model_data
        }

    except Exception as e:
        logger.error(f"Chyba při aktualizaci embedding modelu v DB: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Nepodařilo se aktualizovat embedding model v DB: {str(e)}")


@router.post("/add-missing-models")
async def add_missing_models():
    """
    Přidá chybějící OpenAI a Google modely do databáze se správnými názvy.
    """
    try:
        service = LlmDirectDbService()
        conn = service._get_connection()
        cursor = conn.cursor()

        # Nejprve odstraníme špatné modely
        logger.info("Odstraňování špatných modelů...")

        # Odstranit problematické Google modely
        cursor.execute("""
            DELETE FROM llm_models
            WHERE model_identifier = 'gemini-2.0-flash-lite'
            OR model_name LIKE '%flash-lite%'
            OR model_identifier = 'gemini-2.5-pro-preview-05-06'
            OR model_name LIKE '%Gemini 2.5 Pro Preview 05-06%'
            OR model_name LIKE '%Gemini 2.0 Flash%'
        """)

        # Odstranit VŠECHNY nefunkční OpenAI modely
        bad_openai_models = [
            'GPT-4o', 'GPT-4.5', 'GPT-4-1', 'GPT-4-1 Mini', 'GPT-4-1 Nano', 'O3', 'O3 Mini',
            'gpt-4.5', 'gpt-4-1', 'gpt-4-1-mini', 'gpt-4-1-nano',
            'o1', 'o1-preview', 'o1-pro', 'o3', 'o3-mini'
        ]
        for bad_model in bad_openai_models:
            cursor.execute("""
                DELETE FROM llm_models
                WHERE model_name = %s OR model_identifier = %s
            """, (bad_model, bad_model))

        # Zjistíme ID poskytovatelů
        cursor.execute("""
            SELECT provider_id, provider_name
            FROM llm_providers
            WHERE LOWER(provider_name) IN ('openai', 'google')
            AND is_active = TRUE
        """)

        providers = {row[1].lower(): row[0] for row in cursor.fetchall()}
        logger.info(f"Nalezení poskytovatelé: {providers}")

        if not providers:
            raise HTTPException(status_code=404, detail="Žádní poskytovatelé OpenAI nebo Google nenalezeni!")

        added_count = 0

        # OpenAI modely - POUZE FUNKČNÍ!
        if 'openai' in providers:
            openai_models = [
                # Pouze modely, které skutečně fungují
                ('gpt-4o', 'gpt-4o', 128000, 4096, '{"text": true, "code": true, "reasoning": true, "function_calling": true, "multimodal": true}'),
                ('gpt-3.5-turbo', 'gpt-3.5-turbo', 16385, 4096, '{"text": true, "code": true, "function_calling": true, "fast": true}'),
                ('gpt-4', 'gpt-4', 8192, 4096, '{"text": true, "code": true, "reasoning": true, "function_calling": true}'),
                ('gpt-4-turbo', 'gpt-4-turbo', 128000, 4096, '{"text": true, "code": true, "reasoning": true, "function_calling": true}')
            ]

            for name, identifier, context, max_tokens, capabilities in openai_models:
                # Zkontrolujeme, zda model už neexistuje
                cursor.execute("""
                    SELECT COUNT(*)
                    FROM llm_models
                    WHERE provider_id = %s AND model_identifier = %s
                """, (providers['openai'], identifier))

                if cursor.fetchone()[0] == 0:
                    # Model neexistuje, přidáme ho
                    cursor.execute("""
                        INSERT INTO llm_models (
                            provider_id, model_name, model_identifier,
                            context_length, max_tokens_output, default_temperature,
                            capabilities, is_active, is_default
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        providers['openai'], name, identifier,
                        context, max_tokens, 0.7,
                        capabilities, True, False
                    ))

                    logger.info(f"Přidán OpenAI model: {name}")
                    added_count += 1

        # Google modely (pouze funkční)
        if 'google' in providers:
            google_models = [
                ('gemini-2.0-flash', 'gemini-2.0-flash', 1000000, 8192, '{"text": true, "vision": true, "audio": true, "video": true, "code": true, "multimodal": true}'),
                ('gemini-2.5-flash-preview-05-20', 'gemini-2.5-flash-preview-05-20', 1000000, 8192, '{"text": true, "code": true, "quick_analysis": true, "general": true}')
            ]

            for name, identifier, context, max_tokens, capabilities in google_models:
                # Zkontrolujeme, zda model už neexistuje
                cursor.execute("""
                    SELECT COUNT(*)
                    FROM llm_models
                    WHERE provider_id = %s AND model_identifier = %s
                """, (providers['google'], identifier))

                if cursor.fetchone()[0] == 0:
                    # Model neexistuje, přidáme ho
                    cursor.execute("""
                        INSERT INTO llm_models (
                            provider_id, model_name, model_identifier,
                            context_length, max_tokens_output, default_temperature,
                            capabilities, is_active, is_default
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        providers['google'], name, identifier,
                        context, max_tokens, 0.7,
                        capabilities, True, False
                    ))

                    logger.info(f"Přidán Google model: {name}")
                    added_count += 1

        # Potvrzení změn
        conn.commit()
        cursor.close()

        # Uzavření připojení
        if hasattr(service, "_close_connection"):
            service._close_connection()

        return {
            "success": True,
            "message": f"Úspěšně přidáno {added_count} nových modelů!",
            "added_count": added_count,
            "providers": list(providers.keys())
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Chyba při přidávání modelů: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Nepodařilo se přidat modely: {str(e)}")
