"""
GENT v10 - API endpointy pro testování MCP nástrojů

Tento modul poskytuje proxy endpointy pro testování MCP nástrojů
aby se vyřešily CORS problémy při volání externích API.
"""

from fastapi import APIRouter, HTTPException
from typing import Dict, Any, Optional
from pydantic import BaseModel
import logging
import requests
import json

# Nastavení loggeru
logger = logging.getLogger("gent.api.app.routes.mcp_test_routes")

# Vytvoření routeru
router = APIRouter(
    prefix="/api/mcp/test",
    tags=["mcp-test"],
    responses={
        404: {"description": "Not found"},
        500: {"description": "Internal server error"},
        400: {"description": "Bad request"}
    }
)

# Pydantic modely pro validaci

class FetchTestRequest(BaseModel):
    """Model pro fetch test request."""
    url: str

class BraveSearchTestRequest(BaseModel):
    """Model pro Brave Search test request."""
    query: str
    count: int = 5

class TavilyTestRequest(BaseModel):
    """Model pro Tavily test request."""
    query: str
    max_results: int = 5

class PerplexityTestRequest(BaseModel):
    """Model pro Perplexity test request."""
    query: str

# API endpointy

@router.post(
    "/fetch",
    response_model=Dict[str, Any],
    summary="Test Fetch nástroje",
    description="Stáhne obsah zadané URL a vrátí výsledek."
)
async def test_fetch(request: FetchTestRequest):
    """
    Testuje fetch nástroj - stáhne obsah zadané URL.
    """
    try:
        logger.info(f"🌐 Fetch test: {request.url}")
        
        headers = {
            'User-Agent': 'GENT-MCP-Test/1.0',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
        }
        
        response = requests.get(request.url, headers=headers, timeout=30)
        
        content_type = response.headers.get('content-type', '')
        
        if 'application/json' in content_type:
            try:
                data = response.json()
                content = json.dumps(data, indent=2, ensure_ascii=False)
            except:
                content = response.text
        else:
            content = response.text
        
        result = {
            "success": response.status_code < 400,
            "status_code": response.status_code,
            "status_text": response.reason,
            "headers": dict(response.headers),
            "content_type": content_type,
            "content_length": len(content),
            "content": content[:2000] + ("..." if len(content) > 2000 else ""),
            "url": request.url
        }
        
        logger.info(f"✅ Fetch test úspěšný: {response.status_code}")
        return result
        
    except Exception as e:
        logger.error(f"❌ Fetch test selhal: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Fetch test selhal: {str(e)}")

@router.post(
    "/brave-search",
    response_model=Dict[str, Any],
    summary="Test Brave Search",
    description="Vyhledá pomocí Brave Search API a vrátí výsledky."
)
async def test_brave_search(request: BraveSearchTestRequest):
    """
    Testuje Brave Search API.
    """
    try:
        logger.info(f"🔍 Brave Search test: '{request.query}'")
        
        api_key = 'BSARir7CGmpWKz5mvNgGJyYp3yV8CDn'
        search_url = f"https://api.search.brave.com/res/v1/web/search"
        
        params = {
            'q': request.query,
            'count': request.count
        }
        
        headers = {
            'Accept': 'application/json',
            'Accept-Encoding': 'gzip',
            'X-Subscription-Token': api_key
        }
        
        response = requests.get(search_url, params=params, headers=headers, timeout=30)
        
        if response.status_code != 200:
            raise Exception(f"HTTP {response.status_code}: {response.text}")
        
        data = response.json()
        
        results = []
        if 'web' in data and 'results' in data['web']:
            for result in data['web']['results'][:5]:
                results.append({
                    'title': result.get('title', ''),
                    'url': result.get('url', ''),
                    'description': result.get('description', ''),
                    'age': result.get('age', '')
                })
        
        result = {
            "success": True,
            "query": request.query,
            "results_count": len(results),
            "results": results,
            "api_response": data,
            "api_key_used": "BSARir7C..."
        }
        
        logger.info(f"✅ Brave Search test úspěšný: {len(results)} výsledků")
        return result
        
    except Exception as e:
        logger.error(f"❌ Brave Search test selhal: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Brave Search test selhal: {str(e)}")

@router.post(
    "/tavily",
    response_model=Dict[str, Any],
    summary="Test Tavily Search",
    description="Vyhledá pomocí Tavily API a vrátí výsledky."
)
async def test_tavily(request: TavilyTestRequest):
    """
    Testuje Tavily Search API.
    """
    try:
        logger.info(f"🔍 Tavily test: '{request.query}'")
        
        # NOVÝ PLATNÝ API KLÍČ!
        api_key = '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'
        search_url = 'https://api.tavily.com/search'
        
        request_body = {
            'api_key': api_key,
            'query': request.query,
            'max_results': request.max_results,
            'search_depth': 'basic',
            'include_answer': True,
            'include_images': False,
            'include_raw_content': False
        }
        
        headers = {
            'Content-Type': 'application/json'
        }
        
        response = requests.post(search_url, json=request_body, headers=headers, timeout=30)
        
        if response.status_code != 200:
            raise Exception(f"HTTP {response.status_code}: {response.text}")
        
        data = response.json()
        
        results = []
        if 'results' in data:
            for result in data['results']:
                results.append({
                    'title': result.get('title', ''),
                    'url': result.get('url', ''),
                    'content': result.get('content', ''),
                    'score': result.get('score', 0)
                })
        
        result = {
            "success": True,
            "query": request.query,
            "max_results": request.max_results,
            "answer": data.get('answer', ''),
            "results_count": len(results),
            "results": results,
            "api_response": data,
            "api_key_used": "CLEK7GE2..."
        }
        
        logger.info(f"✅ Tavily test úspěšný: {len(results)} výsledků")
        return result
        
    except Exception as e:
        logger.error(f"❌ Tavily test selhal: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Tavily test selhal: {str(e)}")

@router.post(
    "/perplexity",
    response_model=Dict[str, Any],
    summary="Test Perplexity AI",
    description="Pošle dotaz do Perplexity AI a vrátí odpověď."
)
async def test_perplexity(request: PerplexityTestRequest):
    """
    Testuje Perplexity AI API.
    """
    try:
        logger.info(f"🤖 Perplexity test: '{request.query}'")
        
        api_key = '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'
        api_url = 'https://api.perplexity.ai/chat/completions'
        
        request_body = {
            'model': 'llama-3.1-sonar-small-128k-online',
            'messages': [
                {
                    'role': 'system',
                    'content': 'Jsi užitečný asistent. Odpovídej v češtině a uveď zdroje informací.'
                },
                {
                    'role': 'user',
                    'content': request.query
                }
            ],
            'max_tokens': 1000,
            'temperature': 0.2,
            'top_p': 0.9,
            'return_citations': True,
            'search_domain_filter': ['perplexity.ai'],
            'return_images': False,
            'return_related_questions': False,
            'search_recency_filter': 'month',
            'top_k': 0,
            'stream': False,
            'presence_penalty': 0,
            'frequency_penalty': 1
        }
        
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        
        response = requests.post(api_url, json=request_body, headers=headers, timeout=60)
        
        if response.status_code != 200:
            raise Exception(f"HTTP {response.status_code}: {response.text}")
        
        data = response.json()
        
        result = {
            "success": True,
            "query": request.query,
            "response": data.get('choices', [{}])[0].get('message', {}).get('content', 'Žádná odpověď'),
            "model": data.get('model', ''),
            "usage": data.get('usage', {}),
            "citations": data.get('citations', []),
            "api_response": data,
            "api_key_used": "pplx-GgwG..."
        }
        
        logger.info(f"✅ Perplexity test úspěšný: {data.get('usage', {}).get('total_tokens', 0)} tokenů")
        return result
        
    except Exception as e:
        logger.error(f"❌ Perplexity test selhal: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Perplexity test selhal: {str(e)}")
