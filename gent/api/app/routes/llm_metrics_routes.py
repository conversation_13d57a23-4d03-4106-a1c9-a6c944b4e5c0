"""
GENT v10 - API endpointy pro LLM metriky a performance tracking

Tento modul poskytuje API endpointy pro ukládání a načítání
výkonových metrik LLM modelů.
"""

import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
import json

# Nastavení loggeru
logger = logging.getLogger("gent.api.app.routes.llm_metrics_routes")

# Vytvoření routeru
router = APIRouter(
    prefix="/api/llm/metrics",
    tags=["llm_metrics"],
    responses={404: {"description": "Not found"}}
)


class LLMRequestMetric(BaseModel):
    """Model pro LLM request metriku."""
    model_id: Optional[int] = None
    model_name: str
    provider: str
    prompt: str
    response: Optional[str] = None
    tokens_used: Optional[int] = None
    latency: Optional[float] = None  # v milisekundách
    status: str = "pending"  # pending, completed, failed
    error_message: Optional[str] = None
    extra_data: Optional[Dict[str, Any]] = None


def ensure_llm_requests_table():
    """<PERSON><PERSON><PERSON><PERSON>, že tabulka llm_requests existuje."""
    try:
        import psycopg2
        import json
        import os
        
        # Načtení konfigurace databáze
        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
        config_path = os.path.join(base_dir, "config", "postgres_credentials.json")
        
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # Připojení k hlavní databázi
        conn = psycopg2.connect(
            host="localhost",
            port=5432,
            database="gentdb",
            user="postgres",
            password=config["postgres_password"]
        )
        
        cursor = conn.cursor()
        
        # Vytvoření tabulky pokud neexistuje
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS llm_requests (
            id SERIAL PRIMARY KEY,
            model_id INTEGER,
            model_name VARCHAR(255) NOT NULL,
            provider VARCHAR(100) NOT NULL,
            prompt TEXT NOT NULL,
            response TEXT,
            tokens_used INTEGER,
            latency FLOAT,  -- v milisekundách
            status VARCHAR(20) DEFAULT 'pending',
            error_message TEXT,
            extra_data JSONB,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE INDEX IF NOT EXISTS idx_llm_requests_model_id ON llm_requests(model_id);
        CREATE INDEX IF NOT EXISTS idx_llm_requests_model_name ON llm_requests(model_name);
        CREATE INDEX IF NOT EXISTS idx_llm_requests_provider ON llm_requests(provider);
        CREATE INDEX IF NOT EXISTS idx_llm_requests_status ON llm_requests(status);
        CREATE INDEX IF NOT EXISTS idx_llm_requests_created_at ON llm_requests(created_at);
        """
        
        cursor.execute(create_table_sql)
        conn.commit()
        cursor.close()
        conn.close()
        
        logger.info("Tabulka llm_requests byla úspěšně vytvořena nebo již existuje")
        return True
        
    except Exception as e:
        logger.error(f"Chyba při vytváření tabulky llm_requests: {e}")
        return False


def save_llm_request_metric(metric: LLMRequestMetric) -> Dict[str, Any]:
    """Uloží LLM request metriku do databáze."""
    try:
        # Nejdřív zajistíme, že tabulka existuje
        ensure_llm_requests_table()
        
        import psycopg2
        from psycopg2.extras import RealDictCursor
        import json
        import os
        
        # Načtení konfigurace databáze
        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
        config_path = os.path.join(base_dir, "config", "postgres_credentials.json")
        
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # Připojení k hlavní databázi
        conn = psycopg2.connect(
            host="localhost",
            port=5432,
            database="gentdb",
            user="postgres",
            password=config["postgres_password"]
        )
        
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        
        # Vložení nové metriky
        query = """
            INSERT INTO llm_requests (
                model_id, model_name, provider, prompt, response, 
                tokens_used, latency, status, error_message, extra_data, created_at
            )
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            RETURNING id, model_id, model_name, provider, tokens_used, latency, status, created_at
        """
        
        cursor.execute(query, (
            metric.model_id,
            metric.model_name,
            metric.provider,
            metric.prompt,
            metric.response,
            metric.tokens_used,
            metric.latency,
            metric.status,
            metric.error_message,
            json.dumps(metric.extra_data) if metric.extra_data else None,
            datetime.now()
        ))
        
        result = cursor.fetchone()
        metric_dict = dict(result)
        
        # Převod datetime na ISO string
        if metric_dict['created_at']:
            metric_dict['created_at'] = metric_dict['created_at'].isoformat()
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return metric_dict
        
    except Exception as e:
        logger.error(f"Chyba při ukládání LLM metriky do databáze: {e}")
        # Vrátíme simulovaný výsledek
        return {
            "id": 999,
            "model_id": metric.model_id,
            "model_name": metric.model_name,
            "provider": metric.provider,
            "tokens_used": metric.tokens_used,
            "latency": metric.latency,
            "status": metric.status,
            "created_at": datetime.now().isoformat()
        }


@router.post("/record")
async def record_llm_request(metric: LLMRequestMetric):
    """
    Zaznamenává LLM request metriku do databáze.
    
    Args:
        metric: Data o LLM requestu
    
    Returns:
        Uložená metrika s přiděleným ID
    """
    try:
        saved_metric = save_llm_request_metric(metric)
        return {
            "success": True,
            "message": "LLM metrika byla úspěšně uložena",
            "metric": saved_metric
        }
    except Exception as e:
        logger.error(f"Chyba při ukládání LLM metriky: {e}")
        raise HTTPException(status_code=500, detail=f"Chyba při ukládání LLM metriky: {str(e)}")


@router.post("/test-data")
async def create_test_metrics():
    """
    Vytvoří testovací LLM metriky pro demonstraci.
    
    Returns:
        Potvrzení o vytvoření testovacích dat
    """
    try:
        test_metrics = [
            LLMRequestMetric(
                model_id=1,
                model_name="gpt-4o",
                provider="OpenAI",
                prompt="Explain quantum computing",
                response="Quantum computing is a revolutionary technology...",
                tokens_used=150,
                latency=850.5,
                status="completed"
            ),
            LLMRequestMetric(
                model_id=2,
                model_name="claude-3-5-sonnet-20241022",
                provider="Anthropic",
                prompt="Write a Python function",
                response="Here's a Python function that...",
                tokens_used=200,
                latency=1200.3,
                status="completed"
            ),
            LLMRequestMetric(
                model_id=3,
                model_name="gemini-2.0-flash-exp",
                provider="Google",
                prompt="Summarize this text",
                response="The main points are...",
                tokens_used=75,
                latency=650.8,
                status="completed"
            ),
            LLMRequestMetric(
                model_id=1,
                model_name="gpt-4o",
                provider="OpenAI",
                prompt="Failed request test",
                response=None,
                tokens_used=0,
                latency=5000.0,
                status="failed",
                error_message="API timeout"
            )
        ]
        
        created_metrics = []
        for metric in test_metrics:
            saved_metric = save_llm_request_metric(metric)
            created_metrics.append(saved_metric)
        
        return {
            "success": True,
            "message": f"Vytvořeno {len(created_metrics)} testovacích LLM metrik",
            "metrics": created_metrics
        }
        
    except Exception as e:
        logger.error(f"Chyba při vytváření testovacích metrik: {e}")
        raise HTTPException(status_code=500, detail=f"Chyba při vytváření testovacích metrik: {str(e)}")


@router.get("/stats")
async def get_llm_metrics_stats():
    """
    Získá statistiky LLM metrik.
    
    Returns:
        Statistiky LLM requestů
    """
    try:
        import psycopg2
        from psycopg2.extras import RealDictCursor
        import json
        import os
        
        # Načtení konfigurace databáze
        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
        config_path = os.path.join(base_dir, "config", "postgres_credentials.json")
        
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # Připojení k hlavní databázi
        conn = psycopg2.connect(
            host="localhost",
            port=5432,
            database="gentdb",
            user="postgres",
            password=config["postgres_password"]
        )
        
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        
        # Základní statistiky
        cursor.execute("""
            SELECT 
                COUNT(*) as total_requests,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_requests,
                COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_requests,
                AVG(latency) as avg_latency,
                SUM(tokens_used) as total_tokens,
                COUNT(DISTINCT model_name) as unique_models,
                COUNT(DISTINCT provider) as unique_providers
            FROM llm_requests
        """)
        
        stats = cursor.fetchone()
        
        # Statistiky podle modelů
        cursor.execute("""
            SELECT 
                model_name,
                provider,
                COUNT(*) as request_count,
                AVG(latency) as avg_latency,
                SUM(tokens_used) as total_tokens
            FROM llm_requests
            GROUP BY model_name, provider
            ORDER BY request_count DESC
        """)
        
        model_stats = cursor.fetchall()
        
        cursor.close()
        conn.close()
        
        return {
            "overall": dict(stats) if stats else {},
            "by_model": [dict(row) for row in model_stats],
            "last_updated": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Chyba při získávání LLM statistik: {e}")
        raise HTTPException(status_code=500, detail=f"Chyba při získávání LLM statistik: {str(e)}")


@router.delete("/")
async def clear_llm_metrics():
    """
    Vymaže všechny LLM metriky z databáze.
    
    Returns:
        Potvrzení o vymazání
    """
    try:
        import psycopg2
        import json
        import os
        
        # Načtení konfigurace databáze
        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))
        config_path = os.path.join(base_dir, "config", "postgres_credentials.json")
        
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # Připojení k hlavní databázi
        conn = psycopg2.connect(
            host="localhost",
            port=5432,
            database="gentdb",
            user="postgres",
            password=config["postgres_password"]
        )
        
        cursor = conn.cursor()
        cursor.execute("DELETE FROM llm_requests")
        deleted_count = cursor.rowcount
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return {"message": f"Vymazáno {deleted_count} LLM metrik", "deleted_count": deleted_count}
        
    except Exception as e:
        logger.error(f"Chyba při mazání LLM metrik: {e}")
        raise HTTPException(status_code=500, detail=f"Chyba při mazání LLM metrik: {str(e)}")
