"""
GENT v10 - API endpointy pro MCP Management

Tento modul poskytuje API endpointy pro správu MCP poskytovatelů a nástrojů.
Inspirováno LLM Management systémem.
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from typing import Dict, List, Any, Optional
from pydantic import BaseModel
import logging
import json
import sys
import os

# Přidání cesty pro import MCP DB Service
sys.path.insert(0, '/opt/gent/temp')
from simple_mcp_db_service import SimpleMcpDbService

# Nastavení loggeru
logger = logging.getLogger("gent.api.app.routes.mcp_routes")

# Vytvoření routeru
router = APIRouter(
    prefix="/api/mcp",
    tags=["mcp"],
    responses={
        404: {"description": "Not found"},
        500: {"description": "Internal server error"},
        400: {"description": "Bad request"}
    }
)

# Pydantic modely pro validaci

class ProviderBase(BaseModel):
    """Základní model pro MCP poskytovatele."""
    provider_name: str
    provider_type: str = "mcp"
    display_name: Optional[str] = None
    description: Optional[str] = None
    command: str
    base_url: Optional[str] = None
    api_key: Optional[str] = None
    api_version: Optional[str] = None
    auth_type: str = "none"
    rate_limit: Optional[int] = None
    is_active: bool = True
    is_custom: bool = False
    auto_start: bool = True
    health_check_url: Optional[str] = None
    documentation_url: Optional[str] = None
    version: Optional[str] = None

class ProviderCreate(ProviderBase):
    """Model pro vytvoření MCP poskytovatele."""
    pass

class ProviderUpdate(ProviderBase):
    """Model pro aktualizaci MCP poskytovatele."""
    provider_id: int

class ToolBase(BaseModel):
    """Základní model pro MCP nástroj."""
    tool_name: str
    tool_identifier: str
    display_name: Optional[str] = None
    description: Optional[str] = None
    parameters_schema: Optional[Dict[str, Any]] = None
    response_schema: Optional[Dict[str, Any]] = None
    auto_approve: bool = False
    is_active: bool = True
    is_dangerous: bool = False
    capabilities: Optional[Dict[str, Any]] = None
    usage_examples: Optional[Dict[str, Any]] = None
    rate_limit_per_minute: int = 60
    timeout_seconds: int = 30
    retry_attempts: int = 3
    retry_delay: int = 1000
    pricing_input: Optional[float] = None
    pricing_output: Optional[float] = None

class ToolCreate(ToolBase):
    """Model pro vytvoření MCP nástroje."""
    provider_id: int

class ToolUpdate(ToolBase):
    """Model pro aktualizaci MCP nástroje."""
    tool_id: int
    provider_id: int

# API endpointy

@router.get(
    "/providers",
    response_model=List[Dict[str, Any]],
    summary="Seznam MCP poskytovatelů",
    description="""
    Získá seznam všech MCP poskytovatelů z databáze.
    
    Vrací:
    * Seznam poskytovatelů s jejich základními informacemi
    * Počet nástrojů pro každého poskytovatele
    * Status a konfigurační informace
    """
)
async def get_providers():
    """
    Získá seznam všech MCP poskytovatelů.
    """
    try:
        service = SimpleMcpDbService()
        providers = service.get_providers()
        
        logger.info(f"Načteno {len(providers)} MCP poskytovatelů")
        return providers
        
    except Exception as e:
        logger.error(f"Chyba při získávání MCP poskytovatelů: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Nepodařilo se získat MCP poskytovatele: {str(e)}")

@router.get(
    "/providers/{provider_id}",
    response_model=Dict[str, Any],
    summary="Detail MCP poskytovatele",
    description="""
    Získá detail konkrétního MCP poskytovatele včetně všech jeho nástrojů.
    
    Args:
        provider_id: ID poskytovatele
        
    Vrací:
    * Kompletní informace o poskytovateli
    * Seznam všech nástrojů poskytovatele
    * Konfigurace a nastavení
    """
)
async def get_provider_detail(provider_id: int):
    """
    Získá detail MCP poskytovatele včetně jeho nástrojů.
    """
    try:
        service = SimpleMcpDbService()
        provider = service.get_provider_detail(provider_id)
        
        if not provider:
            raise HTTPException(status_code=404, detail=f"MCP poskytovatel s ID {provider_id} nebyl nalezen")
        
        logger.info(f"Načten detail MCP poskytovatele {provider_id} s {len(provider['tools'])} nástroji")
        return provider
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Chyba při získávání detailu MCP poskytovatele {provider_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Nepodařilo se získat detail MCP poskytovatele: {str(e)}")

@router.get(
    "/tools",
    response_model=List[Dict[str, Any]],
    summary="Seznam všech MCP nástrojů",
    description="""
    Získá seznam všech MCP nástrojů ze všech poskytovatelů.
    
    Vrací:
    * Seznam všech nástrojů s informacemi o poskytovateli
    * Konfigurace a možnosti každého nástroje
    * Status a dostupnost
    """
)
async def get_all_tools():
    """
    Získá seznam všech MCP nástrojů ze všech poskytovatelů.
    """
    try:
        service = SimpleMcpDbService()
        providers = service.get_providers()
        
        all_tools = []
        for provider in providers:
            provider_detail = service.get_provider_detail(provider["provider_id"])
            if provider_detail and provider_detail["tools"]:
                for tool in provider_detail["tools"]:
                    # Přidání informací o poskytovateli k nástroji
                    tool["provider_name"] = provider["provider_name"]
                    tool["provider_type"] = provider["provider_type"]
                    tool["provider_display_name"] = provider["display_name"]
                    all_tools.append(tool)
        
        logger.info(f"Načteno {len(all_tools)} MCP nástrojů ze všech poskytovatelů")
        return all_tools
        
    except Exception as e:
        logger.error(f"Chyba při získávání všech MCP nástrojů: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Nepodařilo se získat MCP nástroje: {str(e)}")

@router.get(
    "/tools/{tool_id}",
    response_model=Dict[str, Any],
    summary="Detail MCP nástroje",
    description="""
    Získá detail konkrétního MCP nástroje.
    
    Args:
        tool_id: ID nástroje
        
    Vrací:
    * Kompletní informace o nástroji
    * Schéma parametrů a odpovědí
    * Konfigurace a omezení
    """
)
async def get_tool_detail(tool_id: int):
    """
    Získá detail konkrétního MCP nástroje.
    """
    try:
        service = SimpleMcpDbService()
        providers = service.get_providers()
        
        # Hledání nástroje ve všech poskytovatelích
        for provider in providers:
            provider_detail = service.get_provider_detail(provider["provider_id"])
            if provider_detail and provider_detail["tools"]:
                for tool in provider_detail["tools"]:
                    if tool["tool_id"] == tool_id:
                        # Přidání informací o poskytovateli
                        tool["provider_name"] = provider["provider_name"]
                        tool["provider_type"] = provider["provider_type"]
                        tool["provider_display_name"] = provider["display_name"]
                        
                        logger.info(f"Načten detail MCP nástroje {tool_id}")
                        return tool
        
        raise HTTPException(status_code=404, detail=f"MCP nástroj s ID {tool_id} nebyl nalezen")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Chyba při získávání detailu MCP nástroje {tool_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Nepodařilo se získat detail MCP nástroje: {str(e)}")

@router.get(
    "/stats",
    response_model=Dict[str, Any],
    summary="Statistiky MCP systému",
    description="""
    Získá základní statistiky MCP systému.
    
    Vrací:
    * Počet poskytovatelů a nástrojů
    * Statistiky podle typů
    * Status aktivních/neaktivních komponent
    """
)
async def get_mcp_stats():
    """
    Získá základní statistiky MCP systému.
    """
    try:
        service = SimpleMcpDbService()
        providers = service.get_providers()
        
        # Základní statistiky
        total_providers = len(providers)
        active_providers = len([p for p in providers if p["is_active"]])
        custom_providers = len([p for p in providers if p["is_custom"]])
        
        # Statistiky podle typů
        provider_types = {}
        total_tools = 0
        active_tools = 0
        auto_approve_tools = 0
        
        for provider in providers:
            provider_type = provider["provider_type"]
            if provider_type not in provider_types:
                provider_types[provider_type] = 0
            provider_types[provider_type] += 1
            
            # Statistiky nástrojů
            provider_detail = service.get_provider_detail(provider["provider_id"])
            if provider_detail and provider_detail["tools"]:
                total_tools += len(provider_detail["tools"])
                active_tools += len([t for t in provider_detail["tools"] if t["is_active"]])
                auto_approve_tools += len([t for t in provider_detail["tools"] if t["auto_approve"]])
        
        stats = {
            "providers": {
                "total": total_providers,
                "active": active_providers,
                "custom": custom_providers,
                "by_type": provider_types
            },
            "tools": {
                "total": total_tools,
                "active": active_tools,
                "auto_approve": auto_approve_tools
            }
        }
        
        logger.info(f"Vygenerovány MCP statistiky: {total_providers} poskytovatelů, {total_tools} nástrojů")
        return stats
        
    except Exception as e:
        logger.error(f"Chyba při získávání MCP statistik: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Nepodařilo se získat MCP statistiky: {str(e)}")

# === CRUD OPERACE ===

@router.post(
    "/providers",
    response_model=Dict[str, Any],
    summary="Přidá nový MCP poskytovatel"
)
async def create_mcp_provider(provider_data: Dict[str, Any]):
    """Přidá nový MCP poskytovatel."""
    try:
        logger.info(f"Přidávání MCP poskytovatele: {provider_data.get('provider_name')}")

        # SKUTEČNÉ VLOŽENÍ DO DATABÁZE!
        import psycopg2
        import json

        with open('/opt/gent/config/postgres_credentials.json', 'r') as f:
            creds = json.load(f)

        conn = psycopg2.connect(
            host='localhost',
            database='gentdb',
            user='postgres',
            password=creds['postgres_password']
        )

        cursor = conn.cursor()

        cursor.execute("""
            INSERT INTO mcp_providers (
                provider_name, display_name, provider_type,
                api_key, description, is_active, command
            ) VALUES (%s, %s, %s, %s, %s, %s, %s)
            RETURNING provider_id
        """, (
            provider_data.get('provider_name'),
            provider_data.get('display_name'),
            provider_data.get('provider_type'),
            provider_data.get('api_key'),
            provider_data.get('description'),
            provider_data.get('is_active', True),
            provider_data.get('command', 'custom-command')
        ))

        provider_id = cursor.fetchone()[0]
        conn.commit()
        cursor.close()
        conn.close()

        logger.info(f"✅ MCP poskytovatel přidán s ID: {provider_id}")
        return {"success": True, "provider_id": provider_id, "message": "Poskytovatel přidán"}

    except Exception as e:
        logger.error(f"Chyba při přidávání: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Chyba při přidávání: {str(e)}")

@router.put(
    "/providers/{provider_id}",
    response_model=Dict[str, Any],
    summary="Upraví MCP poskytovatel"
)
async def update_mcp_provider(provider_id: int, provider_data: Dict[str, Any]):
    """Upraví MCP poskytovatel."""
    try:
        logger.info(f"Úprava MCP poskytovatele ID: {provider_id}")

        # SKUTEČNÁ ÚPRAVA V DATABÁZI!
        import psycopg2
        import json

        with open('/opt/gent/config/postgres_credentials.json', 'r') as f:
            creds = json.load(f)

        conn = psycopg2.connect(
            host='localhost',
            database='gentdb',
            user='postgres',
            password=creds['postgres_password']
        )

        cursor = conn.cursor()

        cursor.execute("""
            UPDATE mcp_providers SET
                provider_name = %s,
                display_name = %s,
                provider_type = %s,
                api_key = %s,
                description = %s,
                is_active = %s
            WHERE provider_id = %s
        """, (
            provider_data.get('provider_name'),
            provider_data.get('display_name'),
            provider_data.get('provider_type'),
            provider_data.get('api_key'),
            provider_data.get('description'),
            provider_data.get('is_active', True),
            provider_id
        ))

        conn.commit()
        cursor.close()
        conn.close()

        logger.info(f"✅ MCP poskytovatel {provider_id} upraven")
        return {"success": True, "message": "Poskytovatel upraven"}

    except Exception as e:
        logger.error(f"Chyba při úpravě: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Chyba při úpravě: {str(e)}")

@router.delete(
    "/providers/{provider_id}",
    response_model=Dict[str, Any],
    summary="Smaže MCP poskytovatel"
)
async def delete_mcp_provider(provider_id: int):
    """Smaže MCP poskytovatel."""
    try:
        logger.info(f"Mazání MCP poskytovatele ID: {provider_id}")

        # SKUTEČNÉ MAZÁNÍ Z DATABÁZE!
        import psycopg2
        import json

        with open('/opt/gent/config/postgres_credentials.json', 'r') as f:
            creds = json.load(f)

        conn = psycopg2.connect(
            host='localhost',
            database='gentdb',
            user='postgres',
            password=creds['postgres_password']
        )

        cursor = conn.cursor()

        # Nejdříve smazat nástroje
        cursor.execute("DELETE FROM mcp_tools WHERE provider_id = %s", (provider_id,))

        # Pak smazat poskytovatele
        cursor.execute("DELETE FROM mcp_providers WHERE provider_id = %s", (provider_id,))

        conn.commit()
        cursor.close()
        conn.close()

        logger.info(f"✅ MCP poskytovatel {provider_id} smazán")
        return {"success": True, "message": "Poskytovatel smazán"}

    except Exception as e:
        logger.error(f"Chyba při mazání: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Chyba při mazání: {str(e)}")
