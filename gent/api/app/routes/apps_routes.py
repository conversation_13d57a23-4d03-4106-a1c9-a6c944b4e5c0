"""
API routes pro správu aplikací GENT systému.
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import List, Optional
from datetime import datetime
import uuid
import json

from gent.db.database import get_db_connection
from pydantic import BaseModel


router = APIRouter(prefix="/api/apps", tags=["apps"])


# Pydantic modely pro API
class AppCreate(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    file_path: str
    language: str = "python"
    version: str = "1.0.0"
    author: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[List[str]] = None
    command: Optional[str] = None
    arguments: Optional[dict] = None
    requirements: Optional[dict] = None
    is_active: bool = True
    is_public: bool = False
    ui_config: Optional[dict] = None


class AppUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    file_path: Optional[str] = None
    language: Optional[str] = None
    version: Optional[str] = None
    author: Optional[str] = None
    category: Optional[str] = None
    tags: Optional[List[str]] = None
    command: Optional[str] = None
    arguments: Optional[dict] = None
    requirements: Optional[dict] = None
    is_active: Optional[bool] = None
    is_public: Optional[bool] = None
    ui_config: Optional[dict] = None


class AppExecutionCreate(BaseModel):
    app_id: str
    user_id: Optional[str] = None
    arguments_used: Optional[dict] = None


@router.post("/init-tables")
async def init_tables(db = Depends(get_db_connection)):
    """Inicializuje tabulky pro aplikace."""
    try:
        # Vytvoření tabulek pomocí raw SQL
        await db.execute("""
            CREATE TABLE IF NOT EXISTS apps (
                id VARCHAR PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                file_path VARCHAR(500) NOT NULL,
                language VARCHAR(50) NOT NULL DEFAULT 'python',
                version VARCHAR(50) DEFAULT '1.0.0',
                author VARCHAR(255),
                category VARCHAR(100),
                tags JSON,
                command VARCHAR(500),
                arguments JSON,
                requirements JSON,
                is_active BOOLEAN NOT NULL DEFAULT true,
                is_public BOOLEAN NOT NULL DEFAULT false,
                execution_count INTEGER NOT NULL DEFAULT 0,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                last_executed_at TIMESTAMP WITH TIME ZONE,
                ui_config JSON
            )
        """)

        await db.execute("""
            CREATE TABLE IF NOT EXISTS app_executions (
                id VARCHAR PRIMARY KEY,
                app_id VARCHAR NOT NULL,
                user_id VARCHAR,
                arguments_used JSON,
                exit_code INTEGER,
                output TEXT,
                error_output TEXT,
                execution_time INTEGER,
                status VARCHAR(50) NOT NULL DEFAULT 'running',
                started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                completed_at TIMESTAMP WITH TIME ZONE
            )
        """)

        await db.commit()

        return {"message": "Tabulky pro aplikace byly úspěšně vytvořeny"}

    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Chyba při vytváření tabulek: {str(e)}")


@router.get("/")
async def get_apps(db = Depends(get_db_connection)):
    """Získá seznam všech aplikací."""
    try:
        cursor = await db.execute("SELECT * FROM apps ORDER BY created_at DESC")
        apps = await cursor.fetchall()
        return [dict(app) for app in apps]
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chyba při načítání aplikací: {str(e)}")


@router.get("/{app_id}")
async def get_app(app_id: str, db = Depends(get_db_connection)):
    """Získá detail aplikace."""
    try:
        cursor = await db.execute("SELECT * FROM apps WHERE id = %s", (app_id,))
        app = await cursor.fetchone()
        if not app:
            raise HTTPException(status_code=404, detail="Aplikace nenalezena")
        return dict(app)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chyba při načítání aplikace: {str(e)}")


@router.post("/")
async def create_app(app_data: AppCreate, db = Depends(get_db_connection)):
    """Vytvoří novou aplikaci."""
    try:
        # Kontrola, zda aplikace již existuje
        cursor = await db.execute("SELECT id FROM apps WHERE id = %s", (app_data.id,))
        existing_app = await cursor.fetchone()
        if existing_app:
            raise HTTPException(status_code=400, detail="Aplikace s tímto ID již existuje")

        # Vytvoření nové aplikace
        await db.execute("""
            INSERT INTO apps (
                id, name, description, file_path, language, version, author,
                category, tags, command, arguments, requirements, is_active,
                is_public, ui_config
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            app_data.id, app_data.name, app_data.description, app_data.file_path,
            app_data.language, app_data.version, app_data.author, app_data.category,
            json.dumps(app_data.tags) if app_data.tags else None,
            app_data.command,
            json.dumps(app_data.arguments) if app_data.arguments else None,
            json.dumps(app_data.requirements) if app_data.requirements else None,
            app_data.is_active, app_data.is_public,
            json.dumps(app_data.ui_config) if app_data.ui_config else None
        ))

        await db.commit()

        # Vrátíme vytvořenou aplikaci
        cursor = await db.execute("SELECT * FROM apps WHERE id = %s", (app_data.id,))
        app = await cursor.fetchone()
        return dict(app)

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Chyba při vytváření aplikace: {str(e)}")


# Další endpointy budou implementovány později
# Pro teď ponecháváme jen základní funkcionalitu
