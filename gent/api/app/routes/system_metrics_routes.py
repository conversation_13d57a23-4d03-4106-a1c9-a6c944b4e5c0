"""
GENT v10 - API endpointy pro skutečné systémové metriky

Tento modul poskytuje API endpointy pro získání skutečných
systémových metrik (CPU, RAM, disk, uptime) místo hardcoded hodnot.
"""

import os
import psutil
import subprocess
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any
from fastapi import APIRouter, HTTPException
import logging

# Nastavení loggeru
logger = logging.getLogger("gent.api.app.routes.system_metrics_routes")

# Vytvoření routeru
router = APIRouter(
    prefix="/api/system",
    tags=["system-metrics"],
    responses={404: {"description": "Not found"}}
)


def get_cpu_usage() -> float:
    """Získá aktuální využití CPU v procentech."""
    try:
        # Použijeme psutil pro přesnější měření
        return psutil.cpu_percent(interval=1)
    except Exception as e:
        logger.error(f"Chyba při získávání CPU usage: {e}")
        # Fallback na load average
        try:
            with open('/proc/loadavg', 'r') as f:
                load_avg = float(f.read().split()[0])
            cpu_count = psutil.cpu_count()
            return min((load_avg / cpu_count) * 100, 100)
        except:
            return 0.0


def get_memory_usage() -> Dict[str, Any]:
    """Získá informace o využití paměti."""
    try:
        memory = psutil.virtual_memory()
        return {
            "total": memory.total,
            "used": memory.used,
            "free": memory.available,
            "percent": memory.percent
        }
    except Exception as e:
        logger.error(f"Chyba při získávání memory usage: {e}")
        return {"total": 0, "used": 0, "free": 0, "percent": 0}


def get_disk_usage() -> Dict[str, Any]:
    """Získá informace o využití disku."""
    try:
        disk = psutil.disk_usage('/')
        return {
            "total": disk.total,
            "used": disk.used,
            "free": disk.free,
            "percent": (disk.used / disk.total) * 100
        }
    except Exception as e:
        logger.error(f"Chyba při získávání disk usage: {e}")
        return {"total": 0, "used": 0, "free": 0, "percent": 0}


def get_system_uptime() -> Dict[str, Any]:
    """Získá informace o uptime systému."""
    try:
        boot_time = psutil.boot_time()
        uptime_seconds = datetime.now().timestamp() - boot_time
        uptime_delta = timedelta(seconds=uptime_seconds)
        
        days = uptime_delta.days
        hours, remainder = divmod(uptime_delta.seconds, 3600)
        minutes, _ = divmod(remainder, 60)
        
        return {
            "uptime_seconds": int(uptime_seconds),
            "uptime_formatted": f"{days}d {hours}h {minutes}m",
            "boot_time": datetime.fromtimestamp(boot_time).isoformat()
        }
    except Exception as e:
        logger.error(f"Chyba při získávání uptime: {e}")
        return {"uptime_seconds": 0, "uptime_formatted": "0d 0h 0m", "boot_time": ""}


def get_process_info() -> Dict[str, Any]:
    """Získá informace o GENT procesech."""
    try:
        gent_processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'cpu_percent', 'memory_percent']):
            try:
                cmdline = ' '.join(proc.info['cmdline'] or [])
                if 'gent' in cmdline.lower() or 'uvicorn' in cmdline.lower() or 'vite' in cmdline.lower():
                    gent_processes.append({
                        "pid": proc.info['pid'],
                        "name": proc.info['name'],
                        "cmdline": cmdline[:100] + "..." if len(cmdline) > 100 else cmdline,
                        "cpu_percent": proc.info['cpu_percent'],
                        "memory_percent": proc.info['memory_percent']
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        return {"processes": gent_processes, "count": len(gent_processes)}
    except Exception as e:
        logger.error(f"Chyba při získávání process info: {e}")
        return {"processes": [], "count": 0}


@router.get("/metrics")
async def get_system_metrics():
    """
    Získá aktuální systémové metriky.
    """
    try:
        cpu_usage = get_cpu_usage()
        memory_info = get_memory_usage()
        disk_info = get_disk_usage()
        uptime_info = get_system_uptime()
        process_info = get_process_info()
        
        return {
            "timestamp": datetime.now().isoformat(),
            "cpu": {
                "usage_percent": round(cpu_usage, 1),
                "cores": psutil.cpu_count(),
                "load_average": os.getloadavg() if hasattr(os, 'getloadavg') else [0, 0, 0]
            },
            "memory": {
                "usage_percent": round(memory_info["percent"], 1),
                "total_gb": round(memory_info["total"] / (1024**3), 1),
                "used_gb": round(memory_info["used"] / (1024**3), 1),
                "free_gb": round(memory_info["free"] / (1024**3), 1)
            },
            "disk": {
                "usage_percent": round(disk_info["percent"], 1),
                "total_gb": round(disk_info["total"] / (1024**3), 1),
                "used_gb": round(disk_info["used"] / (1024**3), 1),
                "free_gb": round(disk_info["free"] / (1024**3), 1)
            },
            "uptime": uptime_info,
            "processes": process_info
        }
    except Exception as e:
        logger.error(f"Chyba při získávání systémových metrik: {e}")
        raise HTTPException(status_code=500, detail=f"Chyba při získávání systémových metrik: {str(e)}")


@router.get("/health")
async def get_system_health():
    """
    Získá zdravotní stav systému.
    """
    try:
        metrics = await get_system_metrics()
        
        # Určení zdravotního stavu na základě metrik
        cpu_status = "healthy" if metrics["cpu"]["usage_percent"] < 80 else "warning" if metrics["cpu"]["usage_percent"] < 95 else "critical"
        memory_status = "healthy" if metrics["memory"]["usage_percent"] < 80 else "warning" if metrics["memory"]["usage_percent"] < 95 else "critical"
        disk_status = "healthy" if metrics["disk"]["usage_percent"] < 80 else "warning" if metrics["disk"]["usage_percent"] < 90 else "critical"
        
        # Celkový stav
        statuses = [cpu_status, memory_status, disk_status]
        if "critical" in statuses:
            overall_status = "critical"
        elif "warning" in statuses:
            overall_status = "warning"
        else:
            overall_status = "healthy"
        
        return {
            "overall_status": overall_status,
            "components": {
                "cpu": cpu_status,
                "memory": memory_status,
                "disk": disk_status
            },
            "metrics": metrics
        }
    except Exception as e:
        logger.error(f"Chyba při získávání zdravotního stavu: {e}")
        raise HTTPException(status_code=500, detail=f"Chyba při získávání zdravotního stavu: {str(e)}")
