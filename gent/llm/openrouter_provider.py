"""
GENT v10 - OpenRouter poskytovatel LLM

Tento modul implementuje poskytovatele LLM pro OpenRouter API
včetně podpory pro streamování odpověd<PERSON>, počít<PERSON><PERSON> tokenů a dal<PERSON><PERSON> funkce.
OpenRouter poskytuje přístup k různým LLM modelům přes jednotné API.
"""

import os
import json
import logging
import asyncio
import time
import httpx
from datetime import datetime
from typing import Dict, List, Optional, Union, Any, AsyncIterator, cast, Callable
import tiktoken

from gent.config.config_loader import get_env_var, load_config
from .base_provider import (
    LLMProvider, LLMProviderConfig, LLMResponse, Message, MessageRole, TokenUsage,
    LLMError, RateLimitError, AuthenticationError, ModelNotAvailableError,
    RequestTimeoutError, ContentFilterError, InvalidRequestError, ServerError
)

logger = logging.getLogger(__name__)


class OpenRouterProvider(LLMProvider):
    """
    Poskytovatel LLM pro OpenRouter API.
    
    OpenRouter poskytuje přístup k různým LLM modelům (OpenAI, Anthropic, Google, atd.)
    přes jednotné OpenAI-kompatibilní API.
    """

    def __init__(self, config: Optional[LLMProviderConfig] = None):
        """
        Inicializuje OpenRouter poskytovatele.

        Args:
            config: Konfigurace poskytovatele (pokud None, načte se z konfiguračního souboru)
        """
        if config is None:
            # Načtení konfigurace z llm_config.json
            llm_config = load_config("llm_config")
            openrouter_config = llm_config.get("providers", {}).get("openrouter", {})

            # Získání API klíče z proměnné prostředí, pokud je v konfiguraci jako ${OPENROUTER_API_KEY}
            api_key = openrouter_config.get("api_key", "")
            if api_key.startswith("${") and api_key.endswith("}"):
                env_var_name = api_key[2:-1]
                api_key = get_env_var(env_var_name, "")

            config = LLMProviderConfig(
                api_key=api_key,
                base_url=openrouter_config.get("base_url", "https://openrouter.ai/api/v1"),
                default_model=openrouter_config.get("default_model", "nousresearch/deephermes-3-mistral-24b-preview:free"),
                timeout=openrouter_config.get("timeout", 60),
                retry_count=openrouter_config.get("retry_count", 3),
                models=openrouter_config.get("models", {})
            )

        super().__init__(config)

        # Inicializace HTTP klienta s OpenRouter specifickými headers
        self.http_client = httpx.AsyncClient(
            base_url=config.base_url,
            headers={
                "Authorization": f"Bearer {config.api_key}",
                "HTTP-Referer": "http://192.168.14.150:8000",  # GENT site URL
                "X-Title": "GENT v10",  # GENT site name
                "Content-Type": "application/json"
            },
            timeout=config.timeout,
        )

        # Tokenizerové cache (používáme OpenAI tokenizer pro kompatibilitu)
        self._tokenizers = {}

        logger.info(f"Inicializován OpenRouter poskytovatel s výchozím modelem {self.default_model}")

    async def generate_text(
        self,
        prompt: str,
        model: Optional[str] = None,
        max_tokens: int = 1000,
        temperature: float = 0.7,
        top_p: float = 1.0,
        frequency_penalty: float = 0.0,
        presence_penalty: float = 0.0,
        stop: Optional[Union[str, List[str]]] = None,
        stream: bool = False,
        **kwargs
    ) -> Union[LLMResponse, AsyncIterator[str]]:
        """
        Generuje text pomocí OpenRouter API.

        Args:
            prompt: Vstupní prompt
            model: Název modelu (pokud None, použije se výchozí)
            max_tokens: Maximální počet tokenů v odpovědi
            temperature: Teplota generování (0.0-2.0)
            top_p: Top-p sampling (0.0-1.0)
            frequency_penalty: Penalizace frekvence (-2.0 až 2.0)
            presence_penalty: Penalizace přítomnosti (-2.0 až 2.0)
            stop: Stop sekvence
            stream: Zda streamovat odpověď
            **kwargs: Další parametry

        Returns:
            LLMResponse nebo AsyncIterator[str] pro streamování
        """
        model = model or self.default_model
        
        # Převod na chat formát (OpenRouter používá chat completions)
        messages = [{"role": "user", "content": prompt}]
        
        return await self.chat_completion(
            messages=messages,
            model=model,
            max_tokens=max_tokens,
            temperature=temperature,
            top_p=top_p,
            frequency_penalty=frequency_penalty,
            presence_penalty=presence_penalty,
            stop=stop,
            stream=stream,
            **kwargs
        )

    async def chat_completion(
        self,
        messages: List[Message],
        model: Optional[str] = None,
        max_tokens: int = 1000,
        temperature: float = 0.7,
        top_p: float = 1.0,
        frequency_penalty: float = 0.0,
        presence_penalty: float = 0.0,
        stop: Optional[Union[str, List[str]]] = None,
        stream: bool = False,
        **kwargs
    ) -> Union[LLMResponse, AsyncIterator[str]]:
        """
        Generuje odpověď v chatovém formátu pomocí OpenRouter API.

        Args:
            messages: Seznam zpráv v konverzaci
            model: Název modelu (pokud None, použije se výchozí)
            max_tokens: Maximální počet tokenů v odpovědi
            temperature: Teplota generování (0.0-2.0)
            top_p: Top-p sampling (0.0-1.0)
            frequency_penalty: Penalizace frekvence (-2.0 až 2.0)
            presence_penalty: Penalizace přítomnosti (-2.0 až 2.0)
            stop: Stop sekvence
            stream: Zda streamovat odpověď
            **kwargs: Další parametry

        Returns:
            LLMResponse nebo AsyncIterator[str] pro streamování
        """
        model = model or self.default_model
        
        # Příprava payload pro OpenRouter API
        payload = {
            "model": model,
            "messages": self._format_messages(messages),
            "max_tokens": max_tokens,
            "temperature": temperature,
            "top_p": top_p,
            "frequency_penalty": frequency_penalty,
            "presence_penalty": presence_penalty,
            "stream": stream
        }

        if stop:
            payload["stop"] = stop

        # Přidání dalších parametrů
        payload.update({k: v for k, v in kwargs.items() if k not in payload})

        if stream:
            return self._stream_chat_completion(payload)
        else:
            return await self._complete_chat_completion(payload)

    def _format_messages(self, messages: List[Message]) -> List[Dict[str, str]]:
        """
        Formátuje zprávy pro OpenRouter API.

        Args:
            messages: Seznam zpráv

        Returns:
            List[Dict[str, str]]: Formátované zprávy
        """
        formatted = []
        for message in messages:
            if isinstance(message, dict):
                formatted.append(message)
            else:
                formatted.append({
                    "role": message.role.value if hasattr(message.role, 'value') else str(message.role),
                    "content": message.content
                })
        return formatted

    async def _complete_chat_completion(self, payload: Dict[str, Any]) -> LLMResponse:
        """
        Provede kompletní chat completion bez streamování.

        Args:
            payload: Payload pro API

        Returns:
            LLMResponse: Odpověď z modelu
        """
        start_time = time.time()
        
        try:
            response = await self.http_client.post("/chat/completions", json=payload)
            response.raise_for_status()
            
            data = response.json()
            end_time = time.time()
            
            # Extrakce odpovědi
            choice = data["choices"][0]
            content = choice["message"]["content"]
            finish_reason = choice.get("finish_reason", "stop")
            
            # Extrakce usage informací
            usage_data = data.get("usage", {})
            token_usage = TokenUsage(
                prompt_tokens=usage_data.get("prompt_tokens", 0),
                completion_tokens=usage_data.get("completion_tokens", 0),
                total_tokens=usage_data.get("total_tokens", 0)
            )
            
            return LLMResponse(
                content=content,
                model=payload["model"],
                finish_reason=finish_reason,
                token_usage=token_usage,
                response_time=end_time - start_time,
                provider="openrouter"
            )
            
        except httpx.HTTPStatusError as e:
            await self._handle_http_error(e)
        except Exception as e:
            logger.error(f"Neočekávaná chyba při OpenRouter API volání: {e}")
            raise ServerError(f"Neočekávaná chyba: {e}")

    async def _stream_chat_completion(self, payload: Dict[str, Any]) -> AsyncIterator[str]:
        """
        Provede streamovanou chat completion.

        Args:
            payload: Payload pro API

        Yields:
            str: Části odpovědi
        """
        try:
            async with self.http_client.stream("POST", "/chat/completions", json=payload) as response:
                response.raise_for_status()
                
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data_str = line[6:]  # Odstranění "data: "
                        
                        if data_str.strip() == "[DONE]":
                            break
                            
                        try:
                            data = json.loads(data_str)
                            choice = data["choices"][0]
                            delta = choice.get("delta", {})
                            content = delta.get("content", "")
                            
                            if content:
                                yield content
                                
                        except json.JSONDecodeError:
                            continue
                            
        except httpx.HTTPStatusError as e:
            await self._handle_http_error(e)
        except Exception as e:
            logger.error(f"Neočekávaná chyba při streamovaném OpenRouter API volání: {e}")
            raise ServerError(f"Neočekávaná chyba: {e}")

    async def _handle_http_error(self, error: httpx.HTTPStatusError):
        """
        Zpracuje HTTP chyby z OpenRouter API.

        Args:
            error: HTTP chyba

        Raises:
            Příslušná LLM chyba
        """
        status_code = error.response.status_code
        
        try:
            error_data = error.response.json()
            error_message = error_data.get("error", {}).get("message", str(error))
        except:
            error_message = str(error)
        
        if status_code == 401:
            raise AuthenticationError(f"Neplatný OpenRouter API klíč: {error_message}")
        elif status_code == 403:
            raise AuthenticationError(f"Přístup zamítnut: {error_message}")
        elif status_code == 404:
            raise ModelNotAvailableError(f"Model nenalezen: {error_message}")
        elif status_code == 429:
            raise RateLimitError(f"Rate limit překročen: {error_message}")
        elif status_code == 400:
            raise InvalidRequestError(f"Neplatný požadavek: {error_message}")
        elif status_code >= 500:
            raise ServerError(f"Chyba serveru OpenRouter: {error_message}")
        else:
            raise LLMError(f"OpenRouter API chyba {status_code}: {error_message}")

    def count_tokens(self, text: str, model: Optional[str] = None) -> int:
        """
        Spočítá tokeny v textu.

        Args:
            text: Text k spočítání
            model: Název modelu (pro výběr tokenizeru)

        Returns:
            int: Počet tokenů
        """
        model = model or self.default_model
        
        # Pro OpenRouter používáme OpenAI tokenizer jako aproximaci
        # Různé modely mohou mít různé tokenizery, ale toto je rozumná aproximace
        try:
            if model not in self._tokenizers:
                # Použijeme cl100k_base tokenizer jako výchozí
                self._tokenizers[model] = tiktoken.get_encoding("cl100k_base")
            
            tokenizer = self._tokenizers[model]
            return len(tokenizer.encode(text))
            
        except Exception as e:
            logger.warning(f"Chyba při počítání tokenů pro model {model}: {e}")
            # Fallback: aproximace na základě počtu slov
            return len(text.split()) * 1.3  # Hrubá aproximace

    async def get_available_models(self) -> List[str]:
        """
        Získá seznam dostupných modelů z OpenRouter API.

        Returns:
            List[str]: Seznam názvů modelů
        """
        try:
            response = await self.http_client.get("/models")
            response.raise_for_status()
            
            data = response.json()
            models = [model["id"] for model in data.get("data", [])]
            
            logger.info(f"Načteno {len(models)} modelů z OpenRouter API")
            return models
            
        except Exception as e:
            logger.error(f"Chyba při načítání modelů z OpenRouter API: {e}")
            return []

    async def close(self):
        """Uzavře HTTP klienta."""
        if self.http_client:
            await self.http_client.aclose()

    def __del__(self):
        """Destruktor - zajistí uzavření HTTP klienta."""
        if hasattr(self, 'http_client') and self.http_client:
            try:
                asyncio.create_task(self.http_client.aclose())
            except:
                pass
