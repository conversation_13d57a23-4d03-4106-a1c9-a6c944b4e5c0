"""
GENT v10 - Služba pro přímý přístup k LLM konfiguracím v databázi

Tento modul poskytuje třídu pro přístup k LLM konfiguracím přímo v PostgreSQL databázi
bez nutnosti použití API. Slouží pro frontend, který potřebuje načítat a ukládat
konfigurace LLM modelů, cache a embedding modelů.

V souladu s hlavními pravidly je před přístupem k DB vždy aktivováno virtuální prostředí.
"""

import json
import logging
import uuid
import os
from typing import Dict, List, Any, Optional, Union
from datetime import datetime

from gent.db.init_db_env import activate_venv
from gent.db.direct_connector import get_db_connection
from gent.db.models.config import ConfigType, LlmConfig, LlmCacheConfig, EmbeddingConfig

# Nastavení loggeru
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s"
)
logger = logging.getLogger("gent.db.llm_db_service")


class LlmDirectDbService:
    """
    Služba pro přímý přístup k LLM konfiguracím v databázi.
    
    Tato třída poskytuje metody pro čtení a zápis konfigurací LLM, cache
    a embedding modelů přímo v PostgreSQL databázi bez nutnosti použití API.
    """
    
    def __init__(self):
        """Inicializace služby pro přímý přístup k databázi."""
        # Při inicializaci nepřipojujeme k DB, aby neblokovala
        # Připojení vytvoříme až při prvním požadavku
        self.conn = None
    
    def _get_connection(self):
        """Získání připojení k databázi."""
        # V souladu s hlavními pravidly aktivujeme virtuální prostředí
        venv_activated = activate_venv()
        if not venv_activated:
            logger.warning("Nepodařilo se aktivovat virtuální prostředí, přístup k DB může selhat")
        
        if self.conn is None or self.conn.closed:
            try:
                self.conn = get_db_connection()
                if self.conn is None:
                    if not venv_activated:
                        raise Exception("Nepodařilo se připojit k databázi - virtuální prostředí není aktivováno")
                    else:
                        raise Exception("Nepodařilo se připojit k databázi")
            except Exception as e:
                logger.error(f"Chyba při připojování k databázi: {str(e)}")
                if not venv_activated:
                    logger.error("Pravděpodobná příčina: Virtuální prostředí není aktivováno")
                raise
        return self.conn
    
    def _close_connection(self):
        """Uzavření připojení k databázi."""
        if self.conn and not self.conn.closed:
            self.conn.close()
            self.conn = None
    
    def get_providers(self) -> List[Dict[str, Any]]:
        """
        Získá seznam poskytovatelů LLM z databáze.
        
        Returns:
            List[Dict[str, Any]]: Seznam poskytovatelů jako slovníky
            
        Raises:
            Exception: Pokud se nepodaří získat data z databáze
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            # Získání všech LLM konfigurací z tabulky llm_configs
            cursor.execute("""
                SELECT 
                    config_id, provider, model, api_key, base_url, timeout, 
                    retry_count, is_default, parameters
                FROM 
                    llm_configs
            """)
            
            providers = []
            for row in cursor.fetchall():
                provider = {
                    "id": row[0],
                    "name": row[1],
                    "model": row[2],
                    "api_key": row[3] if row[3] else "********",
                    "base_url": row[4],
                    "timeout": row[5],
                    "retry_count": row[6],
                    "is_default": row[7],
                }
                # Zpracování parametrů, pokud existují
                if row[8]:
                    try:
                        if isinstance(row[8], str):
                            params = json.loads(row[8])
                        else:
                            params = row[8]
                        provider.update(params)
                    except json.JSONDecodeError:
                        logger.warning(f"Chyba při dekódování JSON parametrů pro poskytovatele {row[1]}")
                
                providers.append(provider)
            
            cursor.close()
            return providers
            
        except Exception as e:
            logger.error(f"Chyba při získávání poskytovatelů z databáze: {str(e)}")
            # Propagujeme chybu dále místo vracení prázdného seznamu
            raise Exception(f"Nelze načíst poskytovatele z databáze: {str(e)}")
        finally:
            # Necháme připojení otevřené pro další požadavky
            pass
    
    def get_provider_detail(self, provider_id: str) -> Optional[Dict[str, Any]]:
        """
        Získá detail poskytovatele podle ID.
        
        Args:
            provider_id: ID poskytovatele
            
        Returns:
            Optional[Dict[str, Any]]: Detail poskytovatele nebo None, pokud neexistuje
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            # Získání detailu poskytovatele podle ID
            cursor.execute("""
                SELECT 
                    config_id, provider, model, api_key, base_url, timeout, 
                    retry_count, is_default, parameters
                FROM 
                    llm_configs
                WHERE 
                    config_id = %s
            """, (provider_id,))
            
            row = cursor.fetchone()
            if not row:
                return None
            
            provider = {
                "id": row[0],
                "name": row[1],
                "model": row[2],
                "api_key": row[3] if row[3] else "********",
                "base_url": row[4],
                "timeout": row[5],
                "retry_count": row[6],
                "is_default": row[7],
                "models": {}  # Zde budeme ukládat modely poskytovatele
            }
            
            # Načtení modelů z tabulky llm_models
            cursor.execute("""
                SELECT 
                    id, provider_id, name, context_window, max_tokens, is_default,
                    capabilities, suitable_for, parameters, created_at, updated_at
                FROM 
                    llm_models
                WHERE 
                    provider_id = %s
            """, (provider_id,))
            
            models = cursor.fetchall()
            
            # Použijeme strukturu tabulky llm_models
            for model_row in models:
                model_name = model_row[2]  # Název modelu
                
                # Parsování JSONB sloupců
                capabilities = model_row[6]
                if isinstance(capabilities, str):
                    try:
                        capabilities = json.loads(capabilities)
                    except json.JSONDecodeError:
                        capabilities = ["text"]
                
                suitable_for = model_row[7]
                if isinstance(suitable_for, str):
                    try:
                        suitable_for = json.loads(suitable_for)
                    except json.JSONDecodeError:
                        suitable_for = ["general"]
                
                # Vytvoření detailu modelu pro fronted
                provider["models"][model_name] = {
                    "context_window": model_row[3],
                    "max_tokens": model_row[4],
                    "capabilities": capabilities,
                    "suitable_for": suitable_for
                }
                
                # Nastavení výchozího modelu, pokud je tento model výchozí
                if model_row[5]:  # is_default
                    provider["model"] = model_name
            
            logger.info(f"Načteno {len(models)} modelů z tabulky llm_models pro poskytovatele {provider_id}")
            
            # Zpracování parametrů, pokud existují (ale už ne modely z JSON)
            if row[8]:
                try:
                    if isinstance(row[8], str):
                        params = json.loads(row[8])
                    else:
                        params = row[8]
                    
                    # Přidáme další parametry (kromě modelů)
                    for key, value in params.items():
                        if key != "models":
                            provider[key] = value
                            
                except json.JSONDecodeError:
                    logger.warning(f"Chyba při dekódování JSON parametrů pro poskytovatele {row[1]}")
            
            # Pokud poskytovatel nemá žádné modely, ale má defaultní model, vytvoříme záznam v DB
            if not provider["models"] and provider["model"]:
                model_name = provider["model"]
                model_id = str(uuid.uuid4())
                current_time = datetime.now()
                
                try:
                    # Přidáme model do tabulky llm_models
                    capabilities = json.dumps(["text"])
                    suitable_for = json.dumps(["general"])
                    
                    cursor.execute("""
                        INSERT INTO llm_models (
                            id, provider_id, name, context_window, max_tokens, 
                            is_default, capabilities, suitable_for, created_at, updated_at
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        model_id,
                        provider_id,
                        model_name,
                        32000,
                        4096,
                        True,
                        capabilities,
                        suitable_for,
                        current_time,
                        current_time
                    ))
                    
                    conn.commit()
                    logger.info(f"Vytvořen záznam pro výchozí model {model_name} v tabulce llm_models")
                    
                    # Přidáme model do odpovědi
                    provider["models"][model_name] = {
                        "context_window": 32000,
                        "max_tokens": 4096,
                        "capabilities": ["text"],
                        "suitable_for": ["general"]
                    }
                    logger.info(f"Přidán výchozí model {model_name} do seznamu modelů pro poskytovatele {provider_id}")
                except Exception as e:
                    logger.error(f"Nepodařilo se vytvořit záznam pro model {model_name} v DB: {str(e)}")
                    raise Exception(f"Nepodařilo se vytvořit model v databázi: {str(e)}")
            
            cursor.close()
            return provider
            
        except Exception as e:
            logger.error(f"Chyba při získávání detailu poskytovatele {provider_id}: {str(e)}")
            return None
        finally:
            # Necháme připojení otevřené pro další požadavky
            pass
    
    def save_provider(self, provider_data: Dict[str, Any]) -> bool:
        """
        Uloží nebo aktualizuje poskytovatele v databázi.
        
        Args:
            provider_data: Data poskytovatele
            
        Returns:
            bool: True pokud se podařilo uložit, jinak False
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            # Extrahujeme základní data
            provider_id = provider_data.get("id")
            name = provider_data.get("name")
            model = provider_data.get("model")
            api_key = provider_data.get("api_key")
            base_url = provider_data.get("base_url")
            timeout = provider_data.get("timeout", 60)
            retry_count = provider_data.get("retry_count", 3)
            is_default = provider_data.get("is_default", False)
            
            # Připravíme parametry jako JSON
            params = {}
            for key, value in provider_data.items():
                if key not in ["id", "name", "model", "api_key", "base_url", "timeout", "retry_count", "is_default"]:
                    params[key] = value
            
            # Zkontrolujeme, zda poskytovatel již existuje v config_entities
            cursor.execute("SELECT COUNT(*) FROM config_entities WHERE id = %s", (provider_id,))
            entity_exists = cursor.fetchone()[0] > 0
            
            # Zkontrolujeme, zda poskytovatel již existuje v llm_configs
            cursor.execute("SELECT COUNT(*) FROM llm_configs WHERE config_id = %s", (provider_id,))
            config_exists = cursor.fetchone()[0] > 0
            
            current_time = datetime.now()
            
            if entity_exists and config_exists:
                # Aktualizujeme existujícího poskytovatele v config_entities
                cursor.execute("""
                    UPDATE config_entities
                    SET 
                        name = %s,
                        updated_at = %s
                    WHERE id = %s
                """, (
                    name,
                    current_time,
                    provider_id
                ))
                
                # Aktualizujeme existujícího poskytovatele v llm_configs
                cursor.execute("""
                    UPDATE llm_configs
                    SET 
                        provider = %s,
                        model = %s,
                        api_key = %s,
                        base_url = %s,
                        timeout = %s,
                        retry_count = %s,
                        is_default = %s,
                        parameters = %s
                    WHERE config_id = %s
                """, (
                    name, 
                    model, 
                    api_key, 
                    base_url, 
                    timeout, 
                    retry_count, 
                    is_default, 
                    json.dumps(params), 
                    provider_id
                ))
            else:
                # Vytvoříme nového poskytovatele
                if not provider_id:
                    provider_id = str(uuid.uuid4())
                
                # Nejprve vytvoříme záznam v config_entities
                if not entity_exists:
                    cursor.execute("""
                        INSERT INTO config_entities (
                            id, type, name, description, created_at, updated_at
                        ) VALUES (%s, %s, %s, %s, %s, %s)
                    """, (
                        provider_id,
                        "LLM_CONFIG",
                        name,
                        f"LLM konfigurace pro poskytovatele {name}",
                        current_time,
                        current_time
                    ))
                
                # Poté vytvoříme záznam v llm_configs
                if not config_exists:
                    cursor.execute("""
                        INSERT INTO llm_configs (
                            config_id, provider, model, api_key, base_url, timeout, 
                            retry_count, is_default, parameters
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        provider_id,
                        name,
                        model,
                        api_key,
                        base_url,
                        timeout,
                        retry_count,
                        is_default,
                        json.dumps(params)
                    ))
            
            # Pokud je tento poskytovatel výchozí, nastavíme ostatní jako nevýchozí
            if is_default:
                cursor.execute("""
                    UPDATE llm_configs
                    SET is_default = FALSE
                    WHERE config_id != %s
                """, (provider_id,))
            
            conn.commit()
            cursor.close()
            return True
            
        except Exception as e:
            logger.error(f"Chyba při ukládání poskytovatele: {str(e)}")
            if conn:
                conn.rollback()
            return False
        finally:
            # Necháme připojení otevřené pro další požadavky
            pass
    
    def get_cache_config(self) -> Dict[str, Any]:
        """
        Získá konfiguraci LLM cache z databáze.
        
        Returns:
            Dict[str, Any]: Konfigurace cache z databáze
            
        Raises:
            Exception: Pokud se nepodaří získat data z databáze a v databázi není výchozí konfigurace
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            # Získání všech cache konfigurací z tabulky llm_cache_configs
            cursor.execute("""
                SELECT 
                    config_id, enabled, ttl, backend, connection_string, namespace, similarity_threshold
                FROM 
                    llm_cache_configs
                LIMIT 1
            """)
            
            row = cursor.fetchone()
            if not row:
                logger.warning("V databázi nebyla nalezena žádná cache konfigurace")
                # Pokud v DB nic není, pokusíme se vytvořit výchozí konfiguraci
                cache_id = str(uuid.uuid4())
                current_time = datetime.now()
                
                # Vytvoříme záznam v config_entities
                cursor.execute("""
                    INSERT INTO config_entities (
                        id, type, name, description, created_at, updated_at
                    ) VALUES (%s, %s, %s, %s, %s, %s)
                """, (
                    cache_id,
                    "CACHE_CONFIG",
                    "Cache Config Default",
                    "Výchozí konfigurace cache",
                    current_time,
                    current_time
                ))
                
                # Vytvoříme záznam v llm_cache_configs s výchozími hodnotami
                cursor.execute("""
                    INSERT INTO llm_cache_configs (
                        config_id, enabled, ttl, backend, namespace, similarity_threshold
                    ) VALUES (%s, %s, %s, %s, %s, %s)
                """, (
                    cache_id,
                    True,
                    3600,
                    "redis",
                    "llm_cache",
                    0.95
                ))
                
                conn.commit()
                
                # Vrátíme nově vytvořenou konfiguraci
                cache_config = {
                    "id": cache_id,
                    "enabled": True,
                    "ttl": 3600,
                    "backend": "redis",
                    "connection_string": None,
                    "namespace": "llm_cache",
                    "similarity_threshold": 0.95
                }
                
                logger.info("Vytvořena výchozí cache konfigurace v databázi")
                cursor.close()
                return cache_config
            
            cache_config = {
                "id": row[0],
                "enabled": row[1],
                "ttl": row[2],
                "backend": row[3],
                "connection_string": row[4],
                "namespace": row[5],
                "similarity_threshold": row[6]
            }
            
            cursor.close()
            return cache_config
            
        except Exception as e:
            logger.error(f"Chyba při získávání konfigurace cache: {str(e)}")
            # Propagujeme chybu dále místo vracení statických dat
            raise Exception(f"Nelze načíst konfiguraci cache z databáze: {str(e)}")
        finally:
            # Necháme připojení otevřené pro další požadavky
            pass
    
    def save_cache_config(self, cache_data: Dict[str, Any]) -> bool:
        """
        Uloží konfiguraci LLM cache do databáze.
        
        Args:
            cache_data: Data konfigurace cache
            
        Returns:
            bool: True pokud se podařilo uložit, jinak False
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            # Extrahujeme data
            cache_id = cache_data.get("id")
            enabled = cache_data.get("enabled", True)
            ttl = cache_data.get("ttl", 3600)
            backend = cache_data.get("backend", "redis")
            connection_string = cache_data.get("connection_string")
            namespace = cache_data.get("namespace", "llm_cache")
            similarity_threshold = cache_data.get("similarity_threshold", 0.95)
            
            # Zkontrolujeme, zda konfigurace již existuje v config_entities
            if not cache_id:
                cache_id = str(uuid.uuid4())
                
            cursor.execute("SELECT COUNT(*) FROM config_entities WHERE id = %s", (cache_id,))
            entity_exists = cursor.fetchone()[0] > 0
            
            # Zkontrolujeme, zda konfigurace již existuje v llm_cache_configs
            cursor.execute("SELECT COUNT(*) FROM llm_cache_configs WHERE config_id = %s", (cache_id,))
            config_exists = cursor.fetchone()[0] > 0
            
            current_time = datetime.now()
            
            if entity_exists and config_exists:
                # Aktualizujeme existující konfiguraci v config_entities
                cursor.execute("""
                    UPDATE config_entities
                    SET 
                        name = %s,
                        updated_at = %s
                    WHERE id = %s
                """, (
                    f"Cache Config {backend}",
                    current_time,
                    cache_id
                ))
                
                # Aktualizujeme existující konfiguraci v llm_cache_configs
                cursor.execute("""
                    UPDATE llm_cache_configs
                    SET 
                        enabled = %s,
                        ttl = %s,
                        backend = %s,
                        connection_string = %s,
                        namespace = %s,
                        similarity_threshold = %s
                    WHERE config_id = %s
                """, (
                    enabled,
                    ttl,
                    backend,
                    connection_string,
                    namespace,
                    similarity_threshold,
                    cache_id
                ))
            else:
                # Nejprve vytvoříme záznam v config_entities
                if not entity_exists:
                    cursor.execute("""
                        INSERT INTO config_entities (
                            id, type, name, description, created_at, updated_at
                        ) VALUES (%s, %s, %s, %s, %s, %s)
                    """, (
                        cache_id,
                        "CACHE_CONFIG",
                        f"Cache Config {backend}",
                        f"Konfigurace cache pro backend {backend}",
                        current_time,
                        current_time
                    ))
                
                # Poté vytvoříme záznam v llm_cache_configs
                if not config_exists:
                    cursor.execute("""
                        INSERT INTO llm_cache_configs (
                            config_id, enabled, ttl, backend, connection_string,
                            namespace, similarity_threshold
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s)
                    """, (
                        cache_id,
                        enabled,
                        ttl,
                        backend,
                        connection_string,
                        namespace,
                        similarity_threshold
                    ))
            
            conn.commit()
            cursor.close()
            return True
            
        except Exception as e:
            logger.error(f"Chyba při ukládání konfigurace cache: {str(e)}")
            if conn:
                conn.rollback()
            return False
        finally:
            # Necháme připojení otevřené pro další požadavky
            pass
    
    def get_embedding_models(self) -> Dict[str, Dict[str, Any]]:
        """
        Získá embedding modely z databáze.
        
        Returns:
            Dict[str, Dict[str, Any]]: Slovník embedding modelů, kde klíčem je ID modelu
            
        Raises:
            Exception: Pokud se nepodaří získat data z databáze
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            # Získání všech embedding konfigurací z tabulky embedding_configs
            cursor.execute("""
                SELECT 
                    config_id, provider, model, api_key, base_url, dimension,
                    normalized, batch_size, timeout, is_default, parameters
                FROM 
                    embedding_configs
            """)
            
            embedding_models = {}
            for row in cursor.fetchall():
                model = {
                    "id": row[0],
                    "provider": row[1],
                    "model": row[2],
                    "api_key": row[3] if row[3] else "********",
                    "base_url": row[4],
                    "dimension": row[5],
                    "normalized": row[6],
                    "batch_size": row[7],
                    "timeout": row[8],
                    "is_default": row[9]
                }
                
                # Zpracování parametrů, pokud existují
                if row[10]:
                    try:
                        if isinstance(row[10], str):
                            params = json.loads(row[10])
                        else:
                            params = row[10]
                        model.update(params)
                    except json.JSONDecodeError:
                        logger.warning(f"Chyba při dekódování JSON parametrů pro embedding model {row[2]}")
                
                # Použijeme provider jako klíč pro kompatibilitu s frontendem
                embedding_models[row[1] + "-embedding"] = model
            
            # Pokud nemáme žádné modely, vytvoříme výchozí v databázi
            if not embedding_models:
                logger.warning("V databázi nebyly nalezeny žádné embedding modely")
                
                # Vytvoříme výchozí OpenAI embedding model
                openai_id = "openai-embedding"
                current_time = datetime.now()
                
                # Vytvoříme záznamy pro OpenAI embedding
                cursor.execute("""
                    INSERT INTO config_entities (
                        id, type, name, description, created_at, updated_at
                    ) VALUES (%s, %s, %s, %s, %s, %s)
                """, (
                    openai_id,
                    "EMBEDDING_CONFIG",
                    "Embedding OpenAI",
                    "Výchozí OpenAI embedding model",
                    current_time,
                    current_time
                ))
                
                cursor.execute("""
                    INSERT INTO embedding_configs (
                        config_id, provider, model, dimension, normalized, 
                        batch_size, timeout, is_default
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    openai_id,
                    "openai",
                    "text-embedding-3-large",
                    1536,
                    True,
                    100,
                    60,
                    True
                ))
                
                # Vytvoříme výchozí lokální embedding model
                local_id = "local-embedding"
                
                # Vytvoříme záznamy pro lokální embedding
                cursor.execute("""
                    INSERT INTO config_entities (
                        id, type, name, description, created_at, updated_at
                    ) VALUES (%s, %s, %s, %s, %s, %s)
                """, (
                    local_id,
                    "EMBEDDING_CONFIG",
                    "Embedding Local",
                    "Výchozí lokální embedding model",
                    current_time,
                    current_time
                ))
                
                cursor.execute("""
                    INSERT INTO embedding_configs (
                        config_id, provider, model, dimension, normalized, 
                        batch_size, timeout, is_default
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    local_id,
                    "local",
                    "all-MiniLM-L6-v2",
                    384,
                    True,
                    32,
                    30,
                    False
                ))
                
                conn.commit()
                
                # Připravíme data pro vrácení
                embedding_models = {
                    "openai-embedding": {
                        "id": openai_id,
                        "provider": "openai",
                        "model": "text-embedding-3-large",
                        "api_key": "********",
                        "base_url": None,
                        "dimension": 1536,
                        "normalized": True,
                        "batch_size": 100,
                        "timeout": 60,
                        "is_default": True
                    },
                    "local-embedding": {
                        "id": local_id,
                        "provider": "local",
                        "model": "all-MiniLM-L6-v2",
                        "api_key": None,
                        "base_url": None,
                        "dimension": 384,
                        "normalized": True,
                        "batch_size": 32,
                        "timeout": 30,
                        "is_default": False
                    }
                }
                
                logger.info("Vytvořeny výchozí embedding modely v databázi")
            
            cursor.close()
            return embedding_models
            
        except Exception as e:
            logger.error(f"Chyba při získávání embedding modelů: {str(e)}")
            # Propagujeme chybu dále místo vracení statických dat
            raise Exception(f"Nelze načíst embedding modely z databáze: {str(e)}")
        finally:
            # Necháme připojení otevřené pro další požadavky
            pass
    
    def save_embedding_model(self, model_data: Dict[str, Any]) -> bool:
        """
        Uloží nebo aktualizuje embedding model v databázi.
        
        Args:
            model_data: Data embedding modelu
            
        Returns:
            bool: True pokud se podařilo uložit, jinak False
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            # Extrahujeme základní data
            model_id = model_data.get("id")
            provider = model_data.get("provider")
            model_name = model_data.get("model")
            api_key = model_data.get("api_key")
            base_url = model_data.get("base_url")
            dimension = model_data.get("dimension", 1536)
            normalized = model_data.get("normalized", True)
            batch_size = model_data.get("batch_size", 32)
            timeout = model_data.get("timeout", 60)
            is_default = model_data.get("is_default", False)
            
            # Připravíme parametry jako JSON
            params = {}
            for key, value in model_data.items():
                if key not in ["id", "provider", "model", "api_key", "base_url", "dimension", 
                            "normalized", "batch_size", "timeout", "is_default"]:
                    params[key] = value
            
            # Zkontrolujeme, zda model již existuje v config_entities
            if not model_id:
                model_id = str(uuid.uuid4())
                
            cursor.execute("SELECT COUNT(*) FROM config_entities WHERE id = %s", (model_id,))
            entity_exists = cursor.fetchone()[0] > 0
            
            # Zkontrolujeme, zda model již existuje v embedding_configs
            cursor.execute("SELECT COUNT(*) FROM embedding_configs WHERE config_id = %s", (model_id,))
            config_exists = cursor.fetchone()[0] > 0
            
            current_time = datetime.now()
            
            if entity_exists and config_exists:
                # Aktualizujeme existující model v config_entities
                cursor.execute("""
                    UPDATE config_entities
                    SET 
                        name = %s,
                        updated_at = %s
                    WHERE id = %s
                """, (
                    f"Embedding {provider}/{model_name}",
                    current_time,
                    model_id
                ))
                
                # Aktualizujeme existující model v embedding_configs
                cursor.execute("""
                    UPDATE embedding_configs
                    SET 
                        provider = %s,
                        model = %s,
                        api_key = %s,
                        base_url = %s,
                        dimension = %s,
                        normalized = %s,
                        batch_size = %s,
                        timeout = %s,
                        is_default = %s,
                        parameters = %s
                    WHERE config_id = %s
                """, (
                    provider,
                    model_name,
                    api_key,
                    base_url,
                    dimension,
                    normalized,
                    batch_size,
                    timeout,
                    is_default,
                    json.dumps(params),
                    model_id
                ))
            else:
                # Nejprve vytvoříme záznam v config_entities
                if not entity_exists:
                    cursor.execute("""
                        INSERT INTO config_entities (
                            id, type, name, description, created_at, updated_at
                        ) VALUES (%s, %s, %s, %s, %s, %s)
                    """, (
                        model_id,
                        "EMBEDDING_CONFIG",
                        f"Embedding {provider}/{model_name}",
                        f"Embedding model {model_name} od poskytovatele {provider}",
                        current_time,
                        current_time
                    ))
                
                # Poté vytvoříme záznam v embedding_configs
                if not config_exists:
                    cursor.execute("""
                        INSERT INTO embedding_configs (
                            config_id, provider, model, api_key, base_url, dimension,
                            normalized, batch_size, timeout, is_default, parameters
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        model_id,
                        provider,
                        model_name,
                        api_key,
                        base_url,
                        dimension,
                        normalized,
                        batch_size,
                        timeout,
                        is_default,
                        json.dumps(params)
                    ))
            
            # Pokud je tento model výchozí, nastavíme ostatní jako nevýchozí
            if is_default:
                cursor.execute("""
                    UPDATE embedding_configs
                    SET is_default = FALSE
                    WHERE config_id != %s
                """, (model_id,))
            
            conn.commit()
            cursor.close()
            return True
            
        except Exception as e:
            logger.error(f"Chyba při ukládání embedding modelu: {str(e)}")
            if conn:
                conn.rollback()
            return False
        finally:
            # Necháme připojení otevřené pro další požadavky
            pass
    
    def set_default_provider(self, provider_id: str) -> bool:
        """
        Nastaví poskytovatele jako výchozí.
        
        Args:
            provider_id: ID poskytovatele
            
        Returns:
            bool: True pokud se podařilo nastavit, jinak False
        """
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            # Nastavíme všechny poskytovatele jako nevýchozí
            cursor.execute("""
                UPDATE llm_configs
                SET is_default = FALSE
            """)
            
            # Nastavíme vybraného poskytovatele jako výchozí
            cursor.execute("""
                UPDATE llm_configs
                SET is_default = TRUE
                WHERE config_id = %s
            """, (provider_id,))
            
            # Aktualizujeme hodnotu v systémové konfiguraci
            # Toto je volitelné - záleží na implementaci
            
            conn.commit()
            cursor.close()
            return True
            
        except Exception as e:
            logger.error(f"Chyba při nastavování výchozího poskytovatele: {str(e)}")
            if conn:
                conn.rollback()
            return False
        finally:
            # Necháme připojení otevřené pro další požadavky
            pass
    
    def test_provider_connection(self, provider_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Otestuje připojení k API poskytovatele.
        
        Args:
            provider_data: Data poskytovatele
            
        Returns:
            Dict[str, Any]: Výsledek testu s klíči 'success' a 'message'
        """
        # Toto je simulace testu připojení, v reálné implementaci by se provedl
        # skutečný test připojení k API poskytovatele
        
        provider = provider_data.get("name", "").lower()
        api_key = provider_data.get("api_key")
        
        if not api_key or api_key == "********":
            return {
                "success": False,
                "message": f"Chybí API klíč pro poskytovatele {provider}"
            }
        
        # Zde by byl skutečný test připojení k API
        # Například:
        # - Pro OpenAI test volání /models endpointu
        # - Pro Anthropic test volání /models endpointu
        # - atd.
        
        # Simulujeme úspěšné připojení
        return {
            "success": True,
            "message": f"Připojení k API poskytovatele {provider} bylo úspěšné"
        }
    
    def __del__(self):
        """Destruktor, který zajistí uzavření připojení."""
        self._close_connection()
