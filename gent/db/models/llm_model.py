"""
GENT v10 - Model pro LLM modely v databázi

Tento modul definuje třídy pro práci s LLM modely v samostatné tabulce.
Nový přístup odděluje modely od poskytovatelů pro lepší správu a flexibilitu.
"""

import json
import uuid
from typing import Dict, List, Any, Optional
from datetime import datetime

class LlmModel:
    """Model pro práci s LLM modely v databázi."""
    
    def __init__(
        self,
        id: str = None,
        provider_id: str = None,
        name: str = None,
        context_window: int = 32000,
        max_tokens: int = 4096,
        is_default: bool = False,
        capabilities: List[str] = None,
        suitable_for: List[str] = None,
        parameters: Dict[str, Any] = None,
        created_at: datetime = None,
        updated_at: datetime = None
    ):
        """
        Inicializace modelu LLM.
        
        Args:
            id: UUID modelu
            provider_id: UUID poskytovatele
            name: Název modelu
            context_window: Velikost kontextového okna v tokenech
            max_tokens: Maximální počet tokenů v odpovědi
            is_default: Zda je model výchozí pro daného poskytovatele
            capabilities: Seznam schopností modelu
            suitable_for: Seznam vhodných použití modelu
            parameters: Dodatečné parametry modelu
            created_at: Čas vytvoření záznamu
            updated_at: Čas poslední aktualizace záznamu
        """
        self.id = id or str(uuid.uuid4())
        self.provider_id = provider_id
        self.name = name
        self.context_window = context_window
        self.max_tokens = max_tokens
        self.is_default = is_default
        self.capabilities = capabilities or ["text"]
        self.suitable_for = suitable_for or ["general"]
        self.parameters = parameters or {}
        self.created_at = created_at
        self.updated_at = updated_at
    
    @classmethod
    def from_db_row(cls, row):
        """
        Vytvoří instanci modelu z databázového řádku.
        
        Args:
            row: Řádek z databáze
            
        Returns:
            LlmModel: Instance modelu
        """
        capabilities = row[6] if isinstance(row[6], list) else json.loads(row[6]) if row[6] else []
        suitable_for = row[7] if isinstance(row[7], list) else json.loads(row[7]) if row[7] else []
        parameters = row[8] if isinstance(row[8], dict) else json.loads(row[8]) if row[8] else {}
        
        return cls(
            id=row[0],
            provider_id=row[1],
            name=row[2],
            context_window=row[3],
            max_tokens=row[4],
            is_default=row[5],
            capabilities=capabilities,
            suitable_for=suitable_for,
            parameters=parameters,
            created_at=row[9],
            updated_at=row[10]
        )
    
    def to_dict(self):
        """
        Převede model na slovník pro použití v API a frontendu.
        
        Returns:
            Dict[str, Any]: Slovník s daty modelu
        """
        return {
            "id": self.id,
            "name": self.name,
            "context_window": self.context_window,
            "max_tokens": self.max_tokens,
            "is_default": self.is_default,
            "capabilities": self.capabilities,
            "suitable_for": self.suitable_for,
            **self.parameters
        }
