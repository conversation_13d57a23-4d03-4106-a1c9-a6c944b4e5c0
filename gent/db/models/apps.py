"""
Databázové modely pro aplikace GENT systému.
"""

from sqlalchemy import Column, String, Text, DateTime, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, JSON
from sqlalchemy.sql import func
from .base import Base


class App(Base):
    """Model pro aplikace v GENT systému."""
    
    __tablename__ = "apps"
    
    # Základní informace
    id = Column(String, primary_key=True)  # např. "calendar_benchmark"
    name = Column(String(255), nullable=False)  # Zobrazovaný název
    description = Column(Text, nullable=True)  # Popis aplikace
    
    # Technické informace
    file_path = Column(String(500), nullable=False)  # Cesta k souboru
    language = Column(String(50), nullable=False, default="python")  # Programovací jazyk
    version = Column(String(50), nullable=True, default="1.0.0")  # Verze aplikace
    
    # Metadata
    author = Column(String(255), nullable=True)  # Autor aplikace
    category = Column(String(100), nullable=True)  # Kategorie (benchmark, utility, game, atd.)
    tags = Column(JSON, nullable=True)  # Seznam tagů pro vyhledávání
    
    # Ovládání a spuštění
    command = Column(String(500), nullable=True)  # Příkaz pro spuštění
    arguments = Column(JSON, nullable=True)  # Možné argumenty
    requirements = Column(JSON, nullable=True)  # Požadavky (dependencies)
    
    # Status a dostupnost
    is_active = Column(Boolean, nullable=False, default=True)  # Je aplikace aktivní
    is_public = Column(Boolean, nullable=False, default=False)  # Je veřejně dostupná
    execution_count = Column(Integer, nullable=False, default=0)  # Počet spuštění
    
    # Časové značky
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    last_executed_at = Column(DateTime(timezone=True), nullable=True)  # Poslední spuštění
    
    # Konfigurace UI
    ui_config = Column(JSON, nullable=True)  # Konfigurace pro zobrazení v admin rozhraní
    
    def __repr__(self):
        return f"<App(id='{self.id}', name='{self.name}', category='{self.category}')>"
    
    def to_dict(self):
        """Převede model na slovník pro API."""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'file_path': self.file_path,
            'language': self.language,
            'version': self.version,
            'author': self.author,
            'category': self.category,
            'tags': self.tags,
            'command': self.command,
            'arguments': self.arguments,
            'requirements': self.requirements,
            'is_active': self.is_active,
            'is_public': self.is_public,
            'execution_count': self.execution_count,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'last_executed_at': self.last_executed_at.isoformat() if self.last_executed_at else None,
            'ui_config': self.ui_config
        }


class AppExecution(Base):
    """Model pro záznamy spuštění aplikací."""
    
    __tablename__ = "app_executions"
    
    # Základní informace
    id = Column(String, primary_key=True)  # UUID
    app_id = Column(String, nullable=False)  # Reference na App.id
    
    # Informace o spuštění
    user_id = Column(String, nullable=True)  # Kdo spustil (pokud je známo)
    arguments_used = Column(JSON, nullable=True)  # Použité argumenty
    
    # Výsledky
    exit_code = Column(Integer, nullable=True)  # Exit kód
    output = Column(Text, nullable=True)  # Výstup aplikace
    error_output = Column(Text, nullable=True)  # Chybový výstup
    execution_time = Column(Integer, nullable=True)  # Doba běhu v ms
    
    # Status
    status = Column(String(50), nullable=False, default="running")  # running, completed, failed
    
    # Časové značky
    started_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    
    def __repr__(self):
        return f"<AppExecution(id='{self.id}', app_id='{self.app_id}', status='{self.status}')>"
    
    def to_dict(self):
        """Převede model na slovník pro API."""
        return {
            'id': self.id,
            'app_id': self.app_id,
            'user_id': self.user_id,
            'arguments_used': self.arguments_used,
            'exit_code': self.exit_code,
            'output': self.output,
            'error_output': self.error_output,
            'execution_time': self.execution_time,
            'status': self.status,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None
        }
