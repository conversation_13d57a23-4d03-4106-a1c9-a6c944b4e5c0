#!/usr/bin/env python3
"""
GENT v10 - Mem0 MCP Server
Lokální MCP server pro Mem0 memory management integrovaný do GENT systému.

Autor: GENT AI System
Datum: 2025-01-06
Verze: 1.0.0
"""

import asyncio
import json
import logging
import os
import sys
import argparse
from datetime import datetime
from typing import Any, Dict, List, Optional, Sequence

# FastMCP imports
from mcp.server.fastmcp import FastMCP
from starlette.applications import Starlette
from mcp.server.sse import SseServerTransport
from starlette.requests import Request
from starlette.routing import Mount, Route
from mcp.server import Server
import uvicorn

# Mem0 imports
from mem0 import Memory

# Environment
from dotenv import load_dotenv
load_dotenv()

# Logging setup
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/opt/gent/logs/mem0_mcp.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("mem0-mcp-server")

# Initialize FastMCP server for GENT Mem0 tools
mcp = FastMCP("gent-mem0-mcp")

# Initialize Mem0 memory system
memory = None
DEFAULT_USER_ID = "gent_system"

def initialize_memory():
    """Inicializace Mem0 memory systému."""
    global memory
    try:
        logger.info("🔧 Inicializace Mem0 memory systému...")

        # Konfigurace pro lokální použití
        config = {
            "vector_store": {
                "provider": "qdrant",
                "config": {
                    "host": "localhost",
                    "port": 6333,
                    "collection_name": "gent_memories"
                }
            },
            "llm": {
                "provider": "openai",
                "config": {
                    "model": "gpt-4o-mini",
                    "temperature": 0.1
                }
            }
        }

        memory = Memory(config=config)
        logger.info("✅ Mem0 memory systém úspěšně inicializován")

    except Exception as e:
        logger.error(f"❌ Chyba při inicializaci Mem0: {str(e)}")
        # Fallback na základní konfiguraci
        memory = Memory()
        logger.info("✅ Mem0 inicializován s výchozí konfigurací")

# Inicializace při startu
initialize_memory()

# FastMCP nástroje pro GENT Mem0 systém

@mcp.tool(
    description="""Uloží novou vzpomínku do Mem0 systému pro GENT.

    Tento nástroj ukládá informace, konverzace, poznatky a data pro pozdější použití
    v komunikaci mezi LLM a AI agenty. Vzpomínky jsou automaticky indexovány
    pro sémantické vyhledávání.

    Použití:
    - Ukládání důležitých informací z konverzací
    - Zaznamenávání preferencí uživatelů
    - Ukládání kontextu pro budoucí interakce
    - Archivace poznatků a řešení problémů
    """
)
async def store_memory(content: str, user_id: str = DEFAULT_USER_ID, metadata: str = "{}") -> str:
    """Uloží vzpomínku do Mem0 systému.

    Args:
        content: Obsah vzpomínky k uložení
        user_id: ID uživatele (výchozí: gent_system)
        metadata: JSON string s dodatečnými metadaty

    Returns:
        JSON string s výsledkem operace
    """
    try:
        # Parsování metadat
        try:
            meta_dict = json.loads(metadata) if metadata != "{}" else {}
        except json.JSONDecodeError:
            meta_dict = {"raw_metadata": metadata}

        # Přidání časového razítka a zdroje
        meta_dict.update({
            "timestamp": datetime.now().isoformat(),
            "source": "gent_mcp_server",
            "server_version": "1.0.0"
        })

        # Uložení do Mem0
        result = memory.add(content, user_id=user_id, metadata=meta_dict)

        response = {
            "success": True,
            "message": "Vzpomínka úspěšně uložena",
            "memory_id": result.get("id", "unknown"),
            "user_id": user_id,
            "content": content,
            "metadata": meta_dict
        }

        logger.info(f"✅ Vzpomínka uložena pro uživatele {user_id}")
        return json.dumps(response, ensure_ascii=False, indent=2)

    except Exception as e:
        logger.error(f"❌ Chyba při ukládání vzpomínky: {str(e)}")
        error_response = {
            "success": False,
            "error": f"Chyba při ukládání vzpomínky: {str(e)}",
            "user_id": user_id,
            "content": content
        }
        return json.dumps(error_response, ensure_ascii=False, indent=2)
        
    def _setup_handlers(self):
        """Nastavení MCP handlerů."""
        
        @self.server.list_tools()
        async def list_tools() -> ListToolsResult:
            """Seznam dostupných nástrojů."""
            return ListToolsResult(
                tools=[
                    Tool(
                        name="store_memory",
                        description="Uloží novou vzpomínku do Mem0 systému",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "content": {
                                    "type": "string",
                                    "description": "Obsah vzpomínky k uložení"
                                },
                                "user_id": {
                                    "type": "string", 
                                    "description": "ID uživatele (volitelné)",
                                    "default": "gent_system"
                                },
                                "metadata": {
                                    "type": "object",
                                    "description": "Dodatečná metadata (volitelné)",
                                    "default": {}
                                }
                            },
                            "required": ["content"]
                        }
                    ),
                    Tool(
                        name="retrieve_memories",
                        description="Načte vzpomínky na základě dotazu",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "query": {
                                    "type": "string",
                                    "description": "Vyhledávací dotaz"
                                },
                                "user_id": {
                                    "type": "string",
                                    "description": "ID uživatele (volitelné)",
                                    "default": "gent_system"
                                },
                                "limit": {
                                    "type": "integer",
                                    "description": "Maximální počet výsledků",
                                    "default": 10
                                }
                            },
                            "required": ["query"]
                        }
                    ),
                    Tool(
                        name="search_memories",
                        description="Vyhledá vzpomínky pomocí sémantického vyhledávání",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "query": {
                                    "type": "string",
                                    "description": "Vyhledávací dotaz"
                                },
                                "user_id": {
                                    "type": "string",
                                    "description": "ID uživatele (volitelné)",
                                    "default": "gent_system"
                                },
                                "limit": {
                                    "type": "integer",
                                    "description": "Maximální počet výsledků",
                                    "default": 5
                                }
                            },
                            "required": ["query"]
                        }
                    ),
                    Tool(
                        name="delete_memory",
                        description="Smaže konkrétní vzpomínku",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "memory_id": {
                                    "type": "string",
                                    "description": "ID vzpomínky k smazání"
                                }
                            },
                            "required": ["memory_id"]
                        }
                    ),
                    Tool(
                        name="list_all_memories",
                        description="Vypíše všechny uložené vzpomínky",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "user_id": {
                                    "type": "string",
                                    "description": "ID uživatele (volitelné)",
                                    "default": "gent_system"
                                },
                                "limit": {
                                    "type": "integer",
                                    "description": "Maximální počet výsledků",
                                    "default": 50
                                }
                            }
                        }
                    ),
                    Tool(
                        name="get_memory_stats",
                        description="Získá statistiky o uložených vzpomínkách",
                        inputSchema={
                            "type": "object",
                            "properties": {
                                "user_id": {
                                    "type": "string",
                                    "description": "ID uživatele (volitelné)",
                                    "default": "gent_system"
                                }
                            }
                        }
                    )
                ]
            )
        
        @self.server.call_tool()
        async def call_tool(name: str, arguments: Dict[str, Any]) -> CallToolResult:
            """Zpracování volání nástrojů."""
            try:
                # Inicializace Mem0 pokud ještě není
                if self.memory is None:
                    await self._initialize_memory()
                
                if name == "store_memory":
                    return await self._store_memory(arguments)
                elif name == "retrieve_memories":
                    return await self._retrieve_memories(arguments)
                elif name == "search_memories":
                    return await self._search_memories(arguments)
                elif name == "delete_memory":
                    return await self._delete_memory(arguments)
                elif name == "list_all_memories":
                    return await self._list_all_memories(arguments)
                elif name == "get_memory_stats":
                    return await self._get_memory_stats(arguments)
                else:
                    return CallToolResult(
                        content=[TextContent(
                            type="text",
                            text=f"Neznámý nástroj: {name}"
                        )],
                        isError=True
                    )
                    
            except Exception as e:
                logger.error(f"Chyba při volání nástroje {name}: {str(e)}")
                return CallToolResult(
                    content=[TextContent(
                        type="text",
                        text=f"Chyba při volání nástroje {name}: {str(e)}"
                    )],
                    isError=True
                )
    
    async def _initialize_memory(self):
        """Inicializace Mem0 memory systému."""
        try:
            logger.info("Inicializace Mem0 memory systému...")
            
            # Konfigurace pro lokální použití
            config = {
                "vector_store": {
                    "provider": "qdrant",
                    "config": {
                        "host": "localhost",
                        "port": 6333,
                        "collection_name": "gent_memories"
                    }
                },
                "llm": {
                    "provider": "openai",
                    "config": {
                        "model": "gpt-4o-mini",
                        "temperature": 0.1
                    }
                }
            }
            
            self.memory = Memory(config=config)
            logger.info("✅ Mem0 memory systém úspěšně inicializován")
            
        except Exception as e:
            logger.error(f"❌ Chyba při inicializaci Mem0: {str(e)}")
            # Fallback na základní konfiguraci
            self.memory = Memory()
            logger.info("✅ Mem0 inicializován s výchozí konfigurací")
    
    async def _store_memory(self, arguments: Dict[str, Any]) -> CallToolResult:
        """Uloží vzpomínku do Mem0."""
        content = arguments.get("content", "")
        user_id = arguments.get("user_id", "gent_system")
        metadata = arguments.get("metadata", {})
        
        try:
            # Přidání časového razítka
            metadata["timestamp"] = datetime.now().isoformat()
            metadata["source"] = "gent_mcp_server"
            
            result = self.memory.add(content, user_id=user_id, metadata=metadata)
            
            response = {
                "success": True,
                "message": "Vzpomínka úspěšně uložena",
                "memory_id": result.get("id", "unknown"),
                "user_id": user_id,
                "content": content,
                "metadata": metadata
            }
            
            logger.info(f"✅ Vzpomínka uložena pro uživatele {user_id}")
            
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=json.dumps(response, ensure_ascii=False, indent=2)
                )]
            )
            
        except Exception as e:
            logger.error(f"❌ Chyba při ukládání vzpomínky: {str(e)}")
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=f"Chyba při ukládání vzpomínky: {str(e)}"
                )],
                isError=True
            )

    async def _retrieve_memories(self, arguments: Dict[str, Any]) -> CallToolResult:
        """Načte vzpomínky na základě dotazu."""
        query = arguments.get("query", "")
        user_id = arguments.get("user_id", "gent_system")
        limit = arguments.get("limit", 10)

        try:
            results = self.memory.search(query, user_id=user_id, limit=limit)

            response = {
                "success": True,
                "query": query,
                "user_id": user_id,
                "results_count": len(results.get("results", [])),
                "memories": results.get("results", [])
            }

            logger.info(f"✅ Načteno {len(results.get('results', []))} vzpomínek pro dotaz: {query}")

            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=json.dumps(response, ensure_ascii=False, indent=2)
                )]
            )

        except Exception as e:
            logger.error(f"❌ Chyba při načítání vzpomínek: {str(e)}")
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=f"Chyba při načítání vzpomínek: {str(e)}"
                )],
                isError=True
            )

    async def _search_memories(self, arguments: Dict[str, Any]) -> CallToolResult:
        """Vyhledá vzpomínky pomocí sémantického vyhledávání."""
        query = arguments.get("query", "")
        user_id = arguments.get("user_id", "gent_system")
        limit = arguments.get("limit", 5)

        try:
            results = self.memory.search(query, user_id=user_id, limit=limit)

            # Formátování výsledků pro lepší čitelnost
            formatted_memories = []
            for memory in results.get("results", []):
                formatted_memory = {
                    "id": memory.get("id", "unknown"),
                    "memory": memory.get("memory", ""),
                    "score": memory.get("score", 0.0),
                    "metadata": memory.get("metadata", {}),
                    "created_at": memory.get("created_at", "")
                }
                formatted_memories.append(formatted_memory)

            response = {
                "success": True,
                "search_query": query,
                "user_id": user_id,
                "results_count": len(formatted_memories),
                "memories": formatted_memories
            }

            logger.info(f"✅ Vyhledáno {len(formatted_memories)} vzpomínek pro dotaz: {query}")

            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=json.dumps(response, ensure_ascii=False, indent=2)
                )]
            )

        except Exception as e:
            logger.error(f"❌ Chyba při vyhledávání vzpomínek: {str(e)}")
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=f"Chyba při vyhledávání vzpomínek: {str(e)}"
                )],
                isError=True
            )

    async def _delete_memory(self, arguments: Dict[str, Any]) -> CallToolResult:
        """Smaže konkrétní vzpomínku."""
        memory_id = arguments.get("memory_id", "")

        try:
            result = self.memory.delete(memory_id)

            response = {
                "success": True,
                "message": f"Vzpomínka {memory_id} byla úspěšně smazána",
                "memory_id": memory_id,
                "deleted": True
            }

            logger.info(f"✅ Vzpomínka {memory_id} byla smazána")

            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=json.dumps(response, ensure_ascii=False, indent=2)
                )]
            )

        except Exception as e:
            logger.error(f"❌ Chyba při mazání vzpomínky {memory_id}: {str(e)}")
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=f"Chyba při mazání vzpomínky {memory_id}: {str(e)}"
                )],
                isError=True
            )

    async def _list_all_memories(self, arguments: Dict[str, Any]) -> CallToolResult:
        """Vypíše všechny uložené vzpomínky."""
        user_id = arguments.get("user_id", "gent_system")
        limit = arguments.get("limit", 50)

        try:
            # Použijeme prázdný dotaz pro získání všech vzpomínek
            results = self.memory.get_all(user_id=user_id, limit=limit)

            response = {
                "success": True,
                "user_id": user_id,
                "total_memories": len(results) if results else 0,
                "memories": results if results else []
            }

            logger.info(f"✅ Načteno {len(results) if results else 0} vzpomínek pro uživatele {user_id}")

            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=json.dumps(response, ensure_ascii=False, indent=2)
                )]
            )

        except Exception as e:
            logger.error(f"❌ Chyba při načítání všech vzpomínek: {str(e)}")
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=f"Chyba při načítání všech vzpomínek: {str(e)}"
                )],
                isError=True
            )

    async def _get_memory_stats(self, arguments: Dict[str, Any]) -> CallToolResult:
        """Získá statistiky o uložených vzpomínkách."""
        user_id = arguments.get("user_id", "gent_system")

        try:
            # Získání všech vzpomínek pro statistiky
            all_memories = self.memory.get_all(user_id=user_id)

            # Základní statistiky
            total_count = len(all_memories) if all_memories else 0

            # Statistiky podle metadat
            sources = {}
            dates = {}

            if all_memories:
                for memory in all_memories:
                    metadata = memory.get("metadata", {})

                    # Statistiky zdrojů
                    source = metadata.get("source", "unknown")
                    sources[source] = sources.get(source, 0) + 1

                    # Statistiky dat
                    timestamp = metadata.get("timestamp", "")
                    if timestamp:
                        date = timestamp.split("T")[0]  # Pouze datum
                        dates[date] = dates.get(date, 0) + 1

            response = {
                "success": True,
                "user_id": user_id,
                "statistics": {
                    "total_memories": total_count,
                    "sources": sources,
                    "dates": dates,
                    "last_updated": datetime.now().isoformat()
                }
            }

            logger.info(f"✅ Statistiky pro uživatele {user_id}: {total_count} vzpomínek")

            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=json.dumps(response, ensure_ascii=False, indent=2)
                )]
            )

        except Exception as e:
            logger.error(f"❌ Chyba při získávání statistik: {str(e)}")
            return CallToolResult(
                content=[TextContent(
                    type="text",
                    text=f"Chyba při získávání statistik: {str(e)}"
                )],
                isError=True
            )

    async def run(self):
        """Spuštění MCP serveru."""
        logger.info("🚀 Spouštím Mem0 MCP Server pro GENT...")

        # Vytvoření logs adresáře pokud neexistuje
        os.makedirs("/opt/gent/logs", exist_ok=True)

        async with stdio_server() as (read_stream, write_stream):
            await self.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="mem0-mcp-server",
                    server_version="1.0.0",
                    capabilities=self.server.get_capabilities(
                        notification_options=None,
                        experimental_capabilities=None,
                    ),
                ),
            )

async def main():
    """Hlavní funkce."""
    try:
        server = Mem0MCPServer()
        await server.run()
    except KeyboardInterrupt:
        logger.info("🛑 Server zastaven uživatelem")
    except Exception as e:
        logger.error(f"❌ Kritická chyba serveru: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    # Nastavení environment variables pokud nejsou nastaveny
    if not os.getenv("OPENAI_API_KEY"):
        logger.warning("⚠️ OPENAI_API_KEY není nastavena - Mem0 může nefungovat správně")

    logger.info("🔧 GENT v10 - Mem0 MCP Server")
    logger.info("📍 Lokální memory management pro komunikaci mezi LLM")
    logger.info("🔗 Integrace s GENT MCP systémem")

    asyncio.run(main())
