[Unit]
Description=GENT v10 - Mem0 MCP Server
Documentation=https://mem0.ai/docs
After=network.target postgresql.service
Wants=postgresql.service
PartOf=gent.target

[Service]
Type=forking
User=root
Group=root
WorkingDirectory=/opt/gent/mcp_servers/mem0

# Environment variables
Environment=PYTHONPATH=/opt/gent/mcp_servers/mem0
Environment=OPENAI_API_KEY=
Environment=MEM0_CONFIG_PATH=/opt/gent/mcp_servers/mem0/config.json

# Service execution
ExecStart=/opt/gent/mcp_servers/mem0/start_mem0_server.sh start
ExecStop=/opt/gent/mcp_servers/mem0/start_mem0_server.sh stop
ExecReload=/opt/gent/mcp_servers/mem0/start_mem0_server.sh restart

# Process management
PIDFile=/opt/gent/logs/mem0_mcp.pid
TimeoutStartSec=30
TimeoutStopSec=15
Restart=always
RestartSec=10
StartLimitInterval=60
StartLimitBurst=3

# Security settings
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/gent/logs /opt/gent/data /opt/gent/mcp_servers/mem0
PrivateTmp=true

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096
MemoryMax=2G
CPUQuota=200%

# Logging
StandardOutput=append:/opt/gent/logs/mem0_mcp.log
StandardError=append:/opt/gent/logs/mem0_mcp_error.log
SyslogIdentifier=gent-mem0

[Install]
WantedBy=multi-user.target
Also=gent.target
