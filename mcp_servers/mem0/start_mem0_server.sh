#!/bin/bash
# GENT v10 - Mem0 MCP Server Startup Script
# Spouštěcí script pro Mem0 MCP server

set -e

# Konfigurace
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
VENV_PATH="$SCRIPT_DIR/venv"
MAIN_SCRIPT="$SCRIPT_DIR/main.py"
LOG_FILE="/opt/gent/logs/mem0_mcp.log"
PID_FILE="/opt/gent/logs/mem0_mcp.pid"

# Funkce pro logování
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Kontrola závislostí
check_dependencies() {
    log "🔍 Kontrola závislostí..."
    
    # Kontrola virtual environment
    if [ ! -d "$VENV_PATH" ]; then
        log "❌ Virtual environment nenalezen v $VENV_PATH"
        exit 1
    fi
    
    # Kontrola main.py
    if [ ! -f "$MAIN_SCRIPT" ]; then
        log "❌ Main script nenalezen: $MAIN_SCRIPT"
        exit 1
    fi
    
    # Kontrola logs adresáře
    mkdir -p "$(dirname "$LOG_FILE")"
    
    log "✅ Všechny závislosti jsou v pořádku"
}

# Kontrola běžícího procesu
check_running() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0  # Běží
        else
            rm -f "$PID_FILE"
            return 1  # Neběží
        fi
    fi
    return 1  # Neběží
}

# Spuštění serveru
start_server() {
    log "🚀 Spouštím Mem0 MCP Server..."
    
    # Kontrola zda už neběží
    if check_running; then
        log "⚠️ Server už běží (PID: $(cat "$PID_FILE"))"
        return 1
    fi
    
    # Aktivace virtual environment a spuštění
    cd "$SCRIPT_DIR"
    source "$VENV_PATH/bin/activate"
    
    # Export environment variables
    export PYTHONPATH="$SCRIPT_DIR:$PYTHONPATH"
    
    # Spuštění serveru na pozadí
    nohup python3 "$MAIN_SCRIPT" >> "$LOG_FILE" 2>&1 &
    local pid=$!
    
    # Uložení PID
    echo "$pid" > "$PID_FILE"
    
    # Kontrola zda se server spustil
    sleep 2
    if ps -p "$pid" > /dev/null 2>&1; then
        log "✅ Mem0 MCP Server úspěšně spuštěn (PID: $pid)"
        return 0
    else
        log "❌ Nepodařilo se spustit server"
        rm -f "$PID_FILE"
        return 1
    fi
}

# Zastavení serveru
stop_server() {
    log "🛑 Zastavuji Mem0 MCP Server..."
    
    if ! check_running; then
        log "⚠️ Server neběží"
        return 1
    fi
    
    local pid=$(cat "$PID_FILE")
    
    # Pokus o graceful shutdown
    kill -TERM "$pid" 2>/dev/null || true
    
    # Čekání na ukončení
    local count=0
    while ps -p "$pid" > /dev/null 2>&1 && [ $count -lt 10 ]; do
        sleep 1
        count=$((count + 1))
    done
    
    # Force kill pokud stále běží
    if ps -p "$pid" > /dev/null 2>&1; then
        log "⚠️ Graceful shutdown selhal, používám force kill"
        kill -KILL "$pid" 2>/dev/null || true
        sleep 1
    fi
    
    # Cleanup
    rm -f "$PID_FILE"
    log "✅ Server zastaven"
}

# Restart serveru
restart_server() {
    log "🔄 Restartuji Mem0 MCP Server..."
    stop_server
    sleep 2
    start_server
}

# Status serveru
status_server() {
    if check_running; then
        local pid=$(cat "$PID_FILE")
        log "✅ Server běží (PID: $pid)"
        
        # Zobrazení dodatečných informací
        local memory_usage=$(ps -o rss= -p "$pid" 2>/dev/null | awk '{print $1/1024 " MB"}')
        local cpu_usage=$(ps -o %cpu= -p "$pid" 2>/dev/null | awk '{print $1"%"}')
        local start_time=$(ps -o lstart= -p "$pid" 2>/dev/null)
        
        log "📊 Memory: $memory_usage, CPU: $cpu_usage"
        log "⏰ Spuštěn: $start_time"
        return 0
    else
        log "❌ Server neběží"
        return 1
    fi
}

# Hlavní logika
case "${1:-}" in
    start)
        check_dependencies
        start_server
        ;;
    stop)
        stop_server
        ;;
    restart)
        check_dependencies
        restart_server
        ;;
    status)
        status_server
        ;;
    *)
        echo "Použití: $0 {start|stop|restart|status}"
        echo ""
        echo "Příkazy:"
        echo "  start   - Spustí Mem0 MCP server"
        echo "  stop    - Zastaví Mem0 MCP server"
        echo "  restart - Restartuje Mem0 MCP server"
        echo "  status  - Zobrazí stav serveru"
        exit 1
        ;;
esac
