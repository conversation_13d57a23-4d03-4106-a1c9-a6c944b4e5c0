#!/usr/bin/env python3
"""
GENT v10 - Mem0 MCP Server
Lokální MCP server pro Mem0 memory management integrovaný do GENT systému.

Autor: GENT AI System
Datum: 2025-01-06
Verze: 1.0.0
"""

import asyncio
import json
import logging
import os
import sys
import argparse
from datetime import datetime
from typing import Any, Dict, List, Optional

# FastMCP imports
from mcp.server.fastmcp import FastMCP
from starlette.applications import Starlette
from mcp.server.sse import SseServerTransport
from starlette.requests import Request
from starlette.routing import Mount, Route
from mcp.server import Server
import uvicorn

# Mem0 imports
from mem0 import Memory

# Environment
from dotenv import load_dotenv
load_dotenv()

# Logging setup
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/opt/gent/logs/mem0_mcp.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("gent-mem0-mcp")

# Initialize FastMCP server for GENT Mem0 tools
mcp = FastMCP("gent-mem0-mcp")

# Initialize Mem0 memory system
memory = None
DEFAULT_USER_ID = "gent_system"

def initialize_memory():
    """Inicializace Mem0 memory systému."""
    global memory
    try:
        logger.info("🔧 Inicializace Mem0 memory systému...")
        
        # Konfigurace pro lokální použití
        config = {
            "vector_store": {
                "provider": "qdrant",
                "config": {
                    "host": "localhost",
                    "port": 6333,
                    "collection_name": "gent_memories"
                }
            },
            "llm": {
                "provider": "openai",
                "config": {
                    "model": "gpt-4o-mini",
                    "temperature": 0.1
                }
            }
        }
        
        memory = Memory(config=config)
        logger.info("✅ Mem0 memory systém úspěšně inicializován")
        
    except Exception as e:
        logger.error(f"❌ Chyba při inicializaci Mem0: {str(e)}")
        # Fallback na základní konfiguraci
        memory = Memory()
        logger.info("✅ Mem0 inicializován s výchozí konfigurací")

# Inicializace při startu
initialize_memory()

# FastMCP nástroje pro GENT Mem0 systém

@mcp.tool(
    description="""Uloží novou vzpomínku do Mem0 systému pro GENT.
    
    Tento nástroj ukládá informace, konverzace, poznatky a data pro pozdější použití
    v komunikaci mezi LLM a AI agenty. Vzpomínky jsou automaticky indexovány
    pro sémantické vyhledávání.
    
    Použití:
    - Ukládání důležitých informací z konverzací
    - Zaznamenávání preferencí uživatelů
    - Ukládání kontextu pro budoucí interakce
    - Archivace poznatků a řešení problémů
    """
)
async def store_memory(content: str, user_id: str = DEFAULT_USER_ID, metadata: str = "{}") -> str:
    """Uloží vzpomínku do Mem0 systému.
    
    Args:
        content: Obsah vzpomínky k uložení
        user_id: ID uživatele (výchozí: gent_system)
        metadata: JSON string s dodatečnými metadaty
    
    Returns:
        JSON string s výsledkem operace
    """
    try:
        # Parsování metadat
        try:
            meta_dict = json.loads(metadata) if metadata != "{}" else {}
        except json.JSONDecodeError:
            meta_dict = {"raw_metadata": metadata}
        
        # Přidání časového razítka a zdroje
        meta_dict.update({
            "timestamp": datetime.now().isoformat(),
            "source": "gent_mcp_server",
            "server_version": "1.0.0"
        })
        
        # Uložení do Mem0
        result = memory.add(content, user_id=user_id, metadata=meta_dict)
        
        response = {
            "success": True,
            "message": "Vzpomínka úspěšně uložena",
            "memory_id": result.get("id", "unknown"),
            "user_id": user_id,
            "content": content,
            "metadata": meta_dict
        }
        
        logger.info(f"✅ Vzpomínka uložena pro uživatele {user_id}")
        return json.dumps(response, ensure_ascii=False, indent=2)
        
    except Exception as e:
        logger.error(f"❌ Chyba při ukládání vzpomínky: {str(e)}")
        error_response = {
            "success": False,
            "error": f"Chyba při ukládání vzpomínky: {str(e)}",
            "user_id": user_id,
            "content": content
        }
        return json.dumps(error_response, ensure_ascii=False, indent=2)

@mcp.tool(
    description="""Vyhledá vzpomínky pomocí sémantického vyhledávání v Mem0 systému.
    
    Tento nástroj by měl být volán pro KAŽDÝ uživatelský dotaz k nalezení relevantních
    informací a kontextu. Používá pokročilé sémantické vyhledávání pro nalezení
    souvisejících vzpomínek i když se nepoužívají přesná klíčová slova.
    
    Použití:
    - Vyhledávání relevantního kontextu pro odpovědi
    - Nalezení souvisejících informací z minulých konverzací
    - Získání preferencí a nastavení uživatelů
    - Vyhledávání řešení podobných problémů
    """
)
async def search_memories(query: str, user_id: str = DEFAULT_USER_ID, limit: int = 5) -> str:
    """Vyhledá vzpomínky pomocí sémantického vyhledávání.
    
    Args:
        query: Vyhledávací dotaz (může být přirozený jazyk)
        user_id: ID uživatele (výchozí: gent_system)
        limit: Maximální počet výsledků (výchozí: 5)
    
    Returns:
        JSON string s výsledky vyhledávání
    """
    try:
        results = memory.search(query, user_id=user_id, limit=limit)
        
        # Formátování výsledků pro lepší čitelnost
        formatted_memories = []
        for memory_item in results.get("results", []):
            formatted_memory = {
                "id": memory_item.get("id", "unknown"),
                "memory": memory_item.get("memory", ""),
                "score": memory_item.get("score", 0.0),
                "metadata": memory_item.get("metadata", {}),
                "created_at": memory_item.get("created_at", "")
            }
            formatted_memories.append(formatted_memory)
        
        response = {
            "success": True,
            "search_query": query,
            "user_id": user_id,
            "results_count": len(formatted_memories),
            "memories": formatted_memories
        }
        
        logger.info(f"✅ Vyhledáno {len(formatted_memories)} vzpomínek pro dotaz: {query}")
        return json.dumps(response, ensure_ascii=False, indent=2)
        
    except Exception as e:
        logger.error(f"❌ Chyba při vyhledávání vzpomínek: {str(e)}")
        error_response = {
            "success": False,
            "error": f"Chyba při vyhledávání vzpomínek: {str(e)}",
            "query": query,
            "user_id": user_id
        }
        return json.dumps(error_response, ensure_ascii=False, indent=2)

@mcp.tool(
    description="""Získá všechny uložené vzpomínky pro konkrétního uživatele.
    
    Tento nástroj vrací kompletní seznam všech vzpomínek uložených v Mem0 systému
    pro daného uživatele. Užitečné pro analýzu všech dostupných informací
    a získání úplného kontextu.
    
    Použití:
    - Analýza všech dostupných vzpomínek
    - Kontrola historie uložených informací
    - Získání úplného kontextu pro komplexní úkoly
    - Audit uložených dat
    """
)
async def get_all_memories(user_id: str = DEFAULT_USER_ID, limit: int = 50) -> str:
    """Získá všechny vzpomínky pro uživatele.
    
    Args:
        user_id: ID uživatele (výchozí: gent_system)
        limit: Maximální počet výsledků (výchozí: 50)
    
    Returns:
        JSON string se všemi vzpomínkami
    """
    try:
        # Získání všech vzpomínek
        results = memory.get_all(user_id=user_id, limit=limit)
        
        response = {
            "success": True,
            "user_id": user_id,
            "total_memories": len(results) if results else 0,
            "memories": results if results else []
        }
        
        logger.info(f"✅ Načteno {len(results) if results else 0} vzpomínek pro uživatele {user_id}")
        return json.dumps(response, ensure_ascii=False, indent=2)
        
    except Exception as e:
        logger.error(f"❌ Chyba při načítání všech vzpomínek: {str(e)}")
        error_response = {
            "success": False,
            "error": f"Chyba při načítání všech vzpomínek: {str(e)}",
            "user_id": user_id
        }
        return json.dumps(error_response, ensure_ascii=False, indent=2)

@mcp.tool(
    description="""Smaže konkrétní vzpomínku z Mem0 systému.

    Tento nástroj umožňuje smazání konkrétní vzpomínky na základě jejího ID.
    Používejte opatrně, smazané vzpomínky nelze obnovit.
    """
)
async def delete_memory(memory_id: str) -> str:
    """Smaže konkrétní vzpomínku."""
    try:
        result = memory.delete(memory_id)

        response = {
            "success": True,
            "message": f"Vzpomínka {memory_id} byla úspěšně smazána",
            "memory_id": memory_id,
            "deleted": True
        }

        logger.info(f"✅ Vzpomínka {memory_id} byla smazána")
        return json.dumps(response, ensure_ascii=False, indent=2)

    except Exception as e:
        logger.error(f"❌ Chyba při mazání vzpomínky {memory_id}: {str(e)}")
        error_response = {
            "success": False,
            "error": f"Chyba při mazání vzpomínky {memory_id}: {str(e)}",
            "memory_id": memory_id
        }
        return json.dumps(error_response, ensure_ascii=False, indent=2)

@mcp.tool(
    description="""Získá statistiky o uložených vzpomínkách v Mem0 systému."""
)
async def get_memory_stats(user_id: str = DEFAULT_USER_ID) -> str:
    """Získá statistiky o vzpomínkách."""
    try:
        # Získání všech vzpomínek pro statistiky
        all_memories = memory.get_all(user_id=user_id)

        # Základní statistiky
        total_count = len(all_memories) if all_memories else 0

        # Statistiky podle metadat
        sources = {}
        dates = {}

        if all_memories:
            for memory_item in all_memories:
                metadata = memory_item.get("metadata", {})

                # Statistiky zdrojů
                source = metadata.get("source", "unknown")
                sources[source] = sources.get(source, 0) + 1

                # Statistiky dat
                timestamp = metadata.get("timestamp", "")
                if timestamp:
                    date = timestamp.split("T")[0]  # Pouze datum
                    dates[date] = dates.get(date, 0) + 1

        response = {
            "success": True,
            "user_id": user_id,
            "statistics": {
                "total_memories": total_count,
                "sources": sources,
                "dates": dates,
                "last_updated": datetime.now().isoformat()
            }
        }

        logger.info(f"✅ Statistiky pro uživatele {user_id}: {total_count} vzpomínek")
        return json.dumps(response, ensure_ascii=False, indent=2)

    except Exception as e:
        logger.error(f"❌ Chyba při získávání statistik: {str(e)}")
        error_response = {
            "success": False,
            "error": f"Chyba při získávání statistik: {str(e)}",
            "user_id": user_id
        }
        return json.dumps(error_response, ensure_ascii=False, indent=2)

def create_starlette_app(mcp_server: Server, *, debug: bool = False) -> Starlette:
    """Vytvoří Starlette aplikaci pro MCP server s SSE."""
    sse = SseServerTransport("/messages/")

    async def handle_sse(request: Request) -> None:
        async with sse.connect_sse(
            request.scope,
            request.receive,
            request._send,  # noqa: SLF001
        ) as (read_stream, write_stream):
            await mcp_server.run(
                read_stream,
                write_stream,
                mcp_server.create_initialization_options(),
            )

    return Starlette(
        debug=debug,
        routes=[
            Route("/sse", endpoint=handle_sse),
            Mount("/messages/", app=sse.handle_post_message),
        ],
    )

if __name__ == "__main__":
    # Argument parsing
    parser = argparse.ArgumentParser(description='GENT v10 - Mem0 MCP Server')
    parser.add_argument('--host', default='0.0.0.0', help='Host to bind to')
    parser.add_argument('--port', type=int, default=8080, help='Port to listen on')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    args = parser.parse_args()

    # Environment check
    if not os.getenv("OPENAI_API_KEY"):
        logger.warning("⚠️ OPENAI_API_KEY není nastavena - Mem0 může nefungovat správně")

    logger.info("🔧 GENT v10 - Mem0 MCP Server")
    logger.info("📍 Lokální memory management pro komunikaci mezi LLM")
    logger.info("🔗 Integrace s GENT MCP systémem")
    logger.info(f"🌐 Server bude spuštěn na {args.host}:{args.port}")

    # Vytvoření logs adresáře
    os.makedirs("/opt/gent/logs", exist_ok=True)

    # Získání MCP serveru z FastMCP
    mcp_server = mcp._mcp_server

    # Vytvoření Starlette aplikace
    starlette_app = create_starlette_app(mcp_server, debug=args.debug)

    # Spuštění serveru
    uvicorn.run(starlette_app, host=args.host, port=args.port)
