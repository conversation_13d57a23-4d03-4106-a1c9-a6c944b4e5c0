{"name": "mem0-mcp-server", "version": "1.0.0", "description": "GENT v10 - Lokální Mem0 MCP server pro memory management", "author": "GENT AI System", "license": "MIT", "server": {"host": "localhost", "port": 8080, "protocol": "stdio", "timeout": 30000, "max_connections": 10}, "mem0": {"config": {"vector_store": {"provider": "qdrant", "config": {"host": "localhost", "port": 6333, "collection_name": "gent_memories", "timeout": 30}}, "llm": {"provider": "openai", "config": {"model": "gpt-4o-mini", "temperature": 0.1, "max_tokens": 1000}}, "embedder": {"provider": "openai", "config": {"model": "text-embedding-3-small"}}}, "storage": {"local_path": "/opt/gent/data/mem0", "backup_enabled": true, "backup_interval": 3600, "max_backup_files": 10}}, "logging": {"level": "INFO", "file": "/opt/gent/logs/mem0_mcp.log", "max_size": "10MB", "backup_count": 5, "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"}, "security": {"allowed_users": ["gent_system", "admin", "user"], "max_memory_size": 1000000, "max_memories_per_user": 10000, "rate_limit": {"requests_per_minute": 100, "burst_size": 20}}, "features": {"auto_cleanup": true, "cleanup_interval": 86400, "max_memory_age_days": 365, "compression_enabled": true, "analytics_enabled": true}, "tools": {"store_memory": {"enabled": true, "description": "Uloží novou vzpomínku do Mem0 systému", "rate_limit": 50}, "retrieve_memories": {"enabled": true, "description": "Načte vzpomínky na základě dotazu", "rate_limit": 100}, "search_memories": {"enabled": true, "description": "Vyhledá vzpomínky pomocí sémantického vyhledávání", "rate_limit": 100}, "delete_memory": {"enabled": true, "description": "Smaže konkrétní vzpomínku", "rate_limit": 20}, "list_all_memories": {"enabled": true, "description": "Vypíše všechny uložené vzpomínky", "rate_limit": 10}, "get_memory_stats": {"enabled": true, "description": "Získá statistiky o uložených vzpomínkách", "rate_limit": 10}}}