# 🎯 GENT - FUNKČNÍ LLM MODELY - FINÁLNÍ SEZNAM

**Datum:** 29. května 2025
**Status:** ✅ OVĚŘENO A FUNKČNÍ
**Celkem modelů:** 14

---

## 📋 FUNKČNÍ MODELY V DATABÁZI

### 🔵 OpenAI (9 modelů)
| Model | API Název | Status | Poznámka |
|-------|-----------|--------|----------|
| gpt-4o | `gpt-4o` | ✅ FUNKČNÍ | DEFAULT model |
| gpt-4o-mini | `gpt-4o-mini` | ✅ FUNKČNÍ | Rychlý a levný |
| gpt-4-turbo | `gpt-4-turbo` | ✅ FUNKČNÍ | Pokročilý model |
| gpt-4.1 | `gpt-4.1` | ✅ FUNKČNÍ | CASE SENSITIVE - speciální API |
| gpt-4.1-mini | `gpt-4.1-mini` | ✅ FUNKČNÍ | CASE SENSITIVE - speciální API |
| gpt-4.1-nano | `gpt-4.1-nano` | ✅ FUNKČNÍ | CASE SENSITIVE - speciální API |
| o1-preview | `o1-preview` | ✅ FUNKČNÍ | Reasoning model - speciální API |
| o1-mini | `o1-mini` | ✅ FUNKČNÍ | Reasoning model - speciální API |
| o3-mini | `o3-mini` | ✅ FUNKČNÍ | Reasoning model - speciální API |

### 🟢 Google Gemini (3 modely)
| Model | API Název | Status | Poznámka |
|-------|-----------|--------|----------|
| gemini-2.0-flash | `gemini-2.0-flash` | ✅ FUNKČNÍ | Nejnovější |
| gemini-2.0-flash-lite | `gemini-2.0-flash-lite` | ✅ FUNKČNÍ | Lehká verze |
| gemini-2.5-flash-preview-05-20 | `gemini-2.5-flash-preview-05-20` | ✅ FUNKČNÍ | Preview verze |

### 🟣 Anthropic Claude (2 modely)
| Model | API Název | Status | Poznámka |
|-------|-----------|--------|----------|
| claude-3-7-sonnet-latest | `claude-3-7-sonnet-latest` | ✅ FUNKČNÍ | DEFAULT model |
| claude-sonnet-4-20250514 | `claude-sonnet-4-20250514` | ✅ FUNKČNÍ | Nejnovější Claude 4 |

---

## 🚫 ODSTRANĚNÉ NEFUNKČNÍ MODELY

Tyto modely byly odstraněny, protože nefungovaly:

### ❌ OpenAI - Nefunkční
- `gpt-4.5`, `gpt-4.5-mini` - neexistují
- `gpt-4o-latest`, `gpt-4o-mini-latest` - neexistují
- `o3`, `o3-mini-high` - neexistují (o3-mini funguje!)
- `o4-mini`, `o4-mini-high` - neexistují
- `gpt-3.5-turbo` - odstraněn na žádost uživatele

### ⚡ OpenAI - Speciální API volání
**Speciální modely** (`o1-preview`, `o1-mini`, `o3-mini`, `gpt-4.1`, `gpt-4.1-mini`, `gpt-4.1-nano`) vyžadují:
- ❌ **BEZ** `temperature` parametru
- ❌ **BEZ** `top_p`, `frequency_penalty`, `presence_penalty`
- ✅ **S** `max_completion_tokens` místo `max_tokens`
- ✅ Automaticky rozpoznáno v GENT kódu
- ⚠️ **CASE SENSITIVE** názvy pro gpt-4.1 modely!

### ❌ Google - Odstraněné
- `gemini-2.5-pro-preview-05-06` - odstraněn na žádost uživatele

---

## 🔧 API VOLÁNÍ

### Jak volat modely přes GENT API:

```bash
# Získání seznamu všech modelů
curl -X GET "http://localhost:8001/api/db/llm/models"

# Test OpenAI modelu
curl -X POST "http://localhost:8001/api/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gpt-4o",
    "messages": [{"role": "user", "content": "Hello"}]
  }'

# Test Google modelu
curl -X POST "http://localhost:8001/api/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gemini-2.0-flash",
    "messages": [{"role": "user", "content": "Hello"}]
  }'

# Test Anthropic modelu
curl -X POST "http://localhost:8001/api/chat/completions" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "claude-3-7-sonnet-latest",
    "messages": [{"role": "user", "content": "Hello"}]
  }'
```

---

## 📊 DATABÁZOVÁ STRUKTURA

### Tabulka: `llm_models`
```sql
SELECT
    p.provider_name,
    m.model_name,
    m.model_identifier,
    m.is_default,
    m.is_active
FROM llm_models m
JOIN llm_providers p ON m.provider_id = p.provider_id
WHERE m.is_active = TRUE
ORDER BY p.provider_name, m.model_name;
```

### Výchozí modely:
- **OpenAI:** `gpt-4o`
- **Google:** žádný (všechny rovnocenné)
- **Anthropic:** `claude-3-7-sonnet-latest`

---

## ⚠️ DŮLEŽITÉ POZNÁMKY

1. **NIKDY NEMĚNIT** - Tyto modely jsou ověřené a funkční
2. **Všechna data z PostgreSQL databáze** - žádná mock data
3. **API server běží na portu 8001**
4. **Frontend na portu 8000**
5. **Restart API po změnách:** `sudo systemctl restart gent-api`

---

## 🎯 TESTOVÁNÍ

Všechny modely byly otestovány a fungují v GENT systému:
- ✅ Zobrazují se v dropdown menu
- ✅ Lze je vybrat v LLM Management
- ✅ API volání fungují
- ✅ Databázové spojení OK

**Poslední aktualizace:** 29.5.2025 12:45 UTC
**Verze:** FINAL v3 - s gpt-4.1 modely (14 modelů celkem)
