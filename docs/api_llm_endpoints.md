# API endpointy pro LLM poskytovatele a modely

Tento dokument detailně popisuje REST API pro přístup k databázovým tabulkám `llm_providers` a `llm_models`. API umožňuje kompletní CRUD operace nad těmito tabulkami a je navrženo s ohledem na bezpečnost, škálovatelnost a snadnou integraci s webovým rozhraním.

## Základní informace

### Základní URL
```
/api/v1
```

### Formát dat
API komunikuje pomocí JSON formátu. Všechny požadavky s tělem by měly obsahovat hlavičku `Content-Type: application/json` a odpovědi budou vraceny ve formátu JSON s hlavičkou `Content-Type: application/json`.

### Autentizace
API vyžaduje autentizaci pomocí JWT (JSON Web Token). Token musí být předán v hlavi<PERSON><PERSON> `Authorization` ve formátu `Bearer {token}`.

### Návratov<PERSON> kódy
- `200 OK` - Požadavek byl úspěšně zpra<PERSON>ván
- `201 Created` - Zdroj byl úspěšně vytvořen
- `400 Bad Request` - Neplatný požadavek nebo chybějící povinná data
- `401 Unauthorized` - Chybějící nebo neplatný autentizační token
- `403 Forbidden` - Uživatel nemá oprávnění pro danou operaci
- `404 Not Found` - Požadovaný zdroj nebyl nalezen
- `409 Conflict` - Konflikt s existujícími daty (např. duplicitní název)
- `500 Internal Server Error` - Interní chyba serveru

### Standardní formát chybových odpovědí
```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Popis chyby v čitelné podobě",
    "details": {
      "field1": ["Chybová zpráva pro pole 1"],
      "field2": ["Chybová zpráva pro pole 2"]
    }
  }
}
```

### Parametry pro stránkování
Pro endpointy vracející kolekce dat lze použít následující parametry pro stránkování:
- `page` - číslo stránky (výchozí hodnota: 1)
- `per_page` - počet položek na stránku (výchozí hodnota: 20, maximum: 100)

### Parametry pro filtrování
Pro endpointy vracející kolekce dat lze použít následující parametry pro filtrování:
- `filter[field]` - filtr podle hodnoty pole (např. `filter[is_active]=true`)
- `search` - fulltextové vyhledávání

### Parametry pro řazení
Pro endpointy vracející kolekce dat lze použít následující parametry pro řazení:
- `sort` - pole, podle kterého se má řadit (např. `sort=created_at`)
- `order` - směr řazení (`asc` nebo `desc`, výchozí hodnota: `asc`)

## Endpointy pro LLM poskytovatele

### Získání seznamu poskytovatelů
```
GET /api/v1/llm/providers
```

#### Parametry
- Standardní parametry pro stránkování, filtrování a řazení
- `filter[is_active]` - filtr podle aktivního stavu (`true`/`false`)

#### Odpověď
```json
{
  "data": [
    {
      "id": 1,
      "provider_name": "OpenAI",
      "api_base_url": "https://api.openai.com/v1",
      "api_version": "1",
      "api_key_required": true,
      "auth_type": "api_key",
      "rate_limit": 3000,
      "is_active": true,
      "created_at": "2025-04-19T06:00:00Z",
      "updated_at": "2025-04-19T06:00:00Z"
    },
    {
      "id": 2,
      "provider_name": "Anthropic",
      "api_base_url": "https://api.anthropic.com",
      "api_version": "1",
      "api_key_required": true,
      "auth_type": "api_key",
      "rate_limit": 2000,
      "is_active": true,
      "created_at": "2025-04-19T06:00:00Z",
      "updated_at": "2025-04-19T06:00:00Z"
    }
  ],
  "meta": {
    "current_page": 1,
    "per_page": 20,
    "total_items": 2,
    "total_pages": 1
  }
}
```

### Získání detailu poskytovatele
```
GET /api/v1/llm/providers/{id}
```

#### Parametry
- `id` - ID poskytovatele (v URL)

#### Odpověď
```json
{
  "data": {
    "id": 1,
    "provider_name": "OpenAI",
    "api_base_url": "https://api.openai.com/v1",
    "api_version": "1",
    "api_key_required": true,
    "auth_type": "api_key",
    "rate_limit": 3000,
    "is_active": true,
    "created_at": "2025-04-19T06:00:00Z",
    "updated_at": "2025-04-19T06:00:00Z"
  }
}
```

### Vytvoření nového poskytovatele
```
POST /api/v1/llm/providers
```

#### Parametry (v těle požadavku)
```json
{
  "provider_name": "Cohere",
  "api_base_url": "https://api.cohere.ai/v1",
  "api_key": "your-api-key",
  "api_version": "1",
  "api_key_required": true,
  "auth_type": "api_key",
  "rate_limit": 1500,
  "is_active": true
}
```

#### Povinná pole
- `provider_name` - unikátní název poskytovatele

#### Odpověď (status: 201 Created)
```json
{
  "data": {
    "id": 3,
    "provider_name": "Cohere",
    "api_base_url": "https://api.cohere.ai/v1",
    "api_version": "1",
    "api_key_required": true,
    "auth_type": "api_key",
    "rate_limit": 1500,
    "is_active": true,
    "created_at": "2025-04-19T06:10:00Z",
    "updated_at": "2025-04-19T06:10:00Z"
  }
}
```

### Aktualizace poskytovatele
```
PUT /api/v1/llm/providers/{id}
```

#### Parametry
- `id` - ID poskytovatele (v URL)
- Tělo požadavku - všechna pole, která mají být aktualizována

```json
{
  "api_base_url": "https://new-api-endpoint.cohere.ai/v1",
  "api_key": "new-api-key",
  "rate_limit": 2000
}
```

#### Odpověď
```json
{
  "data": {
    "id": 3,
    "provider_name": "Cohere",
    "api_base_url": "https://new-api-endpoint.cohere.ai/v1",
    "api_version": "1",
    "api_key_required": true,
    "auth_type": "api_key",
    "rate_limit": 2000,
    "is_active": true,
    "created_at": "2025-04-19T06:10:00Z",
    "updated_at": "2025-04-19T06:15:00Z"
  }
}
```

### Částečná aktualizace poskytovatele
```
PATCH /api/v1/llm/providers/{id}
```

#### Parametry
- `id` - ID poskytovatele (v URL)
- Tělo požadavku - pouze pole, která mají být aktualizována

```json
{
  "is_active": false
}
```

#### Odpověď
```json
{
  "data": {
    "id": 3,
    "provider_name": "Cohere",
    "api_base_url": "https://new-api-endpoint.cohere.ai/v1",
    "api_version": "1",
    "api_key_required": true,
    "auth_type": "api_key",
    "rate_limit": 2000,
    "is_active": false,
    "created_at": "2025-04-19T06:10:00Z",
    "updated_at": "2025-04-19T06:20:00Z"
  }
}
```

### Smazání poskytovatele
```
DELETE /api/v1/llm/providers/{id}
```

#### Parametry
- `id` - ID poskytovatele (v URL)

#### Odpověď (status: 204 No Content)
Prázdná odpověď

## Endpointy pro LLM modely

### Získání seznamu modelů
```
GET /api/v1/llm/models
```

#### Parametry
- Standardní parametry pro stránkování, filtrování a řazení
- `filter[provider_id]` - filtr podle ID poskytovatele
- `filter[is_active]` - filtr podle aktivního stavu (`true`/`false`)
- `filter[is_default]` - filtr podle výchozího stavu (`true`/`false`)
- `filter[capabilities]` - filtr podle schopností (např. `filter[capabilities]=chat,vision`)

#### Odpověď
```json
{
  "data": [
    {
      "id": 1,
      "provider_id": 1,
      "provider_name": "OpenAI",
      "model_name": "GPT-4 Turbo",
      "model_identifier": "gpt-4-turbo",
      "context_length": 128000,
      "max_tokens_output": 4096,
      "default_temperature": 0.7,
      "retry_attempts": 3,
      "retry_delay": 1000,
      "timeout": 30000,
      "pricing_input": 0.01,
      "pricing_output": 0.03,
      "capabilities": {
        "chat": true,
        "completion": true,
        "vision": true,
        "streaming": true,
        "function_calling": true
      },
      "is_default": true,
      "is_active": true,
      "created_at": "2025-04-19T06:00:00Z",
      "updated_at": "2025-04-19T06:00:00Z"
    }
  ],
  "meta": {
    "current_page": 1,
    "per_page": 20,
    "total_items": 10,
    "total_pages": 1
  }
}
```

### Získání detailu modelu
```
GET /api/v1/llm/models/{id}
```

#### Parametry
- `id` - ID modelu (v URL)

#### Odpověď
```json
{
  "data": {
    "id": 1,
    "provider_id": 1,
    "provider_name": "OpenAI",
    "model_name": "GPT-4 Turbo",
    "model_identifier": "gpt-4-turbo",
    "context_length": 128000,
    "max_tokens_output": 4096,
    "default_temperature": 0.7,
    "retry_attempts": 3,
    "retry_delay": 1000,
    "timeout": 30000,
    "pricing_input": 0.01,
    "pricing_output": 0.03,
    "capabilities": {
      "chat": true,
      "completion": true,
      "vision": true,
      "streaming": true,
      "function_calling": true
    },
    "is_default": true,
    "is_active": true,
    "created_at": "2025-04-19T06:00:00Z",
    "updated_at": "2025-04-19T06:00:00Z"
  }
}
```

### Získání modelů poskytovatele
```
GET /api/v1/llm/providers/{provider_id}/models
```

#### Parametry
- `provider_id` - ID poskytovatele (v URL)
- Standardní parametry pro stránkování, filtrování a řazení

#### Odpověď
Stejná struktura jako u endpointu `GET /api/v1/llm/models`

### Vytvoření nového modelu
```
POST /api/v1/llm/models
```

#### Parametry (v těle požadavku)
```json
{
  "provider_id": 1,
  "model_name": "DALL-E 3",
  "model_identifier": "dall-e-3",
  "capabilities": {
    "image_generation": true
  },
  "is_default": false,
  "is_active": true
}
```

#### Povinná pole
- `provider_id` - ID poskytovatele
- `model_name` - název modelu
- `model_identifier` - technický identifikátor modelu (musí být unikátní v rámci poskytovatele)

#### Odpověď (status: 201 Created)
```json
{
  "data": {
    "id": 11,
    "provider_id": 1,
    "provider_name": "OpenAI",
    "model_name": "DALL-E 3",
    "model_identifier": "dall-e-3",
    "context_length": null,
    "max_tokens_output": null,
    "default_temperature": 0.7,
    "retry_attempts": 3,
    "retry_delay": 1000,
    "timeout": 30000,
    "pricing_input": null,
    "pricing_output": null,
    "capabilities": {
      "image_generation": true
    },
    "is_default": false,
    "is_active": true,
    "created_at": "2025-04-19T06:30:00Z",
    "updated_at": "2025-04-19T06:30:00Z"
  }
}
```

### Aktualizace modelu
```
PUT /api/v1/llm/models/{id}
```

#### Parametry
- `id` - ID modelu (v URL)
- Tělo požadavku - všechna pole, která mají být aktualizována

```json
{
  "context_length": 16000,
  "max_tokens_output": 2048,
  "capabilities": {
    "image_generation": true,
    "high_resolution": true
  }
}
```

#### Odpověď
```json
{
  "data": {
    "id": 11,
    "provider_id": 1,
    "provider_name": "OpenAI",
    "model_name": "DALL-E 3",
    "model_identifier": "dall-e-3",
    "context_length": 16000,
    "max_tokens_output": 2048,
    "default_temperature": 0.7,
    "retry_attempts": 3,
    "retry_delay": 1000,
    "timeout": 30000,
    "pricing_input": null,
    "pricing_output": null,
    "capabilities": {
      "image_generation": true,
      "high_resolution": true
    },
    "is_default": false,
    "is_active": true,
    "created_at": "2025-04-19T06:30:00Z",
    "updated_at": "2025-04-19T06:35:00Z"
  }
}
```

### Částečná aktualizace modelu
```
PATCH /api/v1/llm/models/{id}
```

#### Parametry
- `id` - ID modelu (v URL)
- Tělo požadavku - pouze pole, která mají být aktualizována

```json
{
  "is_active": false
}
```

#### Odpověď
```json
{
  "data": {
    "id": 11,
    "provider_id": 1,
    "provider_name": "OpenAI",
    "model_name": "DALL-E 3",
    "model_identifier": "dall-e-3",
    "context_length": 16000,
    "max_tokens_output": 2048,
    "default_temperature": 0.7,
    "retry_attempts": 3,
    "retry_delay": 1000,
    "timeout": 30000,
    "pricing_input": null,
    "pricing_output": null,
    "capabilities": {
      "image_generation": true,
      "high_resolution": true
    },
    "is_default": false,
    "is_active": false,
    "created_at": "2025-04-19T06:30:00Z",
    "updated_at": "2025-04-19T06:40:00Z"
  }
}
```

### Nastavení výchozího modelu
```
PUT /api/v1/llm/models/{id}/set-default
```

#### Parametry
- `id` - ID modelu (v URL)

#### Odpověď
```json
{
  "data": {
    "id": 11,
    "provider_id": 1,
    "provider_name": "OpenAI",
    "model_name": "DALL-E 3",
    "model_identifier": "dall-e-3",
    "context_length": 16000,
    "max_tokens_output": 2048,
    "default_temperature": 0.7,
    "retry_attempts": 3,
    "retry_delay": 1000,
    "timeout": 30000,
    "pricing_input": null,
    "pricing_output": null,
    "capabilities": {
      "image_generation": true,
      "high_resolution": true
    },
    "is_default": true,
    "is_active": false,
    "created_at": "2025-04-19T06:30:00Z",
    "updated_at": "2025-04-19T06:45:00Z"
  }
}
```

### Smazání modelu
```
DELETE /api/v1/llm/models/{id}
```

#### Parametry
- `id` - ID modelu (v URL)

#### Odpověď (status: 204 No Content)
Prázdná odpověď

## Rozšířené možnosti

### Vyhledávání modelů podle schopností
```
GET /api/v1/llm/models/search
```

#### Parametry
- `capabilities` - seznam požadovaných schopností oddělených čárkou (např. `capabilities=chat,vision`)
- `provider_id` - volitelný filtr podle ID poskytovatele
- `is_active` - volitelný filtr podle aktivního stavu (`true`/`false`)
- Standardní parametry pro stránkování a řazení

#### Odpověď
Stejná struktura jako u endpointu `GET /api/v1/llm/models`

### Získání výchozího modelu poskytovatele
```
GET /api/v1/llm/providers/{provider_id}/default-model
```

#### Parametry
- `provider_id` - ID poskytovatele (v URL)

#### Odpověď
```json
{
  "data": {
    "id": 1,
    "provider_id": 1,
    "provider_name": "OpenAI",
    "model_name": "GPT-4 Turbo",
    "model_identifier": "gpt-4-turbo",
    "context_length": 128000,
    "max_tokens_output": 4096,
    "default_temperature": 0.7,
    "retry_attempts": 3,
    "retry_delay": 1000,
    "timeout": 30000,
    "pricing_input": 0.01,
    "pricing_output": 0.03,
    "capabilities": {
      "chat": true,
      "completion": true,
      "vision": true,
      "streaming": true,
      "function_calling": true
    },
    "is_default": true,
    "is_active": true,
    "created_at": "2025-04-19T06:00:00Z",
    "updated_at": "2025-04-19T06:00:00Z"
  }
}
```

## Bezpečnostní opatření

### Ošetření vstupních dat
Všechna vstupní data musí být validována na straně serveru. Validace zahrnuje:
- Kontrolu typů a formátů hodnot
- Kontrolu povinných polí
- Kontrolu rozsahu hodnot
- Sanitizaci vstupních řetězců proti XSS útokům
- Validaci JSONB hodnot

### Autorizace
API implementuje role-based access control (RBAC) s následujícími rolemi:
- `admin` - plný přístup ke všem endpointům
- `editor` - přístup pro čtení, vytváření a aktualizaci, ale ne pro mazání
- `viewer` - pouze přístup pro čtení

### Rate limiting
Pro prevenci DoS útoků je implementován rate limiting:
- 100 požadavků za minutu pro nepřihlášené uživatele
- 1000 požadavků za minutu pro přihlášené uživatele

### Logování
Všechny požadavky na API jsou logovány pro účely auditu, včetně:
- Času požadavku
- IP adresy
- ID uživatele
- Endpointu
- Status kódu odpovědi
- Doby zpracování

## Implementační poznámky

### API Framework
API bude implementováno pomocí FastAPI, které poskytuje:
- Automatické generování OpenAPI/Swagger dokumentace
- Validaci vstupních dat pomocí Pydantic modelů
- Podporu pro asynchronní zpracování požadavků
- Integraci s ORM (SQLAlchemy)

### Databázové připojení
API bude používat SQLAlchemy pro komunikaci s PostgreSQL databází. Pro každý model bude vytvořena odpovídající tabulka a repository třída pro přístup k databázi.

### Cachování
Pro zlepšení výkonu bude implementováno cachování pomocí Redis:
- Cachování často používaných endpointů (GET požadavky)
- Invalidace cache při změně dat
- Konfigurovatelná doba platnosti cache

### Verzování API
API implementuje verzování v URL cestě (`/api/v1/`), což umožňuje v budoucnu zavádět nekompatibilní změny bez ovlivnění stávajících klientů.
