# Phase 2: Vision v9 - Key Data Structures

**Navigation (Vision v9):**
*   [00 Introduction](00_introduction.md)
*   ...
*   [13 Testing](13_testing.md)
*   [14 Deployment and Operations](14_deployment_operations.md)
*   **[15 Data Structures](15_data_structures.md)** (This file)
*   [16 Index](16_index.md) *(Planned)*

---

This document describes the vision for the key data structures used in the GENT v9 system to represent information, tasks, and results. The design is based on concepts identified in older documentation. Implementation is expected to use Pydantic models for validation and serialization/deserialization (e.g., to/from JSON for API and communication).

## 1. Thought

The basic unit of information flowing through the cognitive system and between components.

*   **`thought_id`** (UUID/str): Unique identifier for the thought.
*   **`type`** (Enum/str): Type of thought (e.g., `USER_INPUT`, `PERCEPTION`, `REASONING`, `PLANNING`, `ACTION_STEP`, `AGENT_RESULT`, `SYSTEM_STATUS`, `FEEDBACK_ANALYSIS`).
*   **`content`** (dict/object): Specific data for the given thought type. Structure varies by type.
*   **`metadata`** (dict): Additional information (e.g., `confidence_score`, `processing_time`, `llm_used`, `cost`).
*   **`source_id`** (str): Identifier of the component or agent that created the thought.
*   **`parent_id`** (UUID/str, optional): ID of the thought from which this one originated (for causality tracking).
*   **`children_ids`** (List[UUID/str]): IDs of thoughts that originated from this one.
*   **`timestamp`** (datetime/float): Creation time of the thought.
*   **`embedding`** (List[float], optional): Vector representation of the content for semantic search (stored in pgVector).

## 2. Task

A unit of work assigned to an agent or team.

*   **`task_id`** (UUID/str): Unique identifier for the task.
*   **`parent_task_id`** (UUID/str, optional): ID of the parent task (if it's a subtask).
*   **`workflow_id`** (UUID/str, optional): ID of the workflow the task belongs to.
*   **`type`** (Enum/str): Type of task (e.g., `CODE_GENERATION`, `RESEARCH`, `ANALYSIS`, `TESTING`, `DOCUMENTATION`).
*   **`description`** (str): Textual description of the task's goal.
*   **`input_data`** (dict/object): Input data needed to complete the task.
*   **`context`** (dict): Contextual information (e.g., `project_id`, `relevant_thought_ids`, `constraints`).
*   **`priority`** (Enum/int): Task priority (e.g., `LOW`, `NORMAL`, `HIGH`, `CRITICAL`).
*   **`status`** (Enum/str): Current status of the task (`PENDING`, `ASSIGNED`, `IN_PROGRESS`, `COMPLETED`, `FAILED`, `CANCELLED`).
*   **`assigned_to_id`** (str, optional): ID of the agent or team the task is assigned to.
*   **`dependencies`** (List[UUID/str]): List of task IDs that must be completed before this task can start.
*   **`result`** (dict/object, optional): The result of the task upon completion.
*   **`error_message`** (str, optional): Error message in case of failure.
*   **`created_at`** (datetime): Task creation time.
*   **`updated_at`** (datetime): Time of the last status update.
*   **`completed_at`** (datetime, optional): Task completion time.

## 3. AgentOutput

A specific artifact or result of an agent's work within a task.

*   **`output_id`** (UUID/str): Unique identifier for the output.
*   **`task_id`** (UUID/str): ID of the task the output belongs to.
*   **`agent_id`** (str): ID of the agent that created the output.
*   **`type`** (Enum/str): Type of output (e.g., `CODE_SNIPPET`, `FILE_CONTENT`, `REPORT`, `ANALYSIS_SUMMARY`, `TEST_RESULTS`).
*   **`content`** (dict/object/str): The actual content of the output (can be text, JSON, a file reference, etc.).
*   **`metadata`** (dict): Additional information (e.g., `language` for code, `format` for report, `confidence`).
*   **`version`** (int): Version of the output (if an agent produces multiple versions).
*   **`timestamp`** (datetime): Output creation time.

## 4. Feedback

An evaluation or comment on an agent's output or task progress.

*   **`feedback_id`** (UUID/str): Unique identifier for the feedback.
*   **`target_id`** (UUID/str): ID of the target object (e.g., `output_id`, `task_id`).
*   **`target_type`** (Enum/str): Type of the target object (`OUTPUT`, `TASK`).
*   **`source_id`** (str): ID of the entity providing the feedback (user, supervisor, another agent).
*   **`source_type`** (Enum/str): Type of the entity (`USER`, `SUPERVISOR`, `AGENT`).
*   **`type`** (Enum/str): Type of feedback (e.g., `QUALITY_ASSESSMENT`, `BUG_REPORT`, `SUGGESTION`, `USER_RATING`).
*   **`content`** (dict/str): Textual description of the feedback, identified issues, suggestions for improvement.
*   **`rating`** (float/int, optional): Quantitative rating (e.g., score 0-1, star rating).
*   **`metadata`** (dict): Additional information (e.g., `severity` for a bug).
*   **`timestamp`** (datetime): Time the feedback was provided.

## Other Potential Structures:

*   **`AgentConfig`**: Agent-specific configuration.
*   **`Team`**: Representation of an agent team (ID, manager, members, goal).
*   **`Project`**: Metadata about a project managed by GENT.
*   **`KnowledgeItem`**: Unit of knowledge in the knowledge base.
*   **`Event`**: Structure for events in the Event Bus.

These structures provide the foundation for storing data in the database (using ORM models in `db/models/`) and for communication between components (using serialization/deserialization and validation schemas in `db/schemas/` or `core/communication/`).

---
**Next Chapter:** [16 Index](16_index.md) *(Planned)* - Index for the v9 vision documentation.
