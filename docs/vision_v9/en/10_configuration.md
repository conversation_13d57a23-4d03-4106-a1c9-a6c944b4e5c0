# Phase 2: Vision v9 - System Configuration

**Navigation (Vision v9):**
*   [00 Introduction](00_introduction.md)
*   ...
*   [08 Operational Modes](08_operational_modes.md)
*   [09 MCP Tools](09_mcp_tools.md)
*   **[10 Configuration](10_configuration.md)** (This file)
*   [11 Future Development](11_future_development.md) *(Planned)*
*   ... *(Further files will be added)*

---

This document describes the vision for how the GENT v9 system should be configurable to allow flexible adjustment of its behavior, connection to external services, and adaptation to different environments.

## Configuration System Goals:

*   **Centralization:** Most configuration should be managed in one place or logically structured locations.
*   **Flexibility:** Allow easy customization for different environments (development, testing, production).
*   **Security:** Separate sensitive data (API keys, passwords) from general configuration.
*   **Clarity:** Configuration files should be easy to read and edit.
*   **Dynamic Loading:** The system should be able to load configuration at startup and ideally react to some changes during runtime (if relevant and safe).

## Proposed Structure and Configuration Types:

1.  **Environment Variables (`.env`):**
    *   **Purpose:** Primarily for sensitive data and deployment-specific configuration.
    *   **Content:** API keys (LLM, MCP, other external services), database credentials, external service addresses, runtime environment settings (e.g., `DEVELOPMENT`, `PRODUCTION`).
    *   **Management:** The `.env` file should not be versioned in Git. Instead, a template `.env.example` is versioned.

2.  **Main Configuration Directory (`config/`):**
    *   **Purpose:** Contains most non-sensitive configuration, structured by system components and aspects.
    *   **Subdirectories (Vision):**
        *   `brain/`: Definition of GENT's "brain" (`gent_brain.md`), potentially other related settings.
        *   `llm/`: Configuration for LLM providers, default models, parameters, prompt templates (e.g., `llm_config.yaml` or `.json`, `prompt_templates/`).
        *   `agents/`: Configuration for the `Agent Factory`, default settings for different agent types or roles.
        *   `mcp/`: Configuration for `MCP Integration`, list of available MCP servers, their startup commands or addresses.
        *   `database/`: ORM configuration, connection settings (if not fully in `.env`).
        *   `logging/`: Settings for logging levels, formats, destinations (file, console, external service).
        *   `system/`: General system settings (orchestration parameters, communication settings, etc.).
        *   `environments/`: Specific configuration overrides for different environments (`development.yaml`, `production.yaml`), supplementing or changing the base configuration.
    *   **Format:** Preferably YAML or JSON for structured data, Markdown for textual definitions (like `gent_brain.md`).

3.  **Database Configuration:**
    *   Some more dynamic aspects of configuration (e.g., specific settings for individual users or projects managed by GENT) might be stored in the database.

## Configuration Loading:

*   **Config Manager:** A central component (`Config Manager` in `GENT Core`) responsible for loading, merging (base + environment + `.env`), and providing configuration to other parts of the system.
*   **Hierarchy:** Clearly defined loading priority (e.g., `.env` overrides environment file, which overrides base configuration).
*   **Validation:** Configuration should be validated upon loading (e.g., using Pydantic models) to prevent errors caused by incorrect settings.

## User Configuration:

*   The user should be able to easily modify key configuration parameters, especially those in `.env` and potentially selected parts in `config/` (e.g., LLM preferences).
*   Documentation should clearly describe available configuration options and their meaning.

## Dependency and Environment Management:

*   **GENT Core Runtime:** The core GENT itself (Python code) runs primarily in a virtual environment (`venv`) for easier development and iteration.
*   **Native Services:** The main system services run as native processes:
    *   PostgreSQL database runs as a native service on the host.
    *   GENT Web UI runs as a Node.js application (see `run_frontend.sh`).
    *   The main GENT API runs as a Python service directly from the `venv`.
*   **MCP Servers:** External MCP servers can be run as Docker containers or as native processes as needed and based on availability.
*   **Network and Ports:**
    *   GENT Web UI is accessible on port **8000**.
    *   The main GENT API is accessible on port **8001**.
    *   PostgreSQL database runs on the standard port **5432**.
    *   All services communicate via standard host network interfaces.

This vision for configuration and environment management offers a straightforward installation and operation where the main components run natively on the host, facilitating development, testing, and deployment.

---
**Next Chapter:** [11 Future Development](11_future_development.md) *(Planned)*
