# Phase 2: Vision v9 - Target Architecture (High-Level)

**Navigation (Vision v9):**
*   [00 Introduction](00_introduction.md)
*   [01 Core Philosophy](01_core_philosophy.md)
*   [02 User Interaction](02_user_interaction.md)
*   [03 Key Capabilities](03_key_capabilities.md)
*   **[04 Target Architecture](04_target_architecture.md)** (This file)
*   [05 Team Structure](05_team_structure.md) *(Planned)*
*   ... *(Further files will be added)*

---

This document describes the high-level design of the target architecture for the GENT v9 system. It focuses on the main components and their interrelationships.

## GENT v9 Architecture Overview:

GENT v9 is designed as a modular, distributed system with a central intelligent core and dynamically created agent teams.

```mermaid
graph TD
    subgraph GENT Core
        direction LR
        Brain[🧠 GENT Brain<br>(Identity, Knowledge, Cognition)]
        Orchestrator[⚙️ Orchestrator<br>(Workflow Management)]
        Comm[📡 Communication System<br>(Messages, Queues)]
        LLM_Mgr[🤖 LLM Manager<br>(LLM Provider Management)]
        Agent_Factory[🏭 Agent Factory<br>(Agent/Team Creation)]
        MCP_Int[🔌 MCP Integration<br>(Tool Access)]
        DB_Access[💾 DB Access<br>(Repositories)]
        Config_Mgr[🔧 Config Manager<br>(Configuration Loading)]
        EventBus[✨ Event Bus<br>(Async Events)]
        ProjectMgr[📂 Project Manager<br>(Project Management)]
    end

    subgraph Infrastructure
        direction TB
        Database[🛢️ PostgreSQL<br>+ PostgREST<br>+ pgVector]
        MCP_Servers[(🌐 External MCP Servers)]
        LLM_APIs[(☁️ External LLM APIs)]
    end

    subgraph Agent Teams (Dynamic)
        direction TB
        Mgr[🧑‍💼 Team Manager]
        subgraph Team X
            SA1[👨‍💻 Spec. Agent 1]
            SA2[📊 Spec. Agent 2]
            StdA1[📄 Documentation Agent]
            StdA2[📢 Communication Agent]
            StdA3[🐛 Error Handling Agent]
            StdA4[📜 Logging Agent]
        end
        Supervisor[🕵️ Supervisor (opt.)]
    end

    subgraph Permanent Teams
        direction TB
        MonitorTeam[📈 Monitoring Team]
        ImproveTeam[💡 Self-Improvement Team]
    end

    User[👤 User] -- Dialogue --> UI[🌐 Web UI (Port 8000)]
    UI -- Requests/Responses --> API[🔌 Main API (Port 8001)]
    API -- Commands/Data --> Comm
    Comm -- Messages --> Brain
    Comm -- Messages --> Orchestrator
    Comm -- Messages --> Agent_Factory
    Comm -- Messages/Events --> EventBus
    EventBus -- Events --> Brain
    EventBus -- Events --> Orchestrator
    EventBus -- Events --> MonitorTeam
    EventBus -- Events --> ImproveTeam
    EventBus -- Events --> Mgr

    Brain -- Instructions --> Orchestrator
    Brain -- Requests --> Agent_Factory
    Brain -- Uses --> LLM_Mgr
    Brain -- Uses --> DB_Access
    Brain -- Uses --> Config_Mgr
    Brain -- Uses --> ProjectMgr

    Orchestrator -- Tasks --> Comm
    Orchestrator -- Uses --> Agent_Factory
    Orchestrator -- Uses --> DB_Access

    Agent_Factory -- Creates --> Mgr
    Agent_Factory -- Creates --> SA1 & SA2 & StdA1 & StdA2 & StdA3 & StdA4
    Agent_Factory -- Creates --> Supervisor
    Agent_Factory -- Creates --> MonitorTeam
    Agent_Factory -- Creates --> ImproveTeam

    Mgr -- Coordinates --> SA1 & SA2 & StdA1 & StdA2 & StdA3 & StdA4
    Mgr -- Communicates with --> Comm
    Mgr -- Reports to --> Supervisor
    Mgr -- Reports to --> MonitorTeam

    SA1 & SA2 & StdA1 & StdA2 & StdA3 & StdA4 -- Use --> LLM_Mgr
    SA1 & SA2 & StdA1 & StdA2 & StdA3 & StdA4 -- Use --> MCP_Int
    SA1 & SA2 & StdA1 & StdA2 & StdA3 & StdA4 -- Use --> DB_Access
    SA1 & SA2 & StdA1 & StdA2 & StdA3 & StdA4 -- Communicate with --> Comm

    Supervisor -- Oversees --> Mgr & Team X
    Supervisor -- Communicates with --> Comm

    MonitorTeam -- Monitors --> Mgr & Team X
    MonitorTeam -- Writes to --> DB_Access
    MonitorTeam -- Communicates with --> Comm

    ImproveTeam -- Analyzes --> Brain & MonitorTeam Data
    ImproveTeam -- Proposes changes to --> Brain
    ImproveTeam -- Communicates with --> Comm

    MCP_Int -- Calls --> MCP_Servers
    LLM_Mgr -- Calls --> LLM_APIs
    DB_Access -- Accesses --> Database

    note right of Database GENT Core runs in venv.<br>Database, API and Web UI<br>run as native services.<br>MCP servers may run in Docker.

    style GENT Core fill:#f9f,stroke:#333,stroke-width:2px
    style Agent Teams (Dynamic) fill:#ccf,stroke:#333,stroke-width:2px
    style Infrastructure fill:#eee,stroke:#333,stroke-width:2px
    style Permanent Teams fill:#cfc,stroke:#333,stroke-width:2px
```

## Key Components and Flows:

1.  **User Interface and API:**
    *   **Web UI (Port 8000):** The main interface for user interaction with GENT (chat, progress view, results).
    *   **Main API (Port 8001):** Provides a programmatic interface for the Web UI and potentially for external systems. Processes requests and forwards them to the Communication System or Event Bus.
2.  **Communication System & Event Bus:**
    *   **Communication System:** Ensures direct (and asynchronous) exchange of messages (requests, responses) between components.
    *   **Event Bus:** Enables asynchronous publishing and subscription of events (e.g., `task_completed`, `agent_registered`), supporting component decoupling.
3.  **GENT Brain:** The central intelligence. Processes user thoughts, makes decisions, communicates, utilizes LLM, knowledge base (DB), and **Project Manager**. Initiates team creation and orchestration. Reacts to events from the Event Bus.
4.  **Agent Factory:** Responsible for creating and configuring team managers, specialized and standard agents, supervisors, and permanent teams based on requests from the brain or orchestrator.
5.  **Orchestrator:** Manages the execution of complex workflows broken down into tasks for agents. Manages dependencies and aggregates results.
6.  **LLM Manager:** Provides a unified interface for accessing various LLM providers.
7.  **MCP Integration:** Mediates agent access to external tools running as MCP servers (ideally in Docker).
8.  **DB Access and Database:**
    *   **Technology:** Uses **PostgreSQL** with **PostgREST** (for easy API over DB) and **pgVector** (for vector search and semantic memory) extensions.
    *   **Purpose:** Stores all persistent data: system state, knowledge base, GENT and agent memory (potential inspiration/integration of **Mem0**), event logs, interaction history, task results.
    *   **Access:** The `DB Access` component encapsulates interaction using repositories.
9.  **Config Manager:** Loads and provides configuration to other components (see `10_configuration.md`).
10. **Project Manager:** Manages the creation, modification, and organization of projects generated or modified by GENT in the `projects/` directory.
11. **Runtime Environment:**
    *   **GENT Core:** Runs directly in a Python `venv` for easier development and iteration.
    *   **Native Services:** Database (PostgreSQL), Web UI (Port 8000) and Main API (Port 8001) run as native services without containerization.
    *   **MCP Servers:** External MCP servers and potentially other integrated tools can be managed as Docker containers as needed.
12. **Dynamic Agent Teams:** Created ad-hoc to realize a specific thought. Led by a manager, potentially overseen by a supervisor. Contain specialized and standard agents.
13. **Permanent Teams:**
    *   **Monitoring Team:** Continuously collects data about the activity of other teams (stores in DB, publishes events) for learning and analysis purposes.
    *   **Self-Improvement Team:** Analyzes the system and monitoring data, proposes improvements.

This architecture emphasizes separation of concerns, modularity, and the ability to dynamically assemble and manage agent teams. It utilizes standardized technologies (PostgreSQL, Docker/Kubernetes, MCP), asynchronous communication (Event Bus), and includes mechanisms for learning and self-improvement.

---
**Next Chapter:** [05 Team Structure](05_team_structure.md) *(Planned)*
