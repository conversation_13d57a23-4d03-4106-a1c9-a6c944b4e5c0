# Phase 2: Vision v9 - Testing Strategy

**Navigation (Vision v9):**
*   [00 Introduction](00_introduction.md)
*   ...
*   [11 Future Development](11_future_development.md)
*   [12 Web Interface](12_web_interface.md)
*   **[13 Testing](13_testing.md)** (This file)
*   [14 Deployment and Operations](14_deployment_operations.md) *(Planned)*
*   ... *(Further files will be added)*

---

This document describes the vision for the automated testing strategy in the GENT v9 system, which is crucial for ensuring code quality, reliability, and maintainability.

## Testing Goals:

*   **Verify Functionality:** Ensure that all components and the system as a whole function according to specifications.
*   **Prevent Regressions:** Detect unintentional errors introduced during code modifications.
*   **Ensure Quality:** Maintain high quality of the code and system outputs.
*   **Support Refactoring:** Enable safe refactoring and code modifications with confidence that tests will reveal potential issues.
*   **Document Behavior:** Tests also serve as a form of living documentation of the system's expected behavior.

## Testing Levels:

The testing strategy in GENT v9 should include multiple levels:

1.  **Unit Tests:**
    *   **Focus:** Testing isolated parts of the code (functions, methods, classes) without external dependencies (database, API, LLM).
    *   **Tools:** **Pytest** using mock objects (`pytest-mock`) and fixtures (`conftest.py`).
    *   **Goal:** Rapid verification of the correctness of individual component logic. High code coverage.

2.  **Integration Tests:**
    *   **Focus:** Testing the collaboration between several components or modules (e.g., interaction between `BrainManager` and `LLMManager`, communication between agents, writing to a real (test) database).
    *   **Tools:** **Pytest** using fixtures to set up the test environment (e.g., test database, mocked external APIs).
    *   **Goal:** Verification of correct interaction and data flow between components.

3.  **End-to-End (E2E) Tests:**
    *   **Focus:** Testing the entire system from the user's perspective or through key scenarios (e.g., submitting a thought -> planning -> team assembly -> task execution -> result display).
    *   **Tools:** **Pytest** for backend E2E scenarios; for the UI, tools like Cypress or Playwright can be considered (if UI interaction testing is needed).
    *   **Goal:** Verification of the functionality of key user scenarios across the entire system.

4.  **Performance Tests:**
    *   **Focus:** Measuring the system's performance, latency, throughput, and scalability under load.
    *   **Tools:** **Locust** (as suggested by older documentation) or other tools for load testing the API and system.
    *   **Goal:** Identification of bottlenecks and ensuring the system meets performance requirements.

## Test Organization and Management:

*   **Directory Consolidation:** Unlike the older structure with multiple directories (`tests`, `tests_new`...), v9 should have a **single, clear structure** for all tests, likely in the main `tests/` directory, with subdirectories for each level (e.g., `tests/unit`, `tests/integration`, `tests/e2e`, `tests/performance`).
*   **Conventions:** Use standard Pytest conventions for file names (`test_*.py`) and test functions (`test_*`).
*   **Markers:** Utilize Pytest markers (`@pytest.mark.*`) to categorize tests (e.g., `@pytest.mark.unit`, `@pytest.mark.integration`, `@pytest.mark.slow`) and allow selective execution.
*   **Test Data and Mocks:** Centralize test data and mock objects (e.g., in `tests/fixtures/`, `tests/mocks/`).
*   **CI/CD Integration:** All relevant tests (at least unit and integration) should be automatically run as part of the CI/CD pipeline (e.g., GitHub Actions) on every commit or pull request.
*   **Code Coverage:** Regularly measure and track code coverage by tests (`pytest-cov`) with the goal of maintaining a high level of coverage.
*   **Scripts:** Create simple scripts for easily running different test suites and generating reports (e.g., `scripts/run_tests.sh`, `scripts/run_coverage.sh`).

## Testing AI Components:

Testing LLM-dependent components requires a specific approach:

*   **LLM Mocking:** For unit and most integration tests, use a mock LLM provider that returns predefined responses to make tests fast, deterministic, and independent of external APIs (and costs).
*   **Specific Integration Tests:** Create a limited set of integration tests that actually call the LLM API (with test keys and models) to verify the correctness of request formatting and response processing. Mark these tests as `@pytest.mark.slow` or `@pytest.mark.api`.
*   **Output Quality Evaluation:** For E2E tests or specific scenarios, it might be necessary to implement mechanisms for (at least partially) automated evaluation of the quality of LLM or agent outputs (e.g., using another LLM as an "evaluator," comparison with a reference output, metrics).

A consistent and well-structured testing strategy is essential for the development and maintenance of a complex system like GENT v9.

---
**Next Chapter:** [14 Deployment and Operations](14_deployment_operations.md) *(Planned)*
