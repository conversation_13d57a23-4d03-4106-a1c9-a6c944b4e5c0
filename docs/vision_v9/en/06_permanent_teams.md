# Phase 2: Vision v9 - Permanent Teams (Monitoring and Self-Improvement)

**Navigation (Vision v9):**
*   [00 Introduction](00_introduction.md)
*   [01 Core Philosophy](01_core_philosophy.md)
*   [02 User Interaction](02_user_interaction.md)
*   [03 Key Capabilities](03_key_capabilities.md)
*   [04 Target Architecture](04_target_architecture.md)
*   [05 Team Structure](05_team_structure.md)
*   **[06 Permanent Teams](06_permanent_teams.md)** (This file)
*   [07 LLM Integration](07_llm_integration.md) *(Planned)*
*   ... *(Further files will be added)*

---

This document describes the vision for the functioning of two key **permanent teams** in the GENT v9 system, which ensure its ability to learn, adapt, and optimize its own operation. Unlike dynamic teams created for specific user thoughts, these teams operate continuously or are regularly activated by GENT.

## 1. Monitoring Team

*   **Purpose:** To systematically monitor, record, and analyze the activity of all other teams (dynamic and permanent) and the entire GENT system.
*   **Responsibilities:**
    *   **Data Collection:** Automated collection of logs, performance metrics, task results, error reports, communication records, and other relevant data from agent and team activities.
    *   **Process Analysis:** Analysis of successful and unsuccessful workflows, identification of patterns, bottlenecks, and best practices.
    *   **Knowledge Storage:** Structured storage of acquired insights and analyzed data into a central knowledge base or database (link to `core/knowledge/` and `db/`).
    *   **Reporting:** Providing summary reports and analyses to GENT (its brain/cognitive units) and the Self-Improvement Team.
*   **Composition:** Likely consists of agents specialized in data collection, data analysis, natural language processing (for analyzing logs and communication), and knowledge management.
*   **Significance:** This team is crucial for GENT's ability to learn from experience and obtain objective data about its own functioning.

## 2. Self-Improvement Team

*   **Purpose:** To actively analyze the functioning of the GENT system and propose (and potentially implement) specific improvements to its architecture, algorithms, processes, or "brain".
*   **Responsibilities:**
    *   **System Analysis:** In-depth analysis of GENT's components, its performance, efficiency, and behavior based on data from the Monitoring Team and possibly direct introspection (`core/introspection/`, `core/metacognition/`).
    *   **Identifying Weaknesses and Opportunities:** Searching for areas for optimization, bug fixing, algorithm improvement, or capability expansion.
    *   **Proposing Changes:** Formulating specific proposals for modifications to the code, "brain" configuration (`config/brain/gent_brain.md`), LLM training data, or system architecture.
    *   **Testing Proposals:** Simulating or testing proposed changes (if possible).
    *   **Implementation (Optional/Controlled):** Depending on the level of autonomy, this team might either only propose changes to GENT (or a human operator) for approval, or have a limited ability to directly implement some changes (e.g., configuration adjustments, less critical code parts).
*   **Composition:** Likely consists of agents specialized in system analysis, software engineering, AI/ML (for analyzing models and algorithms), planning, and possibly testing.
*   **Significance:** This team ensures the proactive evolution and adaptation of GENT, fulfilling the vision of a self-improving system.

## Interaction of Permanent Teams:

*   The Monitoring Team provides data and analyses to the Self-Improvement Team and the GENT Brain.
*   The Self-Improvement Team analyzes data and the system, proposing changes to the GENT Brain.
*   The GENT Brain (or its relevant cognitive units) processes inputs from both teams and may initiate system changes or adjust its own behavior.
*   Both teams utilize GENT's standard infrastructure (communication, LLM, MCP, DB).

The existence of these permanent teams elevates GENT from a mere task executor to a system capable of reflection, learning, and autonomous development.

---
**Next Chapter:** [07 LLM Integration](07_llm_integration.md) *(Planned)*
