# Phase 2: Vision v9 - Operational Modes

**Navigation (Vision v9):**
*   [00 Introduction](00_introduction.md)
*   ...
*   [06 Permanent Teams](06_permanent_teams.md)
*   [07 LLM Integration](07_llm_integration.md)
*   **[08 Operational Modes](08_operational_modes.md)** (This file)
*   [09 MCP Tools](09_mcp_tools.md) *(Planned)*
*   ... *(Further files will be added)*

---

This document describes the vision for the functioning of different operational modes in the GENT v9 system. These modes allow GENT to adapt its behavior, focus, and utilized tools to the specific phase of work or type of task.

## Overview of Operational Modes:

GENT v9 should possess at least the following operational modes:

1.  **PLAN (Planning Mode):**
    *   **Purpose:** Collaborative definition, analysis, and planning of tasks and thoughts in cooperation with the user.
    *   **GENT's Behavior:**
        *   Actively discusses with the user, asks questions, analyzes requirements.
        *   Helps structure thoughts, identify goals and success criteria.
        *   Creates detailed, step-by-step plans for realizing the thought.
        *   May utilize agents for analysis or research to support planning.
        *   **DOES NOT** perform implementation or code execution (except possibly tools for analysis and planning).
    *   **Typical Tools:** Tools for text analysis, diagram generation, information structuring, possibly search (for concept validation).
    *   **Output:** A detailed, user-approved realization plan.

2.  **ACT (Action/Realization Mode):**
    *   **Purpose:** Autonomous realization of a user-approved plan or thought.
    *   **GENT's Behavior:**
        *   Takes the approved plan (or thought, if sufficiently concrete).
        *   Creates a manager and assembles the necessary team of agents.
        *   Orchestrates the team's work, delegates tasks.
        *   Agents execute tasks (writing code, testing, deployment, etc.) using LLMs and MCP tools.
        *   Monitors progress, resolves issues (possibly in collaboration with a supervisor).
        *   Produces concrete outputs (code, applications, documentation).
    *   **Typical Tools:** Tools for file operations, command execution, Git, LLMs for code generation, MCP tools for specific tasks.
    *   **Output:** Implemented solution according to the plan.

3.  **RESEARCH (Research Mode):**
    *   **Purpose:** Systematic collection, analysis, and synthesis of information on a given topic.
    *   **GENT's Behavior:**
        *   Defines research questions (possibly in collaboration with the user).
        *   Utilizes research agents to search the web, databases, documents using MCP tools.
        *   Analyzes and structures the found information.
        *   Synthesizes findings into a clear format.
    *   **Typical Tools:** MCP tools for search (Brave, Tavily, Perplexity), tools for text analysis and processing, LLMs for summarization and synthesis.
    *   **Output:** Research report, analysis, literature review, answers to specific questions.

4.  **IMPROVE (Improvement Mode):**
    *   **Purpose:** Analysis and optimization of the functioning of the GENT system itself or its parts.
    *   **GENT's Behavior:**
        *   Activates the Self-Improvement Team (or parts of it).
        *   Analyzes data from the Monitoring Team, logs, performance metrics, feedback.
        *   Identifies areas for improvement (efficiency, quality, cost).
        *   Proposes specific changes (in configuration, algorithms, "brain").
        *   May (controlledly) implement proposed improvements.
    *   **Typical Tools:** Tools for data analysis, performance profiling, working with configuration files, LLMs for analysis and solution proposal.
    *   **Output:** Improvement proposals, implemented enhancements, optimization reports.

## Switching Modes:

*   **User Initiation:** The user should be able to explicitly request GENT to switch to a specific mode (e.g., "Let's plan now...", "Conduct research on topic...").
*   **Automatic Initiation by GENT:** GENT might automatically transition between modes based on context (e.g., switching from PLAN to ACT after plan approval) or internal logic (e.g., periodic activation of IMPROVE mode).
*   **Clear Indication:** The current operational mode should always be clearly indicated to the user.

This set of operational modes provides GENT v9 with the necessary flexibility to handle different work phases and task types in an effective and contextually appropriate manner.

---
**Next Chapter:** [09 MCP Tools](09_mcp_tools.md) *(Planned)*
