# GENT v9: Target Vision Documentation - Index

This directory (`docs/gent_v9/vision_v9/en/`) contains a set of documents describing the target vision for the GENT system version 9.

## Contents:

1.  **[00 Introduction](00_introduction.md)**: Basic introduction to the GENT v9 vision as an intelligent partner.
2.  **[01 Core Philosophy](01_core_philosophy.md)**: Purpose, principles, values, ethics, and identity of GENT v9.
3.  **[02 User Interaction](02_user_interaction.md)**: Ideal flow of dialogue and thought submission.
4.  **[03 Key Capabilities](03_key_capabilities.md)**: Description of main capabilities (teams, tools, modes, learning, cognition, orchestration, LLM debate).
5.  **[04 Target Architecture](04_target_architecture.md)**: High-level design of the architecture, components, and flows (including Event Bus, Project Manager, Kubernetes).
6.  **[05 Team Structure](05_team_structure.md)**: Ideal functioning of dynamic teams, agent roles (including Critique Agent), and supervisors.
7.  **[06 Permanent Teams](06_permanent_teams.md)**: Role and function of the Monitoring and Self-Improvement teams.
8.  **[07 LLM Integration](07_llm_integration.md)**: Vision for selection, configuration, utilization of LLMs (including LiteLLM, Retry, Cache, Rate Limit), and self-optimization.
9.  **[08 Operational Modes](08_operational_modes.md)**: Detailed description of PLAN, ACT, RESEARCH, IMPROVE modes.
10. **[09 MCP Tools](09_mcp_tools.md)**: Vision for utilizing MCP for accessing external tools.
11. **[10 Configuration](10_configuration.md)**: Vision for system configuration (including `.env`, `config/`, DB, dependency management, and Docker).
12. **[11 Future Development](11_future_development.md)**: Longer-term vision and possibilities (optimization, learning, multimodality, proactivity, UI enhancements, inspiration from external tools).
13. **[12 Web Interface](12_web_interface.md)**: Vision for the UI (Vue.js, dark mode, modular CSS, offline support, display of tests/logs).
14. **[13 Testing](13_testing.md)**: Vision for the testing strategy (Pytest, Locust, test levels, CI/CD, coverage).
15. **[14 Deployment and Operations](14_deployment_operations.md)**: Vision for deployment (Docker Compose/Kubernetes), CI/CD (GitHub Actions), monitoring (Prometheus/Grafana), backup.
16. **[15 Data Structures](15_data_structures.md)**: Detailed description of key structures (Thought, Task, Output, Feedback).
17. **[16 Index](16_index.md)**: This index file.

---
This set of documents serves as the foundation for **Phase 3: Refactoring Plan**, where this vision will be compared with the current state analysis (`../current_state_analysis/en/`) and specific steps for code modification will be proposed. The progress of this phase is tracked in `../progress/en/phase_2_vision_status.md`.
