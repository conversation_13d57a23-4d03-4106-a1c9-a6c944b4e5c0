# Phase 2: Vision v9 - Team Structure and Agent Roles

**Navigation (Vision v9):**
*   [00 Introduction](00_introduction.md)
*   [01 Core Philosophy](01_core_philosophy.md)
*   [02 User Interaction](02_user_interaction.md)
*   [03 Key Capabilities](03_key_capabilities.md)
*   [04 Target Architecture](04_target_architecture.md)
*   **[05 Team Structure](05_team_structure.md)** (This file)
*   [06 Permanent Teams](06_permanent_teams.md) *(Planned)*
*   ... *(Further files will be added)*

---

This document describes the vision for the functioning of dynamically created AI agent teams in the GENT v9 system, including their structure and the roles of individual members.

## Dynamic Team Creation:

*   For each user-approved "thought," GENT (via the `Agent Factory`) creates a **dedicated team of agents**.
*   The goal is to assemble an optimal group of experts for the efficient realization of the given task.

## Roles within the Team:

Each dynamically created team has a defined structure and roles:

1.  **Team Manager:**
    *   Created and configured by GENT (or `Agent Factory`) specifically for the given thought.
    *   Receives the high-level goal and context from GENT (or the Orchestrator).
    *   **Responsibilities:**
        *   Decomposition of the main goal into smaller, specific tasks.
        *   Selection and assembly of the agent team (standard and specialized) in collaboration with the `Agent Factory`.
        *   Assignment of tasks to individual agents.
        *   Coordination of agent work within the team.
        *   Monitoring progress and resolving issues within the team.
        *   Aggregation of results from agents.
        *   Communication with GENT (or the Orchestrator) regarding progress and results.
        *   Potential communication with the Supervisor.

2.  **Standard Agents (present in most teams):**
    *   **Documentation Agent:** Responsible for the continuous creation and maintenance of technical and user documentation related to the realized thought.
    *   **Logging Agent:** Records important events, decisions, and actions performed by the team for tracking, auditing, and analysis purposes.
    *   **Error Handling Agent:** Monitors and records errors occurring during the team's work, potentially initiating the resolution process.
    *   **Communication Agent (with GENT/Orchestrator):** Ensures standardized communication between the team manager and GENT's central components.

3.  **Specialized Agents:**
    *   Selected by the team manager (or GENT) based on the specific needs of the given thought.
    *   Each has expertise in a particular area.
    *   **Examples:**
        *   *Developer Agent:* Writes, modifies, refactors code.
        *   *Testing Agent:* Creates and runs tests, reports bugs.
        *   *Analytical Agent:* Analyzes data, requirements, problems.
        *   *Research Agent:* Searches for information on the web or other sources using MCP.
        *   *Database Agent:* Designs schemas, writes queries, manages data.
        *   *UI/UX Agent:* Designs user interfaces.
        *   *Creative Agent:* Generates texts, ideas, proposals.
        *   *Security Agent:* Performs security analysis, suggests measures.
        *   *Deployment Agent:* Prepares and performs deployments.
        *   ***Critique Agent:*** Specializes in evaluating the quality, consistency, and correctness of other agents' outputs, providing constructive criticism.
    *   The specific set of specialized agents varies from team to team.

4.  **Supervisor (optional oversight and evaluation role):**
    *   May be assigned by GENT to oversee the team's work or a specific aspect of the project (e.g., quality, security), especially for complex or critical tasks.
    *   **Responsibilities (general):**
        *   Monitoring the quality of the team's work and adherence to standards.
        *   Providing feedback to the manager and agents (utilizing `Evaluation Modules` and `Feedback Modules`).
        *   Escalating issues to GENT or other relevant entities.
        *   Evaluating final results against defined criteria.
    *   **Possible Specializations (inspired by v6):** In the future, the supervisor role could be further specialized (e.g., Quality, Ethical, Security, Performance), with each type focusing on specific aspects of evaluation and oversight. However, in the basic v9 vision, it is understood as a more general role of oversight and quality assessment.

## Interaction and Communication:

*   Communication within the team and between the team and other components occurs via the central **Communication System**.
*   The Team Manager is the main coordinator and communication point for the team.
*   Agents collaborate on tasks defined by the manager, utilizing LLMs, MCP tools, and database access as needed.

This structure allows for flexible and scalable task resolution, where teams with the necessary expertise are assembled for each problem, while standard roles ensure consistency in documentation, logging, and communication.

---
**Next Chapter:** [06 Permanent Teams](06_permanent_teams.md) *(Planned)*
