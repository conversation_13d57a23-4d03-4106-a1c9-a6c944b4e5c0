# Phase 2: Vision v9 - Utilization of MCP Tools

**Navigation (Vision v9):**
*   [00 Introduction](00_introduction.md)
*   ...
*   [07 LLM Integration](07_llm_integration.md)
*   [08 Operational Modes](08_operational_modes.md)
*   **[09 MCP Tools](09_mcp_tools.md)** (This file)
*   [10 Configuration](10_configuration.md) *(Planned)*
*   ... *(Further files will be added)*

---

This document describes the vision for how the GENT v9 system utilizes the Model Context Protocol (MCP) to access external tools and resources, thereby expanding its capabilities and enabling interaction with the real world.

## Role of MCP in GENT v9:

MCP is a key technology that allows GENT and its agents to **securely and standardly** utilize external functions without needing these functions to be directly integrated into GENT's core. This promotes modularity and extensibility.

## Key Aspects of MCP Utilization:

1.  **Tool Access for Agents:**
    *   Specialized agents (and potentially managers/supervisors) are the primary consumers of MCP tools.
    *   Each agent should have a defined **list of MCP tools** available to it, relevant to its role (e.g., a research agent has access to web search tools, a developer agent to file system and Git tools).
    *   This list can be defined in the agent's configuration or dynamically assigned by the team manager or GENT.

2.  **Central MCP Integration (`MCP_Int`):**
    *   The `MCP Integration` component in GENT's core serves as a **gateway** for all MCP tool calls.
    *   It processes requests from agents (or other components).
    *   It communicates with the appropriate MCP servers (whether external or internal).
    *   It processes responses and forwards them back to the calling component.
    *   It might implement logic for caching results, managing permissions, or monitoring tool usage.

3.  **MCP Server Management and Configuration:**
    *   The system must be aware of available MCP servers and how to run/connect to them.
    *   Configuration should allow easy addition or removal of MCP servers.
    *   Ideally, configuration should be managed centrally (e.g., in `config/` or via environment variables) and loaded by the `Config Manager` component.
    *   Running necessary MCP servers can be handled via `docker-compose.yml` or startup scripts (`run_servers.sh`).

4.  **Security and Permissions:**
    *   Access to MCP tools should be controlled. Not every agent should have access to all tools.
    *   `MCP Integration` or `Agent Context` could contain logic to verify if a given agent has permission to use the requested tool.
    *   Special attention must be paid to tools that can make changes to the system or interact with sensitive data (e.g., `write_to_file`, `execute_command`).

5.  **Internal vs. External MCP Servers:**
    *   GENT v9 can utilize both **standard external MCP servers** (filesystem, git, search, fetch, etc.) and **custom internal MCP servers** (as suggested by the `mcp/` directory in the current structure) for specific functions (e.g., advanced memory/knowledge management, specific Git operations).

## Usage Examples:

*   **Research Agent:** Uses `tavily-search` or `brave_web_search` to find information on the web.
*   **Developer Agent:** Uses `read_file`, `write_to_file`, `replace_in_file` for code manipulation and `git_add`, `git_commit` for version control.
*   **Analytical Agent:** Uses `read_file` to read logs or data files, or potentially `read_query` (if a DB MCP server exists) for database queries.
*   **Team Manager:** Might use `list_files` to get an overview of created artifacts.

Effective and secure integration of MCP tools is fundamental to GENT v9's ability to perform a wide range of tasks and interact with its environment.

---
**Next Chapter:** [10 Configuration](10_configuration.md) *(Planned)*
