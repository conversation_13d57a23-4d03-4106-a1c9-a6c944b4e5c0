# Fáze 2: Vize v9 - Konfigurace Systému

**Navigace (Vize v9):**
*   [00 Úvod](00_uvod.md)
*   ...
*   [08 Operační módy](08_operacni_mody.md)
*   [09 MCP Nástroje](09_mcp_nastroje.md)
*   **[10 Konfigurace](10_konfigurace.md)** (Tento soubor)
*   [11 Budoucí vývoj](11_budouci_vyvoj.md) *(Plánováno)*
*   ... *(<PERSON><PERSON><PERSON> soubory budou doplněny)*

---

Tento dokument popisuje vizi, jak má být systém GENT v9 konfigurovatelný, aby umožnil flexibilní nastavení jeho chování, připojení k externím službám a přizpůsobení různým prostředím.

## <PERSON><PERSON>le Konfiguračního Systému:

*   **Centralizace:** Většina konfigurace by m<PERSON><PERSON> b<PERSON>t spravována na jednom místě nebo logicky strukturovaných místech.
*   **Flexibilita:** <PERSON>ožnit snadné přizpůsobení pro různá prostředí (vývoj, testování, produkce).
*   **Bezpečnost:** Oddělit citlivé údaje (API klíče, hesla) od obecné konfigurace.
*   **Přehlednost:** Konfigurační soubory by měly být snadno čitelné a upravitelné.
*   **Dynamické Načítání:** Systém by měl být schopen načíst konfiguraci při startu a ideálně i reagovat na některé změny za běhu (pokud je to relevantní a bezpečné).

## Navrhovaná Struktura a Typy Konfigurace:

1.  **Proměnné Prostředí (`.env`):**
    *   **Účel:** Primárně pro citlivé údaje a konfiguraci specifickou pro dané nasazení.
    *   **Obsah:** API klíče (LLM, MCP, jiné externí služby), přihlašovací údaje k databázi, adresy externích služeb, nastavení běhového prostředí (např. `DEVELOPMENT`, `PRODUCTION`).
    *   **Správa:** Soubor `.env` by neměl být verzován v Gitu. Místo toho se verzuje šablona `.env.example`.

2.  **Hlavní Konfigurační Adresář (`config/`):**
    *   **Účel:** Obsahuje většinu necitlivé konfigurace, strukturovanou podle komponent a aspektů systému.
    *   **Podadresáře (vize):**
        *   `brain/`: Definice "mozku" GENTa (`gent_brain.md`), případně další související nastavení.
        *   `llm/`: Konfigurace LLM providerů, výchozích modelů, parametrů, šablon promptů (např. `llm_config.yaml` nebo `.json`, `prompt_templates/`).
        *   `agents/`: Konfigurace pro `Agent Factory`, výchozí nastavení pro různé typy agentů nebo rolí.
        *   `mcp/`: Konfigurace pro `MCP Integraci`, seznam dostupných MCP serverů, jejich spouštěcí příkazy nebo adresy.
        *   `database/`: Konfigurace ORM, nastavení připojení (pokud není plně v `.env`).
        *   `logging/`: Nastavení úrovní logování, formátů, cílů (soubor, konzole, externí služba).
        *   `system/`: Obecná systémová nastavení (parametry orchestrace, nastavení komunikace, atd.).
        *   `environments/`: Specifické přepsání konfigurace pro různá prostředí (`development.yaml`, `production.yaml`), které doplňují nebo mění základní konfiguraci.
    *   **Formát:** Preferovaně YAML nebo JSON pro strukturovaná data, Markdown pro textové definice (jako `gent_brain.md`).

3.  **Konfigurace v Databázi:**
    *   Některé dynamičtější aspekty konfigurace (např. specifická nastavení pro jednotlivé uživatele nebo projekty spravované GENTem) mohou být uloženy v databázi.

## Načítání Konfigurace:

*   **Config Manager:** Centrální komponenta (`Config Manager` v `GENT Core`) zodpovědná za načítání, slučování (základní + environmentální + `.env`) a poskytování konfigurace ostatním částem systému.
*   **Hierarchie:** Jasně definovaná priorita načítání (např. `.env` přepisuje environmentální soubor, ten přepisuje základní konfiguraci).
*   **Validace:** Konfigurace by měla být validována při načítání (např. pomocí Pydantic modelů), aby se předešlo chybám způsobeným nesprávným nastavením.

## Uživatelská Konfigurace:

*   Uživatel by měl mít možnost snadno upravovat klíčové konfigurační parametry, zejména ty v `.env` a případně vybrané části v `config/` (např. preference LLM).
*   Dokumentace by měla jasně popisovat dostupné konfigurační volby a jejich význam.

## Správa Závislostí a Prostředí:

*   **GENT Core Runtime:** Samotné jádro GENTa (Python kód) běží primárně ve virtuálním prostředí (`venv`) pro usnadnění vývoje a iterací.
*   **Nativní Služby:** Hlavní služby systému běží jako nativní procesy:
    *   Databáze PostgreSQL běží jako nativní služba na hostiteli.
    *   GENT Web UI běží jako Node.js aplikace (viz `run_frontend.sh`).
    *   Hlavní GENT API běží jako Python služba přímo z `venv`.
*   **MCP Servery:** Externí MCP servery mohou být spouštěny jako Docker kontejnery nebo jako nativní procesy podle potřeby a dostupnosti.
*   **Síť a Porty:**
    *   GENT Web UI je dostupné na portu **8000**.
    *   Hlavní GENT API je dostupné na portu **8001**.
    *   Databáze PostgreSQL běží standardně na portu **5432**.
    *   Všechny služby komunikují přes standardní síťová rozhraní hostitele.

Tato vize konfigurace a správy prostředí nabízí přímočarou instalaci a provoz, kde hlavní komponenty běží nativně na hostiteli, což usnadňuje vývoj, testování a nasazení.

---
**Další kapitola:** [11 Budoucí vývoj](11_budouci_vyvoj.md) *(Plánováno)*
