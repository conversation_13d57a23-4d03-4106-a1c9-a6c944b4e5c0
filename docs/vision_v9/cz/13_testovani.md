# Fáze 2: Vize v9 - Strategie Testování

**Navigace (Vize v9):**
*   [00 Úvod](00_uvod.md)
*   ...
*   [11 Budoucí vývoj](11_budouci_vyvoj.md)
*   [12 Webové rozhraní](12_webove_rozhrani.md)
*   **[13 Testování](13_testovani.md)** (<PERSON>to soubor)
*   [14 Nasazení a provoz](14_nasazeni_a_provoz.md) *(Plán<PERSON>)*
*   ... *(<PERSON>š<PERSON> soubory budou doplněny)*

---

Tento dokument popisuje vizi pro strategii automatizovaného testování v systému GENT v9, kter<PERSON> je klíčová pro zajištění kvality, spolehlivosti a udržitelnosti kódu.

## Cíle Testování:

*   **Ově<PERSON><PERSON><PERSON>:** <PERSON><PERSON><PERSON><PERSON>, že všechny komponenty a systém jako celek fungují podle specifikace.
*   **Prevence Regresí:** Odhalit neúmyslné chyby zavedené při úpravách kódu.
*   **Zajištění Kvality:** Udržovat vysokou kvalitu kódu a výstupů systému.
*   **Podpora Refaktoringu:** Umožnit bezpečný refaktoring a úpravy kódu s důvěrou, že testy odhalí případné problémy.
*   **Dokumentace Chování:** Testy slouží i jako forma živé dokumentace očekávaného chování systému.

## Úrovně Testování:

Strategie testování v GENT v9 by měla zahrnovat více úrovní:

1.  **Jednotkové Testy (Unit Tests):**
    *   **Zaměření:** Testování izolovaných částí kódu (funkce, metody, třídy) bez externích závislostí (databáze, API, LLM).
    *   **Nástroje:** **Pytest** s využitím mock objektů (`pytest-mock`) a fixtures (`conftest.py`).
    *   **Cíl:** Rychlé ověření správnosti logiky jednotlivých komponent. Vysoké pokrytí kódu (Code Coverage).

2.  **Integrační Testy (Integration Tests):**
    *   **Zaměření:** Testování spolupráce mezi několika komponentami nebo moduly (např. interakce mezi `BrainManager` a `LLMManager`, komunikace mezi agenty, zápis do reálné (testovací) databáze).
    *   **Nástroje:** **Pytest** s využitím fixtures pro nastavení testovacího prostředí (např. testovací databáze, mockované externí API).
    *   **Cíl:** Ověření správné interakce a toku dat mezi komponentami.

3.  **End-to-End Testy (E2E Tests):**
    *   **Zaměření:** Testování celého systému z pohledu uživatele nebo klíčových scénářů (např. zadání myšlenky -> plánování -> sestavení týmu -> vykonání úkolu -> zobrazení výsledku).
    *   **Nástroje:** **Pytest** pro backendové E2E scénáře; pro UI lze zvážit nástroje jako Cypress nebo Playwright (pokud bude potřeba testovat UI interakce).
    *   **Cíl:** Ověření funkčnosti klíčových uživatelských scénářů napříč celým systémem.

4.  **Výkonnostní Testy (Performance Tests):**
    *   **Zaměření:** Měření výkonu, latence, propustnosti a škálovatelnosti systému pod zátěží.
    *   **Nástroje:** **Locust** (jak naznačuje starší dokumentace) nebo jiné nástroje pro zátěžové testování API a systému.
    *   **Cíl:** Identifikace úzkých míst a zajištění, že systém splňuje výkonnostní požadavky.

## Organizace a Správa Testů:

*   **Konsolidace Adresářů:** Na rozdíl od starší struktury s více adresáři (`tests`, `tests_new`...) by měla být pro v9 definována **jednotná a jasná struktura** pro všechny testy, pravděpodobně v hlavním adresáři `tests/`, s podadresáři pro jednotlivé úrovně (např. `tests/unit`, `tests/integration`, `tests/e2e`, `tests/performance`).
*   **Konvence:** Používat standardní konvence Pytestu pro názvy souborů (`test_*.py`) a testovacích funkcí (`test_*`).
*   **Značky (Markers):** Využívat Pytest markery (`@pytest.mark.*`) pro kategorizaci testů (např. `@pytest.mark.unit`, `@pytest.mark.integration`, `@pytest.mark.slow`) a umožnění selektivního spouštění.
*   **Testovací Data a Mocky:** Centralizovat testovací data a mock objekty (např. v `tests/fixtures/`, `tests/mocks/`).
*   **CI/CD Integrace:** Všechny relevantní testy (minimálně unit a integrační) by měly být automaticky spouštěny v rámci CI/CD pipeline (např. GitHub Actions) při každém commitu nebo pull requestu.
*   **Pokrytí Kódu (Code Coverage):** Pravidelně měřit a sledovat pokrytí kódu testy (`pytest-cov`) s cílem udržovat vysokou úroveň pokrytí.
*   **Skripty:** Vytvořit jednoduché skripty pro snadné spouštění různých sad testů a generování reportů (např. `scripts/run_tests.sh`, `scripts/run_coverage.sh`).

## Testování AI Komponent:

Testování komponent závislých na LLM vyžaduje specifický přístup:

*   **Mockování LLM:** Pro unit a většinu integračních testů používat mock LLM providera, který vrací předdefinované odpovědi, aby byly testy rychlé, deterministické a nezávislé na externím API (a nákladech).
*   **Specifické Integrační Testy:** Vytvořit omezenou sadu integračních testů, které skutečně volají LLM API (s testovacími klíči a modely), aby se ověřila správnost formátování požadavků a zpracování odpovědí. Tyto testy označit jako `@pytest.mark.slow` nebo `@pytest.mark.api`.
*   **Hodnocení Kvality Výstupů:** Pro E2E testy nebo specifické scénáře může být potřeba implementovat mechanismy pro (alespoň částečně) automatizované hodnocení kvality výstupů LLM nebo agentů (např. pomocí jiného LLM jako "hodnotitele", porovnání s referenčním výstupem, metrikami).

Důsledná a dobře strukturovaná strategie testování je nezbytná pro vývoj a údržbu komplexního systému jako GENT v9.

---
**Další kapitola:** [14 Nasazení a provoz](14_nasazeni_a_provoz.md) *(Plánováno)*
