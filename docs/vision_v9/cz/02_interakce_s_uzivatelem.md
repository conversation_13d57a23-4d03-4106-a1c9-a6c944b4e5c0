# Fáze 2: Vize v9 - Interakce s Uživatelem

**Navigace (Vize v9):**
*   [00 Úvod](00_uvod.md)
*   [01 Základní filozofie](01_zakladni_filozofie.md)
*   **[02 Interakce s uživatelem](02_interakce_s_uzivatelem.md)** (<PERSON>to soubor)
*   [03 Klíč<PERSON><PERSON> schopnosti](03_klicove_schopnosti.md) *(Plánováno)*
*   ... *(<PERSON>š<PERSON> soubory budou dopln<PERSON>)*

---

Tento dokument popisuje ideální průběh interakce mezi uživatelem a systémem GENT v9, od počátečního nápadu až po zahájení realizace.

## Fáze Interakce:

Interakce typicky probíhá v následujících fázích:

1.  **Iniciace Dialogu a Představení Myšlenky:**
    *   Uživatel osloví GENTa (např. prostřednictvím chatu ve webovém rozhraní).
    *   Uživatel představí svůj počáteční nápad, cíl nebo problém ("myšlenku"). Může jít o velmi obecné zadání (např. "Chci vytvořit aplikaci pro sledování výdajů") nebo již konkrétnější představu.

2.  **Kolaborativní Zpřesňování (Brainstorming & Analýza):**
    *   GENT aktivně reaguje, klade doplňující otázky, aby myšlenku plně pochopil.
    *   Využívá své znalosti a schopnosti (včetně LLM a případně MCP nástrojů pro rychlý průzkum) k:
        *   Analýze myšlenky (proveditelnost, potenciální problémy, alternativy).
        *   Navrhování možných přístupů a řešení.
        *   Identifikaci potřebných informací nebo rozhodnutí od uživatele.
    *   Probíhá iterativní dialog, kde GENT a uživatel společně myšlenku formují, zpřesňují a obohacují. GENT může nabízet různé varianty, vizualizace (např. diagramy) nebo příklady.
    *   Cílem této fáze je dosáhnout **společného porozumění** a jasně definovaného, odsouhlaseného zadání.

3.  **Shrnutí a Potvrzení:**
    *   Jakmile se zdá, že myšlenka je dostatečně definována, GENT představí uživateli její **finální shrnutí**. Toto shrnutí by mělo jasně popisovat:
        *   Cíl myšlenky.
        *   Klíčové požadavky a parametry.
        *   Očekávané výstupy.
        *   Případná omezení nebo předpoklady.
    *   GENT se explicitně zeptá uživatele, zda toto shrnutí přesně odpovídá jeho představě a zda je připraven myšlenku **schválit k realizaci**.

4.  **Uživatelské Schválení:**
    *   Uživatel má možnost:
        *   **Schválit:** Potvrdit, že shrnutí je správné a GENT může začít s realizací.
        *   **Požádat o úpravy:** Vrátit se k Fázi 2 a dále myšlenku upravit.
        *   **Zrušit:** Ukončit proces pro tuto myšlenku.
    *   Schválení uživatelem je **nezbytným krokem** pro přechod k autonomní realizaci.

5.  **Předání k Realizaci:**
    *   Po obdržení explicitního schválení GENT potvrdí převzetí úkolu.
    *   Informuje uživatele, že nyní zahájí proces plánování a realizace (vytvoření manažera, sestavení týmu atd.).
    *   Od tohoto bodu přebírá GENT iniciativu a pracuje autonomně, přičemž ideálně poskytuje uživateli informace o postupu (viz další dokumenty).

## Charakteristiky Ideální Interakce:

*   **Partnerská:** Uživatel a GENT jsou partneři, ne pán a sluha.
*   **Iterativní:** Myšlenky se vyvíjejí postupně v dialogu.
*   **Proaktivní (ze strany GENTa):** GENT nejen odpovídá, ale aktivně klade otázky, navrhuje, analyzuje.
*   **Jasná a Srozumitelná:** Komunikace je vedena tak, aby byla pro uživatele co nejjasnější.
*   **Řízená Uživatelem (do bodu schválení):** Uživatel má kontrolu nad definicí myšlenky a rozhoduje o zahájení realizace.

Tento model interakce klade důraz na počáteční fázi porozumění a definice, což by mělo vést k lepším a relevantnějším výsledkům v následné fázi autonomní realizace.

---
**Další kapitola:** [03 Klíčové schopnosti](03_klicove_schopnosti.md) *(Plánováno)*
