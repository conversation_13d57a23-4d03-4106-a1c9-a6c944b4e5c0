# Fáze 2: Vize v9 - Integrace Velkých Jazykových Modelů (LLM)

**Navigace (Vize v9):**
*   [00 Úvod](00_uvod.md)
*   ...
*   [05 Týmová struktura](05_tymova_struktura.md)
*   [06 St<PERSON>lé týmy](06_stale_tymy.md)
*   **[07 Integrace LLM](07_integrace_llm.md)** (Tento soubor)
*   [08 Operační módy](08_operacni_mody.md) *(Plánováno)*
*   ... *(Další soubory budou doplněny)*

---

Tento dokument popisuje vizi, jak systém GENT v9 integruje a využívá velké jazykové modely (LLM) jako klíčovou komponentu pro své kognitivní schopnosti a schopnosti svých agentů.

## Centrální Správa a Flexibilita:

*   **LLM Manager:** Všechny interakce s LLM probíhají přes centrální komponentu (`LLM Manager`). Ta poskytuje jednotné rozhraní pro zbytek systému a zapouzdřuje komplexitu komunikace s různými API. **Pro zjednodušení správy a komunikace s různými API lze zvážit využití nástrojů jako [LiteLLM](https://github.com/BerriAI/litellm).**
*   **Podpora Více Providerů:** Architektura nativně podporuje snadné přidávání a používání modelů od různých providerů (např. OpenAI, Anthropic, Google, open-source modely). Využívá se společné rozhraní (`Base Provider`), případně abstrakce poskytovaná nástrojem jako LiteLLM.
*   **Výběr Modelu:**
    *   Systém umožňuje flexibilní výběr LLM pro různé účely.
    *   **Globální Konfigurace:** Uživatel může nastavit výchozí preferované modely pro různé typy úloh (např. jiný model pro generování kódu, jiný pro kreativní psaní, jiný pro rychlou analýzu).
    *   **Kontextuální Přepsání:** GENT (jeho mozek nebo orchestrátor) nebo manažer týmu může dynamicky zvolit specifický model pro konkrétní úkol nebo agenta na základě kontextu, požadavků na kvalitu, latenci nebo cenu.
    *   **Parametry:** Možnost konfigurovat parametry volání LLM (teplota, max tokens, atd.) na různých úrovních (globálně, per model, per volání).

## Role LLM v Systému:

*   **GENT Brain:** Centrální mozek GENTa využívá LLM pro své klíčové kognitivní funkce: porozumění uživatelským vstupům, uvažování, plánování, rozhodování, generování odpovědí v dialogu.
*   **Specializovaní Agenti:** Většina specializovaných agentů se silně spoléhá na LLM pro vykonávání svých úkolů (např. generování kódu, analýza textu, psaní dokumentace, výzkum). Každý agent může mít nakonfigurovaný preferovaný model pro svou specializaci.
*   **Manažeři a Supervizoři:** Mohou využívat LLM pro analýzu postupu, hodnocení výsledků nebo generování reportů.
*   **Stálé Týmy:** Monitorovací a Sebezdokonalovací týmy využívají LLM pro analýzu dat, logů a navrhování vylepšení.

## Pokročilé Možnosti:

*   **Fine-Tuning (Doladění):** Systém by měl ideálně podporovat (nebo mít rozhraní pro) fine-tuning open-source nebo kompatibilních modelů na specifických datech GENTa (např. úspěšné postupy, interní znalosti), aby se zlepšil jejich výkon a relevance pro konkrétní úkoly.
*   **Samooptimalizace Výběru LLM (Budoucí Vize):**
    *   GENT (pravděpodobně prostřednictvím Sebezdokonalovacího týmu a dat z Monitorovacího týmu) by se mohl v budoucnu učit **automaticky optimalizovat výběr LLM**.
    *   Cílem je dynamicky volit nejvhodnější model pro daný úkol na základě kombinace faktorů:
        *   Požadovaná kvalita výstupu.
        *   Výkon a latence modelu.
        *   **Cena** volání API.
    *   Systém by mohl experimentovat s různými modely a vyhodnocovat jejich efektivitu pro různé typy úkolů.

## Konfigurace:

*   Uživatel musí mít možnost snadno konfigurovat API klíče pro jednotlivé LLM providery (pravděpodobně přes `.env` soubor).
*   Konfigurace preferovaných modelů a parametrů by měla být dostupná přes konfigurační soubory (např. `config/llm_config.json`, `config/llm/`).

Tato vize integrace LLM zajišťuje flexibilitu, rozšiřitelnost a potenciál pro budoucí optimalizaci, což je klíčové pro dlouhodobý rozvoj a efektivitu systému GENT v9.

---
**Další kapitola:** [08 Operační módy](08_operacni_mody.md) *(Plánováno)*
