# Fáze 2: Vize v9 - Cílová Architektura (Vysokoúrovňová)

**Navigace (Vize v9):**
*   [00 Úvod](00_uvod.md)
*   [01 Základní filozofie](01_zakladni_filozofie.md)
*   [02 Interakce s uživatelem](02_interakce_s_uzivatelem.md)
*   [03 Klíčové schopnosti](03_klicove_schopnosti.md)
*   **[04 Cílová architektura](04_cilova_architektura.md)** (Tento soubor)
*   [05 Týmová struktura](05_tymova_struktura.md) *(Plánováno)*
*   ... *(<PERSON><PERSON><PERSON> soubory budou dopln<PERSON>)*

---

Tento dokument popisuje vysokoúrovňový návrh cílové architektury pro systém GENT v9. Zaměřuje se na hlavní komponenty a jejich vzájemné vztahy.

## Přehled Architektury GENT v9:

GENT v9 je navržen jako mod<PERSON>, distribuovaný systém s centrálním inteligentním jádrem a dynamicky vytvářenými týmy agentů.

```mermaid
graph TD
    subgraph GENT Core
        direction LR
        Brain[🧠 GENT Brain<br>(Identita, Znalosti, Kognice)]
        Orchestrator[⚙️ Orchestrator<br>(Řízení Workflow)]
        Comm[📡 Komunikační Systém<br>(Zprávy, Fronty)]
        LLM_Mgr[🤖 LLM Manager<br>(Správa LLM Providerů)]
        Agent_Factory[🏭 Agent Factory<br>(Tvorba Agentů/Týmů)]
        MCP_Int[🔌 MCP Integrace<br>(Přístup k Nástrojům)]
        DB_Access[💾 DB Přístup<br>(Repozitáře)]
        Config_Mgr[🔧 Config Manager<br>(Načítání Konfigurace)]
        EventBus[✨ Event Bus<br>(Asynchronní Události)]
        ProjectMgr[📂 Project Manager<br>(Správa Projektů)]
    end

    subgraph Infrastructure
        direction TB
        Database[🛢️ PostgreSQL<br>+ PostgREST<br>+ pgVector]
        MCP_Servers[(🌐 Externí MCP Servery)]
        LLM_APIs[(☁️ Externí LLM API)]
    end

    subgraph Agent Teams (Dynamické)
        direction TB
        Mgr[🧑‍💼 Manažer Týmu]
        subgraph Team X
            SA1[👨‍💻 Spec. Agent 1]
            SA2[📊 Spec. Agent 2]
            StdA1[📄 Agent Dokumentace]
            StdA2[📢 Agent Komunikace]
            StdA3[🐛 Agent Chyb]
            StdA4[📜 Agent Logování]
        end
        Supervisor[🕵️ Supervizor (volit.)]
    end

    subgraph Permanent Teams
        direction TB
        MonitorTeam[📈 Monitorovací Tým]
        ImproveTeam[💡 Sebezdokonalovací Tým]
    end

    User[👤 Uživatel] -- Dialog --> UI[🌐 Web UI (Port 8000)]
    UI -- Požadavky/Odpovědi --> API[🔌 Hlavní API (Port 8001)]
    API -- Příkazy/Data --> Comm
    Comm -- Zprávy --> Brain
    Comm -- Zprávy --> Orchestrator
    Comm -- Zprávy --> Agent_Factory
    Comm -- Zprávy/Události --> EventBus
    EventBus -- Události --> Brain
    EventBus -- Události --> Orchestrator
    EventBus -- Události --> MonitorTeam
    EventBus -- Události --> ImproveTeam
    EventBus -- Události --> Mgr

    Brain -- Pokyny --> Orchestrator
    Brain -- Požadavky --> Agent_Factory
    Brain -- Využívá --> LLM_Mgr
    Brain -- Využívá --> DB_Access
    Brain -- Využívá --> Config_Mgr
    Brain -- Využívá --> ProjectMgr

    Orchestrator -- Úkoly --> Comm
    Orchestrator -- Využívá --> Agent_Factory
    Orchestrator -- Využívá --> DB_Access

    Agent_Factory -- Vytváří --> Mgr
    Agent_Factory -- Vytváří --> SA1 & SA2 & StdA1 & StdA2 & StdA3 & StdA4
    Agent_Factory -- Vytváří --> Supervisor
    Agent_Factory -- Vytváří --> MonitorTeam
    Agent_Factory -- Vytváří --> ImproveTeam

    Mgr -- Koordinuje --> SA1 & SA2 & StdA1 & StdA2 & StdA3 & StdA4
    Mgr -- Komunikuje s --> Comm
    Mgr -- Reportuje --> Supervisor
    Mgr -- Reportuje --> MonitorTeam

    SA1 & SA2 & StdA1 & StdA2 & StdA3 & StdA4 -- Využívají --> LLM_Mgr
    SA1 & SA2 & StdA1 & StdA2 & StdA3 & StdA4 -- Využívají --> MCP_Int
    SA1 & SA2 & StdA1 & StdA2 & StdA3 & StdA4 -- Využívají --> DB_Access
    SA1 & SA2 & StdA1 & StdA2 & StdA3 & StdA4 -- Komunikují s --> Comm

    Supervisor -- Dohliží na --> Mgr & Team X
    Supervisor -- Komunikuje s --> Comm

    MonitorTeam -- Sleduje --> Mgr & Team X
    MonitorTeam -- Zapisuje do --> DB_Access
    MonitorTeam -- Komunikuje s --> Comm

    ImproveTeam -- Analyzuje --> Brain & MonitorTeam Data
    ImproveTeam -- Navrhuje změny --> Brain
    ImproveTeam -- Komunikuje s --> Comm

    MCP_Int -- Volá --> MCP_Servers
    LLM_Mgr -- Volá --> LLM_APIs
    DB_Access -- Přistupuje k --> Database

    note right of Database GENT Core běží ve venv.<br>Databáze, API a Web UI<br>běží jako nativní služby.<br>MCP servery mohou běžet v Dockeru.

    style GENT Core fill:#f9f,stroke:#333,stroke-width:2px
    style Agent Teams (Dynamické) fill:#ccf,stroke:#333,stroke-width:2px
    style Infrastructure fill:#eee,stroke:#333,stroke-width:2px
    style Permanent Teams fill:#cfc,stroke:#333,stroke-width:2px
```

## Klíčové Komponenty a Toky:

1.  **Uživatelské Rozhraní a API:**
    *   **Web UI (Port 8000):** Hlavní rozhraní pro interakci uživatele s GENTem (chat, zobrazení postupu, výsledků).
    *   **Hlavní API (Port 8001):** Poskytuje programové rozhraní pro Web UI a potenciálně pro externí systémy. Zpracovává požadavky a předává je do Komunikačního systému nebo Event Busu.
2.  **Komunikační Systém & Event Bus:**
    *   **Komunikační Systém:** Zajišťuje přímou (i asynchronní) výměnu zpráv (požadavky, odpovědi) mezi komponentami.
    *   **Event Bus:** Umožňuje asynchronní publikování a odběr událostí (např. `task_completed`, `agent_registered`), čímž podporuje decoupling komponent.
3.  **GENT Brain:** Centrální inteligence. Zpracovává uživatelské myšlenky, rozhoduje, komunikuje, využívá LLM, znalostní bázi (DB) a **Project Manager**. Iniciuje vytváření týmů a orchestraci. Reaguje na události z Event Busu.
4.  **Agent Factory:** Zodpovídá za vytváření a konfiguraci manažerů týmů, specializovaných a standardních agentů, supervizorů a stálých týmů na základě požadavků od mozku nebo orchestrátoru.
5.  **Orchestrator:** Řídí vykonávání komplexních workflow rozdělených na úkoly pro agenty. Spravuje závislosti a agreguje výsledky.
6.  **LLM Manager:** Poskytuje jednotné rozhraní pro přístup k různým LLM providerům.
7.  **MCP Integrace:** Zprostředkovává přístup agentů k externím nástrojům běžícím jako MCP servery (ideálně v Dockeru).
8.  **DB Přístup a Databáze:**
    *   **Technologie:** Využívá **PostgreSQL** s rozšířeními **PostgREST** (pro snadné API nad DB) a **pgVector** (pro vektorové vyhledávání a sémantickou paměť).
    *   **Účel:** Ukládání veškerých perzistentních dat: stav systému, znalostní báze, paměť GENTa a agentů (možná inspirace/integrace **Mem0**), logy událostí, historie interakcí, výsledky úkolů.
    *   **Přístup:** Komponenta `DB Přístup` zapouzdřuje interakci pomocí repozitářů.
9.  **Config Manager:** Načítá a poskytuje konfiguraci ostatním komponentám (viz `10_konfigurace.md`).
10. **Project Manager:** Spravuje vytváření, úpravu a organizaci projektů generovaných nebo upravovaných GENTem v adresáři `projects/`.
11. **Běhové Prostředí:**
    *   **GENT Core:** Běží přímo v Python `venv` pro snazší vývoj a iteraci.
    *   **Nativní Služby:** Databáze (PostgreSQL), Web UI (Port 8000) a Hlavní API (Port 8001) běží jako nativní služby bez kontejnerizace.
    *   **MCP Servery:** Externí MCP servery a potenciálně další integrované nástroje mohou být spravovány jako Docker kontejnery podle potřeby.
12. **Dynamické Týmy Agentů:** Vytvářeny ad-hoc pro realizaci konkrétní myšlenky. Vedeny manažerem, potenciálně pod dohledem supervizora. Obsahují specializované a standardní agenty.
13. **Stálé Týmy:**
    *   **Monitorovací Tým:** Průběžně sbírá data o činnosti ostatních týmů (ukládá do DB, publikuje události) pro účely učení a analýzy.
    *   **Sebezdokonalovací Tým:** Analyzuje systém a data z monitoringu, navrhuje vylepšení.

Tato architektura klade důraz na oddělení zodpovědností, modularitu a schopnost dynamicky sestavovat a řídit týmy agentů. Využívá standardizované technologie (PostgreSQL, Docker/Kubernetes, MCP), asynchronní komunikaci (Event Bus) a zahrnuje mechanismy pro učení a sebezdokonalování.

---
**Další kapitola:** [05 Týmová struktura](05_tymova_struktura.md) *(Plánováno)*
