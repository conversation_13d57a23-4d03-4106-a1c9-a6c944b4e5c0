# Fáze 2: Vize v9 - Týmová Struktura a Role Agentů

**Navigace (Vize v9):**
*   [00 Úvod](00_uvod.md)
*   [01 Základní filozofie](01_zakladni_filozofie.md)
*   [02 Interakce s uživatelem](02_interakce_s_uzivatelem.md)
*   [03 Klíčové schopnosti](03_klicove_schopnosti.md)
*   [04 Cílová architektura](04_cilova_architektura.md)
*   **[05 Týmová struktura](05_tymova_struktura.md)** (Tento soubor)
*   [06 Stálé týmy](06_stale_tymy.md) *(Plán<PERSON>)*
*   ... *(Další soubory budou doplněny)*

---

Tento dokument popisuje vizi fungování dynamicky vytvářených týmů AI agentů v systému GENT v9, vč<PERSON><PERSON><PERSON> jejich struktury a rol<PERSON> jed<PERSON>v<PERSON>ch členů.

## <PERSON><PERSON><PERSON><PERSON> Týmů:

*   Pro každou uživatelem schvá<PERSON> "myšlenku" GENT (prostřednictvím `Agent Factory`) vytváří **dedikovaný tým agentů**.
*   Cílem je sestavit optimální skupinu expertů pro efektivní realizaci daného úkolu.

## Role v Týmu:

Každý dynamicky vytvořený tým má definovanou strukturu a role:

1.  **Manažer Týmu (Team Manager):**
    *   Vytvořen a nakonfigurován GENTem (nebo `Agent Factory`) specificky pro danou myšlenku.
    *   Obdrží od GENTa (nebo Orchestratoru) vysokoúrovňový cíl a kontext.
    *   **Zodpovědnosti:**
        *   Dekompozice hlavního cíle na menší, konkrétní úkoly.
        *   Výběr a sestavení týmu agentů (standardních a specializovaných) ve spolupráci s `Agent Factory`.
        *   Přiřazování úkolů jednotlivým agentům.
        *   Koordinace práce agentů v týmu.
        *   Sledování postupu a řešení problémů v rámci týmu.
        *   Agregace výsledků od agentů.
        *   Komunikace s GENTem (nebo Orchestratorem) ohledně postupu a výsledků.
        *   Potenciálně komunikace se Supervizorem.

2.  **Standardní Agenti (přítomni ve většině týmů):**
    *   **Agent Dokumentace:** Zodpovídá za průběžné vytváření a údržbu technické i uživatelské dokumentace související s realizovanou myšlenkou.
    *   **Agent Logování:** Zaznamenává důležité události, rozhodnutí a akce prováděné týmem pro účely sledování, auditu a analýzy.
    *   **Agent Správy Chyb:** Monitoruje a zaznamenává chyby vzniklé během práce týmu, případně iniciuje proces jejich řešení.
    *   **Agent Komunikace (s GENTem/Orchestratorem):** Zajišťuje standardizovanou komunikaci mezi manažerem týmu a centrálními komponentami GENTa.

3.  **Specializovaní Agenti:**
    *   Vybíráni manažerem týmu (nebo GENTem) na základě specifických potřeb dané myšlenky.
    *   Každý má expertízu v konkrétní oblasti.
    *   **Příklady:**
        *   *Vývojářský Agent:* Píše, upravuje, refaktoruje kód.
        *   *Testovací Agent:* Vytváří a spouští testy, reportuje chyby.
        *   *Analytický Agent:* Analyzuje data, požadavky, problémy.
        *   *Výzkumný Agent:* Vyhledává informace na webu nebo v jiných zdrojích pomocí MCP.
        *   *Databázový Agent:* Navrhuje schéma, píše dotazy, spravuje data.
        *   *UI/UX Agent:* Navrhuje uživatelská rozhraní.
        *   *Kreativní Agent:* Generuje texty, nápady, návrhy.
        *   *Bezpečnostní Agent:* Provádí bezpečnostní analýzu, navrhuje opatření.
        *   *Deployment Agent:* Připravuje a provádí nasazení.
        *   ***Critique Agent (Agent Kritik):*** Specializuje se na hodnocení kvality, konzistence a správnosti výstupů jiných agentů, poskytuje konstruktivní kritiku.
    *   Konkrétní sada specializovaných agentů se liší tým od týmu.

4.  **Supervizor (volitelná role dohledu a hodnocení):**
    *   Může být přiřazen GENTem k dohledu nad prací týmu nebo specifickým aspektem projektu (např. kvalita, bezpečnost), zejména u komplexních nebo kritických úkolů.
    *   **Zodpovědnosti (obecné):**
        *   Monitorování kvality práce týmu a dodržování standardů.
        *   Poskytování zpětné vazby manažerovi a agentům (využívá `Evaluation Modules` a `Feedback Modules`).
        *   Eskalace problémů GENTovi nebo jiným relevantním entitám.
        *   Hodnocení finálních výsledků podle definovaných kritérií.
    *   **Možné Specializace (inspirováno v6):** V budoucnu může být role supervizora dále specializována (např. Kvalitativní, Etický, Bezpečnostní, Výkonnostní), přičemž každý typ by se zaměřoval na specifické aspekty hodnocení a dohledu. V základní vizi v9 je však chápán jako obecnější role dohledu a hodnocení kvality.

## Interakce a Komunikace:

*   Komunikace v rámci týmu a mezi týmem a ostatními komponentami probíhá přes centrální **Komunikační Systém**.
*   Manažer týmu je hlavním koordinátorem a komunikačním bodem týmu.
*   Agenti spolupracují na úkolech definovaných manažerem, využívají LLM, MCP nástroje a přístup k databázi podle potřeby.

Tato struktura umožňuje flexibilní a škálovatelné řešení úkolů, kde jsou pro každý problém sestaveny týmy s potřebnou expertízou, zatímco standardní role zajišťují konzistenci v dokumentaci, logování a komunikaci.

---
**Další kapitola:** [06 Stálé týmy](06_stale_tymy.md) *(Plánováno)*
