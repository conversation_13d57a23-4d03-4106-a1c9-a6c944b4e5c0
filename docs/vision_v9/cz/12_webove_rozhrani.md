# Fáze 2: Vize v9 - <PERSON><PERSON><PERSON> (UI)

**Navigace (Vize v9):**
*   [00 Úvod](00_uvod.md)
*   ...
*   [10 Konfigurace](10_konfigurace.md)
*   [11 Budoucí vývoj](11_budouci_vyvoj.md)
*   **[12 Webové rozhraní](12_webove_rozhrani.md)** (Tento soubor)
*   [13 Testování](13_testovani.md) *(Plánováno)*
*   ... *(<PERSON><PERSON><PERSON> soubory budou doplněny)*

---

Tento dokument popisuje vizi pro webové uživatelské rozhraní (UI) systému GENT v9, které slouží jako hlavní bod interakce pro uživatele.

## Cíle a Principy UI:

*   **Intuitivnost:** Rozhraní musí být snadno pochopitelné a použitelné i pro méně technicky zdatné uživatele.
*   **Přehlednost:** Poskytovat jasný přehled o stavu systému, probíhajících úkolech a výsledcích.
*   **Interaktivita:** Umožnit plynulý dialog s GENTem a efektivní zadávání myšlenek.
*   **Responzivita:** UI by mělo být použitelné na různých velikostech obrazovek (desktop).
*   **Konzistence:** Dodržovat jednotný vizuální styl a principy ovládání napříč celým rozhraním.
*   **Výkon:** Rozhraní by mělo být rychlé a responzivní.

## Klíčové Funkce UI:

1.  **Chatovací Rozhraní:**
    *   Hlavní prostor pro dialog s GENTem (fáze PLAN).
    *   Podpora formátovaného textu, vkládání kódu, případně i obrázků (pro multimodální vstup).
    *   Zobrazení historie konverzace.
2.  **Správa Myšlenek/Projektů:**
    *   Přehled zadaných myšlenek a jejich stavu (plánování, realizace, dokončeno).
    *   Možnost vytvářet, upravovat, schvalovat nebo rušit myšlenky.
    *   Prohlížení detailů realizovaných projektů a jejich artefaktů (kód, dokumentace).
3.  **Monitorování Postupu:**
    *   Vizualizace postupu práce týmů agentů na realizaci schválené myšlenky (fáze ACT).
    *   Zobrazení aktuálního stavu úkolů, případných problémů nebo zpráv od systému.
4.  **Zobrazení Výsledků:**
    *   Přehledné zobrazení finálních výstupů a artefaktů vytvořených GENTem (kód, dokumenty, reporty).
5.  **Konfigurace:**
    *   Rozhraní pro správu uživatelského profilu.
    *   Možnost konfigurace klíčových parametrů (např. preference LLM, API klíče - bezpečně).
6.  **Zobrazení Logů (Budoucí Vylepšení):**
    *   Možnost prohlížet systémové logy nebo logy konkrétních týmů pro účely ladění a transparentnosti.
7.  **Zobrazení Testů (Budoucí Vylepšení):**
    *   Zobrazení výsledků automatizovaných testů spojených s generovaným kódem.
    *   Potenciálně možnost spouštět vybrané testy přímo z UI.

## Technologický Stack (Předpoklad):

*   **Frontend Framework:** **Vue.js 3** (jak naznačuje starší dokumentace).
*   **Správa Stavu:** **Vuex**.
*   **Routing:** **Vue Router**.
*   **Build Nástroj:** **Vite**.
*   **API Komunikace:** **Axios** (nebo ekvivalent).
*   **Styling:** CSS s důrazem na modularitu (např. pomocí CSS Modules, Scoped CSS nebo utility-first frameworku jako Tailwind CSS - k diskuzi).

## Vizuální Styl:

*   **Tmavý Režim (Dark Mode):** Celé UI bude **výhradně v tmavém provedení**.
*   **Čistý a Moderní Design:** Důraz na přehlednost, čitelnost a konzistenci.
*   **Žádné Inline Styly:** Striktní oddělení CSS od HTML struktury komponent.
*   **Modulární CSS:** Rozdělení stylů na globální, layoutové, komponentové a utility třídy pro lepší údržbu a znovupoužitelnost.
*   **Sdílené Komponenty:** Komponenty jako menu, tlačítka, formulářové prvky budou mít centrálně definovaný styl a budou znovupoužitelné.

## Offline Podpora:

*   Jak naznačuje starší dokumentace, UI by mělo detekovat offline stav a informovat uživatele.
*   Požadavky na změnu dat by měly být ukládány lokálně (IndexedDB) a synchronizovány po obnovení připojení.

Tato vize definuje moderní, funkční a uživatelsky přívětivé rozhraní pro interakci se systémem GENT v9.

---
**Další kapitola:** [13 Testování](13_testovani.md) *(Plánováno)*
