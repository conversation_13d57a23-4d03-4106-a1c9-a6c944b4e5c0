# Fáze 2: Vize v9 - <PERSON><PERSON><PERSON><PERSON>

**Navigace (Vize v9):**
*   [00 Úvod](00_uvod.md)
*   ...
*   [06 St<PERSON>lé týmy](06_stale_tymy.md)
*   [07 Integrace LLM](07_integrace_llm.md)
*   **[08 Operační módy](08_operacni_mody.md)** (Tento soubor)
*   [09 MCP Nástroje](09_mcp_nastroje.md) *(Plánováno)*
*   ... *(<PERSON><PERSON><PERSON> soubory budou doplněny)*

---

Tento dokument popisuje vizi fungování různých operačních módů v systému GENT v9. Tyto módy umožňují GENTu přizpůsobit své chování, zaměření a používané nástroje specifické fázi práce nebo typu úkolu.

## Přehled Operačních Módů:

GENT v9 by m<PERSON><PERSON> disponovat minimálně následujícími operačními módy:

1.  **PLAN (Plánovací Mód):**
    *   **Účel:** Kolaborativní definování, analýza a plánování úkolů a myšlenek ve spolupráci s uživatelem.
    *   **Chování GENTa:**
        *   Aktivně diskutuje s uživatelem, klade otázky, analyzuje požadavky.
        *   Pomáhá strukturovat myšlenky, identifikovat cíle a kritéria úspěchu.
        *   Vytváří detailní, krok-za-krokem plány pro realizaci myšlenky.
        *   Může využívat agenty pro analýzu nebo výzkum k podpoře plánování.
        *   **NEPROVÁDÍ** implementaci ani exekuci kódu (kromě případných nástrojů pro analýzu a plánování).
    *   **Typické Nástroje:** Nástroje pro analýzu textu, generování diagramů, strukturování informací, případně vyhledávání (pro ověření konceptů).
    *   **Výstup:** Detailní, uživatelem schválený plán realizace.

2.  **ACT (Akční/Realizační Mód):**
    *   **Účel:** Autonomní realizace uživatelem schváleného plánu nebo myšlenky.
    *   **Chování GENTa:**
        *   Převezme schválený plán (nebo myšlenku, pokud je dostatečně konkrétní).
        *   Vytvoří manažera a sestaví potřebný tým agentů.
        *   Orchestruje práci týmu, deleguje úkoly.
        *   Agenti vykonávají úkoly (psaní kódu, testování, nasazení, atd.) s využitím LLM a MCP nástrojů.
        *   Monitoruje postup, řeší problémy (případně ve spolupráci se supervizorem).
        *   Produkuje konkrétní výstupy (kód, aplikace, dokumentace).
    *   **Typické Nástroje:** Nástroje pro práci se soubory, spouštění příkazů, Git, LLM pro generování kódu, MCP nástroje pro specifické úkoly.
    *   **Výstup:** Implementované řešení podle plánu.

3.  **RESEARCH (Výzkumný Mód):**
    *   **Účel:** Systematický sběr, analýza a syntéza informací na dané téma.
    *   **Chování GENTa:**
        *   Definuje výzkumné otázky (případně ve spolupráci s uživatelem).
        *   Využívá agenty pro výzkum k prohledávání webu, databází, dokumentů pomocí MCP nástrojů.
        *   Analyzuje a strukturuje nalezené informace.
        *   Syntetizuje zjištění do přehledné formy.
    *   **Typické Nástroje:** MCP nástroje pro vyhledávání (Brave, Tavily, Perplexity), nástroje pro analýzu a zpracování textu, LLM pro shrnutí a syntézu.
    *   **Výstup:** Výzkumná zpráva, analýza, přehled literatury, odpovědi na specifické otázky.

4.  **IMPROVE (Zlepšovací Mód):**
    *   **Účel:** Analýza a optimalizace fungování samotného systému GENT nebo jeho částí.
    *   **Chování GENTa:**
        *   Aktivuje Sebezdokonalovací tým (nebo jeho části).
        *   Analyzuje data z Monitorovacího týmu, logy, metriky výkonu, zpětnou vazbu.
        *   Identifikuje oblasti pro zlepšení (efektivita, kvalita, náklady).
        *   Navrhuje konkrétní změny (v konfiguraci, algoritmech, "mozku").
        *   Může (kontrolovaně) implementovat navržená zlepšení.
    *   **Typické Nástroje:** Nástroje pro analýzu dat, profilování výkonu, práce s konfiguračními soubory, LLM pro analýzu a návrh řešení.
    *   **Výstup:** Návrhy na zlepšení, implementovaná vylepšení, reporty o optimalizaci.

## Přepínání Módů:

*   **Iniciace Uživatelem:** Uživatel by měl mít možnost explicitně požádat GENTa o přepnutí do určitého módu (např. "Pojďme teď naplánovat...", "Proveď výzkum na téma...").
*   **Automatická Iniciace GENTem:** GENT může automaticky přecházet mezi módy na základě kontextu (např. po schválení plánu přejde z PLAN do ACT) nebo interní logiky (např. pravidelná aktivace módu IMPROVE).
*   **Jasná Indikace:** Aktuální operační mód by měl být uživateli vždy jasně indikován.

Tato sada operačních módů poskytuje GENTu v9 potřebnou flexibilitu pro zvládání různých fází práce a typů úkolů efektivním a kontextuálně vhodným způsobem.

---
**Další kapitola:** [09 MCP Nástroje](09_mcp_nastroje.md) *(Plánováno)*
