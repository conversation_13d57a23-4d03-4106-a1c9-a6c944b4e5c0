# GENT v9: Dokumentace Cílové Vize - Rozcestník

<PERSON><PERSON> (`docs/gent_v9/vision_v9/cz/`) obsahuje sadu dokumentů popisujících cílovou vizi pro systém GENT verze 9.

## Obsah:

1.  **[00 Úvod](00_uvod.md)**: Základní představení vize GENT v9 jako inteligentního partnera.
2.  **[01 Základní filozofie](01_zakladni_filozofie.md)**: <PERSON><PERSON><PERSON>, principy, hodnoty, etika a identita GENT v9.
3.  **[02 Interakce s uživatelem](02_interakce_s_uzivatelem.md)**: Ideální průběh dialogu a zadávání myšlenek.
4.  **[03 Klíčové schopnosti](03_klicove_schopnosti.md)**: <PERSON><PERSON> hlavní<PERSON> schopností (tý<PERSON>, n<PERSON><PERSON><PERSON>, m<PERSON><PERSON>, u<PERSON><PERSON><PERSON>, k<PERSON><PERSON><PERSON>, <PERSON>ce, LLM debata).
5.  **[04 Cílová architektura](04_cilova_architektura.md)**: Vysokoúrovňový návrh architektury, komponent a toků (včetně Event Busu, Project Manageru, Kubernetes).
6.  **[05 Týmová struktura](05_tymova_struktura.md)**: Ideální fungování dynamických týmů, role agentů (včetně Critique Agenta) a supervizorů.
7.  **[06 Stálé týmy](06_stale_tymy.md)**: Role a funkce Monitorovacího a Sebezdokonalovacího týmu.
8.  **[07 Integrace LLM](07_integrace_llm.md)**: Vize pro výběr, konfiguraci, využití LLM (včetně LiteLLM, Retry, Cache, Rate Limit) a samooptimalizaci.
9.  **[08 Operační módy](08_operacni_mody.md)**: Detailní popis módů PLAN, ACT, RESEARCH, IMPROVE.
10. **[09 MCP Nástroje](09_mcp_nastroje.md)**: Vize využití MCP pro přístup k externím nástrojům.
11. **[10 Konfigurace](10_konfigurace.md)**: Vize pro konfiguraci systému (včetně `.env`, `config/`, DB, správy závislostí a Dockeru).
12. **[11 Budoucí vývoj](11_budouci_vyvoj.md)**: Dlouhodobější vize a možnosti (optimalizace, učení, multimodalita, proaktivita, UI vylepšení, inspirace externími nástroji).
13. **[12 Webové rozhraní](12_webove_rozhrani.md)**: Vize pro UI (Vue.js, tmavý režim, modulární CSS, offline podpora, zobrazení testů/logů).
14. **[13 Testování](13_testovani.md)**: Vize pro strategii testování (Pytest, Locust, úrovně testů, CI/CD, pokrytí).
15. **[14 Nasazení a provoz](14_nasazeni_a_provoz.md)**: Vize pro nasazení (Docker Compose/Kubernetes), CI/CD (GitHub Actions), monitoring (Prometheus/Grafana), zálohování.
16. **[15 Datové struktury](15_datove_struktury.md)**: Detailní popis klíčových struktur (Thought, Task, Output, Feedback).
17. **[16 Index](16_index.md)**: Tento rozcestník.

---
Tato sada dokumentů slouží jako základ pro **Fázi 3: Plán Refaktoringu**, kde bude tato vize porovnána s analýzou současného stavu (`../current_state_analysis/cz/`) a budou navrženy konkrétní kroky pro úpravu kódu. Postup práce na této fázi je sledován v `../progress/cz/phase_2_vision_status.md`.
