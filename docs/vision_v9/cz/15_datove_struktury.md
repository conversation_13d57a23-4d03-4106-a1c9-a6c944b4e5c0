# Fáze 2: Vize v9 - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

**Navigace (Vize v9):**
*   [00 Úvod](00_uvod.md)
*   ...
*   [13 Testování](13_testovani.md)
*   [14 Nasazení a provoz](14_nasazeni_a_provoz.md)
*   **[15 Da<PERSON>é struktury](15_datove_struktury.md)** (Tento soubor)
*   [16 Index](16_index.md) *(Plánováno)*

---

Tento dokument popisuje vizi pro klíčové datové struktury používané v systému GENT v9 pro reprezentaci informací, úkolů a výsledků. Návrh vychází z konceptů identifikovaných ve starší dokumentaci. Pro implementaci se předpokládá použití Pydantic modelů pro validaci a serializaci/deserializaci (např. do/z JSON pro API a komunikaci).

## 1. <PERSON><PERSON><PERSON><PERSON> (Thought)

Základní jednotka informace proudící kognitivním systémem a mezi komponentami.

*   **`thought_id`** (UUID/str): Unikátní identifikátor myšlenky.
*   **`type`** (Enum/str): Typ myšlenky (např. `USER_INPUT`, `PERCEPTION`, `REASONING`, `PLANNING`, `ACTION_STEP`, `AGENT_RESULT`, `SYSTEM_STATUS`, `FEEDBACK_ANALYSIS`).
*   **`content`** (dict/object): Specifická data pro daný typ myšlenky. Struktura se liší podle typu.
*   **`metadata`** (dict): Dodatečné informace (např. `confidence_score`, `processing_time`, `llm_used`, `cost`).
*   **`source_id`** (str): Identifikátor komponenty nebo agenta, který myšlenku vytvořil.
*   **`parent_id`** (UUID/str, volitelné): ID myšlenky, ze které tato vznikla (pro sledování kauzality).
*   **`children_ids`** (List[UUID/str]): ID myšlenek, které vznikly z této.
*   **`timestamp`** (datetime/float): Čas vytvoření myšlenky.
*   **`embedding`** (List[float], volitelné): Vektorová reprezentace obsahu pro sémantické vyhledávání (uloženo v pgVector).

## 2. Úkol (Task)

Jednotka práce přidělená agentovi nebo týmu.

*   **`task_id`** (UUID/str): Unikátní identifikátor úkolu.
*   **`parent_task_id`** (UUID/str, volitelné): ID nadřazeného úkolu (pokud jde o podúkol).
*   **`workflow_id`** (UUID/str, volitelné): ID workflow, ke kterému úkol patří.
*   **`type`** (Enum/str): Typ úkolu (např. `CODE_GENERATION`, `RESEARCH`, `ANALYSIS`, `TESTING`, `DOCUMENTATION`).
*   **`description`** (str): Slovní popis cíle úkolu.
*   **`input_data`** (dict/object): Vstupní data potřebná pro splnění úkolu.
*   **`context`** (dict): Kontextové informace (např. `project_id`, `relevant_thought_ids`, `constraints`).
*   **`priority`** (Enum/int): Priorita úkolu (např. `LOW`, `NORMAL`, `HIGH`, `CRITICAL`).
*   **`status`** (Enum/str): Aktuální stav úkolu (`PENDING`, `ASSIGNED`, `IN_PROGRESS`, `COMPLETED`, `FAILED`, `CANCELLED`).
*   **`assigned_to_id`** (str, volitelné): ID agenta nebo týmu, kterému je úkol přidělen.
*   **`dependencies`** (List[UUID/str]): Seznam ID úkolů, které musí být dokončeny před zahájením tohoto úkolu.
*   **`result`** (dict/object, volitelné): Výsledek úkolu po jeho dokončení.
*   **`error_message`** (str, volitelné): Chybová zpráva v případě selhání.
*   **`created_at`** (datetime): Čas vytvoření úkolu.
*   **`updated_at`** (datetime): Čas poslední aktualizace stavu.
*   **`completed_at`** (datetime, volitelné): Čas dokončení úkolu.

## 3. Výstup Agenta (AgentOutput)

Konkrétní artefakt nebo výsledek práce agenta v rámci úkolu.

*   **`output_id`** (UUID/str): Unikátní identifikátor výstupu.
*   **`task_id`** (UUID/str): ID úkolu, ke kterému výstup patří.
*   **`agent_id`** (str): ID agenta, který výstup vytvořil.
*   **`type`** (Enum/str): Typ výstupu (např. `CODE_SNIPPET`, `FILE_CONTENT`, `REPORT`, `ANALYSIS_SUMMARY`, `TEST_RESULTS`).
*   **`content`** (dict/object/str): Samotný obsah výstupu (může být text, JSON, odkaz na soubor atd.).
*   **`metadata`** (dict): Dodatečné informace (např. `language` pro kód, `format` pro report, `confidence`).
*   **`version`** (int): Verze výstupu (pokud agent produkuje více verzí).
*   **`timestamp`** (datetime): Čas vytvoření výstupu.

## 4. Zpětná Vazba (Feedback)

Hodnocení nebo komentář k výstupu agenta nebo průběhu úkolu.

*   **`feedback_id`** (UUID/str): Unikátní identifikátor zpětné vazby.
*   **`target_id`** (UUID/str): ID cílového objektu (např. `output_id`, `task_id`).
*   **`target_type`** (Enum/str): Typ cílového objektu (`OUTPUT`, `TASK`).
*   **`source_id`** (str): ID entity poskytující zpětnou vazbu (uživatel, supervizor, jiný agent).
*   **`source_type`** (Enum/str): Typ entity (`USER`, `SUPERVISOR`, `AGENT`).
*   **`type`** (Enum/str): Typ zpětné vazby (např. `QUALITY_ASSESSMENT`, `BUG_REPORT`, `SUGGESTION`, `USER_RATING`).
*   **`content`** (dict/str): Textový popis zpětné vazby, identifikované problémy, návrhy na zlepšení.
*   **`rating`** (float/int, volitelné): Kvantitativní hodnocení (např. skóre 0-1, počet hvězdiček).
*   **`metadata`** (dict): Dodatečné informace (např. `severity` pro chybu).
*   **`timestamp`** (datetime): Čas poskytnutí zpětné vazby.

## Další Potenciální Struktury:

*   **`AgentConfig`**: Konfigurace specifická pro agenta.
*   **`Team`**: Reprezentace týmu agentů (ID, manažer, členové, cíl).
*   **`Project`**: Metadata o projektu spravovaném GENTem.
*   **`KnowledgeItem`**: Jednotka znalosti v znalostní bázi.
*   **`Event`**: Struktura pro události v Event Busu.

Tyto struktury poskytují základ pro ukládání dat v databázi (pomocí ORM modelů v `db/models/`) a pro komunikaci mezi komponentami (pomocí serializace/deserializace a validačních schémat v `db/schemas/` nebo `core/communication/`).

---
**Další kapitola:** [16 Index](16_index.md) *(Plánováno)* - Rozcestník pro dokumentaci vize v9.
