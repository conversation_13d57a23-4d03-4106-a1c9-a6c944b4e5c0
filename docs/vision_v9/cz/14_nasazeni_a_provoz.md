# Fáze 2: Vize v9 - Na<PERSON><PERSON><PERSON> a Provoz

**Navigace (Vize v9):**
*   [00 Úvod](00_uvod.md)
*   ...
*   [12 Webové rozhraní](12_webove_rozhrani.md)
*   [13 Testování](13_testovani.md)
*   **[14 Nasazení a provoz](14_nasazeni_a_provoz.md)** (Tento soubor)
*   [15 Da<PERSON><PERSON> struktury](15_datove_struktury.md) *(Plánováno)*
*   ... *(<PERSON>š<PERSON> soubory budou doplněny)*

---

Tento dokument popisuje vizi pro nasazení, správu a provoz systému GENT v9, včetně aspektů jako CI/CD, monitoring a zálohování. Cílem je zajistit spolehlivý, šk<PERSON>lovatelný a snadno spravovatelný provoz systému.

## Strategie Nasazení:

1.  **Vývojové a Jednoduché <PERSON> (Nativní <PERSON>):**
    *   Pro lokální vývoj, testování a jednodušší nasazení jsou komponenty systému provozovány jako **nativní služby** na hostiteli.
    *   **GENT Core** běží ve virtuálním prostředí Python (`venv`).
    *   **Databáze PostgreSQL** je nainstalována a nakonfigurována jako nativní služba na hostiteli.
    *   **Frontend (Web UI)** je spouštěn pomocí skriptu `run_frontend.sh` jako Node.js aplikace.
    *   **API** běží jako Python služba přímo z virtuálního prostředí.
    *   Konfigurace je spravována pomocí souborů v adresáři `config/` a případně `.env` souboru.
    *   **MCP servery** mohou volitelně běžet jako Docker kontejnery podle potřeby.

2.  **Pokročilé a Produkční Nasazení:**
    *   Pro produkční prostředí a scénáře vyžadující vysokou dostupnost, škálovatelnost a pokročilou správu lze jednotlivé komponenty provozovat jako samostatné služby.
    *   Mohou být nasazeny jako systémové služby s automatickým startem a monitoringem.
    *   Pro pokročilé nasazení lze zvážit kontejnerizaci pomocí Dockeru nebo orchestraci pomocí Kubernetes pouze pro vybrané komponenty.
    *   Tento přístup nabízí flexibilitu kombinovat nativní služby s kontejnery podle potřeby.

## Kontinuální Integrace a Nasazení (CI/CD):

*   **Platforma:** **GitHub Actions** (jak naznačuje starší dokumentace a existence `.github/` adresáře).
*   **Workflow:**
    *   **CI (Kontinuální Integrace):** Při každém pushi nebo pull requestu do hlavních větví se automaticky spustí:
        *   Linting a formátování kódu.
        *   Jednotkové (Unit) testy.
        *   Integrační (Integration) testy.
        *   Analýza pokrytí kódu (Code Coverage).
    *   **CD (Kontinuální Nasazení/Doručování):** Při úspěšném sloučení do hlavní větve (nebo při vytvoření tagu/release) se automaticky spustí:
        *   Sestavení balíčků pro nasazení.
        *   Nasazení nové verze do testovacího/staging/produkčního prostředí pomocí automatizovaných skriptů.
        *   Automatické spuštění nebo restart potřebných služeb.

## Monitoring a Logování:

*   **Monitoring Metrik:**
    *   Využití **Prometheus** pro sběr metrik z jednotlivých komponent systému (využití CPU/RAM, latence API, počet zpracovaných úkolů, stav front atd.). Aplikace a služby budou vystavovat metriky ve formátu kompatibilním s Prometheus.
    *   Využití **Grafana** pro vizualizaci metrik a vytváření dashboardů pro sledování stavu a výkonu systému.
*   **Logování:**
    *   Všechny komponenty budou generovat strukturované logy (např. JSON).
    *   Logy budou centralizovány – buď pomocí systémového logování (např. syslog, journald), nebo pomocí dedikovaného logovacího stacku (např. EFK - Elasticsearch, Fluentd, Kibana nebo PLG - Promtail, Loki, Grafana).
    *   Cílem je snadné prohledávání, filtrování a analýza logů pro diagnostiku problémů.
*   **Chybové Hlášení:** Integrace s nástrojem jako **Sentry** (jak naznačuje starší dokumentace) pro automatický sběr a reporting chyb a výjimek z běžící aplikace.
*   **Alerting:** Nastavení alertů v Prometheus/Grafana nebo Sentry pro upozornění na kritické problémy (vysoká chybovost, nedostupnost služby, nízký výkon).

## Zálohování a Obnova:

*   **Databáze (PostgreSQL):**
    *   Nastavení **pravidelných automatických záloh** databáze (např. pomocí `pg_dump` a cron jobu běžícího na hostiteli).
    *   Zálohy budou ukládány na bezpečné externí úložiště (např. S3, GCS - viz konfigurace souborového systému).
    *   Definování a otestování **procesu obnovy** databáze ze zálohy.
*   **Konfigurační Soubory:** Konfigurační soubory (`config/`, `.env`) by měly být verzovány (kromě citlivých údajů v `.env`, které se spravují odděleně) nebo zálohovány jiným způsobem.
*   **Datové soubory:** Všechna perzistentní data (databázové soubory, uživatelská data, atd.) musí být zahrnuta do strategie zálohování hostitelského systému.

Tato vize pro nasazení a provoz klade důraz na automatizaci (CI/CD), monitorovatelnost, spolehlivost a bezpečnost, což jsou klíčové aspekty pro dlouhodobě udržitelný provoz komplexního systému jako GENT v9.

---
**Další kapitola:** [15 Datové struktury](15_datove_struktury.md) *(Plánováno)*
