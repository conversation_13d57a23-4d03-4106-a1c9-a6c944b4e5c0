# Fáze 2: Vize v9 - <PERSON><PERSON><PERSON> Týmy (Monitoring a Sebezdokonalování)

**Navigace (Vize v9):**
*   [00 Úvod](00_uvod.md)
*   [01 Základní filozofie](01_zakladni_filozofie.md)
*   [02 Interakce s uživatelem](02_interakce_s_uzivatelem.md)
*   [03 Klíč<PERSON><PERSON> schopnosti](03_klicove_schopnosti.md)
*   [04 Cílová architektura](04_cilova_architektura.md)
*   [05 Týmová struktura](05_tymova_struktura.md)
*   **[06 St<PERSON>lé týmy](06_stale_tymy.md)** (Tento soubor)
*   [07 Integrace LLM](07_integrace_llm.md) *(Plánováno)*
*   ... *(<PERSON><PERSON><PERSON> soubory budou doplněny)*

---

Tento dokument popisuje vizi fungování dvou klíčov<PERSON>ch **st<PERSON><PERSON><PERSON><PERSON> (permanentních) týmů** v systému GENT v9, k<PERSON><PERSON> zaji<PERSON>ují jeho schopnost učit se, adaptovat a optimalizovat své vlastní fungování. Na rozdíl od dynamických týmů vytvářených pro konkrétní uživatelské myšlenky, tyto týmy operují kontinuálně nebo jsou pravidelně aktivovány GENTem.

## 1. Monitorovací Tým (Monitoring Team)

*   **Účel:** Systematicky sledovat, zaznamenávat a analyzovat činnost všech ostatních týmů (dynamických i stálých) a celého systému GENT.
*   **Zodpovědnosti:**
    *   **Sběr Dat:** Automatizovaný sběr logů, metrik výkonu, výsledků úkolů, chybových hlášení, komunikačních záznamů a dalších relevantních dat z činnosti agentů a týmů.
    *   **Analýza Postupů:** Analýza úspěšných i neúspěšných pracovních postupů, identifikace vzorů, úzkých míst a osvědčených praktik.
    *   **Ukládání Znalostí:** Strukturované ukládání získaných poznatků a analyzovaných dat do centrální znalostní báze nebo databáze (propojení s `core/knowledge/` a `db/`).
    *   **Reportování:** Poskytování souhrnných reportů a analýz GENTovi (jeho mozku/kognitivním jednotkám) a Sebezdokonalovacímu týmu.
*   **Složení:** Pravděpodobně se skládá z agentů specializovaných na sběr dat, analýzu dat, zpracování přirozeného jazyka (pro analýzu logů a komunikace) a správu znalostí.
*   **Význam:** Tento tým je klíčový pro schopnost GENTa učit se ze zkušeností a získávat objektivní data o vlastním fungování.

## 2. Sebezdokonalovací Tým (Self-Improvement Team)

*   **Účel:** Aktivně analyzovat fungování systému GENT a navrhovat (případně i implementovat) konkrétní vylepšení jeho architektury, algoritmů, procesů nebo "mozku".
*   **Zodpovědnosti:**
    *   **Analýza Systému:** Hloubková analýza komponent GENTa, jeho výkonu, efektivity a chování na základě dat z Monitorovacího týmu a případně i přímé introspekce (`core/introspection/`, `core/metacognition/`).
    *   **Identifikace Slabin a Příležitostí:** Hledání oblastí pro optimalizaci, opravu chyb, zlepšení algoritmů nebo rozšíření schopností.
    *   **Návrh Změn:** Formulace konkrétních návrhů na úpravy kódu, konfigurace "mozku" (`config/brain/gent_brain.md`), trénovacích dat pro LLM, nebo architektury systému.
    *   **Testování Návrhů:** Simulace nebo testování navrhovaných změn (pokud je to možné).
    *   **Implementace (volitelně/kontrolovaně):** V závislosti na úrovni autonomie může tento tým buď pouze navrhovat změny GENTovi (nebo lidskému operátorovi) ke schválení, nebo mít omezenou schopnost některé změny přímo implementovat (např. úpravy konfigurace, méně kritické části kódu).
*   **Složení:** Pravděpodobně se skládá z agentů specializovaných na systémovou analýzu, softwarové inženýrství, AI/ML (pro analýzu modelů a algoritmů), plánování a možná i testování.
*   **Význam:** Tento tým zajišťuje proaktivní evoluci a adaptaci GENTa, čímž naplňuje vizi systému, který se sám zlepšuje.

## Interakce Stálých Týmů:

*   Monitorovací tým poskytuje data a analýzy Sebezdokonalovacímu týmu a GENT Brain.
*   Sebezdokonalovací tým analyzuje data a systém, navrhuje změny GENT Brain.
*   GENT Brain (nebo jeho relevantní kognitivní jednotky) zpracovává vstupy od obou týmů a může iniciovat změny v systému nebo upravit své vlastní chování.
*   Oba týmy využívají standardní infrastrukturu GENTa (komunikace, LLM, MCP, DB).

Existence těchto stálých týmů povyšuje GENTa z pouhého vykonavatele úkolů na systém schopný reflexe, učení a autonomního vývoje.

---
**Další kapitola:** [07 Integrace LLM](07_integrace_llm.md) *(Plánováno)*
