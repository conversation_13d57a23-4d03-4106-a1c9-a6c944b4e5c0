# Fáze 2: Vize v9 - K<PERSON><PERSON><PERSON><PERSON><PERSON>

**Navigace (Vize v9):**
*   [00 Úvod](00_uvod.md)
*   [01 Základní filozofie](01_zakladni_filozofie.md)
*   [02 Interakce s uživatelem](02_interakce_s_uzivatelem.md)
*   **[03 Klíčové schopnosti](03_klicove_schopnosti.md)** (Tento soubor)
*   [04 Cílová architektura](04_cilova_architektura.md) *(Plánováno)*
*   ... *(<PERSON>š<PERSON> soubory budou doplněny)*

---

Tento dokument popisuje klíčové schopnosti, které definují systém GENT v9 a umožňují mu plnit jeho účel jako inteligentního partnera pro realizaci myšlenek.

## Přehled Klíčových Schopností:

1.  **Dynamické Sestavování Tý<PERSON>ů:**
    *   GENT dokáže pro každou schválenou myšlenku dynamicky vytvořit a nakonfigurovat **specializovaný tým AI agentů**.
    *   Každý tým je veden **manažerem** (vytvořeným GENTem), který je zodpovědný za dekompozici úkolu a koordinaci práce.
    *   Týmy typicky zahrnují **standardní role** (dokumentace, logování, komunikace, správa chyb) a **specifické role** potřebné pro daný úkol (vývojář, analytik, výzkumník, tester atd.).
    *   **Supervizoři** mohou dohlížet na práci týmů nebo jednotlivých agentů, zajišťovat kvalitu a koordinaci.

2.  **Využití Specializovaných Agentů:**
    *   GENT disponuje (nebo dokáže vytvořit) širokou škálou **specializovaných agentů**, z nichž každý má expertízu v konkrétní oblasti.
    *   Tito agenti jsou základními stavebními kameny pro řešení komplexních úkolů. Příklady zahrnují (ale nejsou omezeny na):
        *   *Vývojářský Agent:* Píše, upravuje, refaktoruje kód.
        *   *Testovací Agent:* Vytváří a spouští různé typy testů (unit, integrační, **end-to-end (E2E)**), reportuje chyby.
        *   *Analytický Agent:* Analyzuje data, požadavky, problémy.
        *   *Výzkumný Agent:* Vyhledává informace na webu nebo v jiných zdrojích pomocí MCP.
        *   *Databázový Agent:* Navrhuje schéma, píše dotazy, spravuje data.
        *   *UI/UX Agent:* Navrhuje uživatelská rozhraní.
        *   *Kreativní Agent:* Generuje texty, nápady, návrhy.
        *   *Bezpečnostní Agent:* Provádí bezpečnostní analýzu, navrhuje opatření.
        *   *Deployment Agent:* Připravuje a provádí nasazení.
        *   ***Reasoning Agent (Agent pro Uvažování):*** Specializuje se na komplexní logické uvažování, řešení problémů a odvozování závěrů.

3.  **Integrace Externích Nástrojů (MCP):**
    *   GENT a jeho agenti mohou využívat **externí nástroje a zdroje** prostřednictvím Model Context Protocol (MCP).
    *   To zahrnuje schopnosti jako vyhledávání na webu, práce se souborovým systémem, interakce s Git repozitáři, využívání specifických API atd.
    *   MCP umožňuje GENTu efektivně interagovat s reálným světem a získávat potřebné informace nebo provádět akce mimo vlastní systém.

4.  **Flexibilní Operační Módy:**
    *   GENT může operovat v různých **módech** (minimálně PLAN, ACT, RESEARCH, IMPROVE), které přizpůsobují jeho chování a zaměření aktuální potřebě.
    *   **PLAN:** Kolaborativní definování a plánování úkolů bez implementace.
    *   **ACT:** Autonomní exekuce a implementace schválených plánů.
    *   **RESEARCH:** Systematický sběr a analýza informací.
    *   **IMPROVE:** Analýza a optimalizace vlastního systému nebo procesů.
    *   Přepínání mezi módy může být řízeno uživatelem nebo interní logikou GENTa.

5.  **Učení a Adaptace:**
    *   GENT je navržen jako **učící se systém**.
    *   Využívá **zpětnou vazbu** (od uživatele, supervizorů, interní metriky) k postupnému zlepšování.
    *   **Stálý monitorovací tým** sbírá data o průběhu úkolů a efektivitě agentů, což slouží jako vstup pro učení.
    *   **Stálý sebezdokonalovací tým** aktivně analyzuje fungování GENTa a navrhuje (případně implementuje) vylepšení jeho "mozku", algoritmů nebo procesů.
    *   Cílem je adaptivní chování a neustálé zvyšování efektivity a kvality.

6.  **Pokročilá Kognitivní Architektura:**
    *   Jádro GENTa (`core/brain/`) obsahuje **kognitivní jednotky** (percepce, uvažování, plánování, exekuce, reflexe, učení, komunikace), které umožňují komplexní zpracování informací a řízení chování.
    *   Systém disponuje mechanismy pro **introspekci** (sebereflexi) a **metakognici** (přemýšlení o vlastním myšlení).
    *   Správa **znalostí** a **paměti** umožňuje GENTu uchovávat a využívat informace v průběhu času.

7.  **Orchestrace Komplexních Workflow:**
    *   GENT dokáže řídit (orchestrace) komplexní pracovní postupy zahrnující více kroků, závislostí a agentů.
    *   Rozkládá vysokoúrovňové cíle na konkrétní úkoly a spravuje jejich vykonávání.

8.  **Kolaborativní Řešení Problémů (LLM Debata):**
    *   GENT může iniciovat specializovaný proces, kde **několik LLM (nebo agentů s různými LLM/perspektivami)** "diskutuje" o daném problému nebo tématu.
    *   Cílem je prozkoumat problém z více úhlů pohledu a dospět k robustnějšímu, konsensuálnímu nebo kreativnějšímu řešení, než by poskytl jediný model/agent.
    *   Tato schopnost může být využita GENTem interně nebo jako specifický typ týmu sestavený pro komplexní rozhodování či brainstorming.

Tyto schopnosti společně tvoří základ pro GENT v9 jakožto výkonný a flexibilní systém pro realizaci myšlenek.

---
**Další kapitola:** [04 Cílová architektura](04_cilova_architektura.md) *(Plánováno)*
