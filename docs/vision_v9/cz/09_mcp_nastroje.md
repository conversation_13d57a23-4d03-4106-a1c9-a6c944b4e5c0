# Fáze 2: Vize v9 - Využití MCP Nástrojů

**Navigace (Vize v9):**
*   [00 Úvod](00_uvod.md)
*   ...
*   [07 Integrace LLM](07_integrace_llm.md)
*   [08 Opera<PERSON>n<PERSON> módy](08_operacni_mody.md)
*   **[09 MCP Nástroje](09_mcp_nastroje.md)** (Tento soubor)
*   [10 Konfigurace](10_konfigurace.md) *(Plánováno)*
*   ... *(<PERSON><PERSON><PERSON> soubory budou doplněny)*

---

Tento dokument popisuje vizi, jak systém GENT v9 využívá Model Context Protocol (MCP) pro přístup k externím nástrojům a zdrojům, č<PERSON><PERSON>ž rozšiřuje své schopnosti a umožňuje interakci s reálným světem.

## Role MCP v GENT v9:

MCP je klíčovou technologií, kter<PERSON> umožňuje GENTu a jeho agentům **bez<PERSON><PERSON><PERSON><PERSON> a standardizovaně** využívat externí funkce, an<PERSON><PERSON> by muse<PERSON> b<PERSON>t tyto funkce přímo integrovány do jádra GENTa. To podporuje modularitu a rozšiřitelnost.

## Klíčové Aspekty Využití MCP:

1.  **Přístup k Nástrojům pro Agenty:**
    *   Specializovaní agenti (a případně i manažeři/supervizoři) jsou hlavními konzumenty MCP nástrojů.
    *   Každý agent by měl mít definovaný **seznam MCP nástrojů**, které má k dispozici a které jsou relevantní pro jeho roli (např. výzkumný agent má přístup k web search nástrojům, vývojářský agent k nástrojům pro práci se soubory a Gitem).
    *   Tento seznam může být definován v konfiguraci agenta nebo dynamicky přidělen manažerem týmu či GENTem.

2.  **Centrální MCP Integrace (`MCP_Int`):**
    *   Komponenta `MCP Integrace` v jádře GENTa slouží jako **brána** pro všechna volání MCP nástrojů.
    *   Zpracovává požadavky od agentů (nebo jiných komponent).
    *   Komunikuje s příslušnými MCP servery (ať už externími nebo interními).
    *   Zpracovává odpovědi a předává je zpět volající komponentě.
    *   Může implementovat logiku pro caching výsledků, správu oprávnění nebo monitorování využití nástrojů.

3.  **Správa a Konfigurace MCP Serverů:**
    *   Systém musí mít přehled o dostupných MCP serverech a způsobu jejich spuštění/připojení.
    *   Konfigurace by měla umožnit snadné přidávání nebo odebírání MCP serverů.
    *   Ideálně by konfigurace měla být spravována centrálně (např. v `config/` nebo přes proměnné prostředí) a načítána komponentou `Config Manager`.
    *   Spouštění potřebných MCP serverů může být zajištěno pomocí `docker-compose.yml` nebo spouštěcích skriptů (`run_servers.sh`).

4.  **Bezpečnost a Oprávnění:**
    *   Přístup k MCP nástrojům by měl být řízen. Ne každý agent by měl mít přístup ke všem nástrojům.
    *   `MCP Integrace` nebo `Agent Context` by mohly obsahovat logiku pro ověření, zda má daný agent oprávnění použít požadovaný nástroj.
    *   Zvláštní pozornost je třeba věnovat nástrojům, které mohou provádět změny v systému nebo interagovat s citlivými daty (např. `write_to_file`, `execute_command`).

5.  **Interní vs. Externí MCP Servery:**
    *   GENT v9 může využívat jak **standardní externí MCP servery** (filesystem, git, search, fetch atd.), tak **vlastní interní MCP servery** (jak naznačuje adresář `mcp/` ve stávající struktuře) pro specifické funkce (např. pokročilá správa paměti/znalostí, specifické Git operace).

## Příklady Využití:

*   **Výzkumný Agent:** Používá `tavily-search` nebo `brave_web_search` pro hledání informací na webu.
*   **Vývojářský Agent:** Používá `read_file`, `write_to_file`, `replace_in_file` pro práci s kódem a `git_add`, `git_commit` pro správu verzí.
*   **Analytický Agent:** Používá `read_file` pro čtení logů nebo datových souborů, případně `read_query` (pokud existuje DB MCP server) pro dotazy do databáze.
*   **Manažer Týmu:** Může použít `list_files` pro přehled o vytvořených artefaktech.

Efektivní a bezpečná integrace MCP nástrojů je zásadní pro schopnost GENT v9 plnit širokou škálu úkolů a interagovat s okolním prostředím.

---
**Další kapitola:** [10 Konfigurace](10_konfigurace.md) *(Plánováno)*
