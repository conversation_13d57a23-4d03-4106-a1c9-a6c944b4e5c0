# 📚 GENT LLM MODELS - Kompletní dokumentace

> **Datum:** 2025-05-29  
> **Status:** FINÁLNÍ - VŠECHNY MODELY FUNKČNÍ  
> **Celkem modelů:** 14  
> **Verze:** PRODUCTION READY

---

## 🎯 PŘEHLED FUNKČNÍCH MODELŮ

### 📊 **CELKOVÝ STAV**
- **✅ Anthropic:** 2 modely (100% funkční)
- **✅ Google:** 3 modely (100% funkční)  
- **✅ OpenAI:** 9 modelů (100% funkční)
- **🎯 CELKEM:** 14 modelů - všechny ověřené a funkční

---

## 🔵 OPENAI MODELY (9 modelů)

### 🟢 **STANDARDNÍ MODELY** (3 modely)
Používají standardní OpenAI API volání s `temperature` a `max_tokens`.

| Model | API Název | Typ | Kontext | Max Tokens | Poznámka |
|-------|-----------|-----|---------|------------|----------|
| **gpt-4o** | `gpt-4o` | Multimodal | 128K | 4K | **[DEFAULT]** Nejnovější GPT-4 |
| **gpt-4o-mini** | `gpt-4o-mini` | Multimodal | 128K | 16K | Rychlý a levný |
| **gpt-4-turbo** | `gpt-4-turbo` | Text | 128K | 4K | Pokročilý model |

### ⚡ **SPECIÁLNÍ MODELY** (6 modelů)
Používají speciální API volání **BEZ** `temperature` a **S** `max_completion_tokens`.

| Model | API Název | Typ | Kontext | Max Tokens | Poznámka |
|-------|-----------|-----|---------|------------|----------|
| **gpt-4.1** | `gpt-4.1` | Advanced | 128K | 4K | **CASE SENSITIVE!** |
| **gpt-4.1-mini** | `gpt-4.1-mini` | Advanced | 128K | 16K | **CASE SENSITIVE!** |
| **gpt-4.1-nano** | `gpt-4.1-nano` | Advanced | 128K | 8K | **CASE SENSITIVE!** |
| **o1-preview** | `o1-preview` | Reasoning | 128K | 32K | Reasoning model |
| **o1-mini** | `o1-mini` | Reasoning | 128K | 65K | Rychlý reasoning |
| **o3-mini** | `o3-mini` | Reasoning | 128K | 65K | Next-gen reasoning |

---

## 🟢 GOOGLE MODELY (3 modely)

Všechny používají standardní Google API volání.

| Model | API Název | Typ | Kontext | Max Tokens | Poznámka |
|-------|-----------|-----|---------|------------|----------|
| **gemini-2.0-flash** | `gemini-2.0-flash` | Multimodal | 1M | 8K | Nejnovější Gemini |
| **gemini-2.0-flash-lite** | `gemini-2.0-flash-lite` | Multimodal | 1M | 8K | Rychlá verze |
| **gemini-2.5-flash-preview-05-20** | `gemini-2.5-flash-preview-05-20` | Multimodal | 2M | 8K | Preview verze |

---

## 🟣 ANTHROPIC MODELY (2 modely)

Všechny používají standardní Anthropic API volání.

| Model | API Název | Typ | Kontext | Max Tokens | Poznámka |
|-------|-----------|-----|---------|------------|----------|
| **claude-3-7-sonnet-latest** | `claude-3-7-sonnet-latest` | Text | 200K | 4K | **[DEFAULT]** Claude 3.5 |
| **claude-sonnet-4-20250514** | `claude-sonnet-4-20250514` | Text | 200K | 8K | Claude 4 Sonnet |

---

## ⚡ SPECIÁLNÍ API VOLÁNÍ

### 🔴 **POZOR: OpenAI Speciální modely**

Tyto modely vyžadují **odlišné API volání**:
- `gpt-4.1`, `gpt-4.1-mini`, `gpt-4.1-nano`
- `o1-preview`, `o1-mini`, `o3-mini`

**❌ NEPODPOROVANÉ parametry:**
```json
{
  "temperature": 0.7,        // ❌ NEPOUŽÍVAT!
  "top_p": 1.0,             // ❌ NEPOUŽÍVAT!
  "frequency_penalty": 0.0,  // ❌ NEPOUŽÍVAT!
  "presence_penalty": 0.0,   // ❌ NEPOUŽÍVAT!
  "stop": ["..."]           // ❌ NEPOUŽÍVAT!
}
```

**✅ SPRÁVNÉ volání:**
```json
{
  "model": "o1-preview",
  "messages": [{"role": "user", "content": "..."}],
  "max_completion_tokens": 1000  // ✅ MÍSTO max_tokens!
}
```

---

## 🔧 IMPLEMENTACE V GENT

### 📍 **Automatická detekce**
GENT automaticky rozpozná speciální modely v kódu:

```python
# gent/llm/openai_provider.py
is_special_model = model in [
    "o1-preview", "o1-mini", "o3-mini", 
    "gpt-4.1", "gpt-4.1-mini", "gpt-4.1-nano"
]
```

### 📍 **API endpointy**
```python
# gent/api/app/routes/llm_direct_db_routes.py
is_special_model = model in [
    "o1-preview", "o1-mini", "o3-mini", 
    "gpt-4.1", "gpt-4.1-mini", "gpt-4.1-nano"
]
```

---

## 🎯 DEFAULT MODELY

| Provider | Default Model | Důvod |
|----------|---------------|-------|
| **OpenAI** | `gpt-4o` | Nejnovější a nejvýkonnější |
| **Google** | `gemini-2.0-flash` | Nejnovější Gemini |
| **Anthropic** | `claude-3-7-sonnet-latest` | Stabilní a výkonný |

---

## 📋 CASE SENSITIVITY

**⚠️ POZOR:** Tyto modely jsou **case-sensitive**:
- ✅ `gpt-4.1` (správně)
- ❌ `GPT-4.1` (špatně)
- ❌ `Gpt-4.1` (špatně)

**Všechny názvy modelů musí být přesně jak jsou uvedeny v dokumentaci!**

---

## 🧪 TESTOVÁNÍ

### ✅ **Ověřené funkčnosti:**
- Všech 14 modelů je v databázi
- API server běží a načítá modely
- Frontend zobrazuje všechny modely v dropdown
- Speciální API volání implementována
- Automatická detekce funguje

### 🔬 **Test příkazy:**
```bash
# Kontrola modelů v databázi
python3 check_current_models.py

# Test o1-preview přes přímé API
python3 test_o1_direct.py

# Restart API serveru
sudo systemctl restart gent-api
```

---

## 📚 DALŠÍ DOKUMENTACE

- `WORKING_MODELS_FINAL.md` - Detailní specifikace
- `log_rollback.md` - Historie změn
- `add_*.py` - Skripty pro přidání modelů
- `test_*.py` - Testovací skripty

---

**🎉 Všechny modely jsou funkční a připravené k použití!**
