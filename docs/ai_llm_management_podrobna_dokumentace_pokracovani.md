# Pokračování dokumentace AI LLM Management

## 4. Frontend vrstva (pokračování)

### 4.3 JavaScript logika (`ai-llm.js`)

JavaScript soubor `ai-llm.js` obsahuje kompletní logiku komponenty AiLlm využívající Vue.js kompoziční API.

#### 4.3.1 Základní struktura a reaktivní stav

```javascript
export default {
  name: 'AiLlm',
  
  setup() {
    // Reaktivní stav
    const isLoading = ref(false);
    const errorMessage = ref(null);
    const successMessage = ref(null);
    const providers = ref([]);
    const selectedProviderId = ref(null);
    const selectedProvider = ref(null);
    
    // Stav pro formuláře
    const showAddProviderForm = ref(false);
    const showEditProviderForm = ref(false);
    const showAddModelForm = ref(false);
    const showEditModelForm = ref(false);
    
    // Formulářová data
    const newProvider = reactive({...});
    const editedProvider = reactive({...});
    const newModel = reactive({...});
    const editedModel = reactive({...});
    
    // Dostupné schopnosti modelů
    const availableCapabilities = [
      'text', 'images', 'code', 'reasoning', 'planning', 'search',
      'math', 'embeddings', 'function_calling', 'vision'
    ];
    
    // Metody...
    
    // Návratové hodnoty...
    return {
      // Vystavení proměnných a metod do šablony...
    };
  }
};
```

Důležité prvky:
- Využití Vue.js kompozičního API (`ref`, `reactive`, `computed`, `onMounted`)
- Oddělení stavových proměnných pro různé účely (loading, chyby, data, formuláře)
- Definice formulářových modelů jako reaktivní objekty

#### 4.3.2 Metody pro načítání dat

```javascript
const loadProviders = async () => {
  clearMessages();
  isLoading.value = true;
  try {
    console.log('Načítání poskytovatelů LLM...');
    const response = await llmDbService.getProviders();
    
    if (response.error) {
      showError(response.error.message);
      providers.value = [];
      return;
    }
    
    providers.value = response.data || [];
    // ... další logika ...
  } catch (error) {
    console.error('Chyba při načítání poskytovatelů LLM:', error);
    showError('Nepodařilo se načíst poskytovatele. ' + error.message);
    providers.value = [];
  } finally {
    isLoading.value = false;
  }
};

const loadProviderDetail = async (providerId) => {
  if (!providerId) return;
  
  clearMessages();
  isLoading.value = true;
  
  try {
    const response = await llmDbService.getProviderDetail(providerId);
    // ... zpracování odpovědi ...
  } catch (error) {
    // ... zpracování chyby ...
  } finally {
    isLoading.value = false;
  }
};
```

Tyto metody:
- Komunikují s backend API přes službu `llmDbService`
- Zobrazují indikátor načítání během asynchronních operací
- Zpracovávají chyby a zobrazují chybové zprávy
- Aktualizují reaktivní stav po dokončení operací

#### 4.3.3 Implementace CRUD operací v JavaScript

**Vytvoření poskytovatele:**

```javascript
const createProvider = async () => {
  // Validace
  if (!newProvider.name.trim()) {
    showError('Je nutné zadat název poskytovatele.');
    return;
  }
  
  clearMessages();
  isLoading.value = true;
  
  try {
    const response = await llmDbService.saveProvider(newProvider);
    
    if (response.error) {
      showError(response.error.message);
      return;
    }
    
    showSuccess('Nový poskytovatel byl úspěšně vytvořen.');
    closeAddProviderForm();
    
    // Aktualizujeme seznam poskytovatelů
    await loadProviders();
  } catch (error) {
    // ... zpracování chyby ...
  } finally {
    isLoading.value = false;
  }
};
```

**Aktualizace modelu:**

```javascript
const updateModel = async () => {
  // Validace
  if (!editedModel.name.trim()) {
    showError('Je nutné zadat název modelu.');
    return;
  }
  
  // ... další validace ...
  
  clearMessages();
  isLoading.value = true;
  
  try {
    // Zjistíme, zda se změnil název modelu
    const isNameChanged = editedModel.name !== editedModel.original_name;
    
    console.log(`Detekce změny názvu modelu: ${isNameChanged ? 'název změněn' : 'název nezměněn'}`);
    
    // Připravíme data pro API a zajistíme správné mapování property
    const modelData = {
      model_name: editedModel.name,
      context_length: editedModel.context_window,
      max_tokens_output: editedModel.max_tokens,
      capabilities: {...editedModel.capabilities},
      provider_id: selectedProvider.value.id,
      is_default: isDefault,
      is_name_changed: isNameChanged
    };
    
    // Zachováme model_identifier pouze pokud se název nezměnil
    if (!isNameChanged && model.model_identifier) {
      modelData.model_identifier = model.model_identifier;
    }
    
    // ... volání API a zpracování odpovědi ...
  } catch (error) {
    // ... zpracování chyby ...
  } finally {
    isLoading.value = false;
  }
};
```

### 4.4 Služba pro komunikaci s API

Soubor `frontend-vue/src/services/llm_db.service.js` obsahuje implementaci služby pro komunikaci s backend API.

#### 4.4.1 Základní struktura služby

```javascript
import apiService from './api.service';

/**
 * LLM DB služba pro přímý přístup k DB
 * Poskytuje metody pro načtení a uložení konfigurace jazykových modelů přímo z/do databáze
 */

const BASE_URL = '/api/db/llm';

// Informace o stavu DB připojení
let dbConnectionStatus = {
  connected: false,
  lastChecked: null,
  message: 'Připojení k DB nebylo ověřeno',
  error: null,
  venvActive: null // Indikátor stavu virtuálního prostředí
};

export const llmDbService = {
  /* Metody služby... */
};
```

#### 4.4.2 Metody pro práci s poskytovateli

```javascript
/**
 * Získat kompletní seznam poskytovatelů LLM z databáze
 * 
 * @returns {Promise} - Promise s odpovědí
 */
getProviders() {
  console.log('Načítání poskytovatelů LLM přímo z databáze');
  return apiService.get(`${BASE_URL}/providers`)
    .then(response => {
      console.log('Úspěšně načteno', response.data.length, 'poskytovatelů přímo z DB');
      return response;
    })
    .catch(error => {
      console.error('Chyba při načítání poskytovatelů z DB:', error);
      
      // Vracíme prázdná data a informaci o chybě místo statických dat
      return { 
        data: [], 
        error: {
          message: 'Nepodařilo se načíst poskytovatele z databáze. Zkontrolujte připojení k DB.',
          details: error.message,
          code: error.response?.status || 'UNKNOWN'
        }
      };
    });
},
```

#### 4.4.3 Metody pro práci s modely

```javascript
/**
 * Aktualizovat model podle ID
 * 
 * @param {Number} modelId - ID modelu
 * @param {Object} modelData - Data modelu k aktualizaci
 * @returns {Promise} - Promise s odpovědí
 */
updateModelById(modelId, modelData) {
  console.log(`Aktualizace modelu s ID ${modelId}:`, modelData);
  
  // API očekává data modelů s jinými názvy property, proto je mapujeme
  // Tady musíme být obzvláště opatrní, protože na straně API 
  // je očekáváno specifické pojmenování polí
  const apiModelData = {
    model_id: modelId
  };
  
  // model_name je povinné - název modelu
  if (modelData.model_name) {
    apiModelData.model_name = modelData.model_name;
  } else if (modelData.name) {
    apiModelData.model_name = modelData.name;
  }
  
  // context_length - velikost kontextového okna
  if (modelData.context_length !== undefined) {
    apiModelData.context_length = modelData.context_length;
  } else if (modelData.context_window !== undefined) {
    apiModelData.context_length = modelData.context_window; 
  }
  
  // ... další mapování ...
  
  // Model identifier - zajistíme unikátnost při změně názvu
  if (modelData.is_name_changed === true) {
    // Pokud se mění název, potřebujeme zajistit nový unikátní model_identifier
    // Nepřidáváme model_identifier, aby backend mohl generovat nový
    console.log('Název modelu byl změněn, necháváme backend generovat nový model_identifier');
  } else if (modelData.model_identifier) {
    // Pokud se název nemění a máme model_identifier, použijeme ho
    apiModelData.model_identifier = modelData.model_identifier;
  }
  
  console.log('Odesílám data pro aktualizaci do API:', apiModelData);
  
  return apiService.put(`${BASE_URL}/models/${modelId}`, apiModelData)
    .then(response => {
      // ... zpracování úspěšné odpovědi ...
    })
    .catch(error => {
      // ... zpracování chyby ...
    });
},
```

### 4.5 Stylování komponenty

Soubor `frontend-vue/src/styles/ai-llm.css` definuje kaskádové styly pro komponentu AiLlm.

#### 4.5.1 Barevné schéma

```css
:root {
  --background-primary: #121212;
  --background-secondary: #1e1e1e;
  --background-tertiary: #252525;
  --text-primary: #e1e1e1;
  --text-secondary: #b0b0b0;
  --text-muted: #6c757d;
  --primary-color: #2563eb;
  --primary-hover: #1d4ed8;
  --secondary-color: #4b5563;
  --secondary-hover: #374151;
  --success-color: #059669;
  --error-color: #dc2626;
  --border-color: #333;
  --form-bg: #2d2d2d;
  --card-bg: #232323;
  --hover-bg: rgba(255, 255, 255, 0.05);
  --active-bg: rgba(255, 255, 255, 0.1);
}
```

Barevné schéma používá:
- Tmavé pozadí s různými úrovněmi kontrastu
- Světlý text s různými úrovněmi důležitosti
- Modrou barvu pro primární akce a zvýraznění
- Sémantické barvy pro úspěch a chyby

#### 4.5.2 Layout a responsivní design

```css
.content-wrapper {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
  margin-top: 30px;
}

@media (min-width: 960px) {
  .content-wrapper {
    grid-template-columns: minmax(320px, 2fr) 3fr;
  }
}
```

Komponenta používá:
- CSS Grid pro layout
- Responsivní design s breakpointem na 960px
- Jednostranný layout na mobilních zařízeních, dvousloupcový na větších zařízeních

#### 4.5.3 Komponenty UI

```css
.provider-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: var(--card-bg);
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-left: 3px solid transparent;
}

.provider-item:hover {
  background-color: var(--hover-bg);
}

.provider-item.selected {
  background-color: var(--active-bg);
  border-left-color: var(--primary-color);
}

/* ... další styly ... */
```

---

## 5. Komunikační protokoly

### 5.1 Mapování dat mezi frontendem a backendem

Frontend a backend komunikují přes HTTP API, přičemž data jsou přenášena ve formátu JSON. Obě strany však používají mírně odlišné struktury dat, proto je potřeba provádět mapování.

#### 5.1.1 Mapování názvů polí

| Frontend | Backend | Popis |
|----------|---------|-------|
| `name` | `provider_name` | Název poskytovatele |
| `base_url` | `api_base_url` | URL API |
| `context_window` | `context_length` | Velikost kontextového okna modelu |
| `max_tokens` | `max_tokens_output` | Maximální počet výstupních tokenů |

Toto mapování je implementováno v metodách služby `llmDbService`, např. `updateModelById`:

```javascript
// model_name je povinné - název modelu
if (modelData.model_name) {
  apiModelData.model_name = modelData.model_name;
} else if (modelData.name) {
  apiModelData.model_name = modelData.name;
}

// context_length - velikost kontextového okna
if (modelData.context_length !== undefined) {
  apiModelData.context_length = modelData.context_length;
} else if (modelData.context_window !== undefined) {
  apiModelData.context_length = modelData.context_window; 
}
```

#### 5.1.2 Mapování capabilities

Capabilities (schopnosti) modelů jsou reprezentovány různým způsobem na frontendu a backendu:

- **Frontend**: Pole řetězců, např. `['text', 'code', 'vision']`
- **Backend**: Objekt s booleovskými hodnotami, např. `{text: true, code: true, vision: true}`

**Mapování z pole na objekt (frontend → backend):**

```javascript
// capabilities - schopnosti modelu
if (modelData.capabilities) {
  // API očekává capabilities jako objekt, ne jako pole
  if (Array.isArray(modelData.capabilities)) {
    // Převedeme pole na objekt { feature: true }
    const capabilitiesObj = {};
    modelData.capabilities.forEach(cap => {
      capabilitiesObj[cap] = true;
    });
    apiModelData.capabilities = capabilitiesObj;
  } else {
    // Již je objekt, použijeme tak jak je
    apiModelData.capabilities = modelData.capabilities;
  }
}
```

**Mapování z objektu na pole (backend → frontend):**

```javascript
// Převedení objektu capabilities na pole pro frontend
Object.assign(editedModel, {
  capabilities: Object.keys(model.capabilities || { text: true, code: true })
});
```

### 5.2 Formát požadavků a odpovědí API

#### 5.2.1 Obecná struktura odpovědí

API odpovědi jsou strukturovány konzistentně:

```json
{
  "success": true,
  "message": "Operace byla úspěšně provedena.",
  "data": { /* Data odpovědi */ }
}
```

V případě chyby:

```json
{
  "success": false,
  "message": "Operace se nezdařila.",
  "error": {
    "message": "Detailní popis chyby",
    "details": "Technické detaily",
    "code": "KÓD_CHYBY"
  }
}
```

#### 5.2.2 Příklad požadavku na aktualizaci modelu

**HTTP požadavek:**

```
PUT /api/db/llm/models/42 HTTP/1.1
Content-Type: application/json

{
  "model_name": "GPT-4",
  "context_length": 32000,
  "max_tokens_output": 4000,
  "capabilities": {
    "text": true,
    "code": true,
    "function_calling": true
  },
  "provider_id": 5,
  "is_default": true,
  "is_name_changed": false,
  "model_identifier": "gpt-4-32k"
}
```

**HTTP odpověď:**

```
HTTP/1.1 200 OK
Content-Type: application/json

{
  "success": true,
  "message": "Model s ID 42 byl úspěšně aktualizován",
  "model": {
    "model_id": 42,
    "model_name": "GPT-4",
    "model_identifier": "gpt-4-32k",
    "context_length": 32000,
    "max_tokens": 4000,
    "capabilities": {
      "text": true,
      "code": true,
      "function_calling": true
    },
    "is_default": true,
    "is_active": true
  }
}
```

### 5.3 Specifické HTTP endpointy

| URL | Metoda | Popis |
|-----|--------|-------|
| `/api/db/llm/providers` | GET | Získání seznamu poskytovatelů |
| `/api/db/llm/providers` | POST | Vytvoření nového poskytovatele |
| `/api/db/llm/providers/{id}` | GET | Získání detailu poskytovatele |
| `/api/db/llm/providers/{id}` | PUT | Aktualizace poskytovatele |
| `/api/db/llm/providers/{id}` | DELETE | Smazání poskytovatele |
| `/api/db/llm/models/{id}` | PUT | Aktualizace modelu |
| `/api/db/llm/models/{id}` | DELETE | Smazání modelu |

---

## 6. Implementace CRUD operací

### 6.1 CRUD operace pro poskytovatele

#### 6.1.1 Vytvoření poskytovatele (Create)

**Frontend:**

1. Uživatel vyplní formulář pro přidání poskytovatele
2. Po kliknutí na tlačítko "Vytvořit" se zavolá metoda `createProvider()`
3. Metoda validuje vstupní data a odešle požadavek na server
4. Po úspěšném vytvoření poskytovatele se aktualizuje seznam poskytovatelů

**Backend:**

1. Handler `create_provider` přijme data poskytovatele
2. Data jsou předána službě `LlmDirectDbService.save_provider()`
3. Služba vytvoří nový záznam v tabulce `llm_providers`
4. Po úspěchu jsou vrácena data nově vytvořeného poskytovatele

#### 6.1.2 Čtení poskytovatelů (Read)

**Frontend:**

1. Při načtení komponenty se zavolá metoda `loadProviders()`
2. Metoda odešle požadavek na server a načte seznam poskytovatelů
3. Seznam je zobrazen v UI

**Backend:**

1. Handler `get_providers` zpracuje požadavek
2. Data jsou získána ze služby `LlmDirectDbService.get_providers()`
3. Služba provede SQL dotaz na tabulku `llm_providers`
4. Data jsou vrácena jako JSON odpověď

#### 6.1.3 Aktualizace poskytovatele (Update)

**Frontend:**

1. Uživatel vybere poskytovatele a klikne na tlačítko editace
2. Otevře se formulář s předvyplněnými daty
3. Po úpravě dat a kliknutí na "Uložit" se zavolá metoda `updateProvider()`
4. Metoda odešle aktualizovaná data na server

**Backend:**

1. Handler `update_provider` přijme data poskytovatele
2. Data jsou validována a předána službě `LlmDirectDbService.save_provider()`
3. Služba aktualizuje záznam v tabulce `llm_providers`
4. Po úspěchu jsou vrácena aktualizovaná data poskytovatele

#### 6.1.4 Odstranění poskytovatele (Delete)

**Frontend:**

1. Uživatel vybere poskytovatele a klikne na tlačítko smazání
2. Po potvrzení dialogu se zavolá metoda `deleteProvider()`
3. Metoda odešle požadavek na server
4. Po úspěšném smazání je poskytovatel odstraněn ze seznamu

**Backend:**

1. Handler `delete_provider` přijme ID poskytovatele
2. ID je předáno službě `LlmDirectDbService.delete_provider()`
3. Služba provede SQL dotaz DELETE s omezením na ID poskytovatele
4. Díky omezení `ON DELETE CASCADE` jsou smazány i všechny modely poskytovatele
5. Po úspěchu je vrácena zpráva o úspěšném smazání

### 6.2 CRUD operace pro modely

#### 6.2.1 Vytvoření modelu (Create)

Vytvoření modelu je implementováno jako aktualizace poskytovatele, protože model je vždy vázán na poskytovatele:

1. Uživatel vyplní formulář pro přidání modelu
2. Po kliknutí na tlačítko "Přidat" se model přidá do objektu poskytovatele
3. Aktualizovaný poskytovatel (včetně nového modelu) je odeslán na server
4. Server uloží nový model do tabulky `llm_models`

#### 6.2.2 Čtení modelů (Read)

Čtení modelů je součástí čtení detailu poskytovatele:

1. Po výběru poskytovatele se zavolá metoda `loadProviderDetail()`
2. Server vrátí detail poskytovatele včetně všech jeho modelů
3. Modely jsou zobrazeny v sekci modelů v UI

#### 6.2.3 Aktualizace modelu (Update)

Aktualizace modelu je implementována pomocí samostatného endpointu:

1. Uživatel vybere model a klikne na tlačítko editace
2. Otevře se formulář s předvyplněnými daty
3. Po úpravě dat a kliknutí na "Uložit" se zavolá metoda `updateModel()`
4. Metoda připraví data pro server a odešle je pomocí `updateModelById()`
5. Server aktualizuje záznam v tabulce `llm_models`

#### 6.2.4 Odstranění modelu (Delete)

Implementováno pomocí samostatného endpointu:

1. Uživatel vybere model a klikne na tlačítko smazání
2. Po potvrzení dialogu se zavolá metoda `deleteModel()`
3. Metoda odešle požadavek na server pomocí `deleteModelById()`
4. Server smaže záznam z tabulky `llm_models`
5. Po úspěchu je model odstraněn ze seznamu modelů v UI

### 6.3 Řešení problémů s unikátním klíčem

Při editaci modelu může dojít k problémům s jedinečným klíčem `(provider_id, model_identifier)`, zejména pokud se změní název modelu. Pro řešení tohoto problému byl implementován speciální mechanismus:

1. Frontend detekuje změnu názvu modelu
2. Příznak `is_name_changed` je nastaven na `true`
3. Backend podle tohoto příznaku rozhodne, zda vygenerovat nový `model_identifier`
4. Při změně názvu se nevyplňuje `model_identifier`, aby backend mohl vygenerovat nový
5. Tím se zajistí, že nevznikne konflikt s unikátním klíčem

---

## 7. Zpracování chyb a validace

### 7.1 Validace na frontendu

Frontend implementuje základní validaci dat před odesláním na server:

```javascript
const updateModel = async () => {
  // Validace
  if (!editedModel.name.trim()) {
    showError('Je nutné zadat název modelu.');
    return;
  }
  
  if (editedModel.context_window <= 0) {
    showError('Velikost kontextového okna musí být kladné číslo.');
    return;
  }
  
  if (editedModel.max_tokens <= 0) {
    showError('Maximální počet tokenů musí být kladné číslo.');
    return;
  }
  
  if (editedModel.name !== editedModel.original_name && 
      selectedProvider.value.models && 
      selectedProvider.value.models[editedModel.name]) {
    showError('Model s tímto názvem již existuje.');
    return;
  }
  
  // ... zbytek implementace ...
};
```

### 7.2 Validace na backendu

Backend implementuje validaci dat pomocí Pydantic modelů:

```python
class LlmModelBase(BaseModel):
    """Základní model pro LLM model."""
    model_name: str
    model_identifier: Optional[str] = None
    context_length: Optional[int] = 32000
    max_tokens_output: Optional[int] = 4096
    default_temperature: Optional[float] = 0.7
    # ... další parametry ...
```

Pydantic zajišťuje:
- Validaci typů dat
- Validaci povinných polí
- Kontrolu rozsahu hodnot
- Nastavení výchozích hodnot

### 7.3 Databázové omezení

Databáze implementuje vlastní omezení pro zajištění integrity dat:

- **CHECK (default_temperature BETWEEN 0.0 AND 1.0)**: Zajišťuje, že teplota je v platném rozsahu
- **UNIQUE(provider_id, model_identifier)**: Zajišťuje unikátnost kombinace poskytovatele a identifikátoru modelu
- **NOT NULL**: Zajišťuje, že povinná pole mají hodnotu
- **REFERENCES ... ON DELETE CASCADE**: Zajišťuje, že modely jsou smazány při smazání poskytovatele

### 7.4 Zpracování a zobrazení chyb

#### 7.4.1 Zobrazení chyb na frontendu

Frontend zobrazuje chyby pomocí chybového boxu:

```html
<div v-if="errorMessage" class="error-box">
  {{ errorMessage }}
</div>
```

Zobrazení chyby je implementováno v metodě `showError`:

```javascript
const showError = (message) => {
  errorMessage.value = message;
};
```

#### 7.4.2 Logování chyb

Všechny chyby jsou logovány v konzoli prohlížeče a ve službě:

```javascript
catch (error) {
  console.error('Chyba při aktualizaci modelu:', error);
  console.error('Detail chyby:', error.message);
  showError('Nepodařilo se aktualizovat model. ' + error.message);
}
```

Backend loguje chyby do logovacího systému:

```python
try:
    # ... implementace ...
except Exception as e:
    logger.error(f"Chyba při mazání poskytovatele: {str(e)}")
    import traceback
    logger.error(f"Stack trace: {traceback.format_exc()}")
    if 'conn' in locals() and conn:
        try:
            conn.rollback()
        except Exception:
            pass
    return False
```

#### 7.4.3 Zpracování HTTP chyb

FastAPI zpracovává HTTP chyby a vrací konzistentní odpovědi:

```python
@router.put("/models/{model_id}")
async def update_model_by_id(
    model_id: int,
    model_data: Dict[str, Any],
    service: LlmDirectDbService = Depends(get_llm_db_service)
):
    try:
        # ... implementace ...
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chyba při aktualizaci modelu: {str(e)}")
```

---

## 8. Postupy údržby a rozšiřování

### 8.1 Přidání nových schopností modelů

Pro přidání nové schopnosti modelu je potřeba upravit pouze frontend:

1. Přidejte novou schopnost do pole `availableCapabilities` v souboru `ai-llm.js`:
```javascript
const availableCapabilities = [
  'text', 'images', 'code', 'reasoning
