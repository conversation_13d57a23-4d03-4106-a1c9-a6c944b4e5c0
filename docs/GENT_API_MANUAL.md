# 🔧 GENT API MANUAL - Pr<PERSON>ce s LLM modely

> **Pro:** Augment Agent a budoucí vývoj  
> **Datum:** 2025-05-29  
> **Verze:** PRODUCTION READY

---

## 🎯 ZÁKLADNÍ INFORMACE

### 📊 **Aktuální stav modelů**
```bash
# Kontrola všech modelů v databázi
cd /opt/gent && python3 check_current_models.py
```

### 🔄 **Restart služeb**
```bash
# Restart API serveru po změnách
sudo systemctl restart gent-api
sudo systemctl status gent-api

# Restart frontend (pokud potřeba)
sudo systemctl restart gent-frontend
```

---

## 🔵 OPENAI MODELY - API VOLÁNÍ

### 🟢 **STANDARDNÍ MODELY**
Modely: `gpt-4o`, `gpt-4o-mini`, `gpt-4-turbo`

```python
# Standardní OpenAI API volání
payload = {
    "model": "gpt-4o",
    "messages": [{"role": "user", "content": "Hello"}],
    "temperature": 0.7,
    "max_tokens": 1000,
    "top_p": 1.0,
    "frequency_penalty": 0.0,
    "presence_penalty": 0.0
}
```

### ⚡ **SPECIÁLNÍ MODELY**
Modely: `gpt-4.1`, `gpt-4.1-mini`, `gpt-4.1-nano`, `o1-preview`, `o1-mini`, `o3-mini`

```python
# Speciální API volání - BEZ temperature!
payload = {
    "model": "o1-preview",  # CASE SENSITIVE!
    "messages": [{"role": "user", "content": "Hello"}],
    "max_completion_tokens": 1000  # MÍSTO max_tokens!
    # ❌ NEPOUŽÍVAT: temperature, top_p, frequency_penalty, atd.
}
```

---

## 🔧 IMPLEMENTACE V GENT KÓDU

### 📍 **Detekce speciálních modelů**

```python
# gent/llm/openai_provider.py (řádky 119-120)
is_special_model = model in [
    "o1-preview", "o1-mini", "o3-mini", 
    "gpt-4.1", "gpt-4.1-mini", "gpt-4.1-nano"
]

# Podmíněné parametry
if not is_special_model:
    payload["temperature"] = temperature
    payload["max_tokens"] = max_tokens
else:
    payload["max_completion_tokens"] = max_tokens
```

### 📍 **Web interface API**

```python
# gent/api/app/routes/llm_direct_db_routes.py (řádky 342-359)
is_special_model = model in [
    "o1-preview", "o1-mini", "o3-mini", 
    "gpt-4.1", "gpt-4.1-mini", "gpt-4.1-nano"
]

if not is_special_model:
    payload["temperature"] = temperature
if max_tokens and max_tokens > 0:
    if is_special_model:
        payload["max_completion_tokens"] = max_tokens
    else:
        payload["max_tokens"] = max_tokens
```

---

## 📝 PŘIDÁNÍ NOVÝCH MODELŮ

### ➕ **Přidání OpenAI modelu**

1. **Vytvořit script:**
```python
# add_new_openai_model.py
new_models = [
    {
        'model_name': 'novy-model',
        'model_identifier': 'novy-model',  # CASE SENSITIVE!
        'context_length': 128000,
        'max_tokens': 4096,
        'capabilities': '{"text": true, "advanced": true}'
    }
]
```

2. **Upravit detekci (pokud speciální):**
```python
# gent/llm/openai_provider.py
is_special_model = model in [
    "o1-preview", "o1-mini", "o3-mini", 
    "gpt-4.1", "gpt-4.1-mini", "gpt-4.1-nano",
    "novy-model"  # PŘIDAT NOVÝ MODEL
]
```

3. **Spustit script a restart:**
```bash
cd /opt/gent && python3 add_new_openai_model.py
sudo systemctl restart gent-api
```

### ➕ **Přidání Google/Anthropic modelu**

```python
# Standardní přidání - bez speciálních úprav
new_models = [
    {
        'model_name': 'gemini-novy',
        'model_identifier': 'gemini-novy',
        'context_length': 1000000,
        'max_tokens': 8192,
        'capabilities': '{"text": true, "multimodal": true}'
    }
]
```

---

## 🗑️ ODSTRANĚNÍ MODELŮ

### ❌ **Odstranění konkrétních modelů**

```python
# remove_specific_models.py
models_to_remove = [
    ("OpenAI", "model-name"),
    ("Google", "gemini-model"),
    ("Anthropic", "claude-model")
]
```

### 🔄 **Kompletní reset modelů**

```bash
# POZOR: Smaže všechny modely!
cd /opt/gent && echo "ano" | python3 update_models_final.py
```

---

## 🧪 TESTOVÁNÍ MODELŮ

### 🔬 **Přímý test OpenAI**

```bash
# Test o1-preview přes OpenAI API
cd /opt/gent && python3 test_o1_direct.py
```

### 🌐 **Test přes GENT web interface**

1. Otevřít: http://192.168.14.150:8000
2. Jít do: LLM Management → Testování
3. Vybrat model z dropdown
4. Napsat testovací zprávu
5. Kliknout "Odeslat"

### 📊 **Test všech modelů**

```bash
# Kontrola stavu všech modelů
cd /opt/gent && python3 check_current_models.py
```

---

## ⚠️ ČASTÉ PROBLÉMY A ŘEŠENÍ

### 🔴 **Chyba 400 - Bad Request**
**Příčina:** Speciální model volán se standardními parametry

**Řešení:**
1. Zkontrolovat detekci speciálních modelů
2. Ověřit, že se neposílá `temperature`
3. Ověřit použití `max_completion_tokens`

### 🔴 **Model se nezobrazuje ve frontend**
**Příčina:** Model není v databázi nebo API server neběží

**Řešení:**
```bash
# 1. Zkontrolovat databázi
python3 check_current_models.py

# 2. Restart API serveru
sudo systemctl restart gent-api

# 3. Obnovit frontend (F5)
```

### 🔴 **Case sensitivity chyby**
**Příčina:** Špatně napsaný název modelu

**Řešení:**
- ✅ `gpt-4.1` (správně)
- ❌ `GPT-4.1` (špatně)
- ❌ `Gpt-4.1` (špatně)

---

## 📋 KONTROLNÍ SEZNAM

### ✅ **Před přidáním nového modelu:**
- [ ] Ověřit oficiální název modelu (case-sensitive)
- [ ] Zjistit typ API volání (standardní/speciální)
- [ ] Připravit script pro přidání
- [ ] Upravit detekci (pokud speciální)
- [ ] Otestovat funkčnost

### ✅ **Po přidání modelu:**
- [ ] Spustit `check_current_models.py`
- [ ] Restart API serveru
- [ ] Obnovit frontend (F5)
- [ ] Otestovat ve web interface
- [ ] Aktualizovat dokumentaci

---

## 📚 UŽITEČNÉ PŘÍKAZY

```bash
# Kontrola stavu služeb
sudo systemctl status gent-api
sudo systemctl status gent-frontend

# Logy API serveru
sudo journalctl -u gent-api -f

# Kontrola portů
netstat -tlnp | grep :8001
netstat -tlnp | grep :8000

# Kontrola databáze
cd /opt/gent && python3 check_current_models.py
```

---

**🎯 Tento manuál obsahuje vše potřebné pro práci s LLM modely v GENT systému!**
