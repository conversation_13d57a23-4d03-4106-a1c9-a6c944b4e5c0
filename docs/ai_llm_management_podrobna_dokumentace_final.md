# Finální část dokumentace AI LLM Management

## 8. <PERSON><PERSON><PERSON> a rozšiřování (pokračování)

### 8.1 Přidání nových schopností modelů (dokončení)

1. Přidejte novou schopnost do pole `availableCapabilities` v souboru `ai-llm.js`:
```javascript
const availableCapabilities = [
  'text', 'images', 'code', 'reasoning', 'planning', 'search',
  'math', 'embeddings', 'function_calling', 'vision', 'nová_schopnost'
];
```

2. Schopnost se automaticky zobrazí v UI ve výběru schopností
3. Není potřeba měnit schéma databáze, protože schopnosti jsou ukládány jako JSONB

### 8.2 Přidání nových vlastností modelů

Pro přidání nové vlastnosti modelu (např. `temperature_range`):

1. **Upravte databázové schéma**:
```sql
ALTER TABLE llm_models
ADD COLUMN temperature_range JSONB DEFAULT '{"min": 0.0, "max": 1.0}';
```

2. **Aktualizujte backend**:
```python
# V metodě get_provider_detail
provider["models"][model_name]["temperature_range"] = model_row[X]  # Nový index sloupce
```

3. **Upravte frontend**:
   - Přidejte pole do formuláře v souboru `AiLlm.vue`
   - Aktualizujte datový model v souboru `ai-llm.js`
   - Aktualizujte službu `llm_db.service.js` pro mapování nové vlastnosti

### 8.3 Přidání nového typu poskytovatele

Pro podporu nového typu poskytovatele (např. `local`):

1. **Přidejte hodnotu do omezení `auth_type`**:
```sql
ALTER TABLE llm_providers
DROP CONSTRAINT llm_providers_auth_type_check;

ALTER TABLE llm_providers
ADD CONSTRAINT llm_providers_auth_type_check
CHECK (auth_type IN ('api_key', 'oauth', 'bearer', 'none', 'local'));
```

2. **Aktualizujte validační model na backendu**:
```python
class ProviderBase(BaseModel):
    """Základní model pro poskytovatele LLM."""
    provider_name: str
    api_base_url: Optional[str] = None
    api_key: Optional[str] = None
    api_version: Optional[str] = None
    api_key_required: bool = True
    auth_type: str = "api_key"  # Validace nyní akceptuje i 'local'
    # ... ostatní pole ...
```

3. **Upravte frontend pro podporu nového typu**:
```html
<div class="form-group">
  <label for="provider-auth-type">Typ autentizace</label>
  <select id="provider-auth-type" v-model="newProvider.auth_type">
    <option value="api_key">API klíč</option>
    <option value="oauth">OAuth</option>
    <option value="bearer">Bearer token</option>
    <option value="none">Žádná</option>
    <option value="local">Lokální</option>
  </select>
</div>
```

---

## 9. Referenční příručka

### 9.1 Klíčové třídy a komponenty

#### 9.1.1 Backend

| Třída/Komponenta | Soubor | Popis |
|------------------|--------|-------|
| `LlmDirectDbService` | `gent/db/llm_db_service.py` | Služba pro přímý přístup k databázi |
| `router` | `gent/api/app/routes/llm_db_routes.py` | FastAPI router pro LLM DB |
| `ProviderBase` | `gent/api/app/routes/llm_db_routes.py` | Pydantic model pro validaci dat poskytovatele |
| `LlmModelBase` | `gent/api/app/routes/llm_db_routes.py` | Pydantic model pro validaci dat modelu |

#### 9.1.2 Frontend

| Třída/Komponenta | Soubor | Popis |
|------------------|--------|-------|
| `AiLlm` | `frontend-vue/src/views/AiLlm.vue` | Vue komponenta pro stránku |
| `setup` | `frontend-vue/src/scripts/ai-llm.js` | JavaScript logika komponenty |
| `llmDbService` | `frontend-vue/src/services/llm_db.service.js` | Služba pro komunikaci s API |

### 9.2 API referenční dokumentace

#### 9.2.1 Endpointy

| URL | Metoda | Popis | Parametry |
|-----|--------|-------|-----------|
| `/api/db/llm/providers` | GET | Seznam poskytovatelů | `check_venv`: boolean (volitelný) |
| `/api/db/llm/providers` | POST | Vytvoření poskytovatele | Body: objekt poskytovatele |
| `/api/db/llm/providers/{provider_id}` | GET | Detail poskytovatele | `provider_id`: ID poskytovatele |
| `/api/db/llm/providers/{provider_id}` | PUT | Aktualizace poskytovatele | `provider_id`: ID poskytovatele, Body: objekt poskytovatele |
| `/api/db/llm/providers/{provider_id}` | DELETE | Smazání poskytovatele | `provider_id`: ID poskytovatele |
| `/api/db/llm/models/{model_id}` | PUT | Aktualizace modelu | `model_id`: ID modelu, Body: objekt modelu |
| `/api/db/llm/models/{model_id}` | DELETE | Smazání modelu | `model_id`: ID modelu |

#### 9.2.2 Datové struktury

**Poskytovatel**:
```json
{
  "id": 1,
  "name": "OpenAI",
  "api_key": "********",
  "base_url": "https://api.openai.com/v1",
  "api_version": "v1",
  "api_key_required": true,
  "auth_type": "api_key",
  "rate_limit": 3000,
  "is_active": true,
  "model": "gpt-4",
  "models": {
    "gpt-4": {
      "model_id": 1,
      "model_identifier": "gpt-4",
      "context_length": 8192,
      "max_tokens": 4096,
      "capabilities": {
        "text": true,
        "chat": true,
        "code": true
      },
      "is_default": true
    },
    "gpt-3.5-turbo": {
      "model_id": 2,
      "model_identifier": "gpt-3.5-turbo",
      "context_length": 4096,
      "max_tokens": 2048,
      "capabilities": {
        "text": true,
        "chat": true
      },
      "is_default": false
    }
  }
}
```

**Model**:
```json
{
  "model_id": 1,
  "model_name": "gpt-4",
  "model_identifier": "gpt-4",
  "context_length": 8192,
  "max_tokens_output": 4096,
  "default_temperature": 0.7,
  "retry_attempts": 3,
  "retry_delay": 1000,
  "timeout": 30000,
  "capabilities": {
    "text": true,
    "chat": true,
    "code": true
  },
  "is_default": true,
  "is_active": true,
  "provider_id": 1
}
```

### 9.3 Databázové referenční tabulky

#### 9.3.1 Tabulka `llm_providers`

| Sloupec | Typ | Nulový | Výchozí | Popis |
|---------|-----|--------|---------|-------|
| `provider_id` | SERIAL | Ne | - | Primární klíč |
| `provider_name` | VARCHAR(100) | Ne | - | Název poskytovatele |
| `api_base_url` | VARCHAR(255) | Ano | NULL | Základní URL API |
| `api_key` | TEXT | Ano | NULL | API klíč |
| `api_version` | VARCHAR(50) | Ano | NULL | Verze API |
| `api_key_required` | BOOLEAN | Ne | TRUE | Zda je vyžadován API klíč |
| `auth_type` | VARCHAR(30) | Ne | 'api_key' | Typ autentizace |
| `rate_limit` | INTEGER | Ano | NULL | Limit požadavků na API |
| `is_active` | BOOLEAN | Ne | TRUE | Aktivní stav poskytovatele |
| `created_at` | TIMESTAMP WITH TIME ZONE | Ne | CURRENT_TIMESTAMP | Čas vytvoření |
| `updated_at` | TIMESTAMP WITH TIME ZONE | Ne | CURRENT_TIMESTAMP | Čas poslední aktualizace |

#### 9.3.2 Tabulka `llm_models`

| Sloupec | Typ | Nulový | Výchozí | Popis |
|---------|-----|--------|---------|-------|
| `model_id` | SERIAL | Ne | - | Primární klíč |
| `provider_id` | INTEGER | Ne | - | Cizí klíč (llm_providers) |
| `model_name` | VARCHAR(100) | Ne | - | Název modelu |
| `model_identifier` | VARCHAR(100) | Ne | - | Technický identifikátor |
| `context_length` | INTEGER | Ano | NULL | Velikost kontextového okna |
| `max_tokens_output` | INTEGER | Ano | NULL | Maximální počet výstupních tokenů |
| `default_temperature` | DECIMAL(3,2) | Ano | 0.70 | Výchozí teplota |
| `retry_attempts` | INTEGER | Ano | 3 | Počet opakovaných pokusů |
| `retry_delay` | INTEGER | Ano | 1000 | Pauza mezi pokusy (ms) |
| `timeout` | INTEGER | Ano | 30000 | Timeout (ms) |
| `pricing_input` | DECIMAL(10,6) | Ano | NULL | Cena za 1000 vstupních tokenů |
| `pricing_output` | DECIMAL(10,6) | Ano | NULL | Cena za 1000 výstupních tokenů |
| `capabilities` | JSONB | Ano | '{}' | Schopnosti modelu |
| `is_default` | BOOLEAN | Ne | FALSE | Výchozí model |
| `is_active` | BOOLEAN | Ne | TRUE | Aktivní stav modelu |
| `created_at` | TIMESTAMP WITH TIME ZONE | Ne | CURRENT_TIMESTAMP | Čas vytvoření |
| `updated_at` | TIMESTAMP WITH TIME ZONE | Ne | CURRENT_TIMESTAMP | Čas poslední aktualizace |

### 9.4 Známé omezení a problémy

#### 9.4.1 Omezení

- Nelze nastavit více výchozích modelů pro jednoho poskytovatele (omezení `idx_llm_models_default`)
- Nelze mít dva modely se stejným `model_identifier` pro jednoho poskytovatele (omezení `UNIQUE(provider_id, model_identifier)`)
- Při změně názvu modelu je třeba generovat nový `model_identifier` (kvůli omezení)

#### 9.4.2 Známé problémy

1. **Konflikt při změně názvu modelu**: Pokud se změní název modelu, ale ne `model_identifier`, může dojít ke konfliktu s unikátním klíčem. Řešením je vždy nastavit `is_name_changed` při změně názvu.

2. **Chybějící kaskádové aktualizace**: Při změně názvu modelu, který je nastaven jako výchozí pro poskytovatele, se automaticky neaktualizuje pole `model` v poskytovateli.

3. **Možná chybějící validace**: Frontend validuje data před odesláním na server, ale některé komplexnější validace (např. vzájemné závislosti polí) mohou chybět.

### 9.5 Doporučené postupy

#### 9.5.1 Správa poskytovatele a modelů

1. **Přidání nového poskytovatele**:
   - Vyplňte všechny povinné údaje (název, autentizace)
   - Pro řádnou funkčnost zadejte platný API klíč
   - Vždy zadejte základní URL API, pokud je jiná než výchozí

2. **Přidání nového modelu**:
   - Vyberte poskytovatele, pro kterého chcete přidat model
   - Zadejte název a základní parametry (kontext, tokeny)
   - Vyberte podporované schopnosti modelu
   - První přidaný model se automaticky stane výchozím

3. **Editace modelu**:
   - Při změně názvu modelu se automaticky vygeneruje nový `model_identifier`
   - Při editaci zachovejte dostatečnou velikost kontextu a tokenů
   - Upravte schopnosti podle aktuálních možností modelu

4. **Mazání**:
   - Před smazáním poskytovatele zvažte dopad na aplikace, které ho používají
   - Při smazání poskytovatele budou smazány i všechny jeho modely
   - Pokud smažete výchozí model, jiný model bude automaticky nastaven jako výchozí

#### 9.5.2 Optimalizace výkonu

1. **Indexace**:
   - Pro efektivní vyhledávání využívejte indexy na často filtrovaných sloupcích
   - Pro vyhledávání podle schopností používejte GIN index na `capabilities`

2. **Zpracování JSON**:
   - Pro práci s `capabilities` používejte vždy PostgreSQL JSONB operátory
   - Využívejte GIN index pro efektivní vyhledávání v JSONB

3. **Transakce**:
   - Při hromadných změnách (např. více modelů najednou) používejte transakce
   - Zajistěte správné rollbacky v případě chyby
