# Dokumentace stránky CHAT-TEST

## Obsah

1. [<PERSON><PERSON><PERSON> a přehled](#úvod-a-přehled)
2. [Architektura a datové toky](#architektura-a-datové-toky)
3. [<PERSON>b<PERSON>zo<PERSON><PERSON> struktura](#databázová-struktura)
4. [Frontend implementace](#frontend-implementace)
5. [Backend implementace](#backend-implementace)
6. [Tok dat a API volání](#tok-dat-a-api-volání)
7. [Interakce s LLM poskytovateli](#interakce-s-llm-poskytovateli)
8. [Styl a designové prvky](#styl-a-designové-prvky)
9. [Chybové stavy a jejich zpracování](#chybové-stavy-a-jejich-zpracování)
10. [Možnosti rozšíření](#možnosti-rozšíření)

## Úvod a přehled

Stránka CHAT-TEST je webové rozhraní pro testování komunikace s různými LLM (Large Language Model) poskytovateli jako jsou OpenAI, Anthropic, Google/Gemini a další. Stránka umožňuje uživateli:

- Vybrat si poskytovatele LLM z databáze
- Vybrat konkrétní model daného poskytovatele
- Odesílat textové zprávy do vybraného modelu
- Zobrazovat odpovědi od LLM
- Provádět rychlé testování pomocí předpřipravených zpráv
- Vyčistit historii chatu

Stránka je dostupná na adrese `http://**************:8000/chat-test` a je implementována jako Vue komponenta v rámci Vue.js aplikace.

## Architektura a datové toky

```mermaid
!include images/chat-test-data-flow.mmd
```

### Hlavní komponenty

1. **Frontend**:
   - Vue.js komponenta `ChatTest.vue`
   - Stylový soubor `chat-test.css`
   - Service soubory pro komunikaci s API:
     - `chat.service.js`: Komunikace s LLM API
     - `llm_db.service.js`: Načítání dat z databáze

2. **Backend**:
   - LiteLLM microservis (`micro_services/litellm/service.py`)
   - API endpointy:
     - `/api/config/llm/test-llm`: Endpoint pro testování LLM
     - `/api/db/llm/providers`: Načítání poskytovatelů z DB
     - `/api/db/llm/providers/{id}`: Detail poskytovatele s modely

3. **Databáze**:
   - PostgreSQL databáze s DB schématem pro LLM poskytovatele a modely
   - Tabulky:
     - `llm_providers`: Poskytovatelé LLM
     - `llm_models`: Modely LLM

### Datový tok

1. Načtení stránky:
   - ChatTest.vue -> llm_db.service.js -> `/api/db/llm/providers` -> DB -> seznam poskytovatelů
   
2. Výběr poskytovatele:
   - ChatTest.vue -> llm_db.service.js -> `/api/db/llm/providers/{id}` -> DB -> detail poskytovatele s modely

3. Odeslání zprávy:
   - ChatTest.vue -> chat.service.js -> `/api/config/llm/test-llm` -> LiteLLM služba -> Externí LLM API -> Odpověď

## Databázová struktura

Stránka CHAT-TEST komunikuje s PostgreSQL databází, která uchovává informace o poskytovatelích a modelech LLM. 

### Schéma databáze

```mermaid
!include images/chat-test-db-schema.mmd
```

#### Tabulka: `llm_providers`

Tabulka obsahuje informace o poskytovatelích LLM jako OpenAI, Anthropic, Google atd.

| Sloupec | Typ | Popis |
|---------|-----|-------|
| id | INTEGER | Primární klíč |
| name | VARCHAR(100) | Název poskytovatele |
| provider_name | VARCHAR(100) | Alterantivní název poskytovatele |
| api_key | TEXT | API klíč pro daného poskytovatele |
| base_url | TEXT | Základní URL pro API poskytovatele |
| api_version | VARCHAR(50) | Verze API |
| api_key_required | BOOLEAN | Zda je vyžadován API klíč |
| auth_type | VARCHAR(50) | Typ autentizace (api_key, oauth, atd.) |
| rate_limit | INTEGER | Limit počtu požadavků za minutu |
| is_active | BOOLEAN | Zda je poskytovatel aktivní |
| created_at | TIMESTAMP | Čas vytvoření záznamu |
| updated_at | TIMESTAMP | Čas poslední aktualizace záznamu |
| model | VARCHAR(100) | Výchozí model poskytovatele |
| model_identifier | VARCHAR(100) | Identifikátor modelu |
| context_length | INTEGER | Velikost kontextového okna modelu |
| max_tokens | INTEGER | Maximální počet tokenů na výstupu |
| temperature | FLOAT | Výchozí hodnota temperature pro model |
| capabilities | JSONB | Schopnosti poskytovatele (kód, text, atd.) |

#### Tabulka: `llm_models`

Tabulka obsahuje informace o modelech dostupných u jednotlivých poskytovatelů.

| Sloupec | Typ | Popis |
|---------|-----|-------|
| model_id | INTEGER | Primární klíč |
| provider_id | INTEGER | Cizí klíč na llm_providers |
| model_name | VARCHAR(100) | Název modelu |
| model_identifier | VARCHAR(100) | Identifikátor modelu |
| context_length | INTEGER | Velikost kontextového okna |
| max_tokens_output | INTEGER | Maximální počet tokenů na výstupu |
| capabilities | JSONB | Schopnosti modelu (kód, text, atd.) |
| is_active | BOOLEAN | Zda je model aktivní |
| created_at | TIMESTAMP | Čas vytvoření záznamu |
| updated_at | TIMESTAMP | Čas poslední aktualizace záznamu |

### Příklady dotazů

1. Získání všech aktivních poskytovatelů:
```sql
SELECT * FROM llm_providers WHERE is_active = true ORDER BY name;
```

2. Získání všech modelů pro konkrétního poskytovatele:
```sql
SELECT * FROM llm_models WHERE provider_id = ? AND is_active = true;
```

3. Získání detailu poskytovatele včetně jeho modelů:
```sql
SELECT p.*, 
  (SELECT json_object_agg(m.model_name, json_build_object(
    'model_id', m.model_id,
    'model_identifier', m.model_identifier,
    'context_length', m.context_length,
    'max_tokens_output', m.max_tokens_output,
    'capabilities', m.capabilities
  ))
  FROM llm_models m
  WHERE m.provider_id = p.id AND m.is_active = true) AS models
FROM llm_providers p
WHERE p.id = ?;
```

## Frontend implementace

### Struktura Vue komponenty

```mermaid
!include images/chat-test-ui-components.mmd
```

#### Soubor: `ChatTest.vue`

Komponenta je implementována jako jedna Vue.js komponenta s těmito hlavními částmi:

1. **Template**:
   - Sekce pro výběr poskytovatele a modelu
   - Oblast chatu s historií zpráv
   - Vstupní pole pro novou zprávu
   - Akční tlačítka (Vyčistit chat, Rychlý test)

2. **Script**:
   - Vue.js composition API s funkcemi pro:
     - Načítání poskytovatelů a modelů
     - Správu stavu UI (loading stavy, chybové zprávy)
     - Odesílání zpráv a zpracování odpovědí
     - Formátování času

3. **Style**:
   - Externě importovaný CSS soubor `chat-test.css`

### Klíčové Vue.js reaktivní proměnné

| Proměnná | Typ | Popis |
|----------|-----|-------|
| providers | Array | Seznam poskytovatelů z DB |
| selectedProviderId | String | ID vybraného poskytovatele |
| selectedProviderName | String | Název vybraného poskytovatele |
| models | Array | Seznam modelů pro vybraného poskytovatele |
| selectedModelId | String | ID vybraného modelu |
| selectedModelName | String | Název vybraného modelu |
| messages | Array | Historie zpráv v chatu |
| newMessage | String | Text nové zprávy |
| isProcessing | Boolean | Indikátor zpracování odpovědi |
| error | String | Chybová zpráva |

### Důležité metody Vue komponenty

#### `loadProviders()`
- Načte seznam poskytovatelů z databáze pomocí llm_db.service.js
- Aktualizuje stav `providers`
- Při chybě nastaví `error` na chybovou zprávu

#### `handleProviderSelect()`
- Volá se při změně výběru poskytovatele
- Aktualizuje `selectedProviderName`
- Volá `loadProviderDetail()` pro načtení detailu poskytovatele a jeho modelů

#### `loadProviderDetail(providerId)`
- Načte detail poskytovatele a jeho modely z databáze
- Aktualizuje stav `models`
- Automaticky vybere první model, pokud existuje

#### `sendMessage()`
- Zpracovává odeslání zprávy na LLM API
- Přidává uživatelskou zprávu do historie
- Volá chat.service.js pro komunikaci s LLM
- Zpracovává odpověď a přidává ji do historie

#### `quickTest()`
- Vyplní předpřipravenou zprávu a odešle ji
- Použito pro rychlé otestování funkčnosti

### Routing

Stránka je definována v souboru `router/index.js` jako lazy-loaded komponenta:

```javascript
const ChatTest = () => import('../views/ChatTest.vue')

const routes = [
  // ...
  {
    path: '/chat-test',
    name: 'ChatTest',
    component: ChatTest,
    meta: {
      title: 'Test chatu - GENT v10',
      requiresAuth: true
    }
  },
  // ...
]
```

## Backend implementace

Backend část sestává z několika komponent:

### 1. LLM DB Service

Implementovaná v `gent/db/llm_db_service.py`, poskytuje přístup k databázi pro získání informací o poskytovatelích a modelech.

#### Klíčové metody

- `get_providers()` - Získá seznam všech poskytovatelů
- `get_provider_detail(provider_id)` - Získá detail poskytovatele včetně jeho modelů
- `get_model_by_id(model_id)` - Získá detail konkrétního modelu

### 2. API Endpointy

API endpointy jsou implementovány pomocí FastAPI v různých souborech:

#### Soubor: `gent/api/app/routes/llm_db_routes.py`

- `/api/db/llm/providers` - GET: Vrací seznam poskytovatelů
- `/api/db/llm/providers/{provider_id}` - GET: Vrací detail poskytovatele

#### Soubor: `gent/api/app/routes/llm_config_routes.py`

- `/api/config/llm/test-llm` - POST: Endpoint pro testování komunikace s LLM

### 3. LiteLLM Service

Implementovaná v `micro_services/litellm/service.py`, poskytuje jednotné rozhraní pro komunikaci s různými LLM API pomocí knihovny LiteLLM.

#### Klíčové metody

- `call_llm(provider, model, prompt, ...)` - Volá LLM API podle poskytovatele a modelu
- `get_provider_info(provider_name)` - Získá informace o poskytovateli
- `format_model_name(provider, model)` - Formátuje název modelu podle poskytovatele

#### Speciální zpracování pro Google/Gemini

LiteLLM služba obsahuje speciální logiku pro zpracování Google/Gemini API:

```python
# Vždy použijeme standartní endpoint Gemini API, který je stabilní
api_base_url = "https://generativelanguage.googleapis.com/v1"

# Mapování modelů na správné endpointy
model_mapping = {
    "gemini-pro": "gemini-pro",
    "gemini-pro-vision": "gemini-pro-vision",
    "gemini-1.0-pro": "gemini-pro",
    "gemini-1.0-pro-vision": "gemini-pro-vision",
    "gemini-1.5-pro": "gemini-1.5-pro",
    "gemini-1.5-flash": "gemini-1.5-flash",
    "gemini-1.5-pro-vision": "gemini-1.5-pro-vision",
    # Přidána podpora pro experimentální modely 2.5
    "gemini-2.5-flash-preview-04-17": "gemini-1.5-flash", # Fallback na stabilní model
    "gemini-2.5-pro-preview-04-17": "gemini-1.5-pro",     # Fallback na stabilní model
    "gemini-2.5-flash-preview": "gemini-1.5-flash",       # Fallback na stabilní model
    "gemini-2.5-pro-preview": "gemini-1.5-pro"            # Fallback na stabilní model
}
```

## Tok dat a API volání

### 1. Načtení stránky

Když uživatel otevře stránku, provede se následující tok:

1. **Frontend**: `ChatTest.vue` -> `onMounted()` -> `loadProviders()`
2. **API Volání**: `llm_db.service.js` -> `getProviders()` -> `apiService.get('/api/db/llm/providers')`
3. **Backend**: FastAPI endpoint `GET /api/db/llm/providers` -> `llm_db_service.py` -> `get_providers()`
4. **Databáze**: SQL dotaz `SELECT * FROM llm_providers WHERE is_active = true`
5. **Zpět do Frontend**: Odpověď data -> `providers.value = response.data`

### 2. Výběr poskytovatele

Když uživatel vybere poskytovatele, provede se:

1. **Frontend**: Uživatel vybere poskytovatele -> `handleProviderSelect()` -> `loadProviderDetail(providerId)`
2. **API Volání**: `llm_db.service.js` -> `getProviderDetail(providerId)` -> `apiService.get('/api/db/llm/providers/${providerId}')`
3. **Backend**: FastAPI endpoint `GET /api/db/llm/providers/{provider_id}` -> `llm_db_service.py` -> `get_provider_detail(provider_id)`
4. **Databáze**: SQL dotaz, který získá poskytovatele a jeho modely
5. **Zpět do Frontend**: Odpověď s poskytovatelem a modely -> zpracování modelů v `loadProviderDetail()`

### 3. Odeslání zprávy

Když uživatel odešle zprávu, provede se:

1. **Frontend**: Uživatel odešle zprávu -> `sendMessage()` -> příprava `chatRequestData`
2. **API Volání**: `chat.service.js` -> `sendMessage(messageData)` -> `axios.post('/api/config/llm/test-llm', payload)`
3. **Backend**: FastAPI endpoint `POST /api/config/llm/test-llm` -> `LiteLLMService.call_llm()`
4. **Externí API**: Volání externího API poskytovatele (OpenAI, Anthropic, Google, atd.)
5. **Zpět do Backend**: Odpověď z externího API -> zpracování odpovědi
6. **Zpět do Frontend**: Odpověď z Backend API -> zpracování v `sendMessage()` -> přidání odpovědi do `messages`

### Příklad HTTP požadavku pro OpenAI

```javascript
// Požadavek na LLM API pro OpenAI
const payload = {
  model: "gpt-4",
  provider: "openai",
  prompt: "Ahoj, jak se máš?",
  temperature: 0.7,
  max_tokens: 1000
};

// HTTP POST na /api/config/llm/test-llm
```

### Příklad HTTP požadavku pro Google/Gemini

```javascript
// Požadavek na LLM API pro Google/Gemini
const payload = {
  model: "gemini-1.5-pro",
  provider: "google",
  prompt: "Ahoj, jak se máš?",
  temperature: 0.7,
  maxOutputTokens: 1000,
  topP: 0.8,
  topK: 40
};

// HTTP POST na /api/config/llm/test-llm
```

## Interakce s LLM poskytovateli

Stránka CHAT-TEST podporuje několik poskytovatelů LLM modelů, každý s různými specifiky:

### OpenAI

- **Base URL**: `https://api.openai.com/v1`
- **Modely**: gpt-3.5-turbo, gpt-4, gpt-4o, ...
- **Parametr pro max. tokeny**: `max_tokens` nebo `max_completion_tokens` (pro novější modely jako gpt-4o)
- **Autentizace**: API klíč v hlavičce `Authorization: Bearer {api_key}`

### Anthropic (Claude)

- **Base URL**: `https://api.anthropic.com`
- **Modely**: claude-3-opus, claude-3-sonnet, claude-3-haiku, ...
- **Parametr pro max. tokeny**: `max_tokens`
- **Autentizace**: API klíč v hlavičce `x-api-key: {api_key}`

### Google/Gemini

- **Base URL**: `https://generativelanguage.googleapis.com/v1`
- **Modely**: gemini-pro, gemini-1.5-pro, gemini-1.5-flash, ...
- **Parametr pro max. tokeny**: `maxOutputTokens`
- **Autentizace**: API klíč jako query parametr `?key={api_key}`
- **Další parametry**: `topP`, `topK`

### Specifická implementace pro Google/Gemini

Pro Google/Gemini byla potřeba speciální implementace kvůli odlišnému API:

1. **Přímé volání API**: LiteLLM knihovna má problémy s Google API, proto je implementováno přímé volání pomocí `requests`
2. **Speciální URL formát**: `{base_url}/models/{model_endpoint}:generateContent?key={api_key}`
3. **Fallback pro experimentální modely**: Mapování nových experimentálních modelů na stabilní verze

```python
# Přímé volání Google API
response = requests.post(
    api_url,
    headers={"Content-Type": "application/json"},
    data=json.dumps(payload),
    timeout=60
)
```

## Styl a designové prvky

Stránka CHAT-TEST používá tmavý režim, který je definován v souboru `chat-test.css`. Hlavní designové prvky zahrnují:

### Barevné schéma

- **Pozadí**: #1e1e2d (tmavě modrá)
- **Pozadí sekcí**: #151521 (tmavší modrá)
- **Pozadí zpráv chat oblasti**: #1a1a27 (středně tmavě modrá)
- **Primární barva**: #3699ff (modrá)
- **Barva textu**: #e4e6ef (světle šedá)
- **Barva sekundárního textu**: #9899ac (středně šedá)
- **Barva chyb**: #f64e60 (červená)

### Komponenty UI

1. **Sekce pro výběr poskytovatele a modelu**:
   - Dropdown seznamy pro výběr
   - Loading indikátory během načítání dat

2. **Chat sekce**:
   - Zprávy uživatele (modré, zarovnané vpravo)
   - Zprávy asistenta (tmavě šedé, zarovnané vlevo)
   - Časové značky u každé zprávy
   - Animovaný indikátor psaní

3. **Vstupní sekce**:
   - Textarea pro vstup
   - Tlačítko pro odeslání

4. **Akční tlačítka**:
   - "Vyčistit chat" - šedé tlačítko
   - "Rychlý test" - modré tlačítko

### Responzivní design

CSS obsahuje media queries pro přizpůsobení na mobilních zařízeních:

```css
/* Responzivní design */
@media (max-width: 768px) {
  .selector-row {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .message {
    max-width: 95%;
  }
}
```

## Chybové stavy a jejich zpracování

Stránka CHAT-TEST implementuje robustní zpracování chyb:

### Frontend chybové stavy

1. **Chyba při načítání poskytovatelů**:
   - Zobrazení chybové zprávy místo dropdownu
   - Ukládání chyby v `error.value`

2. **Chyba při načítání modelů**:
   - Zobrazení chybové zprávy pod dropdownem modelů
   - Nemožnost výběru neexistujících modelů

3. **Chyba při komunikaci s LLM API**:
   - Zachycení chyby v `try/catch` bloku
   - Zobrazení chybové zprávy v UI
   - Logování detailu chyby do konzole

### Backend chybové stavy

1. **Chyba při připojení k databázi**:
   - Logování chyby pomocí loggeru
   - Vrácení HTTP chybové odpovědi

2. **Chyba při komunikaci s externím LLM API**:
   - Zachycení chyby v LiteLLM službě
   - Vrácení strukturované chybové odpovědi včetně detailů

3. **Chyba při zpracování odpovědi od LLM API**:
   - Kontrola formátu odpovědi
   - Extrahování chybové zprávy pokud je k dispozici

### Příklady chybových zpráv

1. **Nedostupná databáze**:
   ```
   Nepodařilo se načíst seznam poskytovatelů z databáze. Zkuste to prosím později.
   ```

2. **Chyba připojení k LLM API**:
   ```
   Chyba při volání Google API: HTTP 404 - Not Found
   ```

3. **Neplatný API klíč**:
   ```
   {"type":"error","error":{"type":"authentication_error","message":"invalid x-api-key"}}
   ```

## Možnosti rozšíření

Stránka CHAT-TEST může být rozšířena o následující funkcionality:

1. **Rozšíření o další parametry LLM**:
   - Přidání možnosti nastavení parametrů `temperature`, `top_p`, `presence_penalty`, atd.

2. **Podpora pro multimodální modely**:
   - Přidání možnosti odesílání a zobrazování obrázků pro modely jako `gpt-4o` nebo `gemini-pro-vision`

3. **Ukládání historie konverzací**:
   - Persistentní ukládání celých konverzací
   - Možnost nahrát historickou konverzaci

4. **Podpora pro konverzační kontext**:
   - Odesílání celé historie zpráv pro lepší kontext

5. **Rozšířená analýza odpovědí**:
   - Zobrazení informací o počtu použitých tokenů
   - Měření časů odezvy pro porovnání modelů

6. **Export a sdílení konverzací**:
   - Možnost exportu konverzace do formátů jako JSON, Markdown nebo PDF
   - Tlačítko pro sdílení odkazu na konverzaci

7. **Uživatelské profily a nastavení**:
   - Ukládání preferovaných poskytovatelů a modelů
   - Vlastní šablony systémových promptů
