# AI LLM Management - Podrobná technická dokumentace

**Verze:** 1.0  
**Datum:** 19. dubna 2025  
**Autor:** Gent AI Team

## Obsah

1. [<PERSON>vod a přehled systému](#1-úvod-a-přehled-systému)
2. [Databázová vrstva](#2-databázová-vrstva)
3. [Backend vrstva](#3-backend-vrstva)
4. [Frontend vrstva](#4-frontend-vrstva)
5. [Komunika<PERSON><PERSON><PERSON> protokoly](#5-komunikační-protokoly)
6. [Implementace CRUD operací](#6-implementace-crud-operací)
7. [Zpracování chyb a validace](#7-zpracování-chyb-a-validace)
8. [Postupy údržby a rozšiřování](#8-postupy-údržby-a-rozšiřování)
9. [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>](#9-referenčn<PERSON>-p<PERSON><PERSON><PERSON>)

---

## 1. Úvod a přehled systému

### 1.1 Účel systému

AI LLM Management je webová aplikace sloužící ke správě poskytovatelů jazykových modelů (LLM) a jejich konkrétních modelů. Systém umožňuje kompletní CRUD operace (Create, Read, Update, Delete) nad těmito entitami a poskytuje centralizovanou správu API konfigurací pro různé poskytovatele.

### 1.2 Architektura systému

Systém je implementován jako třívrstvá aplikace:

1. **Databázová vrstva**: PostgreSQL databáze s tabulkami `llm_providers` a `llm_models`
2. **Backend vrstva**: FastAPI aplikace s endpointy pro manipulaci s daty
3. **Frontend vrstva**: Vue.js aplikace s reaktivním UI

**Diagram architektury:**

```
┌─────────────────┐         ┌─────────────────┐         ┌─────────────────┐
│                 │         │                 │         │                 │
│  Vue.js         │  HTTP   │  FastAPI        │  SQL    │  PostgreSQL     │
│  Frontend       │ ──────> │  Backend        │ ──────> │  Databáze       │
│                 │ <────── │                 │ <────── │                 │
└─────────────────┘         └─────────────────┘         └─────────────────┘
```

### 1.3 Tok dat v systému

1. Uživatel interaguje s Vue.js frontendem
2. Frontend komunikuje s backendem přes HTTP API volání 
3. Backend přistupuje k databázi pomocí služby `LlmDirectDbService`
4. Data jsou ukládána/získávána z PostgreSQL databáze
5. Odpovědi putují zpět k uživateli

### 1.4 Hlavní funkce systému

- Správa poskytovatelů LLM modelů (OpenAI, Anthropic, atd.)
- Správa konkrétních modelů jednotlivých poskytovatelů
- Konfigurace API připojení (klíče, URL, limity)
- Nastavení parametrů modelů (velikost kontextu, maximální počet tokenů)
- Správa schopností modelů (capabilities)

---

## 2. Databázová vrstva

### 2.1 Schéma databáze

Databáze obsahuje především dvě hlavní tabulky pro správu LLM poskytovatelů a modelů.

#### 2.1.1 Tabulka `llm_providers`

Tato tabulka uchovává informace o poskytovatelích LLM modelů, jako jsou OpenAI, Anthropic, atd.

**Schéma tabulky:**

```sql
CREATE TABLE llm_providers (
    provider_id SERIAL PRIMARY KEY,
    provider_name VARCHAR(100) NOT NULL UNIQUE,
    api_base_url VARCHAR(255),
    api_key TEXT,
    api_version VARCHAR(50),
    api_key_required BOOLEAN NOT NULL DEFAULT TRUE,
    auth_type VARCHAR(30) DEFAULT 'api_key' CHECK (auth_type IN ('api_key', 'oauth', 'bearer', 'none')),
    rate_limit INTEGER,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

**Vysvětlení klíčových sloupců:**

- `provider_id`: Primární klíč, unikátní identifikátor poskytovatele
- `provider_name`: Název poskytovatele (např. "OpenAI", "Anthropic") s omezením unikátnosti
- `api_base_url`: Základní URL pro API volání (např. "https://api.openai.com/v1")
- `api_key`: API klíč pro autentizaci (měl by být ideálně šifrovaný)
- `auth_type`: Typ autentizace s omezeným výběrem hodnot (api_key, oauth, bearer, none)
- `is_active`: Příznak, zda je poskytovatel aktivní

**Indexy:**

- `PRIMARY KEY` na `provider_id` (automaticky vytváří B-tree index)
- `UNIQUE` constraint na `provider_name`
- `idx_llm_providers_is_active` na `is_active` pro rychlé filtrování aktivních poskytovatelů

#### 2.1.2 Tabulka `llm_models`

Tato tabulka uchovává informace o konkrétních modelech nabízených jednotlivými poskytovateli.

**Schéma tabulky:**

```sql
CREATE TABLE llm_models (
    model_id SERIAL PRIMARY KEY,
    provider_id INTEGER NOT NULL REFERENCES llm_providers(provider_id) ON DELETE CASCADE,
    model_name VARCHAR(100) NOT NULL,
    model_identifier VARCHAR(100) NOT NULL,
    context_length INTEGER,
    max_tokens_output INTEGER,
    default_temperature DECIMAL(3,2) DEFAULT 0.70 CHECK (default_temperature BETWEEN 0.0 AND 1.0),
    retry_attempts INTEGER DEFAULT 3,
    retry_delay INTEGER DEFAULT 1000,
    timeout INTEGER DEFAULT 30000,
    pricing_input DECIMAL(10,6),
    pricing_output DECIMAL(10,6),
    capabilities JSONB DEFAULT '{}',
    is_default BOOLEAN NOT NULL DEFAULT FALSE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(provider_id, model_identifier)
);
```

**Vysvětlení klíčových sloupců:**

- `model_id`: Primární klíč, unikátní identifikátor modelu
- `provider_id`: Cizí klíč odkazující na poskytovatele s CASCADE omezením
- `model_name`: Uživatelsky přívětivý název modelu (např. "GPT-4", "Claude 3")
- `model_identifier`: Technický identifikátor modelu používaný v API voláních (např. "gpt-4", "claude-3-opus")
- `context_length`: Maximální délka kontextu v tokenech, kterou model podporuje
- `max_tokens_output`: Maximální počet výstupních tokenů
- `capabilities`: JSONB sloupec pro ukládání schopností modelu ve formátu JSON
- `is_default`: Příznak, zda je model výchozím pro daného poskytovatele

**Indexy:**

- `PRIMARY KEY` na `model_id` (automaticky vytváří B-tree index)
- `idx_llm_models_provider_id` na `provider_id` pro rychlé spojování s tabulkou poskytovatelů
- `idx_llm_models_is_active` na `is_active` pro rychlé filtrování aktivních modelů
- `idx_llm_models_default` unikátní index na `provider_id` s podmínkou `WHERE is_default = TRUE` 
- `idx_llm_models_capabilities` GIN index na `capabilities` pro efektivní vyhledávání v JSONB

**Omezení:**

- `UNIQUE(provider_id, model_identifier)`: Zajišťuje, že kombinace poskytovatele a identifikátoru modelu je unikátní
- `CHECK (default_temperature BETWEEN 0.0 AND 1.0)`: Zajišťuje, že teplota je v platném rozsahu
- `ON DELETE CASCADE`: Při smazání poskytovatele se automaticky smažou všechny jeho modely

### 2.2 Relační vztahy

```
llm_providers (1) ──────────────► llm_models (N)
     |                                 ▲
     |                                 |
     |  Relace: poskytovatele k modelům |
     └─────────────────────────────────┘
```

**Vysvětlení vztahu:**

- Jeden poskytovatel může mít mnoho modelů (vztah 1:N)
- Každý model musí patřit právě jednomu poskytovateli
- Při smazání poskytovatele jsou smazány všechny jeho modely (CASCADE)

### 2.3 Datové typy a ukládání dat

#### 2.3.1 Speciální datové typy

- **JSONB**: Použit pro sloupec `capabilities`, umožňuje ukládat a efektivně vyhledávat ve strukturovaných datech. Na rozdíl od typu JSON, JSONB ukládá data v binárním formátu, což umožňuje rychlejší zpracování a indexaci.

#### 2.3.2 Ukládání capabilities

Schopnosti (capabilities) modelů jsou ukládány v JSONB sloupci jako objekt s klíči reprezentujícími schopnosti a hodnotami true/false:

```json
{
  "chat": true,
  "completion": true,
  "function_calling": true,
  "streaming": true,
  "vision": false,
  "code_generation": true
}
```

Tato struktura umožňuje:
- Snadné přidávání nových schopností bez změny schématu
- Efektivní vyhledávání modelů podle schopností (díky GIN indexu)
- Flexibilní reprezentaci schopností v UI

### 2.4 Časové razítka a auditní informace

Obě tabulky obsahují sloupce `created_at` a `updated_at`, které automaticky sledují čas vytvoření a poslední aktualizace záznamu. Tyto informace jsou důležité pro:

- Auditní účely
- Sledování změn v čase
- Řazení záznamů podle času vytvoření nebo aktualizace

### 2.5 Důvody pro zvolené databázové schéma

1. **Normalizace dat**: Oddělení poskytovatelů a modelů do samostatných tabulek sleduje principy normalizace databáze, eliminuje redundanci a umožňuje efektivní správu dat.

2. **Optimalizovaná výkonnost**: Použití vhodných indexů (včetně GIN pro JSONB) zajišťuje rychlé vyhledávání a filtrování.

3. **Integritní omezení**: Cizí klíče a unikátní omezení zajišťují integritu dat a předcházejí vzniku nekonzistentních záznamů.

4. **Flexibilita**: JSONB pro capabilities umožňuje přidávat nové schopnosti bez potřeby měnit schéma databáze.

5. **Auditing**: Časová razítka umožňují sledovat historii změn.

---

## 3. Backend vrstva

Backend vrstva je implementována pomocí FastAPI frameworku a poskytuje REST API endpointy pro manipulaci s poskytovateli a modely.

### 3.1 Struktura backendové služby

```
gent/
├── api/
│   └── app/
│       └── routes/
│           └── llm_db_routes.py  # FastAPI router pro LLM DB
└── db/
    ├── llm_db_service.py         # Služba pro přístup k DB
    └── init_db_env.py            # Aktivace virtuálního prostředí
```

### 3.2 Třída `LlmDirectDbService`

Třída `LlmDirectDbService` v souboru `gent/db/llm_db_service.py` zajišťuje přímý přístup k databázi bez nutnosti API volání. Je zodpovědná za všechny databázové operace související s LLM poskytovateli a modely.

#### 3.2.1 Inicialiace a připojení k DB

```python
def __init__(self):
    """Inicializace služby pro přímý přístup k databázi."""
    # Při inicializaci nepřipojujeme k DB, aby neblokovala
    # Připojení vytvoříme až při prvním požadavku
    self.conn = None

def _get_connection(self):
    """Získání připojení k databázi."""
    # V souladu s hlavními pravidly aktivujeme virtuální prostředí
    venv_activated = activate_venv()
    if not venv_activated:
        logger.warning("Nepodařilo se aktivovat virtuální prostředí, přístup k DB může selhat")
    
    if self.conn is None or self.conn.closed:
        try:
            self.conn = get_db_connection()
            if self.conn is None:
                if not venv_activated:
                    raise Exception("Nepodařilo se připojit k databázi - virtuální prostředí není aktivováno")
                else:
                    raise Exception("Nepodařilo se připojit k databázi")
        except Exception as e:
            logger.error(f"Chyba při připojování k databázi: {str(e)}")
            if not venv_activated:
                logger.error("Pravděpodobná příčina: Virtuální prostředí není aktivováno")
            raise
    return self.conn
```

Důležité aspekty:

- Lazy inicializace připojení k databázi
- Aktivace virtuálního prostředí před každým připojením
- Podrobné logování chyb a jejich pravděpodobných příčin

#### 3.2.2 Metody pro práci s poskytovateli

**Získání seznamu poskytovatelů:**

```python
def get_providers(self) -> List[Dict[str, Any]]:
    """
    Získá seznam poskytovatelů LLM z databáze z nové tabulky llm_providers.
    
    Returns:
        List[Dict[str, Any]]: Seznam poskytovatelů jako slovníky
        
    Raises:
        Exception: Pokud se nepodaří získat data z databáze
    """
    # ... implementace ...
```

**Získání detailu poskytovatele včetně jeho modelů:**

```python
def get_provider_detail(self, provider_id: int) -> Optional[Dict[str, Any]]:
    """
    Získá detail poskytovatele podle ID z nové tabulky llm_providers.
    
    Args:
        provider_id: ID poskytovatele
        
    Returns:
        Optional[Dict[str, Any]]: Detail poskytovatele nebo None, pokud neexistuje
    """
    # ... implementace ...
```

**Ukládání poskytovatele a jeho modelů:**

```python
def save_provider(self, provider_data: Dict[str, Any]) -> bool:
    """
    Uloží nebo aktualizuje poskytovatele v nových tabulkách llm_providers a llm_models.

    Args:
        provider_data: Data poskytovatele včetně modelů

    Returns:
        bool: True pokud se podařilo uložit, jinak False
    """
    # ... implementace ...
```

**Smazání poskytovatele:**

```python
def delete_provider(self, provider_id: int) -> bool:
    """
    Smaže poskytovatele a všechny jeho modely z databáze.
    
    Args:
        provider_id: ID poskytovatele
        
    Returns:
        bool: True pokud se podařilo smazat, jinak False
    """
    # ... implementace ...
```

#### 3.2.3 Metody pro práci s modely

**Smazání modelu:**

```python
def delete_model(self, model_id: int) -> bool:
    """
    Smaže model z databáze.
    
    Args:
        model_id: ID modelu
        
    Returns:
        bool: True pokud se podařilo smazat, jinak False
    """
    # ... implementace ...
```

### 3.3 FastAPI endpointy

FastAPI endpointy jsou definovány v souboru `gent/api/app/routes/llm_db_routes.py`. Poskytují HTTP API pro manipulaci s poskytovateli a modely.

#### 3.3.1 Router a dependency injection

```python
# Vytvoření routeru pro LLM DB API
router = APIRouter(
    prefix="/api/db/llm",
    tags=["llm-db"],
    responses={404: {"description": "Not found"}},
)

# Dependency pro získání instance LlmDirectDbService
def get_llm_db_service():
    """Dependency pro získání instance LlmDirectDbService."""
    service = LlmDirectDbService()
    try:
        yield service
    finally:
        # Zajistíme uzavření připojení k DB
        if hasattr(service, "_close_connection"):
            service._close_connection()
```

Dependency injection je použit pro:
- Automatickou inicializaci a uzavření služby pro přístup k DB
- Zajištění, že spojení s DB je vždy korektně uzavřeno
- Centralizaci logiky přístupu k DB

#### 3.3.2 Endpointy pro poskytovatele

**Získání seznamu poskytovatelů:**

```python
@router.get("/providers", response_model=List[Dict[str, Any]])
async def get_providers(
    check_venv: bool = False,
    service: LlmDirectDbService = Depends(get_llm_db_service)
):
    """
    Získá seznam poskytovatelů LLM z nové tabulky llm_providers v databázi.
    """
    # ... implementace ...
```

**Získání detailu poskytovatele:**

```python
@router.get("/providers/{provider_id}", response_model=Dict[str, Any])
async def get_provider_detail(provider_id: int, service: LlmDirectDbService = Depends(get_llm_db_service)):
    """
    Získá detail poskytovatele podle ID z nové tabulky llm_providers.
    """
    # ... implementace ...
```

**Aktualizace poskytovatele:**

```python
@router.put("/providers/{provider_id}", response_model=Dict[str, Any])
async def update_provider(
    provider_id: int, 
    provider_data: Dict[str, Any],
    service: LlmDirectDbService = Depends(get_llm_db_service)
):
    """
    Aktualizuje poskytovatele v nové tabulce llm_providers a jeho modely v llm_models.
    """
    # ... implementace ...
```

**Vytvoření poskytovatele:**

```python
@router.post("/providers", response_model=Dict[str, Any]])
async def create_provider(
    provider_data: Dict[str, Any],
    service: LlmDirectDbService = Depends(get_llm_db_service)
):
    """
    Vytvoří nového poskytovatele v tabulce llm_providers a jeho modely v llm_models.
    """
    # ... implementace ...
```

**Smazání poskytovatele:**

```python
@router.delete("/providers/{provider_id}")
async def delete_provider(
    provider_id: int,
    service: LlmDirectDbService = Depends(get_llm_db_service)
):
    """
    Smaže poskytovatele a všechny jeho modely z databáze.
    """
    # ... implementace ...
```

#### 3.3.3 Endpointy pro modely

**Smazání modelu podle ID:**

```python
@router.delete("/models/{model_id}")
async def delete_model_by_id(
    model_id: int,
    service: LlmDirectDbService = Depends(get_llm_db_service)
):
    """
    Smaže model z databáze podle jeho ID.
    """
    # ... implementace ...
```

**Aktualizace modelu podle ID:**

```python
@router.put("/models/{model_id}")
async def update_model_by_id(
    model_id: int,
    model_data: Dict[str, Any],
    service: LlmDirectDbService = Depends(get_llm_db_service)
):
    """
    Aktualizuje model podle jeho ID.
    """
    # ... implementace ...
```

**Nastavení výchozího modelu:**

```python
@router.put("/providers/{provider_id}/models/{model_name}/set-default", response_model=Dict[str, Any])
async def set_default_model(
    provider_id: int,
    model_name: str,
    service: LlmDirectDbService = Depends(get_llm_db_service)
):
    """
    Nastaví model jako výchozí pro poskytovatele.
    """
    # ... implementace ...
```

### 3.4 Validační modely (Pydantic)

FastAPI používá Pydantic modely pro validaci vstupních dat.

```python
class LlmCapabilities(BaseModel):
    """Model pro schopnosti LLM modelu."""
    text: bool = True
    chat: bool = True
    code: bool = False
    vision: bool = False
    function_calling: bool = False
    reasoning: bool = False
    search: bool = False
    tools: bool = False


class LlmModelBase(BaseModel):
    """Základní model pro LLM model."""
    model_name: str
    model_identifier: Optional[str] = None
    context_length: Optional[int] = 32000
    max_tokens_output: Optional[int] = 4096
    default_temperature: Optional[float] = 0.7
    # ... další parametry ...


class ProviderBase(BaseModel):
    """Základní model pro poskytovatele LLM."""
    provider_name: str
    api_base_url: Optional[str] = None
    api_key: Optional[str] = None
    # ... další parametry ...
```

Tyto modely zajišťují:
- Validaci vstupních dat před zpracováním
- Automatickou dokumentaci API pomocí OpenAPI (Swagger)
- Typovou bezpečnost a lepší vývoj

---

## 4. Frontend vrstva

Frontend vrstva je implementována pomocí Vue.js frameworku a poskytuje uživatelské rozhraní pro správu poskytovatelů a modelů.

### 4.1 Struktura frontendu

```
frontend-vue/
├── src/
│   ├── views/
│   │   └── AiLlm.vue     # Vue komponenta pro stránku
│   ├── scripts/
│   │   └── ai-llm.js     # Logika komponenty
│   ├── services/
│   │   └── llm_db.service.js  # Služba pro komunikaci s API
│   └── styles/
│       └── ai-llm.css    # Styly komponenty
```

### 4.2 Vue komponenta (`AiLlm.vue`)

Komponenta definuje strukturu uživatelského rozhraní pomocí Vue šablony.

#### 4.2.1 Základní struktura UI

```html
<template>
  <div class="ai-llm-container">
    <h1>AI LLM Management</h1>
    <p>Správa poskytovatelů AI a jejich modelů LLM v databázi.</p>
    
    <!-- Načítání -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>Načítání dat...</p>
    </div>
    
    <!-- Notifikace -->
    <div v-if="errorMessage" class="error-box">
      {{ errorMessage }}
    </div>
    
    <div v-if="successMessage" class="success-box">
      {{ successMessage }}
    </div>
    
    <!-- Hlavní obsah -->
    <div class="content-wrapper">
      <!-- Sekce poskytovatelů -->
      <div class="providers-section">
        <!-- ... Obsah sekce poskytovatelů ... -->
      </div>
      
      <!-- Sekce modelů -->
      <div class="models-section">
        <!-- ... Obsah sekce modelů ... -->
      </div>
    </div>
  </div>
</template>

<!-- Import JavaScript logiky z externího souboru -->
<script src="../scripts/ai-llm.js"></script>

<!-- Import CSS ze samostatného souboru -->
<style src="../styles/ai-llm.css"></style>
```

#### 4.2.2 Sekce poskytovatelů

```html
<!-- Hlavička sekce -->
<div class="section-header">
  <h2>Poskytovatelé AI</h2>
  <button @click="openAddProviderForm" class="btn btn-primary">
    <i class="fas fa-plus"></i> Nový poskytovatel
  </button>
</div>

<!-- Seznam poskytovatelů -->
<div v-if="providers.length === 0" class="empty-list">
  <p>Žádní poskytovatelé nebyli nalezeni.</p>
  <button @click="loadProviders" class="btn">Zkusit znovu</button>
</div>

<div v-else class="providers-list">
  <div v-for="provider in providers" :key="provider.id" class="provider-item"
      :class="{ 'selected': provider.id === selectedProviderId }">
    <div class="provider-info" @click="selectedProviderId = provider.id; handleProviderSelect()">
      <h3>{{ provider.name }}</h3>
      <div class="provider-meta">
        <span v-if="provider.is_default" class="provider-default">Výchozí</span>
        <span class="provider-model">{{ provider.model }}</span>
      </div>
    </div>
    <div class="provider-actions">
      <button @click="openEditProviderForm()" class="btn-icon" 
              :disabled="provider.id !== selectedProviderId"
              title="Upravit poskytovatele">
        <i class="fas fa-edit">&#9998;</i>
      </button>
      <button @click="deleteProvider(provider.id)" class="btn-icon" 
              :disabled="provider.id !== selectedProviderId"
              title="Smazat poskytovatele">
        <i class="fas fa-trash">&#128465;</i>
      </button>
    </div>
  </div>
</div>
```

#### 4.2.3 Sekce modelů

```html
<!-- Hlavička sekce -->
<div class="section-header">
  <h2>Modely {{ selectedProvider ? selectedProvider.name : '' }}</h2>
  <button @click="openAddModelForm()" class="btn btn-primary" :disabled="!selectedProvider">
    <i class="fas fa-plus"></i> Přidat model
  </button>
</div>

<!-- Seznam modelů -->
<div v-if="!selectedProvider" class="empty-list">
  <p>Vyberte poskytovatele pro zobrazení jeho modelů.</p>
</div>

<div v-else-if="!selectedProvider.models || Object.keys(selectedProvider.models).length === 0" class="empty-list">
  <p>Žádné modely nebyly nalezeny pro poskytovatele {{ selectedProvider.name }}.</p>
</div>

<div v-else class="models-list">
  <div v-for="(model, modelName) in selectedProvider.models" :key="modelName" class="model-item">
    <div class="model-header">
      <h3>{{ modelName }}</h3>
      <div class="model-actions">
        <button @click="openEditModelForm(modelName)" class="btn-icon" title="Upravit model">
          <i class="fas fa-edit">&#9998;</i>
        </button>
        <button @click="deleteModel(modelName)" class="btn-icon" title="Smazat model">
          <i class="fas fa-trash">&#128465;</i>
        </button>
      </div>
    </div>
    <div class="model-details">
      <!-- ... Detaily modelu ... -->
    </div>
  </div>
</div>
```

#### 4.2.4 Formuláře

Komponenta obsahuje čtyři hlavní formuláře:

1. **Formulář pro přidání poskytovatele**:
```html
<div v-if="showAddProviderForm" class="form-container">
  <h3>Přidat poskytovatele</h3>
  <div class="form-group">
    <label for="provider-name">Název poskytovatele</label>
    <input type="text" id="provider-name" v-model="newProvider.name" />
  </div>
  <!-- ... další pole formuláře ... -->
  <div class="form-actions">
    <button @click="createProvider()" class="btn btn-primary">Vytvořit</button>
    <button @click="closeAddProviderForm()" class="btn">Zrušit</button>
  </div>
</div>
```

2. **Formulář pro editaci poskytovatele**:
```html
<div v-if="showEditProviderForm" class="form-container">
  <h3>Upravit poskytovatele</h3>
  <!-- ... pole formuláře ... -->
  <div class="form-actions">
    <button @click="updateProvider()" class="
