# Task list pro vytvoření LLM databázových tabulek

## Příprava
- [x] Ověřit stav původních tabulek v databázi
- [x] Zálohovat existující data (pokud existují)
- [x] Připravit rollback plán v případě problémů

## Vytvoření tabulky llm_providers
- [x] Vytvořit SQL skript pro tabulku llm_providers
- [x] Vytvořit primární klíč pro tabulku
- [x] Přidat UNIQUE omezení na provider_name
- [x] Přidat CHECK omezení na auth_type
- [x] Vytvořit indexy pro tabulku
- [x] Přidat komentáře k tabulce a sloupcům pro dokumentaci
- [x] Spustit skript a ověřit správné vytvoření tabulky

## Vytvoření tabulky llm_models
- [x] Vytvořit SQL skript pro tabulku llm_models
- [x] Vytvořit primární klíč pro tabulku
- [x] Přidat cizí klíč na llm_providers s CASCADE omezením
- [x] Přidat UNIQUE omezení pro kombinaci provider_id a model_identifier
- [x] Přidat částečný index pro zajištění jediného výchozího modelu pro poskytovatele
- [x] Vytvořit CHECK omezení na default_temperature
- [x] Vytvořit indexy pro efektivní vyhledávání
- [x] Přidat komentáře k tabulce a sloupcům pro dokumentaci
- [x] Spustit skript a ověřit správné vytvoření tabulky

## Nastavení oprávnění
- [ ] Identifikovat role/uživatele vyžadující přístup k tabulkám
- [ ] Udělit příslušná oprávnění pro čtení, vkládání, úpravu, mazání
- [ ] Ověřit správné nastavení oprávnění pomocí test dotazů

## Testování
- [x] Vytvořit testovací záznamy v llm_providers
- [x] Ověřit unikátnost provider_name
- [x] Vytvořit testovací záznamy v llm_models pro různé poskytovatele
- [x] Otestovat unikátnost kombinace provider_id a model_identifier
- [x] Otestovat nastavení výchozího modelu (ověřit, že pro poskytovatele může být pouze jeden výchozí)
- [x] Otestovat CASCADE DELETE (smazání poskytovatele smaže i jeho modely)
- [x] Otestovat JSONB funkcionalitu pro sloupec capabilities
- [x] Otestovat vyhledávání podle různých kritérií

## Implementace API rozhraní
- [ ] Navrhnout strukturu API endpointů pro tabulky llm_providers a llm_models
- [ ] Implementovat základní CRUD endpointy pro tabulku llm_providers
- [ ] Implementovat základní CRUD endpointy pro tabulku llm_models
- [ ] Implementovat specializované endpointy (např. nastavení výchozího modelu)
- [ ] Zajistit validaci vstupních dat
- [ ] Implementovat autentizaci a autorizaci
- [ ] Vytvořit dokumentaci API (např. pomocí Swagger/OpenAPI)
- [ ] Otestovat API endpointy pomocí automatizovaných testů
- [ ] Provést zátěžové testování API

## Integrace s aplikací
- [ ] Aktualizovat konfigurace ORM (pokud je používán)
- [ ] Aktualizovat dotazy v aplikaci
- [ ] Vytvořit/aktualizovat API endpointy pro práci s tabulkami
- [ ] Otestovat CRUD operace z aplikace
- [ ] Zajistit správnou integraci s webovým rozhraním

## Finalizace
- [ ] Ověřit výkon pomocí EXPLAIN ANALYZE na typických dotazech
- [ ] Optimalizovat indexy dle výsledků analýzy výkonu
- [ ] Aktualizovat aplikační dokumentaci
- [ ] Aktualizovat diagramy databáze
- [ ] Připravit poznámky pro vývojáře ohledně použití nových tabulek
- [ ] Provést finální kontrolu všech implementovaných komponent
- [ ] Dokončit dokumentaci pro administrátory a koncové uživatele

## SQL skripty pro vytvoření tabulek

### Ukázkový SQL skript pro vytvoření tabulky llm_providers
```sql
CREATE TABLE llm_providers (
    provider_id SERIAL PRIMARY KEY,
    provider_name VARCHAR(100) NOT NULL UNIQUE,
    api_base_url VARCHAR(255),
    api_key TEXT,
    api_version VARCHAR(50),
    api_key_required BOOLEAN NOT NULL DEFAULT TRUE,
    auth_type VARCHAR(30) DEFAULT 'api_key' CHECK (auth_type IN ('api_key', 'oauth', 'bearer', 'none')),
    rate_limit INTEGER,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_llm_providers_is_active ON llm_providers(is_active);

COMMENT ON TABLE llm_providers IS 'Tabulka obsahující informace o poskytovatelích LLM služeb a jejich API';
COMMENT ON COLUMN llm_providers.provider_id IS 'Unikátní identifikátor poskytovatele';
COMMENT ON COLUMN llm_providers.provider_name IS 'Unikátní název poskytovatele (např. OpenAI, Anthropic)';
COMMENT ON COLUMN llm_providers.api_base_url IS 'Základní URL pro volání API poskytovatele';
COMMENT ON COLUMN llm_providers.api_key IS 'API klíč pro autentizaci (měl by být šifrovaný)';
COMMENT ON COLUMN llm_providers.api_version IS 'Verze API používaná pro komunikaci';
COMMENT ON COLUMN llm_providers.api_key_required IS 'Indikátor, zda poskytovatel vyžaduje API klíč';
COMMENT ON COLUMN llm_providers.auth_type IS 'Typ autentizace (api_key, oauth, bearer, none)';
COMMENT ON COLUMN llm_providers.rate_limit IS 'Omezení počtu požadavků na API za minutu';
COMMENT ON COLUMN llm_providers.is_active IS 'Indikátor, zda je poskytovatel aktivní';
COMMENT ON COLUMN llm_providers.created_at IS 'Datum a čas vytvoření záznamu';
COMMENT ON COLUMN llm_providers.updated_at IS 'Datum a čas poslední aktualizace záznamu';
```

### Ukázkový SQL skript pro vytvoření tabulky llm_models
```sql
CREATE TABLE llm_models (
    model_id SERIAL PRIMARY KEY,
    provider_id INTEGER NOT NULL REFERENCES llm_providers(provider_id) ON DELETE CASCADE,
    model_name VARCHAR(100) NOT NULL,
    model_identifier VARCHAR(100) NOT NULL,
    context_length INTEGER,
    max_tokens_output INTEGER,
    default_temperature DECIMAL(3,2) DEFAULT 0.70 CHECK (default_temperature BETWEEN 0.0 AND 1.0),
    retry_attempts INTEGER DEFAULT 3,
    retry_delay INTEGER DEFAULT 1000,
    timeout INTEGER DEFAULT 30000,
    pricing_input DECIMAL(10,6),
    pricing_output DECIMAL(10,6),
    capabilities JSONB DEFAULT '{}',
    is_default BOOLEAN NOT NULL DEFAULT FALSE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(provider_id, model_identifier)
);

CREATE INDEX idx_llm_models_provider_id ON llm_models(provider_id);
CREATE INDEX idx_llm_models_is_active ON llm_models(is_active);
CREATE UNIQUE INDEX idx_llm_models_default ON llm_models (provider_id) WHERE is_default = TRUE;
CREATE INDEX idx_llm_models_capabilities ON llm_models USING GIN (capabilities);

COMMENT ON TABLE llm_models IS 'Tabulka obsahující informace o LLM modelech nabízených jednotlivými poskytovateli';
COMMENT ON COLUMN llm_models.model_id IS 'Unikátní identifikátor modelu';
COMMENT ON COLUMN llm_models.provider_id IS 'Cizí klíč odkazující na poskytovatele';
COMMENT ON COLUMN llm_models.model_name IS 'Uživatelsky přívětivý název modelu';
COMMENT ON COLUMN llm_models.model_identifier IS 'Technický identifikátor modelu pro volání API';
COMMENT ON COLUMN llm_models.context_length IS 'Maximální délka kontextu v tokenech';
COMMENT ON COLUMN llm_models.max_tokens_output IS 'Maximální počet výstupních tokenů';
COMMENT ON COLUMN llm_models.default_temperature IS 'Výchozí hodnota teploty (0.0-1.0)';
COMMENT ON COLUMN llm_models.retry_attempts IS 'Počet opakovaných pokusů při selhání';
COMMENT ON COLUMN llm_models.retry_delay IS 'Pauza mezi opakovanými pokusy (ms)';
COMMENT ON COLUMN llm_models.timeout IS 'Maximální doba čekání na odpověď (ms)';
COMMENT ON COLUMN llm_models.pricing_input IS 'Cena za 1000 vstupních tokenů v USD';
COMMENT ON COLUMN llm_models.pricing_output IS 'Cena za 1000 výstupních tokenů v USD';
COMMENT ON COLUMN llm_models.capabilities IS 'JSONB objekt s podporovanými schopnostmi modelu';
COMMENT ON COLUMN llm_models.is_default IS 'Indikátor, zda je model výchozí pro poskytovatele';
COMMENT ON COLUMN llm_models.is_active IS 'Indikátor, zda je model aktivní';
COMMENT ON COLUMN llm_models.created_at IS 'Datum a čas vytvoření záznamu';
COMMENT ON COLUMN llm_models.updated_at IS 'Datum a čas poslední aktualizace záznamu';
```
