# API reference pro přístup k LLM poskytovatelům a modelům

Tato dokumentace obsahuje podrobný popis třídy `LlmDirectDbService` pro přímý přístup k LLM poskytovatelům a modelům v PostgreSQL databázi.

## Obsah
1. [Úvod](#úvod)
2. [Databázov<PERSON> tabulky](#databázové-tabulky)
3. [Inicializace připojení](#inicializace-připojení)
4. [Pr<PERSON><PERSON> s poskytovateli](#práce-s-poskytovateli)
5. [Pr<PERSON>ce s modely](#práce-s-modely)
6. [Práce s cache](#práce-s-cache)
7. [Práce s embedding modely](#práce-s-embedding-modely)
8. [Ošetření chyb](#ošetření-chyb)
9. [Příklady použití](#příklady-použití)
10. [<PERSON><PERSON><PERSON> funkce a omezení](#další-funkce-a-omezení)

## Úvod

Třída `LlmDirectDbService` poskytuje přímý přístup k databázovým tabulkám `llm_providers` a `llm_models` bez nutnosti použití REST API. Je určena primárně pro frontend aplikace, které potřebují načítat a ukládat konfigurace poskytovatelů LLM a jejich modelů.

### Umístění třídy
```
/gent/db/llm_db_service.py
```

### Import třídy
```python
from gent.db.llm_db_service import LlmDirectDbService
```

## Databázové tabulky

Tato služba pracuje s následujícími tabulkami:

### llm_providers
Tabulka obsahující informace o poskytovatelích LLM služeb:

| Sloupec          | Typ           | Popis                                   |
|------------------|---------------|----------------------------------------|
| provider_id      | SERIAL        | Primární klíč                           |
| provider_name    | VARCHAR(100)  | Unikátní název poskytovatele            |
| api_base_url     | VARCHAR(255)  | Základní URL pro API požadavky          |
| api_key          | TEXT          | API klíč poskytovatele                  |
| api_version      | VARCHAR(50)   | Verze API                               |
| api_key_required | BOOLEAN       | Zda je API klíč vyžadován               |
| auth_type        | VARCHAR(30)   | Typ autentizace (api_key, oauth, bearer)|
| rate_limit       | INTEGER       | Limit požadavků za minutu               |
| is_active        | BOOLEAN       | Zda je poskytovatel aktivní             |
| created_at       | TIMESTAMP     | Datum a čas vytvoření                   |
| updated_at       | TIMESTAMP     | Datum a čas poslední aktualizace        |

### llm_models
Tabulka obsahující informace o modelech:

| Sloupec             | Typ           | Popis                                  |
|---------------------|---------------|---------------------------------------|
| model_id            | SERIAL        | Primární klíč                          |
| provider_id         | INTEGER       | Cizí klíč na llm_providers            |
| model_name          | VARCHAR(100)  | Název modelu                           |
| model_identifier    | VARCHAR(100)  | Identifikátor modelu pro API volání    |
| context_length      | INTEGER       | Maximální délka kontextu               |
| max_tokens_output   | INTEGER       | Maximální počet výstupních tokenů      |
| default_temperature | DECIMAL(3,2)  | Výchozí teplota (0.0-1.0)              |
| retry_attempts      | INTEGER       | Počet pokusů při selhání               |
| retry_delay         | INTEGER       | Prodleva mezi pokusy (ms)              |
| timeout             | INTEGER       | Timeout pro požadavek (ms)             |
| pricing_input       | DECIMAL(10,6) | Cena za 1000 vstupních tokenů          |
| pricing_output      | DECIMAL(10,6) | Cena za 1000 výstupních tokenů         |
| capabilities        | JSONB         | Schopnosti modelu jako JSON            |
| is_default          | BOOLEAN       | Zda je model výchozí pro poskytovatele |
| is_active           | BOOLEAN       | Zda je model aktivní                   |
| created_at          | TIMESTAMP     | Datum a čas vytvoření                  |
| updated_at          | TIMESTAMP     | Datum a čas poslední aktualizace       |

## Inicializace připojení

### Konstruktor
```python
def __init__(self):
    """Inicializace služby pro přímý přístup k databázi."""
```

Při inicializaci se nevytváří připojení k databázi, aby se zbytečně neblokovaly zdroje. Připojení je vytvořeno až při prvním požadavku.

### Získání připojení
```python
def _get_connection(self):
    """Získání připojení k databázi."""
```

Metoda zajistí:
1. Aktivaci virtuálního prostředí pro přístup k databázi
2. Vytvoření nového připojení, pokud dosud neexistuje
3. Vrácení existujícího připojení, pokud je stále aktivní

### Uzavření připojení
```python
def _close_connection(self):
    """Uzavření připojení k databázi."""
```

Metoda uzavře připojení, pokud existuje a není již uzavřeno.

## Práce s poskytovateli

### Získání seznamu poskytovatelů
```python
def get_providers(self) -> List[Dict[str, Any]]:
    """
    Získá seznam poskytovatelů LLM z databáze.
    
    Returns:
        List[Dict[str, Any]]: Seznam poskytovatelů jako slovníky
        
    Raises:
        Exception: Pokud se nepodaří získat data z databáze
    """
```

#### Příklad návratové hodnoty:
```python
[
    {
        "id": 1,
        "name": "OpenAI",
        "model": "GPT-4",  # Výchozí model poskytovatele
        "api_key": "********",
        "base_url": "https://api.openai.com/v1",
        "api_version": "1",
        "api_key_required": True,
        "auth_type": "api_key",
        "rate_limit": 3000,
        "is_active": True,
        "model_identifier": "gpt-4",
        "context_length": 8192,
        "max_tokens": 4096,
        "temperature": 0.7,
        "capabilities": {
            "chat": True,
            "completion": True,
            "function_calling": True
        },
        "created_at": "2025-04-19T06:00:00Z",
        "updated_at": "2025-04-19T06:00:00Z"
    },
    {
        "id": 2,
        "name": "Anthropic",
        # ...další atributy...
    }
]
```

### Získání detailu poskytovatele
```python
def get_provider_detail(self, provider_id: int) -> Optional[Dict[str, Any]]:
    """
    Získá detail poskytovatele podle ID.
    
    Args:
        provider_id: ID poskytovatele
        
    Returns:
        Optional[Dict[str, Any]]: Detail poskytovatele nebo None, pokud neexistuje
    """
```

#### Příklad návratové hodnoty:
```python
{
    "id": 1,
    "name": "OpenAI",
    "api_key": "********",
    "base_url": "https://api.openai.com/v1",
    "api_version": "1",
    "api_key_required": True,
    "auth_type": "api_key",
    "rate_limit": 3000,
    "is_active": True,
    "model": "GPT-4",  # Výchozí model
    "model_identifier": "gpt-4",
    "created_at": "2025-04-19T06:00:00Z",
    "updated_at": "2025-04-19T06:00:00Z",
    "models": {
        "GPT-4": {
            "model_id": 1,
            "model_identifier": "gpt-4",
            "context_length": 8192,
            "max_tokens": 4096,
            "default_temperature": 0.7,
            "retry_attempts": 3,
            "retry_delay": 1000,
            "timeout": 30000,
            "pricing_input": 0.03,
            "pricing_output": 0.06,
            "capabilities": {
                "chat": True,
                "completion": True,
                "function_calling": True
            },
            "is_default": True,
            "is_active": True,
            "created_at": "2025-04-19T06:00:00Z",
            "updated_at": "2025-04-19T06:00:00Z"
        },
        "GPT-4 Turbo": {
            # ...atributy modelu...
        }
    }
}
```

### Uložení poskytovatele
```python
def save_provider(self, provider_data: Dict[str, Any]) -> bool:
    """
    Uloží nebo aktualizuje poskytovatele v databázi a také synchronizuje modely.

    Args:
        provider_data: Data poskytovatele včetně modelů

    Returns:
        bool: True pokud se podařilo uložit, jinak False
    """
```

#### Vstupní formát:
```python
{
    "id": 1,  # Volitelné při vytváření nového poskytovatele
    "name": "OpenAI",  # Povinné
    "api_key": "sk-...",  # Volitelné
    "base_url": "https://api.openai.com/v1",  # Volitelné
    "api_version": "1",  # Volitelné
    "api_key_required": True,  # Volitelné, výchozí True
    "auth_type": "api_key",  # Volitelné, výchozí "api_key"
    "rate_limit": 3000,  # Volitelné
    "is_active": True,  # Volitelné, výchozí True
    "model": "GPT-4",  # Volitelné, výchozí model
    "models": {  # Volitelné, modely poskytovatele
        "GPT-4": {
            "model_identifier": "gpt-4",  # Povinné pokud je model uveden
            "context_length": 8192,  # Volitelné
            "max_tokens": 4096,  # Volitelné
            "default_temperature": 0.7,  # Volitelné, výchozí 0.7
            "retry_attempts": 3,  # Volitelné, výchozí 3
            "retry_delay": 1000,  # Volitelné, výchozí 1000
            "timeout": 30000,  # Volitelné, výchozí 30000
            "pricing_input": 0.03,  # Volitelné
            "pricing_output": 0.06,  # Volitelné
            "capabilities": {  # Volitelné
                "chat": True,
                "completion": True,
                "function_calling": True
            },
            "is_active": True  # Volitelné, výchozí True
        }
    }
}
```

#### Chování:
1. Pokud `id` existuje, aktualizuje existujícího poskytovatele
2. Pokud `id` neexistuje, ale `name` odpovídá existujícímu poskytovateli, aktualizuje
3. Jinak vytvoří nového poskytovatele

Pro modely platí:
1. Modely uvedené v `models` jsou aktualizovány nebo vytvořeny
2. Modely, které nejsou v `models` ale existují v databázi, zůstávají beze změn
3. Model uvedený v `model` je nastaven jako výchozí

### Nastavení výchozího poskytovatele
```python
def set_default_provider(self, provider_id: int) -> bool:
    """
    Nastaví poskytovatele jako výchozí v systémových nastaveních.
    
    Args:
        provider_id: ID poskytovatele
        
    Returns:
        bool: True pokud se podařilo nastavit, jinak False
    """
```

Metoda ukládá ID výchozího poskytovatele do tabulky `system_config` s klíčem `default_llm_provider`.

### Testování připojení k poskytovateli
```python
def test_provider_connection(self, provider_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Otestuje připojení k API poskytovatele.
    
    Args:
        provider_data: Data poskytovatele
        
    Returns:
        Dict[str, Any]: Výsledek testu s klíči 'success' a 'message'
    """
```

#### Příklad návratové hodnoty:
```python
{
    "success": True,
    "message": "Připojení k API poskytovatele OpenAI bylo úspěšné"
}
```

## Práce s modely

Modely jsou primárně spravovány prostřednictvím metody `save_provider()`, která zajišťuje synchronizaci modelů s databází. Není tedy potřeba samostatná metoda pro ukládání modelů.

Nicméně modely jsou přístupné:
1. Z metody `get_providers()` je dostupný výchozí model každého poskytovatele
2. Z metody `get_provider_detail()` jsou dostupné všechny modely daného poskytovatele

## Práce s cache

### Získání konfigurace cache
```python
def get_cache_config(self) -> Dict[str, Any]:
    """
    Získá konfiguraci LLM cache z databáze.
    
    Returns:
        Dict[str, Any]: Konfigurace cache z databáze
        
    Raises:
        Exception: Pokud se nepodaří získat data z databáze a v databázi není výchozí konfigurace
    """
```

#### Příklad návratové hodnoty:
```python
{
    "id": "cache-id",
    "enabled": True,
    "ttl": 3600,
    "backend": "redis",
    "connection_string": "redis://localhost:6379/0",
    "namespace": "llm_cache",
    "similarity_threshold": 0.95
}
```

### Uložení konfigurace cache
```python
def save_cache_config(self, cache_data: Dict[str, Any]) -> bool:
    """
    Uloží konfiguraci LLM cache do databáze.
    
    Args:
        cache_data: Data konfigurace cache
        
    Returns:
        bool: True pokud se podařilo uložit, jinak False
    """
```

#### Vstupní formát:
```python
{
    "id": "cache-id",  # Volitelné
    "enabled": True,  # Volitelné, výchozí True
    "ttl": 3600,  # Volitelné, výchozí 3600
    "backend": "redis",  # Volitelné, výchozí "redis"
    "connection_string": "redis://localhost:6379/0",  # Volitelné
    "namespace": "llm_cache",  # Volitelné, výchozí "llm_cache"
    "similarity_threshold": 0.95  # Volitelné, výchozí 0.95
}
```

## Práce s embedding modely

### Získání embedding modelů
```python
def get_embedding_models(self) -> Dict[str, Dict[str, Any]]:
    """
    Získá embedding modely z databáze.
    
    Returns:
        Dict[str, Dict[str, Any]]: Slovník embedding modelů, kde klíčem je ID modelu
        
    Raises:
        Exception: Pokud se nepodaří získat data z databáze
    """
```

#### Příklad návratové hodnoty:
```python
{
    "openai-embedding": {
        "id": "openai-id",
        "provider": "openai",
        "model": "text-embedding-3-large",
        "api_key": "********",
        "base_url": null,
        "dimension": 1536,
        "normalized": True,
        "batch_size": 100,
        "timeout": 60,
        "is_default": True
    },
    "local-embedding": {
        "id": "local-id",
        "provider": "local",
        "model": "all-MiniLM-L6-v2",
        "api_key": null,
        "base_url": null,
        "dimension": 384,
        "normalized": True,
        "batch_size": 32,
        "timeout": 30,
        "is_default": False
    }
}
```

### Uložení embedding modelu
```python
def save_embedding_model(self, model_data: Dict[str, Any]) -> bool:
    """
    Uloží nebo aktualizuje embedding model v databázi.
    
    Args:
        model_data: Data embedding modelu
        
    Returns:
        bool: True pokud se podařilo uložit, jinak False
    """
```

#### Vstupní formát:
```python
{
    "id": "openai-embedding",  # Volitelné
    "provider": "openai",  # Povinné
    "model": "text-embedding-3-large",  # Povinné
    "api_key": "sk-...",  # Volitelné
    "base_url": null,  # Volitelné
    "dimension": 1536,  # Volitelné, výchozí 1536
    "normalized": True,  # Volitelné, výchozí True
    "batch_size": 100,  # Volitelné, výchozí 32
    "timeout": 60,  # Volitelné, výchozí 60
    "is_default": True  # Volitelné, výchozí False
}
```

## Ošetření chyb

### Typy vyhazovaných výjimek
- **Exception** - Obecná výjimka při problémech s databází
- **json.JSONDecodeError** - Při neplatném formátu JSON dat

### Logování
Třída používá standardní Python knihovnu logging. Události jsou logovány do loggeru `gent.db.llm_db_service` s různými úrovněmi:
- **INFO** - Běžné operace
- **WARNING** - Neočekávaná, ale ne kritická situace
- **ERROR** - Chyba, která brání dokončení operace

### Ochrana citlivých dat
API klíče nejsou nikdy v celém znění vraceny zpět klientovi. Při čtení z databáze jsou vždy maskovány jako `********`.

## Příklady použití

### Získání seznamu poskytovatelů
```python
from gent.db.llm_db_service import LlmDirectDbService

llm_service = LlmDirectDbService()
providers = llm_service.get_providers()

for provider in providers:
    print(f"Poskytovatel: {provider['name']}, Výchozí model: {provider['model']}")
```

### Aktualizace poskytovatele s novým modelem
```python
from gent.db.llm_db_service import LlmDirectDbService

llm_service = LlmDirectDbService()

# Načteme detail poskytovatele
provider_id = 1
provider = llm_service.get_provider_detail(provider_id)

if provider:
    # Přidáme nový model
    provider["models"]["Nový model"] = {
        "model_identifier": "new-model",
        "context_length": 100000,
        "max_tokens": 16000,
        "default_temperature": 0.8,
        "capabilities": {
            "chat": True,
            "vision": True
        }
    }
    
    # Změníme výchozí model
    provider["model"] = "Nový model"
    
    # Uložíme změny
    success = llm_service.save_provider(provider)
    
    if success:
        print("Poskytovatel byl úspěšně aktualizován")
    else:
        print("Aktualizace poskytovatele selhala")
```

### Vytvoření nového poskytovatele
```python
from gent.db.llm_db_service import LlmDirectDbService

llm_service = LlmDirectDbService()

# Definujeme nového poskytovatele
new_provider = {
    "name": "Nový poskytovatel",
    "api_key": "api_key_123",
    "base_url": "https://api.novy-poskytovatel.com/v1",
    "api_version": "1.0",
    "auth_type": "api_key",
    "models": {
        "Model A": {
            "model_identifier": "model-a",
            "context_length": 16000,
            "max_tokens": 2000,
            "capabilities": {
                "chat": True,
                "completion": True
            }
        },
        "Model B": {
            "model_identifier": "model-b",
            "context_length": 32000,
            "max_tokens": 4000,
            "capabilities": {
                "chat": True,
                "vision": True
            }
        }
    },
    "model": "Model B"  # Výchozí model
}

# Uložíme nového poskytovatele
success = llm_service.save_provider(new_provider)

if success:
    print("Nový poskytovatel byl úspěšně vytvořen")
else:
    print("Vytvoření poskytovatele selhalo")
```

## Další funkce a omezení

### Transakce
Pro operace, které zasahují do více tabulek (např. ukládání poskytovatele a jeho modelů), jsou použity databázové transakce, které zajišťují konzistenci dat.

### Automatické vytváření výchozích záznamů
Pokud při volání `get_provider_detail()` poskytovatel nemá žádné modely, je automaticky vytvořen výchozí model.

Podobně při volání `get_cache_config()` a `get_embedding_models()`, pokud v databázi nejsou žádné záznamy, jsou vytvořeny výchozí záznamy.

### Aktivace virtuálního prostředí
Před každým připojením k databázi je aktivováno virtuální prostředí pomocí funkce `activate_venv()` z modulu `gent.db.init_db_env`. To zajišťuje, že jsou k dispozici všechny potřebné balíčky pro práci s databází.

### Uzavření připojení
Připojení k databázi není automaticky uzavíráno po každé operaci. Místo toho je ponecháno otevřené pro další požadavky. Destruktor (`__del__`) zajišťuje, že připojení bude uzavřeno, když instance služby bude zničena.
