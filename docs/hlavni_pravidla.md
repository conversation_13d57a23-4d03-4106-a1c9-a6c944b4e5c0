# HLAVNÍ PRAVIDLA PRO AI LLM SYSTÉMY

Tento dokument obsahuje základní pravidla a pokyny, <PERSON><PERSON><PERSON> AI LLM systém musí načíst a dodržovat před začátkem jakékoliv činnosti.

## PRACOVNÍ POSTUP

- VŽDY VYTVOŘ STRUKTUROVANÝ SEZNAM ÚKOLŮ (TASK LIST) PŘED ZAHÁJENÍM PRÁCE:
  1. DEFINUJ VŠECHNY POŽADOVANÉ ÚPRAVY A OPRAVY
  2. ROZDĚL SLOŽITÉ ÚKOLY NA MENŠÍ KROKY
  3. PŘIŘAĎ KAŽDÉMU ÚKOLU PRIORITU
  4. POSTUPUJ SYSTEMATICKY PODLE SEZNAMU
  5. OZNA<PERSON>UJ ÚKOLY JAKO DOKONČENÉ PO JEJICH SPLNĚNÍ
  6. PRAVIDELNĚ AKTUALIZUJ SEZNAM
- PO DOKONČENÍ KAŽDÉHO ÚKOLU VYTVOŘ:
  1. DETAILNÍ DOKUMENTACI V MARKDOWN FORMÁTU
  2. SAMOSTATNÝ MARKDOWN SOUBOR S NÁVODEM NA POUŽITÍ KÓDU
  3. VYSVĚTLUJÍCÍ MARKDOWN DOKUMENT S POPISEM FUNKCÍ A PRINCIPŮ

## STYLOVÁNÍ A DESIGN

- STYLE MUSÍ BÝT VŽDY TMAVÝ
- STYLE NEPATŘÍ NIKDY DO KÓDU, MUSÍ MÍT ZVLÁŠŤ CSS SOUBOR S ADEKVÁTNÍM JMÉNEM
- JAVASCRIPT NIKDY NEPATŘÍ DO KOMPONENTY, MUSÍ MÍT ZVLÁŠŤ JS SOUBOR SE STEJNÝM JMÉNEM JAKO KOMPONENTA
- Dodržuj konzistentní barevné schéma a typografii v celém projektu
- Zajisti responzivní design pro různé velikosti obrazovek
- Používej semantické HTML elementy pro lepší přístupnost

## PROSTŘEDÍ A NÁSTROJE

- VŽDY SI ZAPÍNEJ: `source /opt/gent/venv/bin/activate`
- Kontroluj verze používaných knihoven a jejich kompatibilitu
- Používej virtuální prostředí pro izolaci závislostí
- Dodržuj princip nejmenších oprávnění při přístupu k systémovým zdrojům
- ŽÁDNÁ ZE SLUŽEB NEPOUŽÍVÁ DOCKER - vše běží nativně na systému

## SYSTÉMOVÉ SLUŽBY

- API server (gent-api) je systemd služba - restart pomocí `systemctl restart gent-api`
- PostgreSQL je systemd služba - restart pomocí `systemctl restart postgresql`
- Frontend je spouštěn pomocí skriptu `./run_frontend.sh`
- Detailní dokumentace systémových služeb je v souboru `docs/system_services.md`

## ARCHITEKTURA A STRUKTURA PROJEKTU

- DŮSLEDNĚ ODDĚLUJ TŘI ZÁKLADNÍ VRSTVY SYSTÉMU:
  1. DATOVÁ VRSTVA - pouze v databázi, nikdy v kódu
  2. APLIKAČNÍ VRSTVA - byznys logika a zpracování dat
  3. PREZENTAČNÍ VRSTVA - uživatelské rozhraní a zobrazení
- NEUKLÁDEJ SOUBORY DO KOŘENOVÉHO ADRESÁŘE (/opt/gent)
- POUŽÍVEJ VHODNOU STRUKTURU ADRESÁŘŮ:
  1. `/micro_services/` - pro všechny mikroslužby
  2. `/gent/` - pro hlavní aplikační kód
  3. `/config/` - pouze pro konfigurační soubory (ne data)
  4. `/docs/` - pro veškerou dokumentaci
  5. `/frontend-vue/` - pro UI komponenty
  6. `/tests/` - pro všechny testy
- PŘI VYTVÁŘENÍ NOVÝCH KOMPONENT VŽDY DODRŽUJ ZAVEDENOU STRUKTURU
- MODULY A SLUŽBY MUSÍ BÝT IZOLOVANÉ A NEZÁVISLÉ

## DATOVÉ PRÁCE

- V KÓDU NESMÍ BÝT NIKDY ULOŽENA DATA - DATA JSOU POUZE V DATABÁZI!
- KRITICKÁ KONFIGURACE (API KLÍČE, HESLA) POUZE V .ENV SOUBORECH NEBO PROMĚNNÝCH PROSTŘEDÍ
- MOCK DATA JSOU PŘÍSNĚ ZAKÁZÁNA!
- V JSON NESMÍ BÝT KOMENTÁŘE
- KONFIGURAČNÍ SOUBORY POUŽÍVEJ POUZE PRO NASTAVENÍ APLIKACE, NIKDY PRO UKLÁDÁNÍ DAT
- VŠECHNY ENTITY, UŽIVATELSKÉ PREFERENCE A INFORMACE UKLÁDEJ DO DATABÁZE
- PŘI NAČÍTÁNÍ DAT VŽDY POUŽÍVEJ DATABÁZOVÉ SLUŽBY, NIKDY NEPŘISTUPUJ K DATŮM NAPŘÍMO
- Validuj vstupní data před jejich zpracováním
- Používej parametrizované dotazy při práci s databázemi
- Šifruj citlivá data při přenosu i ukládání
- Implementuj řádné ošetření null hodnot a prázdných řetězců

## TESTOVÁNÍ

- UDĚLEJ SI TESTY A SPUSŤ SI JE!
- TESTUJ SI SÁM!
- Piš jednotkové testy pro všechny klíčové funkce
- Implementuj integrační testy pro ověření součinnosti komponent
- Používej automatizované testování při každé změně kódu
- Testuj hraničí případy a neočekávané vstupy
- Měř a vyhodnocuj pokrytí kódu testy

## LOGY

- LOGY SI VŽDY ČTI SÁM
- MAŽ SI OBSAH LOGŮ SÁM
- Loguj s různými úrovněmi závažnosti (debug, info, warning, error, critical)
- Zaznamenávej časové značky a kontextové informace
- Neukládej do logů citlivá data ani přihlašovací údaje
- Implementuj rotaci logů podle velikosti nebo časového intervalu
- Navrhni mechanismus pro upozornění na kritické chyby

## POVINNÉ SERVERY A SLUŽBY

POUŽÍVEJ VŽDY MCP SERVERY:
- fetch
- perplexity-ask
- tavily
- filesystem
- memory
- sequentialthinking
- brave-search

## OŠETŘENÍ CHYB

- Implementuj robustní zachycení a zpracování výjimek
- Poskytuj srozumitelné chybové zprávy uživatelům
- Loguj detailní informace o chybách pro vývojáře
- Zabraň úniku citlivých informací v chybových hlášeních
- Navrhni strategii pro zotavení z chyb a selhání
- Implementuj mechanismy pro sledování a analýzu častých chyb

## OPTIMALIZACE VÝKONU

- Minimalizuj počet síťových požadavků
- Optimalizuj dotazy do databáze
- Implementuj vhodné cachování dat
- Používej asynchronní zpracování pro náročné operace
- Monitoruj využití zdrojů (CPU, paměť, disk)
- Identifikuj a refaktoruj neefektivní části kódu
- Testuj výkon pod různou zátěží

## FORMÁTOVÁNÍ KÓDU

- Dodržuj jednotný styl formátování v celém projektu
- Používej smysluplné názvy proměnných, funkcí a tříd
- Organizuj kód do logických modulů a tříd
- Dodržuj principy SOLID a DRY
- Používej automatizované nástroje pro kontrolu kvality kódu
- Omez složitost funkcí a metod (max. 30-50 řádků)
- Komentuj komplexní části kódu a vysvětluj "proč", ne "co"

## ZPŮSOB KOMUNIKACE S UŽIVATELEM

- Poskytuj jasné a stručné zprávy a instrukce
- Implementuj konzistentní chybové hlášky
- Navrhni intuitivní rozhraní pro zadávání příkazů
- Poskytuj zpětnou vazbu o stavu prováděných operací
- Nabízej nápovědu a dokumentaci přímo v systému
- Umožni uživateli zvolit preferovaný jazyk komunikace
- Respektuj soukromí uživatele a informuj o zpracování dat

## OBECNÉ

- DĚLEJ SI SÁM TESTY
- SÁM SI VŠECHNO DĚLEJ!

- Aplikuj principy agilního vývoje
- Pravidelně refaktoruj kód pro zlepšení čitelnosti a údržby
- Dodržuj princip postupného vylepšování místo velkých změn
- Sdílej znalosti a postupy s ostatními vývojáři
- Uč se z chyb a neustále zlepšuj své postupy
