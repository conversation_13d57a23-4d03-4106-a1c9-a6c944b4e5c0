# Datab<PERSON><PERSON><PERSON><PERSON> tabulky pro LLM poskytovatele a modely

## <PERSON><PERSON><PERSON><PERSON> systému

Tento dokument popisuje databázové schéma pro ukládání a správu poskytovatelů LLM (Language Learning Models) a jejich modelů v systému Gent. Databázové tabulky jsou navrženy tak, aby um<PERSON><PERSON>ovaly flexibilní konfiguraci, snadnou správu a efektivní využití různých AI modelů od různých poskytovatelů.

Systém se skládá ze dvou hlavních tabulek:
- `llm_providers` - ukládá informace o poskytovatelích LLM služeb
- `llm_models` - ukládá informace o konkrétních modelech nabízených jednotlivými poskytovateli

### Vztah mezi tabulkami

Mezi tabulkami existuje vztah 1:N (jeden-k-mnoha):
- <PERSON><PERSON> poskytovate<PERSON> (`llm_providers`) může nabízet mnoho modelů
- <PERSON><PERSON><PERSON><PERSON> model (`llm_models`) pat<PERSON><PERSON> právě jednomu poskytovateli

```mermaid
erDiagram
    LLM_PROVIDERS ||--o{ LLM_MODELS : "poskytuje"
    
    LLM_PROVIDERS {
        serial provider_id PK
        varchar(100) provider_name UK
        varchar(255) api_base_url
        text api_key
        varchar(50) api_version
        boolean api_key_required
        varchar(30) auth_type
        integer rate_limit
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }
    
    LLM_MODELS {
        serial model_id PK
        integer provider_id FK
        varchar(100) model_name
        varchar(100) model_identifier
        integer context_length
        integer max_tokens_output
        decimal default_temperature
        integer retry_attempts
        integer retry_delay
        integer timeout
        decimal pricing_input
        decimal pricing_output
        jsonb capabilities
        boolean is_default
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }
```

## Tabulka: llm_providers

### Účel a využití

Tabulka `llm_providers` slouží k ukládání informací o poskytovatelích LLM služeb, jako jsou OpenAI, Anthropic, Cohere a další. Tato tabulka je základním prvkem celého systému a umožňuje:

1. Centralizovanou správu všech podporovaných poskytovatelů AI
2. Ukládání přístupových údajů pro jednotlivé poskytovatele
3. Konfiguraci základních parametrů pro komunikaci s API poskytovatelů
4. Aktivaci/deaktivaci jednotlivých poskytovatelů bez nutnosti jejich odstranění

Správa poskytovatelů probíhá prostřednictvím webového rozhraní, kde administrátoři mohou přidávat nové poskytovatele, upravovat stávající a deaktivovat nebo mazat ty, které již nejsou potřeba.

### Struktura tabulky

| Sloupec | Datový typ | Nulový | Výchozí | Popis |
|---------|------------|--------|---------|-------|
| provider_id | SERIAL | Ne | - | Primární klíč, unikátní identifikátor poskytovatele |
| provider_name | VARCHAR(100) | Ne | - | Název poskytovatele (např. "OpenAI", "Anthropic"), musí být unikátní |
| api_base_url | VARCHAR(255) | Ano | NULL | Základní URL pro volání API poskytovatele |
| api_key | TEXT | Ano | NULL | API klíč pro autentizaci (měl by být šifrovaný nebo bezpečně uložený) |
| api_version | VARCHAR(50) | Ano | NULL | Verze API používaná pro komunikaci |
| api_key_required | BOOLEAN | Ne | TRUE | Indikátor, zda poskytovatel vyžaduje API klíč |
| auth_type | VARCHAR(30) | Ano | 'api_key' | Typ autentizace (api_key, oauth, bearer, none) |
| rate_limit | INTEGER | Ano | NULL | Omezení počtu požadavků na API za minutu |
| is_active | BOOLEAN | Ne | TRUE | Indikátor, zda je poskytovatel aktivní |
| created_at | TIMESTAMP WITH TIME ZONE | Ne | CURRENT_TIMESTAMP | Datum a čas vytvoření záznamu |
| updated_at | TIMESTAMP WITH TIME ZONE | Ne | CURRENT_TIMESTAMP | Datum a čas poslední aktualizace záznamu |

### Klíče a omezení

- **Primární klíč**: `provider_id` - automaticky generované číselné ID
- **Unikátní omezení**: `provider_name` - zajišťuje, že nelze vytvořit dva poskytovatele se stejným názvem
- **Check omezení**: `auth_type IN ('api_key', 'oauth', 'bearer', 'none')` - zajišťuje platné hodnoty pro typ autentizace

### Indexy

- **Primární index**: na sloupci `provider_id`
- **Unikátní index**: na sloupci `provider_name`
- **Index**: na sloupci `is_active` - pro rychlé filtrování aktivních poskytovatelů

### Bezpečnostní aspekty

API klíče jsou kritické bezpečnostní údaje, které by měly být chráněny. Doporučení:

1. Ukládat API klíče v šifrované podobě pomocí funkce pgcrypto nebo podobné
2. Omezit přístup k tabulce pouze na nezbytné role
3. Logovat všechny změny API klíčů pro audit
4. Zvážit ukládání klíčů v externím správci tajemství s pouze referencemi v databázi

### Typické operace

#### Vložení nového poskytovatele
```sql
INSERT INTO llm_providers (provider_name, api_base_url, api_key, api_version, api_key_required, auth_type, rate_limit, is_active)
VALUES ('OpenAI', 'https://api.openai.com/v1', 'sk_...', '1', TRUE, 'api_key', 3000, TRUE);
```

#### Aktualizace poskytovatele
```sql
UPDATE llm_providers
SET api_base_url = 'https://new-api-endpoint.com',
    api_key = 'new_api_key',
    updated_at = CURRENT_TIMESTAMP
WHERE provider_id = 1;
```

#### Deaktivace poskytovatele
```sql
UPDATE llm_providers
SET is_active = FALSE,
    updated_at = CURRENT_TIMESTAMP
WHERE provider_id = 1;
```

#### Odstranění poskytovatele (včetně všech jeho modelů)
```sql
DELETE FROM llm_providers
WHERE provider_id = 1;
-- Díky CASCADE omezení budou automaticky odstraněny i všechny související modely
```

#### Získání seznamu aktivních poskytovatelů
```sql
SELECT provider_id, provider_name, api_base_url
FROM llm_providers
WHERE is_active = TRUE
ORDER BY provider_name;
```

## Tabulka: llm_models

### Účel a využití

Tabulka `llm_models` slouží k ukládání informací o jednotlivých LLM modelech, které jsou dostupné od registrovaných poskytovatelů. Tato tabulka umožňuje:

1. Evidenci všech dostupných modelů v systému
2. Konfiguraci specifických parametrů pro každý model
3. Označení výchozích modelů pro jednotlivé poskytovatele
4. Aktivaci/deaktivaci modelů bez nutnosti jejich odstranění
5. Uchování technických a cenových informací o modelech
6. Flexibilní ukládání specifických schopností modelů prostřednictvím JSONB

Správa modelů probíhá prostřednictvím webového rozhraní, kde administrátoři mohou přidávat nové modely k existujícím poskytovatelům, upravovat jejich parametry, označovat výchozí modely a deaktivovat nebo mazat modely, které již nejsou potřeba.

### Struktura tabulky

| Sloupec | Datový typ | Nulový | Výchozí | Popis |
|---------|------------|--------|---------|-------|
| model_id | SERIAL | Ne | - | Primární klíč, unikátní identifikátor modelu |
| provider_id | INTEGER | Ne | - | Cizí klíč odkazující na poskytovatele v tabulce llm_providers |
| model_name | VARCHAR(100) | Ne | - | Název modelu pro zobrazení uživatelům (např. "GPT-4 Turbo") |
| model_identifier | VARCHAR(100) | Ne | - | Technický identifikátor pro volání API (např. "gpt-4-turbo") |
| context_length | INTEGER | Ano | NULL | Maximální délka kontextu v tokenech |
| max_tokens_output | INTEGER | Ano | NULL | Maximální počet výstupních tokenů |
| default_temperature | DECIMAL(3,2) | Ano | 0.70 | Výchozí hodnota teploty pro generování (0.0-1.0) |
| retry_attempts | INTEGER | Ano | 3 | Počet opakovaných pokusů při selhání volání |
| retry_delay | INTEGER | Ano | 1000 | Pauza mezi opakovanými pokusy v milisekundách |
| timeout | INTEGER | Ano | 30000 | Maximální doba čekání na odpověď v milisekundách |
| pricing_input | DECIMAL(10,6) | Ano | NULL | Cena za 1000 vstupních tokenů v USD |
| pricing_output | DECIMAL(10,6) | Ano | NULL | Cena za 1000 výstupních tokenů v USD |
| capabilities | JSONB | Ano | '{}' | Schopnosti modelu jako JSON objekt |
| is_default | BOOLEAN | Ne | FALSE | Indikátor, zda je model výchozí pro daného poskytovatele |
| is_active | BOOLEAN | Ne | TRUE | Indikátor, zda je model aktivní |
| created_at | TIMESTAMP WITH TIME ZONE | Ne | CURRENT_TIMESTAMP | Datum a čas vytvoření záznamu |
| updated_at | TIMESTAMP WITH TIME ZONE | Ne | CURRENT_TIMESTAMP | Datum a čas poslední aktualizace záznamu |

### Klíče a omezení

- **Primární klíč**: `model_id` - automaticky generované číselné ID
- **Cizí klíč**: `provider_id` - reference na tabulku `llm_providers(provider_id)` s CASCADE omezením
- **Unikátní omezení**: Kombinace `provider_id` a `model_identifier` - zajišťuje, že nelze vytvořit dva modely se stejným identifikátorem u jednoho poskytovatele
- **Check omezení**: `default_temperature BETWEEN 0.0 AND 1.0` - zajišťuje platný rozsah hodnot pro teplotu

### Indexy

- **Primární index**: na sloupci `model_id`
- **Index cizího klíče**: na sloupci `provider_id` - pro rychlé spojování s tabulkou poskytovatelů
- **Unikátní index**: na kombinaci sloupců `provider_id` a `model_identifier`
- **Částečný index**: na sloupci `is_default` WHERE `is_default = TRUE` - pro efektivní kontrolu, že každý poskytovatel má maximálně jeden výchozí model
- **Index**: na sloupci `is_active` - pro rychlé filtrování aktivních modelů

### Speciální sloupce

#### Sloupec capabilities (JSONB)

Sloupec `capabilities` používá PostgreSQL datový typ JSONB pro flexibilní ukládání schopností modelu. Tento přístup umožňuje přidávat nové vlastnosti bez nutnosti měnit schéma databáze. Typická struktura dat v tomto sloupci může vypadat takto:

```json
{
  "completion": true,
  "chat": true,
  "embedding": false,
  "fine_tuning": true,
  "vision": true,
  "streaming": true,
  "function_calling": true,
  "supported_languages": ["en", "cs", "de", "fr", "es"],
  "special_features": {
    "code_generation": true,
    "tool_use": true
  }
}
```

#### Sloupec is_default

Sloupec `is_default` slouží k označení výchozího modelu pro každého poskytovatele. Pro zajištění, že každý poskytovatel má maximálně jeden výchozí model, je implementováno částečné indexové omezení:

```sql
CREATE UNIQUE INDEX idx_llm_models_default
ON llm_models (provider_id)
WHERE is_default = TRUE;
```

Toto omezení zajistí, že pro každého poskytovatele může existovat pouze jeden model označený jako výchozí.

### Typické operace

#### Vložení nového modelu
```sql
INSERT INTO llm_models (
  provider_id, model_name, model_identifier, context_length,
  max_tokens_output, default_temperature, retry_attempts,
  retry_delay, timeout, pricing_input, pricing_output,
  capabilities, is_default, is_active
)
VALUES (
  1, 'GPT-4 Turbo', 'gpt-4-turbo', 128000,
  4096, 0.7, 3,
  1000, 60000, 0.01, 0.03,
  '{"completion": true, "chat": true, "vision": true, "streaming": true}',
  FALSE, TRUE
);
```

#### Označení modelu jako výchozího (s automatickým odznačením předchozího výchozího)
```sql
-- Nejprve odznačíme aktuální výchozí model pro daného poskytovatele
UPDATE llm_models
SET is_default = FALSE,
    updated_at = CURRENT_TIMESTAMP
WHERE provider_id = 1 AND is_default = TRUE;

-- Poté označíme nový výchozí model
UPDATE llm_models
SET is_default = TRUE,
    updated_at = CURRENT_TIMESTAMP
WHERE model_id = 5;
```

#### Aktualizace parametrů modelu
```sql
UPDATE llm_models
SET context_length = 16000,
    max_tokens_output = 2048,
    pricing_input = 0.005,
    pricing_output = 0.015,
    capabilities = capabilities || '{"vision": true}'::jsonb,
    updated_at = CURRENT_TIMESTAMP
WHERE model_id = 3;
```

#### Deaktivace modelu
```sql
UPDATE llm_models
SET is_active = FALSE,
    updated_at = CURRENT_TIMESTAMP
WHERE model_id = 2;
```

#### Získání seznamu modelů pro konkrétního poskytovatele
```sql
SELECT m.model_id, m.model_name, m.model_identifier, m.is_default, m.is_active
FROM llm_models m
JOIN llm_providers p ON m.provider_id = p.provider_id
WHERE p.provider_name = 'OpenAI' AND m.is_active = TRUE
ORDER BY m.is_default DESC, m.model_name;
```

#### Získání výchozího modelu pro poskytovatele
```sql
SELECT m.model_id, m.model_name, m.model_identifier
FROM llm_models m
WHERE m.provider_id = 1 AND m.is_default = TRUE AND m.is_active = TRUE;
```

## Relace mezi tabulkami

### Referenční integrita

Cizí klíč `provider_id` v tabulce `llm_models` zajišťuje referenční integritu s tabulkou `llm_providers`. Toto omezení je definováno s CASCADE volbou pro operace UPDATE a DELETE:

```sql
ALTER TABLE llm_models
ADD CONSTRAINT fk_llm_models_provider
FOREIGN KEY (provider_id)
REFERENCES llm_providers(provider_id)
ON DELETE CASCADE
ON UPDATE CASCADE;
```

Tato konfigurace zajišťuje:
1. Při odstranění poskytovatele se automaticky odstraní všechny jeho modely
2. Při změně ID poskytovatele se reference automaticky aktualizují

### Typické JOIN dotazy

#### Získání podrobných informací o všech aktivních modelech a jejich poskytovatelích
```sql
SELECT p.provider_name,
       m.model_name,
       m.model_identifier,
       m.context_length,
       m.is_default,
       m.capabilities
FROM llm_models m
JOIN llm_providers p ON m.provider_id = p.provider_id
WHERE p.is_active = TRUE AND m.is_active = TRUE
ORDER BY p.provider_name, m.is_default DESC, m.model_name;
```

#### Získání statistiky počtu modelů podle poskytovatelů
```sql
SELECT p.provider_name,
       COUNT(m.model_id) AS total_models,
       SUM(CASE WHEN m.is_active THEN 1 ELSE 0 END) AS active_models
FROM llm_providers p
LEFT JOIN llm_models m ON p.provider_id = m.provider_id
GROUP BY p.provider_name
ORDER BY total_models DESC;
```

## API rozhraní pro správu tabulek

Tabulky `llm_providers` a `llm_models` budou v budoucnu plně obsluhovány prostřednictvím dedikovaného API rozhraní, které umožní kompletní CRUD (Create, Read, Update, Delete) operace. Toto API zajistí jednotný přístup k datům a umožní integraci s různými částmi systému.

### Plánované API endpointy

#### Pro tabulku llm_providers:

- `GET /api/llm/providers` - Získání seznamu všech poskytovatelů
- `GET /api/llm/providers/{id}` - Získání detailu konkrétního poskytovatele
- `POST /api/llm/providers` - Vytvoření nového poskytovatele
- `PUT /api/llm/providers/{id}` - Aktualizace existujícího poskytovatele
- `DELETE /api/llm/providers/{id}` - Odstranění poskytovatele a všech jeho modelů

#### Pro tabulku llm_models:

- `GET /api/llm/models` - Získání seznamu všech modelů
- `GET /api/llm/models/{id}` - Získání detailu konkrétního modelu
- `GET /api/llm/providers/{provider_id}/models` - Získání modelů konkrétního poskytovatele
- `POST /api/llm/models` - Vytvoření nového modelu
- `PUT /api/llm/models/{id}` - Aktualizace existujícího modelu
- `PUT /api/llm/models/{id}/set-default` - Nastavení modelu jako výchozího
- `DELETE /api/llm/models/{id}` - Odstranění modelu

### Zabezpečení API

API rozhraní bude implementovat následující bezpečnostní opatření:

1. Autentizace pomocí JWT tokenů nebo API klíčů
2. Autorizace založená na rolích (RBAC)
3. Validace vstupních dat pro prevenci SQL injection a dalších útoků
4. Rate limiting pro prevenci DoS útoků
5. Logování všech operací pro auditní účely

### Formát dat

API bude používat JSON formát pro všechny požadavky a odpovědi. Příklad struktury dat pro vytvoření nového poskytovatele:

```json
{
  "provider_name": "OpenAI",
  "api_base_url": "https://api.openai.com/v1",
  "api_key": "sk_...",
  "api_version": "1",
  "api_key_required": true,
  "auth_type": "api_key",
  "rate_limit": 3000,
  "is_active": true
}
```

### Integrace s webovým rozhraním

Webové rozhraní pro správu poskytovatelů a modelů bude plně integrováno s tímto API. Administrátoři budou moci provádět všechny CRUD operace prostřednictvím přehledného uživatelského rozhraní, které na pozadí bude využívat popsané API endpointy.

### Zpracování chyb

API bude implementovat standardizované zpracování chyb s odpovídajícími HTTP status kódy a detailními chybovými zprávami, které usnadní debugování a integraci:

- 400 Bad Request - Neplatná data nebo parametry
- 401 Unauthorized - Chybějící nebo neplatná autentizace
- 403 Forbidden - Nedostatečná oprávnění pro operaci
- 404 Not Found - Požadovaný zdroj neexistuje
- 409 Conflict - Konflikt s existujícími daty (např. duplicitní názvy)
- 500 Internal Server Error - Interní chyba serveru

Každá chybová odpověď bude obsahovat detailní popis problému a případně i návrh řešení.

## Rozšiřitelnost a údržba

### Budoucí rozšíření

Navržené schéma je flexibilní a umožňuje následující rozšíření bez narušení stávající funkcionality:

1. **Další atributy poskytovatelů**: Lze snadno přidat nové sloupce pro dodatečné informace o poskytovatelích
2. **Další atributy modelů**: Lze přidat nové sloupce pro specifické vlastnosti modelů
3. **Nové schopnosti modelů**: Díky JSONB sloupci `capabilities` lze přidávat nové schopnosti bez změny schématu
4. **Historie využití**: Lze vytvořit související tabulku pro sledování využití jednotlivých modelů
5. **Statistiky úspěšnosti**: Lze přidat tabulku pro sledování úspěšnosti volání API pro jednotlivé modely

### Migrace a zálohy

Pro zajištění bezpečnosti dat během změn schématu je doporučeno:

1. Vždy vytvořit zálohu tabulek před prováděním strukturálních změn
2. Používat transakce pro všechny změny schématu
3. Testovat migrace na vývojovém prostředí před nasazením do produkce
4. Udržovat skriptovou historii všech změn schématu pro možnost rollbacku

### Optimalizace výkonu

Pro optimální výkon při práci s tabulkami je doporučeno:

1. Pravidelně provádět VACUUM a ANALYZE pro aktualizaci statistik
2. Monitorovat využití indexů a případně přidat další indexy dle vzorů dotazování
3. Zvážit partitioning tabulky `llm_models` dle `provider_id` při větším počtu záznamů
4. Využívat GIN indexy pro efektivní vyhledávání v JSONB sloupci `capabilities`

## Bezpečnostní doporučení

Pro zajištění bezpečnosti citlivých dat v tabulkách je doporučeno:

1. Šifrovat API klíče a další citlivé údaje
2. Implementovat role-based access control (RBAC) pro přístup k tabulkám
3. Auditovat všechny změny v tabulkách, zejména změny API klíčů
4. Pravidelně rotovat API klíče dle bezpečnostních politik
5. Zvážit použití row-level security (RLS) pro granulární kontrolu přístupu
