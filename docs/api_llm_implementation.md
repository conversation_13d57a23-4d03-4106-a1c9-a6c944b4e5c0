# Task list pro implementaci API pro LLM poskytovatele a modely

Tento dokument obsahuje podrobný task list pro implementaci FastAPI rozhraní pro přístup k databázovým tabulkám `llm_providers` a `llm_models`. Implementace je rozd<PERSON>lena do logických celků s jasně definovanými kroky.

## 1. Přípravná fáze

### Nastavení prostředí a závislostí
- [ ] Vytvořit virtuální prostředí pro projekt (pokud již neexistuje)
- [ ] Instalovat pož<PERSON><PERSON><PERSON> b<PERSON>:
  ```bash
  pip install fastapi uvicorn sqlalchemy pydantic alembic python-jose pydantic-settings python-multipart
  ```
- [ ] Vytvořit soubor `requirements.txt` s verzemi všech závislostí
- [ ] Nastavit strukturu projektu:
  ```
  /gent
    /api
      /routes
        __init__.py
        llm_providers.py
        llm_models.py
      /schemas
        __init__.py
        llm_providers.py
        llm_models.py
      /services
        __init__.py
        llm_providers_service.py
        llm_models_service.py
      /core
        __init__.py
        config.py
        security.py
        database.py
      /middleware
        __init__.py
        auth.py
        logging.py
        rate_limit.py
      /utils
        __init__.py
        validators.py
        pagination.py
      __init__.py
      main.py
      dependencies.py
  ```

### Konfigurace databáze
- [ ] Aktualizovat soubor `gent/api/core/database.py` s připojením k PostgreSQL
- [ ] Nastavit SQLAlchemy modely odpovídající tabulkám `llm_providers` a `llm_models`
- [ ] Nakonfigurovat Alembic pro migrace databáze (pokud je to potřeba)
- [ ] Implementovat základní databázové operace (CRUD)

### Konfigurace autentizace a autorizace
- [ ] Implementovat JWT autentizaci v `gent/api/core/security.py`
- [ ] Vytvořit middleware pro ověření JWT tokenů v `gent/api/middleware/auth.py`
- [ ] Implementovat RBAC (role-based access control) pro různé úrovně přístupu
- [ ] Vytvořit závislosti pro kontrolu oprávnění v `gent/api/dependencies.py`

## 2. Implementace schémat a modelů

### Vytvoření Pydantic schémat pro `llm_providers`
- [ ] Vytvořit základní schémata v `gent/api/schemas/llm_providers.py`:
  - `ProviderBase` - základní schéma sdílené mezi vytvářením a aktualizací
  - `ProviderCreate` - schéma pro vytváření nových poskytovatelů
  - `ProviderUpdate` - schéma pro aktualizaci existujících poskytovatelů
  - `Provider` - schéma pro odpovědi API
  - `ProviderList` - schéma pro seznam poskytovatelů
- [ ] Implementovat validátory pro jednotlivá pole

### Vytvoření Pydantic schémat pro `llm_models`
- [ ] Vytvořit základní schémata v `gent/api/schemas/llm_models.py`:
  - `ModelBase` - základní schéma sdílené mezi vytvářením a aktualizací
  - `ModelCreate` - schéma pro vytváření nových modelů
  - `ModelUpdate` - schéma pro aktualizaci existujících modelů
  - `Model` - schéma pro odpovědi API
  - `ModelList` - schéma pro seznam modelů
- [ ] Implementovat validátory pro jednotlivá pole, zejména pro JSONB pole `capabilities`

### Vytvoření SQLAlchemy modelů
- [ ] Vytvořit ORM modely pro tabulky:
  - `LLMProvider` - model pro `llm_providers` tabulku
  - `LLMModel` - model pro `llm_models` tabulku
- [ ] Definovat vztahy mezi modely (one-to-many)
- [ ] Implementovat metody pro konverzi mezi ORM modely a Pydantic schématy

## 3. Implementace servisní vrstvy

### Servisní vrstva pro `llm_providers`
- [ ] Vytvořit `gent/api/services/llm_providers_service.py` s následujícími metodami:
  - `get_providers(db, skip, limit, filters)` - získání seznamu poskytovatelů
  - `get_provider(db, provider_id)` - získání detailu poskytovatele
  - `create_provider(db, provider)` - vytvoření nového poskytovatele
  - `update_provider(db, provider_id, provider)` - aktualizace poskytovatele
  - `delete_provider(db, provider_id)` - smazání poskytovatele
- [ ] Implementovat ošetření chyb a výjimek
- [ ] Implementovat validaci vstupních dat
- [ ] Zajistit logování operací

### Servisní vrstva pro `llm_models`
- [ ] Vytvořit `gent/api/services/llm_models_service.py` s následujícími metodami:
  - `get_models(db, skip, limit, filters)` - získání seznamu modelů
  - `get_model(db, model_id)` - získání detailu modelu
  - `get_provider_models(db, provider_id, skip, limit, filters)` - získání modelů poskytovatele
  - `create_model(db, model)` - vytvoření nového modelu
  - `update_model(db, model_id, model)` - aktualizace modelu
  - `set_default_model(db, model_id)` - nastavení výchozího modelu
  - `delete_model(db, model_id)` - smazání modelu
  - `search_models_by_capabilities(db, capabilities, provider_id, is_active)` - vyhledávání modelů podle schopností
  - `get_default_model(db, provider_id)` - získání výchozího modelu poskytovatele
- [ ] Implementovat ošetření chyb a výjimek
- [ ] Implementovat validaci vstupních dat
- [ ] Implementovat logiku pro nastavení výchozího modelu (transakčně)
- [ ] Zajistit logování operací

## 4. Implementace API endpointů

### Routery pro `llm_providers`
- [ ] Vytvořit `gent/api/routes/llm_providers.py` s následujícími endpointy:
  - `GET /api/v1/llm/providers` - seznam poskytovatelů
  - `GET /api/v1/llm/providers/{id}` - detail poskytovatele
  - `POST /api/v1/llm/providers` - vytvoření poskytovatele
  - `PUT /api/v1/llm/providers/{id}` - aktualizace poskytovatele
  - `PATCH /api/v1/llm/providers/{id}` - částečná aktualizace poskytovatele
  - `DELETE /api/v1/llm/providers/{id}` - smazání poskytovatele
- [ ] Implementovat připojení k servisní vrstvě
- [ ] Implementovat ověření oprávnění pro jednotlivé endpointy
- [ ] Nastavit správné návratové kódy a formát odpovědí
- [ ] Implementovat stránkování, filtrování a řazení

### Routery pro `llm_models`
- [ ] Vytvořit `gent/api/routes/llm_models.py` s následujícími endpointy:
  - `GET /api/v1/llm/models` - seznam modelů
  - `GET /api/v1/llm/models/{id}` - detail modelu
  - `GET /api/v1/llm/providers/{provider_id}/models` - modely poskytovatele
  - `POST /api/v1/llm/models` - vytvoření modelu
  - `PUT /api/v1/llm/models/{id}` - aktualizace modelu
  - `PATCH /api/v1/llm/models/{id}` - částečná aktualizace modelu
  - `PUT /api/v1/llm/models/{id}/set-default` - nastavení výchozího modelu
  - `DELETE /api/v1/llm/models/{id}` - smazání modelu
  - `GET /api/v1/llm/models/search` - vyhledávání modelů podle schopností
  - `GET /api/v1/llm/providers/{provider_id}/default-model` - výchozí model poskytovatele
- [ ] Implementovat připojení k servisní vrstvě
- [ ] Implementovat ověření oprávnění pro jednotlivé endpointy
- [ ] Nastavit správné návratové kódy a formát odpovědí
- [ ] Implementovat stránkování, filtrování a řazení

### Hlavní aplikace a nastavení CORS
- [ ] Aktualizovat `gent/api/main.py` pro nastavení FastAPI aplikace
- [ ] Zahrnout všechny routery do aplikace
- [ ] Nastavit CORS policy pro přístup z webového rozhraní
- [ ] Nastavit základní middleware (logování, autentizace, rate limiting)
- [ ] Implementovat globální ošetření chyb

## 5. Optimalizace a bezpečnost

### Implementace cachování
- [ ] Nastavit integraci s Redis pro cachování
- [ ] Implementovat cachování pro často používané GET endpointy
- [ ] Zajistit invalidaci cache při změně dat
- [ ] Nastavit TTL (time-to-live) pro cachované položky

### Zabezpečení API
- [ ] Implementovat rate limiting pro prevenci DoS útoků
- [ ] Zajistit ošetření všech vstupních dat proti injection útokům
- [ ] Implementovat sanitizaci dat proti XSS útokům
- [ ] Implementovat logování přístupů a změn pro audit

### Optimalizace výkonu
- [ ] Implementovat asyncio/await pro asynchronní zpracování požadavků
- [ ] Optimalizovat dotazy do databáze
- [ ] Nastavit connection pooling pro databázi
- [ ] Implementovat bulk operace pro hromadné změny dat

## 6. Testování

### Unit testy
- [ ] Vytvořit unit testy pro schémata a validátory
- [ ] Vytvořit unit testy pro servisní vrstvu
- [ ] Vytvořit unit testy pro utility a helpery
- [ ] Implementovat mock objekty pro databázi a externí služby

### Integrační testy
- [ ] Vytvořit integrační testy pro API endpointy
- [ ] Implementovat testovací databázi s fixtures
- [ ] Otestovat autentizaci a autorizaci
- [ ] Otestovat rate limiting a cachování

### End-to-end testy
- [ ] Vytvořit E2E testy simulující reálné scénáře použití
- [ ] Otestovat integraci s webovým rozhraním
- [ ] Otestovat správnost odpovědí a návratových kódů
- [ ] Implementovat performance testy pro ověření výkonu API

## 7. Dokumentace

### API dokumentace
- [ ] Nastavit Swagger/OpenAPI dokumentaci
- [ ] Přidat podrobné popisy všech endpointů, parametrů a odpovědí
- [ ] Implementovat příklady požadavků a odpovědí
- [ ] Zajistit, aby dokumentace byla dostupná na `/docs` endpointu

### Uživatelská dokumentace
- [ ] Vytvořit uživatelskou dokumentaci pro vývojáře
- [ ] Popsat autentizaci a autorizaci
- [ ] Vytvořit příklady použití API v různých programovacích jazycích
- [ ] Dokumentovat chybové kódy a jejich význam

### Vývojářská dokumentace
- [ ] Dokumentovat architekturu API
- [ ] Popsat strukturu projektu a dependency injection
- [ ] Vytvořit průvodce pro přidávání nových endpointů
- [ ] Dokumentovat principy a best practices pro přispívání do projektu

## 8. Nasazení

### Příprava CI/CD pipeline
- [ ] Vytvořit Dockerfile pro containerizaci API
- [ ] Nastavit CI/CD pipeline (např. GitHub Actions, GitLab CI)
- [ ] Implementovat automatické testy jako součást pipeline
- [ ] Nastavit automatické nasazení po úspěšném buildu

### Produkční nasazení
- [ ] Připravit produkční konfiguraci (environment variables)
- [ ] Nastavit logování a monitoring
- [ ] Implementovat health check endpointy
- [ ] Zajistit správu secrets (API klíče, přihlašovací údaje k DB)
- [ ] Nastavit zálohovací strategii pro databázi

## Implementační příklady

### Příklad SQLAlchemy modelů

```python
# gent/api/models/models.py
from sqlalchemy import Column, Integer, String, Boolean, ForeignKey, DateTime, Text, Numeric
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from gent.api.core.database import Base

class LLMProvider(Base):
    __tablename__ = "llm_providers"

    provider_id = Column(Integer, primary_key=True, index=True)
    provider_name = Column(String(100), unique=True, nullable=False)
    api_base_url = Column(String(255))
    api_key = Column(Text)
    api_version = Column(String(50))
    api_key_required = Column(Boolean, default=True, nullable=False)
    auth_type = Column(String(30), default="api_key")
    rate_limit = Column(Integer)
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationship with LLMModel
    models = relationship("LLMModel", back_populates="provider", cascade="all, delete-orphan")

class LLMModel(Base):
    __tablename__ = "llm_models"
    
    model_id = Column(Integer, primary_key=True, index=True)
    provider_id = Column(Integer, ForeignKey("llm_providers.provider_id", ondelete="CASCADE"), nullable=False)
    model_name = Column(String(100), nullable=False)
    model_identifier = Column(String(100), nullable=False)
    context_length = Column(Integer)
    max_tokens_output = Column(Integer)
    default_temperature = Column(Numeric(3, 2), default=0.7)
    retry_attempts = Column(Integer, default=3)
    retry_delay = Column(Integer, default=1000)
    timeout = Column(Integer, default=30000)
    pricing_input = Column(Numeric(10, 6))
    pricing_output = Column(Numeric(10, 6))
    capabilities = Column(JSONB, default={})
    is_default = Column(Boolean, default=False, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)
    
    # Relationship with LLMProvider
    provider = relationship("LLMProvider", back_populates="models")
    
    # Table constraints are defined in the migration script or via DDL
    __table_args__ = (
        # Unique constraint for provider_id and model_identifier
        # Partial index for is_default is defined in the migration script
    )
```

### Příklad Pydantic schémat

```python
# gent/api/schemas/llm_providers.py
from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field, validator

class ProviderBase(BaseModel):
    provider_name: str = Field(..., min_length=1, max_length=100)
    api_base_url: Optional[str] = Field(None, max_length=255)
    api_version: Optional[str] = Field(None, max_length=50)
    api_key_required: bool = True
    auth_type: str = Field("api_key", max_length=30)
    rate_limit: Optional[int] = None
    is_active: bool = True
    
    @validator('auth_type')
    def validate_auth_type(cls, v):
        allowed_types = ['api_key', 'oauth', 'bearer', 'none']
        if v not in allowed_types:
            raise ValueError(f'auth_type must be one of {allowed_types}')
        return v

class ProviderCreate(ProviderBase):
    api_key: Optional[str] = None

class ProviderUpdate(BaseModel):
    provider_name: Optional[str] = Field(None, min_length=1, max_length=100)
    api_base_url: Optional[str] = Field(None, max_length=255)
    api_key: Optional[str] = None
    api_version: Optional[str] = Field(None, max_length=50)
    api_key_required: Optional[bool] = None
    auth_type: Optional[str] = Field(None, max_length=30)
    rate_limit: Optional[int] = None
    is_active: Optional[bool] = None
    
    @validator('auth_type')
    def validate_auth_type(cls, v):
        if v is None:
            return v
        allowed_types = ['api_key', 'oauth', 'bearer', 'none']
        if v not in allowed_types:
            raise ValueError(f'auth_type must be one of {allowed_types}')
        return v

class Provider(ProviderBase):
    provider_id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        orm_mode = True

class ProviderList(BaseModel):
    data: List[Provider]
    meta: dict
    
    class Config:
        orm_mode = True
```

### Příklad servisní vrstvy

```python
# gent/api/services/llm_providers_service.py
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from fastapi import HTTPException, status

from gent.api.models.models import LLMProvider
from gent.api.schemas.llm_providers import ProviderCreate, ProviderUpdate

def get_providers(
    db: Session, 
    skip: int = 0, 
    limit: int = 100,
    filters: Optional[Dict[str, Any]] = None
) -> List[LLMProvider]:
    """
    Get list of LLM providers with pagination and filtering
    """
    query = db.query(LLMProvider)
    
    # Apply filters if provided
    if filters:
        if 'is_active' in filters:
            query = query.filter(LLMProvider.is_active == filters['is_active'])
        
        if 'search' in filters and filters['search']:
            search = f"%{filters['search']}%"
            query = query.filter(LLMProvider.provider_name.ilike(search))
    
    # Apply pagination
    return query.offset(skip).limit(limit).all()

def get_provider(db: Session, provider_id: int) -> LLMProvider:
    """
    Get a specific LLM provider by ID
    """
    provider = db.query(LLMProvider).filter(LLMProvider.provider_id == provider_id).first()
    if not provider:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Provider not found")
    return provider

def create_provider(db: Session, provider: ProviderCreate) -> LLMProvider:
    """
    Create a new LLM provider
    """
    # Check if provider with same name already exists
    existing = db.query(LLMProvider).filter(LLMProvider.provider_name == provider.provider_name).first()
    if existing:
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=f"Provider with name '{provider.provider_name}' already exists"
        )
    
    # Create new provider
    db_provider = LLMProvider(**provider.dict())
    db.add(db_provider)
    db.commit()
    db.refresh(db_provider)
    return db_provider

def update_provider(db: Session, provider_id: int, provider: ProviderUpdate) -> LLMProvider:
    """
    Update an existing LLM provider
    """
    db_provider = get_provider(db, provider_id)
    
    # Check if trying to update name to existing name
    if provider.provider_name and provider.provider_name != db_provider.provider_name:
        existing = db.query(LLMProvider).filter(LLMProvider.provider_name == provider.provider_name).first()
        if existing:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=f"Provider with name '{provider.provider_name}' already exists"
            )
    
    # Update provider attributes
    update_data = provider.dict(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_provider, key, value)
    
    db.commit()
    db.refresh(db_provider)
    return db_provider

def delete_provider(db: Session, provider_id: int) -> None:
    """
    Delete an LLM provider
    """
    db_provider = get_provider(db, provider_id)
    db.delete(db_provider)
    db.commit()
```

### Příklad API endpointu

```python
# gent/api/routes/llm_providers.py
from typing import Optional, List
from fastapi import APIRouter, Depends, Query, Path, HTTPException, status
from sqlalchemy.orm import Session

from gent.api.core.database import get_db
from gent.api.dependencies import get_current_user, RoleChecker
from gent.api.schemas.llm_providers import Provider, ProviderCreate, ProviderUpdate, ProviderList
from gent.api.services import llm_providers_service

router = APIRouter(prefix="/api/v1/llm/providers", tags=["llm_providers"])

require_admin = RoleChecker(["admin"])
require_editor = RoleChecker(["admin", "editor"])
require_viewer = RoleChecker(["admin", "editor", "viewer"])

@router.get("/", response_model=ProviderList)
def get_providers(
    db: Session = Depends(get_db),
    current_user = Depends(require_viewer),
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100),
    is_active: Optional[bool] = None,
    search: Optional[str] = None,
    sort: Optional[str] = "provider_name",
    order: Optional[str] = "asc"
):
    """
    Get list of LLM providers with pagination and filtering
    """
    skip = (page - 1) * per_page
    
    # Build filters
    filters = {}
    if is_active is not None:
        filters['is_active'] = is_active
    if search:
        filters['search'] = search
    
    providers = llm_providers_service.get_providers(db, skip=skip, limit=per_page, filters=filters)
    
    # Get total count
    total = db.query(Provider).count()
    
    return {
        "data": providers,
        "meta": {
            "current_page": page,
            "per_page": per_page,
            "total_items": total,
            "total_pages": (total + per_page - 1) // per_page
        }
    }

@router.get("/{provider_id}", response_model=Provider)
def get_provider(
    provider_id: int = Path(..., ge=1),
    db: Session = Depends(get_db),
    current_user = Depends(require_viewer)
):
    """
    Get a specific LLM provider by ID
    """
    return llm_providers_service.get_provider(db, provider_id)

@router.post("/", response_model=Provider, status_code=status.HTTP_201_CREATED)
def create_provider(
    provider: ProviderCreate,
    db: Session = Depends(get_db),
    current_user = Depends(require_editor)
):
    """
    Create a new LLM provider
    """
    return llm_providers_service.create_provider(db, provider)

@router.put("/{provider_id}", response_model=Provider)
def update_provider(
    provider: ProviderUpdate,
    provider_id: int = Path(..., ge=1),
    db: Session = Depends(get_db),
    current_user = Depends(require_editor)
):
    """
    Update an existing LLM provider
    """
    return llm_providers_service.update_provider(db, provider_id, provider)

@router.patch("/{provider_id}", response_model=Provider)
def partial_update_provider(
    provider: ProviderUpdate,
    provider_id: int = Path(..., ge=1),
    db: Session = Depends(get_db),
    current_user = Depends(require_editor)
):
    """
    Partial update of an existing LLM provider
    """
    return llm_providers_service.update_provider(db, provider_id, provider)

@router.delete("/{provider_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_provider(
    provider_id: int = Path(..., ge=1),
    db: Session = Depends(get_db),
    current_user = Depends(require_admin)
):
    """
    Delete an LLM provider
    """
    llm_providers_service.delete_provider(db, provider_id)
    return None
```

### Příklad hlavního FastAPI aplikačního souboru

```python
# gent/api/main.py
from fastapi import FastAPI, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from fastapi.openapi.docs import get_swagger_ui_html

from gent.api.routes import llm_providers, llm_models
from gent.api.middleware import auth, logging, rate_limit
from gent.api.core.config import settings

app = FastAPI(
    title="Gent LLM API",
    description="API for managing LLM providers and models",
    version="1.0.0",
    docs_url=None,
    redoc_url=None,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add authentication middleware
app.add_middleware(auth.AuthMiddleware)

# Add rate limiting middleware
app.add_middleware(rate_limit.RateLimitMiddleware)

# Add logging middleware
app.add_middleware(logging.LoggingMiddleware)

# Include routers
app.include_router(llm_providers.router)
app.include_router(llm_models.router)

# Custom exception handler for validation errors
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content={
            "error": {
                "code": "VALIDATION_ERROR",
                "message": "Validation error in request data",
                "details": exc.errors()
            }
        }
    )

# Custom docs endpoints that require authentication
@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html(request: Request):
    return get_swagger_ui_html(
        openapi_url=app.openapi_url,
        title=app.title + " - Swagger UI",
        oauth2_redirect_url=app.swagger_ui_oauth2_redirect_url,
        swagger_js_url="/static/swagger-ui-bundle.js",
        swagger_css_url="/static/swagger-ui.css",
    )

# Root endpoint
@app.get("/")
async def root():
    return {"message": "Welcome to Gent LLM API"}

# Health check endpoint
@app.get("/health")
async def health():
    return {"status": "ok"}
