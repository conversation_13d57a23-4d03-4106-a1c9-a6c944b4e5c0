erDiagram
    llm_providers {
        int id PK
        varchar name
        varchar provider_name
        text api_key
        text base_url
        varchar api_version
        boolean api_key_required
        varchar auth_type
        int rate_limit
        boolean is_active
        timestamp created_at
        timestamp updated_at
        varchar model
        varchar model_identifier
        int context_length
        int max_tokens
        float temperature
        jsonb capabilities
    }

    llm_models {
        int model_id PK
        int provider_id FK
        varchar model_name
        varchar model_identifier
        int context_length
        int max_tokens_output
        jsonb capabilities
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }

    llm_providers ||--o{ llm_models : "has_models"
