graph TD
    subgraph Frontend
        A[ChatTest.vue] --> |loadProviders| B[llm_db.service.js]
        A --> |sendMessage| C[chat.service.js]
        D[Vue Router] --> A
    end

    subgraph API
        B --> |GET /api/db/llm/providers| E[LLM DB API]
        B --> |GET /api/db/llm/providers/:id| E
        C --> |POST /api/config/llm/test-llm| F[LLM Test API]
    end

    subgraph Backend
        E --> G[LLM DB Service]
        F --> H[LiteLLM Service]
        G --> I[(PostgreSQL DB)]
        H --> |OpenAI API| J[Externí LLM API]
        H --> |Anthropic API| J
        H --> |Google/Gemini API| J
    end

    style A fill:#b7c9e2,stroke:#7b8ab8
    style B fill:#fcbf49,stroke:#cc9a3a
    style C fill:#fcbf49,stroke:#cc9a3a
    style D fill:#b7c9e2,stroke:#7b8ab8
    style E fill:#70a288,stroke:#5c8570
    style F fill:#70a288,stroke:#5c8570
    style G fill:#588157,stroke:#4a6c49
    style H fill:#588157,stroke:#4a6c49
    style I fill:#3a5a40,stroke:#2c4530
    style J fill:#283618,stroke:#1a2410
