#!/usr/bin/env python3
"""
Test script pro OpenRouter API podle jejich dokumentace.
Testuje jak OpenAI SDK tak přímé API volání.
"""

import os
import json
import requests
from openai import OpenAI

def load_openrouter_key():
    """Načte OpenRouter API klíč z konfigurace."""
    try:
        # Možnost 1: z environment variables
        api_key = os.getenv('OPENROUTER_API_KEY')

        # Možnost 2: z databáze
        if not api_key:
            try:
                import psycopg2
                conn = psycopg2.connect(
                    host='localhost',
                    port=5432,
                    user='gent_app',
                    password='gent1234secure',
                    database='gentdb'
                )
                cursor = conn.cursor()
                cursor.execute('SELECT api_key FROM llm_providers WHERE provider_name = %s', ('Openrouter',))
                result = cursor.fetchone()
                if result:
                    api_key = result[0]
                cursor.close()
                conn.close()
            except Exception as e:
                print(f"Chyba při načítání z databáze: {e}")

        # Možnost 3: z konfiguračního souboru
        if not api_key:
            config_path = '/opt/gent/config/db.json'
            with open(config_path, 'r') as f:
                config = json.load(f)
                api_key = config.get('openrouter_api_key')

        # Možnost 4: z separátního souboru
        if not api_key:
            try:
                with open('/opt/gent/config/openrouter_key.txt', 'r') as f:
                    api_key = f.read().strip()
            except:
                pass

        return api_key
    except Exception as e:
        print(f"Chyba při načítání API klíče: {e}")
        return None

def test_openrouter_with_openai_sdk(api_key):
    """Test OpenRouter pomocí OpenAI SDK podle dokumentace."""
    print("\n🧪 Test #1: OpenRouter přes OpenAI SDK")
    print("-" * 50)

    try:
        client = OpenAI(
            base_url="https://openrouter.ai/api/v1",
            api_key=api_key,
        )

        completion = client.chat.completions.create(
            extra_headers={
                "HTTP-Referer": "http://**************:8000",  # GENT site URL
                "X-Title": "GENT v10",  # GENT site name
            },
            model="openai/gpt-4o",  # Test s OpenAI modelem přes OpenRouter
            messages=[
                {
                    "role": "user",
                    "content": "What is 2+2? Answer briefly."
                }
            ]
        )

        answer = completion.choices[0].message.content
        tokens_used = completion.usage.total_tokens if hasattr(completion, 'usage') else 0
        finish_reason = completion.choices[0].finish_reason

        print(f"✅ Odpověď: {answer}")
        print(f"📊 Tokeny: {tokens_used}")
        print(f"🏁 Finish reason: {finish_reason}")

        return True

    except Exception as e:
        print(f"❌ Chyba: {e}")
        return False

def test_openrouter_direct_api(api_key):
    """Test OpenRouter přímým API voláním podle dokumentace."""
    print("\n🧪 Test #2: OpenRouter přímé API volání")
    print("-" * 50)

    try:
        response = requests.post(
            url="https://openrouter.ai/api/v1/chat/completions",
            headers={
                "Authorization": f"Bearer {api_key}",
                "HTTP-Referer": "http://**************:8000",  # GENT site URL
                "X-Title": "GENT v10",  # GENT site name
                "Content-Type": "application/json"
            },
            data=json.dumps({
                "model": "openai/gpt-4o",  # Test s OpenAI modelem přes OpenRouter
                "messages": [
                    {
                        "role": "user",
                        "content": "What is 2+2? Answer briefly."
                    }
                ]
            })
        )

        if response.status_code == 200:
            result = response.json()
            answer = result['choices'][0]['message']['content']
            tokens_used = result.get('usage', {}).get('total_tokens', 0)
            finish_reason = result['choices'][0]['finish_reason']

            print(f"✅ Odpověď: {answer}")
            print(f"📊 Tokeny: {tokens_used}")
            print(f"🏁 Finish reason: {finish_reason}")

            return True
        else:
            print(f"❌ HTTP chyba: {response.status_code}")
            print(f"❌ Odpověď: {response.text}")
            return False

    except Exception as e:
        print(f"❌ Chyba: {e}")
        return False

def test_free_model(api_key):
    """Test free modelu z OpenRouter."""
    print("\n🧪 Test #3: OpenRouter free model")
    print("-" * 50)

    try:
        client = OpenAI(
            base_url="https://openrouter.ai/api/v1",
            api_key=api_key,
        )

        # Test s free modelem
        completion = client.chat.completions.create(
            extra_headers={
                "HTTP-Referer": "http://**************:8000",
                "X-Title": "GENT v10",
            },
            model="nousresearch/deephermes-3-mistral-24b-preview:free",  # Free model
            messages=[
                {
                    "role": "user",
                    "content": "Hello! What is your name?"
                }
            ]
        )

        answer = completion.choices[0].message.content
        tokens_used = completion.usage.total_tokens if hasattr(completion, 'usage') else 0
        finish_reason = completion.choices[0].finish_reason

        print(f"✅ Model: nousresearch/deephermes-3-mistral-24b-preview:free")
        print(f"✅ Odpověď: {answer}")
        print(f"📊 Tokeny: {tokens_used}")
        print(f"🏁 Finish reason: {finish_reason}")

        return True

    except Exception as e:
        print(f"❌ Chyba: {e}")
        return False

def main():
    """Hlavní funkce pro testování OpenRouter API."""
    print("🔬 TEST OPENROUTER API")
    print("=" * 60)
    print("Testování podle oficiální OpenRouter dokumentace")
    print()

    # Načtení API klíče
    api_key = load_openrouter_key()
    if not api_key:
        print("❌ Nepodařilo se načíst OpenRouter API klíč!")
        print("💡 Zkontroluj konfiguraci:")
        print("   - Environment variable: OPENROUTER_API_KEY")
        print("   - Config file: /opt/gent/config/db.json")
        print("   - Key file: /opt/gent/config/openrouter_key.txt")
        return

    print(f"🔑 API klíč načten: {api_key[:8]}...")

    # Testování různých způsobů volání
    results = {}

    # Test 1: OpenAI SDK
    results['openai_sdk'] = test_openrouter_with_openai_sdk(api_key)

    # Test 2: Přímé API
    results['direct_api'] = test_openrouter_direct_api(api_key)

    # Test 3: Free model
    results['free_model'] = test_free_model(api_key)

    # Shrnutí výsledků
    print("\n" + "=" * 60)
    print("📋 VÝSLEDKY TESTŮ:")

    successful = 0
    for test_name, success in results.items():
        status = "✅ FUNGUJE" if success else "❌ NEFUNGUJE"
        print(f"  {test_name}: {status}")
        if success:
            successful += 1

    print(f"\n📊 CELKEM: {successful}/{len(results)} testů úspěšných")

    if successful > 0:
        print("\n🎉 OpenRouter API funguje!")
        print("💡 Můžeme přidat OpenRouter modely do GENT")
    else:
        print("\n⚠️  OpenRouter API nefunguje")
        print("💡 Zkontroluj API klíč a připojení")

    print("\n💡 POZNÁMKY:")
    print("   - OpenRouter používá OpenAI-kompatibilní API")
    print("   - Modely mají prefix (např. 'openai/gpt-4o')")
    print("   - Free modely končí ':free'")
    print("   - Vyžaduje HTTP-Referer a X-Title headers")

if __name__ == "__main__":
    main()
