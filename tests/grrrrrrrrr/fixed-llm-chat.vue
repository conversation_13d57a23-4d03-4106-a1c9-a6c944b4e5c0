<template>
  <div class="llm-chat-test">
    <div class="card test-card">
      <h3>Test připojení k API</h3>

      <div class="test-config">
        <div class="config-row">
          <div class="form-group">
            <label for="test-provider">Poskytovatel</label>
            <select id="test-provider" class="form-select" v-model="selectedProvider">
              <option v-for="provider in providers" :key="provider.value" :value="provider.value">
                {{ provider.name }}
              </option>
            </select>
          </div>

          <div class="form-group">
            <label for="test-model">Model</label>
            <select id="test-model" class="form-select" v-model="selectedModel">
              <option v-for="model in availableModels" :key="model" :value="model">{{ model }}</option>
            </select>
          </div>

          <div class="refresh-button">
            <button class="btn btn-secondary" @click="refreshProviders" :disabled="loadingProviders">
              <i class="fas fa-sync-alt" :class="{ 'fa-spin': loadingProviders }"></i>
              {{ loadingProviders ? 'Načítání...' : 'Obnovit' }}
            </button>
            <div v-if="loadingProviders" class="loading-indicator"></div>
          </div>
        </div>

        <div v-if="providers.length === 0" class="no-providers-warning">
          <p>Žádní poskytovatelé nebyli nalezeni. Přidejte poskytovatele v sekci "Poskytovatelé LLM" níže.</p>
        </div>
      </div>

      <div class="chat-container">
        <div class="chat-input">
          <textarea
            v-model="userInput"
            placeholder="Napište zprávu pro otestování připojení..."
            @keydown.enter.prevent="sendMessage"
            :disabled="loading"
            ref="chatInput"
          ></textarea>
          <button class="send-button" @click="sendMessage" :disabled="!userInput.trim() || loading">
            <i class="fas fa-paper-plane"></i>
          </button>
        </div>

        <div class="chat-messages" ref="messagesContainer">
          <!-- Loading zpráva nahoře -->
          <div v-if="loading" class="loading-message">
            <div class="loading-dots">
              <div class="dot"></div>
              <div class="dot"></div>
              <div class="dot"></div>
            </div>
            <div class="loading-text">LLM odpovídá...</div>
          </div>

          <!-- Zprávy - nejnovější nahoře -->
          <div v-for="(message, index) in messages" :key="index" :class="['message', message.role]" :data-error="message.error">
            <div class="message-header">
              <span class="role-badge">{{ message.role === 'user' ? 'Vy' : 'LLM' }}</span>
              <span v-if="message.timestamp" class="timestamp">{{ formatTime(message.timestamp) }}</span>
            </div>
            <div class="message-content">{{ message.content }}</div>
            <div v-if="message.metadata" class="message-metadata">
              <div v-if="message.metadata.tokens" class="metadata-item">
                <span class="metadata-label">Tokeny:</span> {{ message.metadata.tokens }}
              </div>
              <div v-if="message.metadata.time" class="metadata-item">
                <span class="metadata-label">Čas:</span> {{ message.metadata.time.toFixed(2) }}s
              </div>
              <div v-if="message.metadata.model" class="metadata-item">
                <span class="metadata-label">Model:</span> {{ message.metadata.model }}
              </div>
            </div>
          </div>

          <!-- Empty state na konci (dole) -->
          <div v-if="messages.length === 0 && !loading" class="empty-state">
            <p>Zadejte zprávu pro otestování připojení k LLM API</p>
          </div>
        </div>
      </div>

      <div class="connection-status" :class="{
        success: connectionStatus === 'success',
        error: connectionStatus === 'error',
        warning: connectionStatus === 'warning'
      }">
        <span v-if="connectionStatus === 'success'" class="status-icon success-icon">✓</span>
        <span v-if="connectionStatus === 'error'" class="status-icon error-icon">✗</span>
        <span v-if="connectionStatus === 'warning'" class="status-icon warning-icon">⚠</span>

        <span v-if="connectionStatus === 'success'" class="status-message">Připojení k API je funkční</span>
        <span v-if="connectionStatus === 'error'" class="status-message">Chyba připojení k API: {{ errorMessage }}</span>
        <span v-if="connectionStatus === 'warning'" class="status-message">Varování: {{ errorMessage }}</span>
        <span v-if="connectionStatus === ''" class="status-message neutral">Otestujte připojení odesláním zprávy</span>
      </div>

      <div class="test-actions">
        <button class="btn btn-secondary" @click="clearChat">Vyčistit chat</button>
        <button class="btn btn-primary" @click="runQuickTest">Rychlý test</button>
      </div>
    </div>
  </div>
</template>

<script>
import llmService from '@/services/llm.service';
import { llmDbService } from '@/services/llm_db.service';

export default {
  name: 'LlmChatTest',

  props: {
    availableProviders: {
      type: Array,
      default: () => null
    }
  },

  data() {
    return {
      selectedProvider: 'openai',
      selectedModel: '',
      userInput: '',
      messages: [],
      loading: false,
      connectionStatus: '',
      errorMessage: '',
      providersMap: {},
      providersList: [],
      loadingProviders: false
    };
  },

  computed: {
    providers() {
      return this.providersList;
    },

    availableModels() {
      const providerDetail = this.providersMap[this.selectedProvider];
      if (!providerDetail) return [];

      if (providerDetail.models && Object.keys(providerDetail.models).length > 0) {
        console.log('Modely poskytovatele', this.selectedProvider, ':', Object.keys(providerDetail.models));
        return Object.keys(providerDetail.models);
      }
      else if (providerDetail.model) {
        console.log('Pouze jeden model pro', this.selectedProvider, ':', providerDetail.model);
        return [providerDetail.model];
      }

      return [];
    }
  },

  watch: {
    availableProviders: {
      immediate: true,
      handler(newProviders) {
        if (newProviders && newProviders.length > 0) {
          console.log('LlmChatTest: Přijaty nové dostupné modely z nadřazené komponenty', newProviders);
          this.processProvidersFromProps(newProviders);
        }
      }
    },

    selectedProvider() {
      if (this.availableModels.length > 0) {
        this.selectedModel = this.availableModels[0];
      } else {
        this.selectedModel = '';

        if (!this.providersMap[this.selectedProvider]) {
          this.loadProviderDetails(this.selectedProvider);
        }
      }
    },

    // Automatické scrollování nahoru při nových zprávách
    messages: {
      handler(newMessages, oldMessages) {
        console.log('Messages changed:', newMessages.length, 'messages');
        this.$nextTick(() => {
          this.scrollToTop();
        });
      },
      deep: true
    }
  },

  methods: {
    processProvidersFromProps(providersData) {
      if (!providersData || providersData.length === 0) {
        this.providersList = [];
        return;
      }

      this.providersList = providersData.map(provider => ({
        name: provider.name ? (provider.name.charAt(0).toUpperCase() + provider.name.slice(1)) : 'Neznámý',
        value: provider.name ? provider.name.toLowerCase() : 'neznamy'
      }));

      providersData.forEach(provider => {
        const providerName = provider.name.toLowerCase();

        this.providersMap[providerName] = {
          id: provider.id,
          name: provider.name,
          model: provider.model,
          api_key: provider.apiKey,
          base_url: provider.baseUrl,
          models: provider.models || {}
        };

        if (!this.providersMap[providerName].models) {
          this.providersMap[providerName].models = {};
        }

        if (provider.model && !this.providersMap[providerName].models[provider.model]) {
          this.providersMap[providerName].models[provider.model] = {
            context_window: 32000,
            max_tokens: 4096,
            capabilities: ['text'],
            suitable_for: ['general']
          };

          console.log(`Poskytovatel ${providerName} má pouze jeden model (${provider.model}), byl přidán do seznamu modelů.`);
        }
      });

      if (this.providersList.length > 0) {
        const defaultProvider = providersData.find(p => p.isDefault);
        this.selectedProvider = defaultProvider ?
          defaultProvider.name.toLowerCase() :
          this.providersList[0].value;
      }
    },

    async loadProviders() {
      if (this.availableProviders && this.availableProviders.length > 0) {
        this.processProvidersFromProps(this.availableProviders);
        return;
      }

      this.loadingProviders = true;

      try {
        const response = await llmDbService.getProviders();
        if (response.data && Array.isArray(response.data)) {
          this.providersList = response.data.map(provider => ({
            name: provider.name.charAt(0).toUpperCase() + provider.name.slice(1),
            value: provider.name.toLowerCase()
          }));

          if (this.providersList.length > 0) {
            this.selectedProvider = this.providersList[0].value;
            await this.loadProviderDetails(this.selectedProvider);
          }
        }
      } catch (error) {
        console.error('Chyba při načítání poskytovatelů LLM:', error);
        this.connectionStatus = 'error';
        this.errorMessage = 'Nepodařilo se načíst poskytovatele LLM';
      } finally {
        this.loadingProviders = false;
      }
    },

    async loadProviderDetails(providerName) {
      if (!providerName) return;

      try {
        if (this.providersMap[providerName] && this.providersMap[providerName].models) {
          return;
        }

        const providerId = `${providerName}-config`;

        const response = await llmDbService.getProviderDetail(providerId);
        if (response.data) {
          const providerData = response.data;

          if (providerData.model && (!providerData.models || Object.keys(providerData.models).length === 0)) {
            if (!providerData.models) {
              providerData.models = {};
            }

            providerData.models[providerData.model] = {
              context_window: 32000,
              max_tokens: 4096,
              capabilities: ['text'],
              suitable_for: ['general']
            };

            console.log(`Poskytovatel ${providerName} má pouze jeden model (${providerData.model}), byl přidán do seznamu modelů.`);
          }

          this.providersMap[providerName] = providerData;

          if (providerData.models && Object.keys(providerData.models).length > 0) {
            this.selectedModel = providerData.model || Object.keys(providerData.models)[0];
          } else if (providerData.model) {
            this.selectedModel = providerData.model;
          }
        }
      } catch (error) {
        console.error(`Chyba při načítání detailů poskytovatele ${providerName}:`, error);
      }
    },

    async refreshProviders() {
      if (this.availableProviders && this.availableProviders.length > 0) {
        this.processProvidersFromProps(this.availableProviders);
      } else {
        await this.loadProviders();
      }
    },

    async sendMessage() {
      if (!this.userInput.trim() || this.loading) {
        return;
      }

      const userText = this.userInput;
      this.userInput = '';
      this.loading = true;

      try {
        this.connectionStatus = '';
        this.errorMessage = '';

        const startTime = performance.now();

        const response = await llmService.testLlmConnection({
          prompt: userText,
          provider: this.selectedProvider,
          model: this.selectedModel
        });

        const duration = (performance.now() - startTime) / 1000;

        const userMessage = {
          role: 'user',
          content: userText,
          timestamp: new Date()
        };

        let assistantMessage;

        if (response.data.error) {
          assistantMessage = {
            role: 'assistant',
            content: response.data.text,
            timestamp: new Date(),
            error: true,
            metadata: {
              time: duration,
              error: response.data.error_message
            }
          };

          this.connectionStatus = 'error';
          this.errorMessage = response.data.error_message || 'Nepodařilo se připojit k API serveru';
        }
        else if (response.data.simulated) {
          assistantMessage = {
            role: 'assistant',
            content: response.data.text + "\n[SIMULOVANÁ ODPOVĚĎ - NENÍ SKUTEČNÉ PŘIPOJENÍ K API]",
            timestamp: new Date(),
            simulated: true,
            metadata: {
              tokens: response.data.tokens_used,
              time: duration,
              model: response.data.model,
              simulated: true
            }
          };

          this.connectionStatus = 'warning';
          this.errorMessage = 'Používá se simulovaný režim - API server není dostupný';
        }
        else {
          assistantMessage = {
            role: 'assistant',
            content: response.data.text,
            timestamp: new Date(),
            metadata: {
              tokens: response.data.tokens_used,
              time: duration,
              model: response.data.model
            }
          };

          this.connectionStatus = 'success';
        }

        // PŘIDÁNÍ NAHOŘE - nejdříve assistant odpověď, pak user dotaz
        this.messages.unshift(assistantMessage);
        this.messages.unshift(userMessage);

      } catch (error) {
        console.error('Chyba při testu LLM připojení:', error);

        const userMessage = {
          role: 'user',
          content: userText,
          timestamp: new Date()
        };

        const errorMessage = {
          role: 'assistant',
          content: 'Chyba při komunikaci s API: ' +
            (error.response?.data?.detail || error.message || 'Neznámá chyba'),
          timestamp: new Date(),
          error: true
        };

        // PŘIDÁNÍ NAHOŘE - nejdříve error odpověď, pak user dotaz
        this.messages.unshift(errorMessage);
        this.messages.unshift(userMessage);

        this.connectionStatus = 'error';
        this.errorMessage = error.response?.data?.detail || error.message || 'Neznámá chyba';
      } finally {
        this.loading = false;

        this.$nextTick(() => {
          this.$refs.chatInput?.focus();
        });
      }
    },

    runQuickTest() {
      this.userInput = 'Ahoj, tohle je rychlý test připojení. Odpověz jednou větou.';
      this.sendMessage();
    },

    clearChat() {
      this.messages = [];
      this.connectionStatus = '';
      this.errorMessage = '';
    },

    // Scrollování nahoru pro nové zprávy
    scrollToTop() {
      const chatContainer = this.$refs.messagesContainer;
      console.log('ScrollToTop called, container:', chatContainer);
      if (chatContainer) {
        console.log('Before scroll to top - scrollTop:', chatContainer.scrollTop);
        chatContainer.scrollTop = 0;
        console.log('After scroll to top - scrollTop:', chatContainer.scrollTop);
      } else {
        console.log('Chat container not found!');
      }
    },

    formatTime(date) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
  },

  mounted() {
    if (this.availableProviders && this.availableProviders.length > 0) {
      this.processProvidersFromProps(this.availableProviders);
    } else {
      this.loadProviders();
    }

    this.$nextTick(() => {
      this.$refs.chatInput?.focus();
    });
  }
};
</script>

<!-- Styly jsou odděleny do externího souboru -->
<style src="@/styles/llm-chat-test.css" scoped></style>