/*
  LLM Chat Test Styles
  Tmavý motiv pro test<PERSON>ní p<PERSON> k <PERSON>M API
*/

.llm-chat-test {
  width: 100%;
}

/* Karta testu */
.test-card {
  background-color: #1e1e1e;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.25);
  color: #e0e0e0;
  border: 1px solid #333;
}

.test-card h3 {
  color: #f0f0f0;
  font-size: 1.4rem;
  margin-top: 0;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #333;
  padding-bottom: 0.75rem;
}

/* Testovací konfigurace */
.test-config {
  margin-bottom: 1.5rem;
}

.config-row {
  display: flex;
  gap: 1rem;
  align-items: flex-end;
  flex-wrap: wrap;
}

.form-group {
  flex: 1;
  margin-bottom: 1rem;
  min-width: 150px;
}

.refresh-button {
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
}

.refresh-button button {
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.6rem 1rem;
}

.refresh-button button i {
  transition: transform 0.5s;
}

.refresh-button button:disabled {
  cursor: not-allowed;
  opacity: 0.7;
}

.refresh-button .loading-indicator {
  height: 3px;
  width: 100%;
  background-color: #2a2a2a;
  border-radius: 1.5px;
  overflow: hidden;
  margin-top: 5px;
}

.refresh-button .loading-indicator:after {
  content: '';
  display: block;
  width: 30%;
  height: 100%;
  background-color: #3182ce;
  animation: loading-bar 1.5s infinite ease-in-out;
}

@keyframes loading-bar {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(400%); }
}

.no-providers-warning {
  background-color: rgba(255, 193, 7, 0.1);
  border: 1px dashed #ffc107;
  border-radius: 4px;
  padding: 0.75rem;
  margin-top: 0.5rem;
  color: #f0b90b;
  font-size: 0.9rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #b0b0b0;
  font-size: 0.9rem;
}

.form-select {
  width: 100%;
  padding: 0.6rem;
  background-color: #2a2a2a;
  color: #e0e0e0;
  border: 1px solid #444;
  border-radius: 4px;
  font-size: 0.95rem;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23888' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.7rem center;
  background-size: 1rem;
}

.form-select:focus {
  outline: none;
  border-color: #5a8fee;
  box-shadow: 0 0 0 2px rgba(90, 143, 238, 0.2);
}

/* Chat kontejner */
.chat-container {
  background-color: #252525;
  border-radius: 6px;
  border: 1px solid #333;
  height: 350px;
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
}

/* Chat zprávy - OPRAVENO pro zprávy nahoře */
.chat-messages {
  flex-grow: 1;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  scrollbar-width: thin;
  scrollbar-color: #444 #2a2a2a;
  /* Důležité: Zajistí, že nové zprávy se objeví nahoře */
  justify-content: flex-start;
}

.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: #2a2a2a;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #444;
  border-radius: 3px;
}

/* Empty state - zobrazí se na konci/dole */
.empty-state {
  color: #777;
  text-align: center;
  margin-top: auto;
  padding: 2rem;
  font-style: italic;
}

/* Loading message - zobrazí se nahoře */
.loading-message {
  align-self: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: #888;
  margin: 1rem 0;
  /* Důležité: Loading zpráva bude nahoře */
  order: -1;
}

.message {
  padding: 0.8rem 1rem;
  border-radius: 6px;
  max-width: 90%;
  word-break: break-word;
}

.message.user {
  background-color: #2d3748;
  align-self: flex-end;
  border-bottom-right-radius: 0;
}

.message.assistant {
  background-color: #2c3e50;
  align-self: flex-start;
  border-bottom-left-radius: 0;
}

/* Použití data atributu místo error="true" */
.message.assistant[data-error="true"] {
  background-color: #5c2d2d;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.4rem;
  font-size: 0.8rem;
}

.role-badge {
  font-weight: 600;
  color: #a0a0a0;
}

.timestamp {
  color: #666;
}

.message-content {
  color: #e0e0e0;
  line-height: 1.4;
}

.message-metadata {
  margin-top: 0.6rem;
  font-size: 0.75rem;
  color: #888;
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.metadata-label {
  color: #999;
  font-weight: 600;
}

/* Chat input nahoře */
.chat-input {
  display: flex;
  padding: 0.75rem;
  border-bottom: 1px solid #333;
  background-color: #1e1e1e;
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
  /* Důležité: Input zůstane nahoře */
  order: -1;
}

.chat-input textarea {
  flex-grow: 1;
  padding: 0.6rem;
  background-color: #2a2a2a;
  color: #e0e0e0;
  border: 1px solid #444;
  border-radius: 4px;
  font-size: 0.95rem;
  resize: none;
  height: 2.5rem;
  min-height: 2.5rem;
  max-height: 6rem;
  line-height: 1.4;
}

.chat-input textarea:focus {
  outline: none;
  border-color: #5a8fee;
  box-shadow: 0 0 0 2px rgba(90, 143, 238, 0.2);
}

.send-button {
  margin-left: 0.5rem;
  background-color: #3182ce;
  color: white;
  border: none;
  border-radius: 4px;
  width: 2.5rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.send-button:hover {
  background-color: #2c5282;
}

.send-button:disabled {
  background-color: #2a4365;
  color: #a0aec0;
  cursor: not-allowed;
}

/* Loading animace */
.loading-dots {
  display: flex;
  gap: 4px;
}

.dot {
  width: 8px;
  height: 8px;
  background-color: #888;
  border-radius: 50%;
  animation: dot-pulse 1.5s infinite ease-in-out;
}

.dot:nth-child(2) {
  animation-delay: 0.2s;
}

.dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes dot-pulse {
  0%, 100% {
    transform: scale(0.8);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
}

.loading-text {
  font-size: 0.9rem;
  color: #999;
}

/* Status připojení */
.connection-status {
  margin-top: 20px;
  padding: 10px 15px;
  background-color: #2a2a2a;
  border-radius: 4px;
  display: flex;
  align-items: center;
}

.connection-status.success {
  background-color: rgba(40, 167, 69, 0.2);
  border: 1px solid #28a745;
}

.connection-status.warning {
  background-color: rgba(255, 193, 7, 0.2);
  border: 1px solid #ffc107;
}

.connection-status.error {
  background-color: rgba(220, 53, 69, 0.2);
  border: 1px solid #dc3545;
}

.status-icon {
  font-size: 1.2rem;
  margin-right: 0.75rem;
  font-weight: bold;
}

.success-icon {
  color: #38a169;
}

.error-icon {
  color: #e53e3e;
}

.status-message {
  color: #c0c0c0;
}

.status-message.neutral {
  color: #888;
  font-style: italic;
}

/* Akce testování */
.test-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
}

.btn {
  padding: 0.6rem 1.25rem;
  border-radius: 4px;
  font-weight: 500;
  font-size: 0.95rem;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s;
  border: none;
}

.btn:active {
  transform: translateY(1px);
}

.btn-primary {
  background-color: #3182ce;
  color: white;
}

.btn-primary:hover {
  background-color: #2b6cb0;
}

.btn-secondary {
  background-color: #4a5568;
  color: white;
}

.btn-secondary:hover {
  background-color: #2d3748;
}

/* Responzivní design */
@media (max-width: 768px) {
  .test-config {
    flex-direction: column;
    gap: 0.5rem;
  }

  .chat-container {
    height: 400px;
  }
}