"""
Testovací skript pro ověření mazání poskytovatele bez použití služeb z llm_db_service.py
"""

import os
import psycopg2
import json
import sys

def load_db_config():
    """Načte konfiguraci databáze ze souboru db.json."""
    config_path = os.path.join('/opt/gent/config', 'db.json')
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        return {
            "host": config.get("host", "localhost"),
            "port": config.get("port", 5432),
            "user": config.get("user"),
            "password": config.get("password"),
            "database": config.get("database")
        }
    except Exception as e:
        print(f"Chyba při načítání konfigurace: {str(e)}")
        return None

def delete_provider_direct(provider_id):
    """
    Smaže poskytovatele přímo bez použití třídy LlmDirectDbService.
    Slouží pro diagnostiku chyby při mazání poskytovatelů.
    """
    config = load_db_config()
    if not config:
        print("Nepodařilo se načíst konfiguraci databáze.")
        return False

    conn = None
    try:
        # Připojení k databázi
        print(f"Připojuji se k databázi {config['database']} jako uživatel {config['user']}...")
        conn = psycopg2.connect(
            host=config["host"],
            port=config["port"],
            user=config["user"],
            password=config["password"],
            dbname=config["database"]
        )
        cursor = conn.cursor()
        
        # Začátek transakce
        conn.autocommit = False
        
        # Ověříme existenci poskytovatele
        print(f"Kontrola existence poskytovatele s ID {provider_id}...")
        cursor.execute("SELECT provider_name FROM llm_providers WHERE provider_id = %s", (provider_id,))
        provider = cursor.fetchone()
        if not provider:
            print(f"Poskytovatel s ID {provider_id} neexistuje")
            return False
        
        provider_name = provider[0]
        print(f"Nalezen poskytovatel: {provider_name} (ID: {provider_id})")
        
        # Vypiš závislé modely před smazáním
        cursor.execute("SELECT model_id, model_name FROM llm_models WHERE provider_id = %s", (provider_id,))
        models = cursor.fetchall()
        print(f"Nalezeno {len(models)} modelů pro poskytovatele {provider_name}:")
        for model in models:
            print(f"  - Model: {model[1]} (ID: {model[0]})")
        
        # Smazání všech modelů poskytovatele
        print(f"Mazání modelů poskytovatele {provider_name}...")
        cursor.execute("DELETE FROM llm_models WHERE provider_id = %s", (provider_id,))
        deleted_models = cursor.rowcount
        print(f"Smazáno {deleted_models} modelů")
        
        # Smazání poskytovatele
        print(f"Mazání poskytovatele {provider_name}...")
        cursor.execute("DELETE FROM llm_providers WHERE provider_id = %s", (provider_id,))
        deleted_providers = cursor.rowcount
        
        if deleted_providers == 0:
            print(f"CHYBA: Poskytovatel s ID {provider_id} nebyl smazán - žádný řádek nebyl změněn")
            conn.rollback()
            return False
        
        print(f"Poskytovatel {provider_name} (ID: {provider_id}) byl úspěšně smazán")
        conn.commit()
        return True
    except Exception as e:
        print(f"Chyba při mazání poskytovatele: {str(e)}")
        if conn:
            conn.rollback()
        return False
    finally:
        if conn:
            conn.close()
            print("Databázové spojení bylo uzavřeno.")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Použití: python test_delete_provider.py <ID_poskytovatele>")
        sys.exit(1)
    
    try:
        provider_id = int(sys.argv[1])
    except ValueError:
        print("Chyba: ID poskytovatele musí být celé číslo")
        sys.exit(1)
    
    # Pokus o smazání poskytovatele
    success = delete_provider_direct(provider_id)
    sys.exit(0 if success else 1)
