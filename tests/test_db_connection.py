"""
Testovací skript pro ověření připojení k databázi a kontrolu oprávnění.
"""

import sys
import os
import psycopg2
import json

def load_db_config():
    """Načte konfiguraci databáze ze souboru db.json."""
    config_path = os.path.join('/opt/gent/config', 'db.json')
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        return {
            "host": config.get("host", "localhost"),
            "port": config.get("port", 5432),
            "user": config.get("user"),
            "password": config.get("password"),
            "database": config.get("database")
        }
    except Exception as e:
        print(f"Chyba při načítání konfigurace: {str(e)}")
        return None

def test_connection():
    """Testuje připojení k databázi a oprávnění."""
    config = load_db_config()
    if not config:
        print("Nepodařilo se načíst konfiguraci databáze.")
        return False

    try:
        # Připojení k databázi
        print(f"Připojuji se k databázi {config['database']} na {config['host']}:{config['port']} jako uživatel {config['user']}...")
        conn = psycopg2.connect(
            host=config["host"],
            port=config["port"],
            user=config["user"],
            password=config["password"],
            dbname=config["database"]
        )
        
        # Testování čtení
        cursor = conn.cursor()
        print("Test 1: Čtení z tabulky llm_providers...")
        cursor.execute("SELECT COUNT(*) FROM llm_providers")
        provider_count = cursor.fetchone()[0]
        print(f"Počet poskytovatelů v databázi: {provider_count}")
        
        # Testování vytvoření a smazání testovacího poskytovatele
        print("Test 2: Vytváření testovacího poskytovatele...")
        cursor.execute("""
            INSERT INTO llm_providers (
                provider_name, api_base_url, api_key, is_active
            ) VALUES (
                'TestProvider', 'http://test.example.com', 'test_key', true
            ) RETURNING provider_id
        """)
        test_provider_id = cursor.fetchone()[0]
        print(f"Vytvořen testovací poskytovatel s ID: {test_provider_id}")
        
        # Testování smazání
        print(f"Test 3: Mazání testovacího poskytovatele (ID: {test_provider_id})...")
        cursor.execute("DELETE FROM llm_providers WHERE provider_id = %s", (test_provider_id,))
        if cursor.rowcount > 0:
            print(f"Poskytovatel s ID {test_provider_id} byl úspěšně smazán")
        else:
            print(f"Chyba: Poskytovatel s ID {test_provider_id} nebyl smazán")
        
        # Commitování změn
        conn.commit()
        
        print("Všechny testy byly dokončeny úspěšně.")
        return True
    except Exception as e:
        print(f"Chyba při testování databáze: {str(e)}")
        return False
    finally:
        if 'conn' in locals() and conn:
            conn.close()
            print("Databázové spojení bylo uzavřeno.")

if __name__ == "__main__":
    # Kontrola, zda jsme ve virtuálním prostředí
    venv_path = os.environ.get('VIRTUAL_ENV')
    if venv_path:
        print(f"Běžíme ve virtuálním prostředí: {venv_path}")
    else:
        print("Varování: Nebyla detekována aktivace virtuálního prostředí.")
    
    # Testování připojení
    success = test_connection()
    sys.exit(0 if success else 1)
