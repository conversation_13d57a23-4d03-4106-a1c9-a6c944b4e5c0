#!/usr/bin/env python3
"""
Test script pro integraci Ollama provideru do GENT LLM managementu.

Tento script:
1. Přidá Ollama provider do databáze s automatickým načtením modelů
2. Otestuje základní funkcionalnost Ollama API
3. <PERSON><PERSON><PERSON><PERSON><PERSON>, že modely jsou správně uloženy v databázi
"""

import asyncio
import sys
import os
import json
import requests
from datetime import datetime

# Přidání cesty k GENT modulům
sys.path.insert(0, '/opt/gent')

from gent.db.llm_db_service import LlmDirectDbService
from gent.llm.llm_manager import LLMManager

# Konfigurace
OLLAMA_URL = "http://***************:11434"

def test_ollama_connection():
    """Test připojení k Ollama serveru."""
    print("🔗 Testování připojení k Ollama serveru...")
    
    try:
        response = requests.get(f"{OLLAMA_URL}/api/tags", timeout=10)
        if response.status_code == 200:
            data = response.json()
            models = data.get("models", [])
            print(f"✅ Ollama server je dostupný na {OLLAMA_URL}")
            print(f"📋 Nalezeno {len(models)} modelů:")
            for model in models:
                name = model.get("name", "")
                size = model.get("size", 0)
                details = model.get("details", {})
                param_size = details.get("parameter_size", "")
                print(f"   - {name} ({param_size}, {size // (1024**3):.1f} GB)")
            return True
        else:
            print(f"❌ Ollama server vrátil chybu: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Chyba při připojení k Ollama: {str(e)}")
        return False

def test_add_ollama_provider():
    """Test přidání Ollama provideru do databáze."""
    print("\n📊 Přidávání Ollama provideru do databáze...")
    
    try:
        service = LlmDirectDbService()
        success = service.add_ollama_provider(OLLAMA_URL)
        
        if success:
            print("✅ Ollama provider byl úspěšně přidán do databáze")
            return True
        else:
            print("❌ Nepodařilo se přidat Ollama provider do databáze")
            return False
    except Exception as e:
        print(f"❌ Chyba při přidávání Ollama provideru: {str(e)}")
        return False

def test_database_content():
    """Test obsahu databáze po přidání Ollama provideru."""
    print("\n🗄️ Ověřování obsahu databáze...")
    
    try:
        service = LlmDirectDbService()
        providers = service.get_providers()
        
        # Najdeme Ollama provider
        ollama_provider = None
        for provider in providers:
            if provider.get("name") == "Ollama":
                ollama_provider = provider
                break
        
        if ollama_provider:
            print("✅ Ollama provider nalezen v databázi:")
            print(f"   - ID: {ollama_provider.get('id')}")
            print(f"   - Název: {ollama_provider.get('name')}")
            print(f"   - URL: {ollama_provider.get('base_url')}")
            print(f"   - Aktivní: {ollama_provider.get('is_active')}")
            print(f"   - Výchozí model: {ollama_provider.get('model')}")
            
            # Získáme detail provideru s modely
            provider_detail = service.get_provider_detail(ollama_provider.get('id'))
            if provider_detail and provider_detail.get('models'):
                models = provider_detail.get('models', {})
                print(f"   - Počet modelů: {len(models)}")
                for model_name, model_info in models.items():
                    context_len = model_info.get('context_length', 0)
                    max_tokens = model_info.get('max_tokens', 0)
                    is_default = model_info.get('is_default', False)
                    capabilities = model_info.get('capabilities', {})
                    print(f"     • {model_name} (context: {context_len}, max_tokens: {max_tokens})")
                    if is_default:
                        print(f"       → Výchozí model")
                    if capabilities.get('reasoning'):
                        print(f"       → Podporuje reasoning")
                    if capabilities.get('code'):
                        print(f"       → Podporuje kód")
            
            return True
        else:
            print("❌ Ollama provider nebyl nalezen v databázi")
            return False
            
    except Exception as e:
        print(f"❌ Chyba při ověřování databáze: {str(e)}")
        return False

async def test_ollama_llm_manager():
    """Test Ollama pomocí LLM manageru."""
    print("\n🤖 Testování Ollama pomocí LLM manageru...")
    
    try:
        # Inicializace LLM manageru s Ollama konfigurací
        config = {
            "ollama_url": OLLAMA_URL,
            "ollama_models": ["qwq:latest", "deepseek-r1:32b", "qwen3:32b"]
        }
        
        llm_manager = LLMManager(config)
        
        # Test jednoduchého generování textu
        print("📝 Test generování textu...")
        response = await llm_manager.generate_text(
            prompt="Ahoj! Jak se máš?",
            model="qwq:latest",
            provider="ollama",
            max_tokens=50,
            temperature=0.7
        )
        
        print(f"✅ Odpověď z modelu qwq:latest:")
        print(f"   Text: {response.text[:100]}...")
        print(f"   Tokeny: {response.tokens_used}")
        print(f"   Provider: {response.provider}")
        
        # Test chat completion
        print("\n💬 Test chat completion...")
        from gent.llm.models import ChatMessage
        
        messages = [
            ChatMessage(role="user", content="Řekni mi krátký vtip.")
        ]
        
        chat_response = await llm_manager.generate_chat_completion(
            messages=messages,
            model="deepseek-r1:32b",
            provider="ollama",
            max_tokens=100,
            temperature=0.8
        )
        
        print(f"✅ Chat odpověď z modelu deepseek-r1:32b:")
        print(f"   Text: {chat_response.text[:100]}...")
        print(f"   Tokeny: {chat_response.tokens_used}")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při testování LLM manageru: {str(e)}")
        return False

def test_api_endpoint():
    """Test API endpointu pro přidání Ollama provideru."""
    print("\n🌐 Testování API endpointu...")
    
    try:
        # Test API endpointu (pokud běží GENT API server)
        api_url = "http://localhost:8001/api/db/llm/providers/ollama"
        
        response = requests.post(
            api_url,
            params={"ollama_url": OLLAMA_URL},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API endpoint funguje:")
            print(f"   Zpráva: {data.get('message')}")
            print(f"   URL: {data.get('ollama_url')}")
            return True
        else:
            print(f"⚠️ API endpoint není dostupný (status: {response.status_code})")
            print("   (To je v pořádku, pokud GENT API server neběží)")
            return True
            
    except requests.exceptions.ConnectionError:
        print("⚠️ GENT API server není dostupný na localhost:8001")
        print("   (To je v pořádku pro tento test)")
        return True
    except Exception as e:
        print(f"❌ Chyba při testování API: {str(e)}")
        return False

def main():
    """Hlavní testovací funkce."""
    print("🚀 GENT Ollama Integration Test")
    print("=" * 50)
    
    # Test 1: Připojení k Ollama
    if not test_ollama_connection():
        print("\n❌ Test selhal - Ollama server není dostupný")
        return False
    
    # Test 2: Přidání do databáze
    if not test_add_ollama_provider():
        print("\n❌ Test selhal - Nepodařilo se přidat provider do databáze")
        return False
    
    # Test 3: Ověření databáze
    if not test_database_content():
        print("\n❌ Test selhal - Chyba při ověřování databáze")
        return False
    
    # Test 4: LLM Manager
    print("\n🔄 Spouštění asynchronních testů...")
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        if not loop.run_until_complete(test_ollama_llm_manager()):
            print("\n❌ Test selhal - Chyba v LLM manageru")
            return False
    finally:
        loop.close()
    
    # Test 5: API endpoint
    if not test_api_endpoint():
        print("\n❌ Test selhal - Chyba API endpointu")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 Všechny testy prošly úspěšně!")
    print("✅ Ollama provider je připraven k použití v GENT systému")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
