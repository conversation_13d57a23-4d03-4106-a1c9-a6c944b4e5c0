#!/usr/bin/env python3
"""
Kompletní databáze popisů LLM modelů pro GENT
Obsahuje detailní informace o účelu, schopnostech a vhodnosti použití jednotlivých modelů
"""

MODEL_DESCRIPTIONS = {
    # OpenAI modely
    "gpt-4o": {
        "name": "GPT-4o",
        "provider": "OpenAI",
        "description": "Nejnovější multimodální model OpenAI s vynikajícími schopnostmi v textu, kódu a vizuálním obsahu",
        "capabilities": ["text", "code", "vision", "reasoning", "function_calling"],
        "suitable_for": ["general", "coding", "analysis", "creative", "research"],
        "use_cases": [
            "Komplexní analýza a reasoning",
            "Pokročilé programování a debugging",
            "Analýza obrázků a dokumentů",
            "Kreativní psaní a brainstorming",
            "Vědecký výzkum a analýza dat"
        ],
        "strengths": ["Vysoká kvalita odpovědí", "Multimodální schopnosti", "Pokročilé reasoning"],
        "context_window": 128000,
        "cost_tier": "premium"
    },

    "gpt-4o-mini": {
        "name": "GPT-4o Mini",
        "provider": "OpenAI",
        "description": "Kompaktní verze GPT-4o optimalizovaná pro rychlost a efektivitu při zachování vysoké kvality",
        "capabilities": ["text", "code", "vision", "function_calling"],
        "suitable_for": ["general", "coding", "quick_tasks"],
        "use_cases": [
            "Rychlé odpovědi na dotazy",
            "Základní programování",
            "Analýza kratších textů",
            "Chatbot aplikace",
            "Automatizace jednoduchých úkolů"
        ],
        "strengths": ["Rychlost", "Nízké náklady", "Dobrý poměr cena/výkon"],
        "context_window": 128000,
        "cost_tier": "budget"
    },

    "gpt-4.5": {
        "name": "GPT-4.5",
        "provider": "OpenAI",
        "description": "Vylepšená verze GPT-4 s pokročilými schopnostmi reasoning a lepším porozuměním kontextu",
        "capabilities": ["text", "code", "reasoning", "function_calling", "tools"],
        "suitable_for": ["research", "coding", "analysis", "complex_reasoning"],
        "use_cases": [
            "Pokročilý vědecký výzkum",
            "Komplexní analýza dat",
            "Sofistikované programování",
            "Strategické plánování",
            "Akademické psaní"
        ],
        "strengths": ["Pokročilé reasoning", "Lepší logické myšlení", "Vysoká přesnost"],
        "context_window": 200000,
        "cost_tier": "premium"
    },

    "gpt-4-1": {
        "name": "GPT-4.1",
        "provider": "OpenAI",
        "description": "Nejnovější flagship model OpenAI pro nejkomplexnější úkoly s vynikajícím reasoning",
        "capabilities": ["text", "code", "reasoning", "function_calling", "tools", "advanced_reasoning"],
        "suitable_for": ["research", "coding", "analysis", "complex_reasoning", "professional"],
        "use_cases": [
            "Nejkomplexnější analýzy",
            "Pokročilé programování a architektura",
            "Vědecké publikace",
            "Strategické rozhodování",
            "Expertní konzultace"
        ],
        "strengths": ["Nejlepší reasoning", "Nejvyšší kvalita", "Expertní úroveň"],
        "context_window": 200000,
        "cost_tier": "premium_plus"
    },

    "gpt-4-1-mini": {
        "name": "GPT-4.1 Mini",
        "provider": "OpenAI",
        "description": "Kompaktní verze GPT-4.1 s vynikajícím poměrem výkon/cena",
        "capabilities": ["text", "code", "reasoning", "function_calling"],
        "suitable_for": ["general", "coding", "analysis"],
        "use_cases": [
            "Kvalitní analýzy za rozumnou cenu",
            "Programování střední složitosti",
            "Vzdělávací účely",
            "Prototypování aplikací"
        ],
        "strengths": ["Dobrý poměr cena/výkon", "Rychlost", "Kvalitní reasoning"],
        "context_window": 128000,
        "cost_tier": "standard"
    },

    "gpt-4-1-nano": {
        "name": "GPT-4.1 Nano",
        "provider": "OpenAI",
        "description": "Nejmenší a nejrychlejší model z řady GPT-4.1, optimalizovaný pro jednoduché úkoly",
        "capabilities": ["text", "basic_code", "function_calling"],
        "suitable_for": ["quick_tasks", "chatbot", "simple_automation"],
        "use_cases": [
            "Chatbot aplikace",
            "Rychlé odpovědi",
            "Jednoduché automatizace",
            "Základní analýzy textu",
            "Prototypování"
        ],
        "strengths": ["Velmi rychlý", "Velmi levný", "Nízká latence"],
        "context_window": 32000,
        "cost_tier": "budget"
    },

    "o3": {
        "name": "OpenAI o3",
        "provider": "OpenAI",
        "description": "Specializovaný reasoning model pro nejkomplexnější logické úkoly a vědecké problémy",
        "capabilities": ["advanced_reasoning", "mathematics", "science", "logic", "problem_solving"],
        "suitable_for": ["research", "mathematics", "science", "complex_reasoning"],
        "use_cases": [
            "Matematické důkazy",
            "Vědecké výpočty",
            "Logické puzzle",
            "Komplexní analýzy",
            "Akademický výzkum"
        ],
        "strengths": ["Nejlepší reasoning", "Matematické schopnosti", "Logické myšlení"],
        "context_window": 128000,
        "cost_tier": "premium_plus"
    },

    "o3-mini": {
        "name": "OpenAI o3 Mini",
        "provider": "OpenAI",
        "description": "Kompaktní verze o3 pro reasoning úkoly s lepším poměrem cena/výkon",
        "capabilities": ["reasoning", "mathematics", "logic", "problem_solving"],
        "suitable_for": ["education", "analysis", "problem_solving"],
        "use_cases": [
            "Vzdělávací reasoning",
            "Základní matematika",
            "Logické úkoly",
            "Analýza problémů",
            "Studijní pomoc"
        ],
        "strengths": ["Dobrý reasoning", "Přijatelná cena", "Rychlost"],
        "context_window": 64000,
        "cost_tier": "standard"
    }
}

# Anthropic modely
MODEL_DESCRIPTIONS.update({
    "claude-3-7-sonnet-latest": {
        "name": "Claude 3.7 Sonnet Latest",
        "provider": "Anthropic",
        "description": "Nejnovější verze Claude 3.7 Sonnet s pokročilými schopnostmi reasoning, analýzy a kreativního myšlení",
        "capabilities": ["text", "code", "analysis", "reasoning", "creative", "function_calling", "advanced_reasoning"],
        "suitable_for": ["coding", "analysis", "creative", "research", "professional", "complex_reasoning"],
        "use_cases": [
            "Nejpokročilejší programování a architektura",
            "Komplexní analýza a reasoning",
            "Kreativní projekty a storytelling",
            "Vědecký výzkum a publikace",
            "Strategické rozhodování a plánování",
            "Expertní konzultace"
        ],
        "strengths": ["Nejlepší reasoning", "Pokročilé kódování", "Kreativita", "Bezpečnost", "Logické myšlení"],
        "context_window": 200000,
        "cost_tier": "premium_plus"
    }
})

# Google Gemini modely
MODEL_DESCRIPTIONS.update({
    "gemini-2.0-flash": {
        "name": "Gemini 2.0 Flash",
        "provider": "Google",
        "description": "Nejnovější multimodální model Google s podporou textu, obrázků, videa a audia",
        "capabilities": ["text", "vision", "audio", "video", "code", "multimodal"],
        "suitable_for": ["multimodal", "creative", "analysis", "general"],
        "use_cases": [
            "Analýza multimediálního obsahu",
            "Kreativní projekty s různými médii",
            "Vzdělávací materiály",
            "Obsahová tvorba",
            "Interaktivní aplikace"
        ],
        "strengths": ["Multimodální schopnosti", "Rychlost", "Kreativita"],
        "context_window": 1000000,
        "cost_tier": "standard"
    },

    "gemini-2.5-pro-preview-05-06": {
        "name": "Gemini 2.5 Pro Preview",
        "provider": "Google",
        "description": "Preview verze Gemini 2.5 Pro s pokročilými schopnostmi reasoning a analýzy",
        "capabilities": ["text", "code", "reasoning", "analysis", "research"],
        "suitable_for": ["research", "analysis", "coding", "professional"],
        "use_cases": [
            "Pokročilý výzkum a analýza",
            "Komplexní programování",
            "Vědecké publikace",
            "Strategické analýzy",
            "Expertní konzultace"
        ],
        "strengths": ["Pokročilé reasoning", "Velký kontext", "Analýza"],
        "context_window": 2000000,
        "cost_tier": "premium"
    },

    "gemini-2.5-flash-preview-05-20": {
        "name": "Gemini 2.5 Flash Preview",
        "provider": "Google",
        "description": "Rychlá preview verze Gemini 2.5 optimalizovaná pro rychlost a efektivitu",
        "capabilities": ["text", "code", "quick_analysis", "general"],
        "suitable_for": ["quick_tasks", "general", "coding", "automation"],
        "use_cases": [
            "Rychlé analýzy a odpovědi",
            "Automatizace úkolů",
            "Chatbot aplikace",
            "Základní programování",
            "Obsahová tvorba"
        ],
        "strengths": ["Rychlost", "Efektivita", "Dobrý poměr cena/výkon"],
        "context_window": 1000000,
        "cost_tier": "budget"
    }
})

def get_model_description(model_identifier):
    """Získá popis modelu podle identifikátoru"""
    return MODEL_DESCRIPTIONS.get(model_identifier, {
        "description": "Popis modelu není k dispozici",
        "capabilities": ["text"],
        "suitable_for": ["general"],
        "use_cases": ["Obecné použití"],
        "strengths": ["Standardní LLM model"]
    })

def get_models_by_provider(provider_name):
    """Získá všechny modely daného poskytovatele"""
    return {k: v for k, v in MODEL_DESCRIPTIONS.items() if v["provider"].lower() == provider_name.lower()}

def get_models_by_capability(capability):
    """Získá modely s danou schopností"""
    return {k: v for k, v in MODEL_DESCRIPTIONS.items() if capability in v.get("capabilities", [])}

def get_models_by_use_case(use_case):
    """Získá modely vhodné pro daný případ použití"""
    return {k: v for k, v in MODEL_DESCRIPTIONS.items() if use_case in v.get("suitable_for", [])}

if __name__ == "__main__":
    print("🤖 GENT Model Descriptions Database")
    print(f"📊 Celkem modelů: {len(MODEL_DESCRIPTIONS)}")

    for provider in ["OpenAI", "Anthropic", "Google"]:
        models = get_models_by_provider(provider)
        print(f"🏢 {provider}: {len(models)} modelů")
        for model_id, info in models.items():
            print(f"  - {info['name']}: {info['description'][:80]}...")
