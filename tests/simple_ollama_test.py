#!/usr/bin/env python3
"""
Jednoduchý test pro přidání Ollama provideru.
"""

import sys
import os
import requests

# Přidání cesty k GENT modulům
sys.path.insert(0, '/opt/gent')

def test_ollama_connection():
    """Test připojení k <PERSON>llama."""
    print("Testování Ollama připojení...")
    
    try:
        response = requests.get("http://***************:11434/api/tags", timeout=10)
        if response.status_code == 200:
            data = response.json()
            models = data.get("models", [])
            print(f"✅ Ollama dostu<PERSON>ná, {len(models)} modelů")
            for model in models:
                print(f"  - {model.get('name')}")
            return True
        else:
            print(f"❌ Ollama chyba: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Chyba: {e}")
        return False

def add_ollama_to_db():
    """Přidání <PERSON>lla<PERSON> do databáze."""
    print("Přidávání Ollama do databáze...")
    
    try:
        from gent.db.llm_db_service import LlmDirectDbService
        
        service = LlmDirectDbService()
        success = service.add_ollama_provider("http://***************:11434")
        
        if success:
            print("✅ Ollama provider přidán do databáze")
            return True
        else:
            print("❌ Nepodařilo se přidat Ollama provider")
            return False
            
    except Exception as e:
        print(f"❌ Chyba při přidávání: {e}")
        return False

def main():
    print("🚀 Jednoduchý Ollama test")
    print("=" * 30)
    
    if not test_ollama_connection():
        return False
    
    if not add_ollama_to_db():
        return False
    
    print("🎉 Test dokončen úspěšně!")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
