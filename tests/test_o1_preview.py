#!/usr/bin/env python3
"""
Test script pro o1-preview model v GENT systému.
"""

import asyncio
import sys
import os

# Přidání rootovského adresáře do cesty pro import
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__))))

from gent.llm.openai_provider import OpenAIProvider
from gent.llm.base_provider import Message

async def test_o1_preview():
    """Test o1-preview modelu."""
    print("🧪 Testování o1-preview modelu...")
    
    try:
        # Vytvoření OpenAI providera
        provider = OpenAIProvider()
        
        # Testovací zpráva
        messages = [
            Message(role="user", content="What is 2+2? Answer briefly.")
        ]
        
        print("📡 Odesílám test request na o1-preview...")
        
        # Test completion
        response = await provider.generate_completion(
            messages=messages,
            model="o1-preview",
            max_tokens=100
        )
        
        print(f"✅ Odpověď od o1-preview: {response.content[:200]}...")
        print(f"📊 Tokeny použité: {response.usage.total_tokens if response.usage else 'N/A'}")
        print(f"🏁 Finish reason: {response.finish_reason}")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při testování o1-preview: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_standard_model():
    """Test standardního modelu pro porovnání."""
    print("\n🧪 Testování standardního modelu (gpt-4o) pro porovnání...")
    
    try:
        # Vytvoření OpenAI providera
        provider = OpenAIProvider()
        
        # Testovací zpráva
        messages = [
            Message(role="user", content="What is 2+2? Answer briefly.")
        ]
        
        print("📡 Odesílám test request na gpt-4o...")
        
        # Test completion
        response = await provider.generate_completion(
            messages=messages,
            model="gpt-4o",
            max_tokens=100,
            temperature=0.7
        )
        
        print(f"✅ Odpověď od gpt-4o: {response.content[:200]}...")
        print(f"📊 Tokeny použité: {response.usage.total_tokens if response.usage else 'N/A'}")
        print(f"🏁 Finish reason: {response.finish_reason}")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při testování gpt-4o: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Hlavní funkce."""
    print("🔬 GENT - Test o1-preview modelu")
    print("=" * 50)
    
    # Test o1-preview
    o1_success = await test_o1_preview()
    
    # Test standardního modelu pro porovnání
    standard_success = await test_standard_model()
    
    print("\n" + "=" * 50)
    print("📋 VÝSLEDKY TESTŮ:")
    print(f"  o1-preview: {'✅ FUNGUJE' if o1_success else '❌ NEFUNGUJE'}")
    print(f"  gpt-4o:     {'✅ FUNGUJE' if standard_success else '❌ NEFUNGUJE'}")
    
    if o1_success:
        print("\n🎉 o1-preview model je nyní funkční v GENT systému!")
        print("💡 Můžeš ho testovat na webu v LLM Management")
    else:
        print("\n💥 o1-preview model stále nefunguje")
        print("💡 Zkontroluj API klíče a konfiguraci")

if __name__ == "__main__":
    asyncio.run(main())
