#!/usr/bin/env python3
"""
Test script pro ov<PERSON><PERSON><PERSON><PERSON> o1-preview, o1-mini a o3-mini modelů.
Tyto modely používají speciální API volání bez temperature a s max_completion_tokens.
"""

import openai
import os
import json

def load_api_key():
    """Načte OpenAI API klíč z konfigurace."""
    try:
        config_path = '/opt/gent/config/db.json'
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # Zkusíme najít OpenAI API klíč v různých možných místech
        api_key = None
        
        # Možnost 1: přímo v config
        if 'openai_api_key' in config:
            api_key = config['openai_api_key']
        
        # Možnost 2: v environment variables
        if not api_key:
            api_key = os.getenv('OPENAI_API_KEY')
        
        # Možnost 3: v separátním souboru
        if not api_key:
            try:
                with open('/opt/gent/config/openai_key.txt', 'r') as f:
                    api_key = f.read().strip()
            except:
                pass
        
        return api_key
    except Exception as e:
        print(f"Chyba při načítání API klíče: {e}")
        return None

def test_model(model_name, api_key):
    """Testuje konkrétní model."""
    print(f"\n🧪 Testování {model_name}...")
    
    try:
        client = openai.OpenAI(api_key=api_key)
        
        # Kontrola, zda je to o1/o3 model
        is_o1_model = model_name in ["o1-preview", "o1-mini", "o3-mini"]
        
        # Příprava parametrů podle typu modelu
        if is_o1_model:
            # o1/o3 modely - bez temperature, s max_completion_tokens
            response = client.chat.completions.create(
                model=model_name,
                messages=[
                    {"role": "user", "content": "What is 2+2? Answer briefly."}
                ],
                max_completion_tokens=100
            )
        else:
            # Standardní modely
            response = client.chat.completions.create(
                model=model_name,
                messages=[
                    {"role": "user", "content": "What is 2+2? Answer briefly."}
                ],
                temperature=0.7,
                max_tokens=100
            )
        
        # Zpracování odpovědi
        answer = response.choices[0].message.content
        tokens_used = response.usage.total_tokens if hasattr(response, 'usage') else 0
        finish_reason = response.choices[0].finish_reason
        
        print(f"  ✅ Odpověď: {answer}")
        print(f"  📊 Tokeny: {tokens_used}")
        print(f"  🏁 Finish reason: {finish_reason}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Chyba: {e}")
        return False

def main():
    """Hlavní funkce pro testování všech o1/o3 modelů."""
    print("🔬 TEST o1/o3 MODELŮ - Speciální API volání")
    print("=" * 60)
    
    # Načtení API klíče
    api_key = load_api_key()
    if not api_key:
        print("❌ Nepodařilo se načíst OpenAI API klíč!")
        print("💡 Zkontroluj konfiguraci v /opt/gent/config/")
        return
    
    print(f"🔑 API klíč načten: {api_key[:8]}...")
    
    # Modely k testování
    models_to_test = [
        "o1-preview",
        "o1-mini", 
        "o3-mini"
    ]
    
    # Testování každého modelu
    results = {}
    for model in models_to_test:
        success = test_model(model, api_key)
        results[model] = success
    
    # Shrnutí výsledků
    print("\n" + "=" * 60)
    print("📋 VÝSLEDKY TESTŮ:")
    
    successful = 0
    for model, success in results.items():
        status = "✅ FUNGUJE" if success else "❌ NEFUNGUJE"
        print(f"  {model}: {status}")
        if success:
            successful += 1
    
    print(f"\n📊 CELKEM: {successful}/{len(models_to_test)} modelů funguje")
    
    if successful == len(models_to_test):
        print("\n🎉 Všechny o1/o3 modely fungují!")
        print("💡 Můžeš je testovat ve webovém rozhraní")
    else:
        print("\n⚠️  Některé modely nefungují")
        print("💡 Zkontroluj API klíče a konfiguraci")
    
    print("\n💡 POZNÁMKA: o1/o3 modely používají speciální API:")
    print("   - BEZ temperature parametru")
    print("   - S max_completion_tokens místo max_tokens")
    print("   - Automaticky rozpoznáno v GENT kódu")

if __name__ == "__main__":
    main()
