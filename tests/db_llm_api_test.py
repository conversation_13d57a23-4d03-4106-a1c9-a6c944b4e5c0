"""
Test pro API přístupu k LLM poskytovatelům a modelům v databázi.

Tento test ověřu<PERSON> funkčnost třídy LlmDirectDbService pro práci s nov<PERSON><PERSON> tabulkami
llm_providers a llm_models.
"""

import sys
import os
import unittest
import json
import logging
from datetime import datetime

# Přidání cesty k projektu
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from gent.db.llm_db_service import LlmDirectDbService
from gent.db.direct_connector import get_db_connection

# Nastavení loggingu
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestLlmDirectDbService(unittest.TestCase):
    """Test pro API přístupu k LLM poskytovatelům a modelům."""

    def setUp(self):
        """Nastavení před každým testem."""
        # Vytvoříme instanci služby
        self.llm_service = LlmDirectDbService()
        
        # Připravíme testovací data pro poskytovatele
        self.test_provider = {
            "name": "Test Provider",
            "api_key": "test_api_key_12345",
            "base_url": "https://api.test-provider.com/v1",
            "api_version": "1.0",
            "auth_type": "api_key",
            "api_key_required": True,
            "rate_limit": 1000,
            "is_active": True,
            "models": {
                "Test Model 1": {
                    "model_identifier": "test-model-1",
                    "context_length": 16000,
                    "max_tokens": 2000,
                    "default_temperature": 0.7,
                    "capabilities": {
                        "text": True,
                        "chat": True,
                        "code": True
                    }
                },
                "Test Model 2": {
                    "model_identifier": "test-model-2",
                    "context_length": 32000,
                    "max_tokens": 4000,
                    "default_temperature": 0.8,
                    "capabilities": {
                        "text": True,
                        "chat": True,
                        "vision": True
                    }
                }
            },
            "model": "Test Model 1"  # Výchozí model
        }
        
        # Připojíme se k databázi a připravíme testovací prostředí
        self.conn = get_db_connection()
        cursor = self.conn.cursor()
        
        # Vyčistíme testovací data z předchozích testů - včetně aktualizovaných názvů
        try:
            cursor.execute("DELETE FROM llm_models WHERE model_name LIKE 'Test Model%'")
            cursor.execute("DELETE FROM llm_providers WHERE provider_name = 'Test Provider' OR provider_name = 'Test Provider Updated'")
            self.conn.commit()
            logger.info("Vyčištěna testovací data z databáze")
        except Exception as e:
            logger.error(f"Chyba při čištění testovacích dat: {str(e)}")
            self.conn.rollback()
        finally:
            cursor.close()

    def tearDown(self):
        """Úklid po každém testu."""
        # Vyčistíme testovací data - včetně aktualizovaných názvů
        cursor = self.conn.cursor()
        try:
            cursor.execute("DELETE FROM llm_models WHERE model_name LIKE 'Test Model%'")
            cursor.execute("DELETE FROM llm_providers WHERE provider_name = 'Test Provider' OR provider_name = 'Test Provider Updated'")
            self.conn.commit()
            logger.info("Vyčištěna testovací data z databáze")
        except Exception as e:
            logger.error(f"Chyba při čištění testovacích dat: {str(e)}")
            self.conn.rollback()
        finally:
            cursor.close()
            if hasattr(self, 'conn') and self.conn:
                self.conn.close()

    def test_save_and_get_provider(self):
        """Test vytvoření, uložení a načtení poskytovatele."""
        # Uložíme testovacího poskytovatele
        success = self.llm_service.save_provider(self.test_provider)
        self.assertTrue(success, "Uložení poskytovatele selhalo")
        
        # Získáme seznam poskytovatelů
        providers = self.llm_service.get_providers()
        
        # Najdeme našeho testovacího poskytovatele
        test_provider = None
        for provider in providers:
            if provider["name"] == "Test Provider":
                test_provider = provider
                break
        
        # Ověříme, že poskytovatel byl nalezen
        self.assertIsNotNone(test_provider, "Testovací poskytovatel nebyl nalezen v seznamu")
        
        # Ověříme data poskytovatele
        self.assertEqual(test_provider["name"], self.test_provider["name"])
        self.assertEqual(test_provider["base_url"], self.test_provider["base_url"])
        self.assertEqual(test_provider["api_version"], self.test_provider["api_version"])
        self.assertEqual(test_provider["model"], self.test_provider["model"])
        
        # Získáme detail poskytovatele
        provider_detail = self.llm_service.get_provider_detail(test_provider["id"])
        
        # Ověříme detail poskytovatele
        self.assertIsNotNone(provider_detail, "Detail poskytovatele nebyl nalezen")
        self.assertEqual(provider_detail["name"], self.test_provider["name"])
        self.assertEqual(provider_detail["base_url"], self.test_provider["base_url"])
        self.assertEqual(provider_detail["api_version"], self.test_provider["api_version"])
        
        # Ověříme, že má správný počet modelů
        self.assertEqual(len(provider_detail["models"]), 2, "Nesprávný počet modelů poskytovatele")
        
        # Ověříme, že jsou přítomny oba modely
        self.assertIn("Test Model 1", provider_detail["models"])
        self.assertIn("Test Model 2", provider_detail["models"])
        
        # Ověříme detail prvního modelu
        model1 = provider_detail["models"]["Test Model 1"]
        self.assertEqual(model1["model_identifier"], "test-model-1")
        self.assertEqual(model1["context_length"], 16000)
        self.assertEqual(model1["max_tokens"], 2000)
        self.assertEqual(model1["default_temperature"], 0.7)
        self.assertTrue(model1["is_default"])
        
        # Ověříme detail druhého modelu
        model2 = provider_detail["models"]["Test Model 2"]
        self.assertEqual(model2["model_identifier"], "test-model-2")
        self.assertEqual(model2["context_length"], 32000)
        self.assertEqual(model2["max_tokens"], 4000)
        self.assertEqual(model2["default_temperature"], 0.8)
        self.assertFalse(model2["is_default"])
        
        logger.info("Test vytvoření, uložení a načtení poskytovatele úspěšný")

    def test_update_provider(self):
        """Test aktualizace poskytovatele."""
        # Nejprve vytvoříme poskytovatele
        success = self.llm_service.save_provider(self.test_provider)
        self.assertTrue(success, "Uložení poskytovatele selhalo")
        
        # Získáme seznam poskytovatelů
        providers = self.llm_service.get_providers()
        test_provider = None
        for provider in providers:
            if provider["name"] == "Test Provider":
                test_provider = provider
                break
        
        self.assertIsNotNone(test_provider, "Testovací poskytovatel nebyl nalezen v seznamu")
        
        # Aktualizujeme data poskytovatele
        provider_id = test_provider["id"]
        updated_provider = {
            "id": provider_id,
            "name": "Test Provider Updated",
            "api_key": "updated_test_api_key",
            "base_url": "https://api-updated.test-provider.com/v1",
            "api_version": "2.0",
            "auth_type": "bearer",
            "api_key_required": True,
            "rate_limit": 2000,
            "is_active": True,
            "models": {
                "Test Model 1": {
                    "model_identifier": "test-model-1-updated",
                    "context_length": 24000,
                    "max_tokens": 3000,
                    "default_temperature": 0.5,
                    "capabilities": {
                        "text": True,
                        "chat": True,
                        "code": True,
                        "function_calling": True
                    }
                },
                "Test Model 3": {  # Nový model
                    "model_identifier": "test-model-3",
                    "context_length": 64000,
                    "max_tokens": 8000,
                    "default_temperature": 0.9,
                    "capabilities": {
                        "text": True,
                        "chat": True,
                        "vision": True,
                        "audio": True
                    }
                }
            },
            "model": "Test Model 3"  # Změna výchozího modelu
        }
        
        # Uložíme aktualizovaného poskytovatele
        success = self.llm_service.save_provider(updated_provider)
        self.assertTrue(success, "Aktualizace poskytovatele selhala")
        
        # Získáme aktualizovaný detail poskytovatele
        provider_detail = self.llm_service.get_provider_detail(provider_id)
        
        # Ověříme aktualizované údaje
        self.assertIsNotNone(provider_detail, "Detail aktualizovaného poskytovatele nebyl nalezen")
        self.assertEqual(provider_detail["name"], "Test Provider Updated")
        self.assertEqual(provider_detail["base_url"], "https://api-updated.test-provider.com/v1")
        self.assertEqual(provider_detail["api_version"], "2.0")
        self.assertEqual(provider_detail["auth_type"], "bearer")
        self.assertEqual(provider_detail["rate_limit"], 2000)
        
        # Ověříme modely (měly by být 3: model1 aktualizovaný, model2 původní, model3 nový)
        self.assertEqual(len(provider_detail["models"]), 3, "Nesprávný počet modelů po aktualizaci")
        
        # Ověříme aktualizovaný model
        self.assertIn("Test Model 1", provider_detail["models"])
        model1 = provider_detail["models"]["Test Model 1"]
        self.assertEqual(model1["model_identifier"], "test-model-1-updated")
        self.assertEqual(model1["context_length"], 24000)
        self.assertEqual(model1["max_tokens"], 3000)
        self.assertEqual(model1["default_temperature"], 0.5)
        self.assertFalse(model1["is_default"])
        
        # Ověříme nový model
        self.assertIn("Test Model 3", provider_detail["models"])
        model3 = provider_detail["models"]["Test Model 3"]
        self.assertEqual(model3["model_identifier"], "test-model-3")
        self.assertEqual(model3["context_length"], 64000)
        self.assertEqual(model3["max_tokens"], 8000)
        self.assertEqual(model3["default_temperature"], 0.9)
        self.assertTrue(model3["is_default"])
        
        # Ověříme, že původní model2 je stále v databázi
        self.assertIn("Test Model 2", provider_detail["models"])
        
        logger.info("Test aktualizace poskytovatele úspěšný")

    def test_default_provider(self):
        """Test nastavení výchozího poskytovatele."""
        # Nejprve vytvoříme poskytovatele
        success = self.llm_service.save_provider(self.test_provider)
        self.assertTrue(success, "Uložení poskytovatele selhalo")
        
        # Získáme jeho ID
        providers = self.llm_service.get_providers()
        provider_id = None
        for provider in providers:
            if provider["name"] == "Test Provider":
                provider_id = provider["id"]
                break
        
        self.assertIsNotNone(provider_id, "Testovací poskytovatel nebyl nalezen v seznamu")
        
        # Nastavíme poskytovatele jako výchozí
        success = self.llm_service.set_default_provider(provider_id)
        
        # Ověříme, že nastavení proběhlo úspěšně
        self.assertTrue(success, "Nastavení výchozího poskytovatele selhalo")
        
        # V tomto testu pouze ověřujeme, že metoda vrátila True
        # V reálném prostředí bychom ověřili, zda byl poskytovatel skutečně nastaven jako výchozí
        # Například kontrolou sloupce is_default v tabulce llm_providers, až bude tento sloupec přidán
        
        logger.info("Test nastavení výchozího poskytovatele úspěšný")


if __name__ == "__main__":
    unittest.main()
