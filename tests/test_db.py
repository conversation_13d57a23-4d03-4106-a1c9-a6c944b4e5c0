#!/usr/bin/env python3
"""
Test připojení k databázi a zobrazení současných modelů.
"""

import subprocess
import os

def run_sql_command(sql_command):
    """Spustí SQL příkaz pomocí sudo -u postgres psql."""
    try:
        cmd = [
            'sudo', '-u', 'postgres', 'psql', '-d', 'gentdb', '-c', sql_command
        ]
        
        print(f"Spouštím: {' '.join(cmd)}")
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            cwd='/opt/gent',
            timeout=30
        )
        
        print(f"Return code: {result.returncode}")
        print(f"Stdout: {result.stdout}")
        print(f"Stderr: {result.stderr}")
        
        return result.returncode == 0
            
    except subprocess.TimeoutExpired:
        print("✗ Timeout při spouštění SQL příkazu")
        return False
    except Exception as e:
        print(f"✗ Chyba při spouštění SQL příkazu: {e}")
        return False

def main():
    """Hlavní funkce."""
    print("=== TEST PŘIPOJENÍ K DATABÁZI ===\n")
    
    # Zkontrolujeme, zda jsme v správném adresáři
    if not os.path.exists('/opt/gent'):
        print("✗ Adresář /opt/gent neexistuje")
        return False
    
    os.chdir('/opt/gent')
    
    # Test základního připojení
    print("1. Test základního připojení...")
    sql = "SELECT version();"
    if run_sql_command(sql):
        print("✓ Připojení k databázi funguje")
    else:
        print("✗ Připojení k databázi nefunguje")
        return False
    
    print("\n2. Zobrazení poskytovatelů...")
    sql = "SELECT provider_id, provider_name FROM llm_providers WHERE is_active = TRUE;"
    run_sql_command(sql)
    
    print("\n3. Počet modelů podle poskytovatelů...")
    sql = """
    SELECT 
        p.provider_name, 
        COUNT(m.model_id) as model_count
    FROM llm_providers p
    LEFT JOIN llm_models m ON p.provider_id = m.provider_id AND m.is_active = TRUE
    WHERE p.is_active = TRUE
    GROUP BY p.provider_name, p.provider_id
    ORDER BY p.provider_name;
    """
    run_sql_command(sql)
    
    return True

if __name__ == "__main__":
    main()
