#!/usr/bin/env python3
"""
Test skript pro přidání modelů přes API
"""

import requests
import json

def test_api():
    """Testuje API endpoint pro modely"""
    try:
        # Test základního API
        response = requests.get("http://localhost:8001/api/db/llm/models")
        
        if response.status_code == 200:
            models = response.json()
            print(f"✅ API funguje! Načteno {len(models)} modelů")
            
            # Zobrazíme modely podle poskytovatelů
            providers = {}
            for model in models:
                provider = model['provider_name']
                if provider not in providers:
                    providers[provider] = []
                providers[provider].append(model['name'])
            
            print(f"\n📊 Aktuální modely v databázi:")
            for provider, model_list in providers.items():
                print(f"\n🏢 {provider}: {len(model_list)} modelů")
                for model_name in model_list:
                    print(f"  - {model_name}")
            
            # Zkontrolujeme, které OpenAI modely chybí
            openai_models = providers.get('OpenAI', [])
            required_openai = ['GPT-4o', 'GPT-4.5', 'GPT-4-1', 'GPT-4-1 Mini', 'GPT-4-1 Nano', 'O3', 'O3 Mini']
            missing_openai = [m for m in required_openai if m not in openai_models]
            
            if missing_openai:
                print(f"\n⚠️  Chybějící OpenAI modely: {missing_openai}")
            else:
                print(f"\n✅ Všechny požadované OpenAI modely jsou k dispozici!")
            
            # Zkontrolujeme Google modely
            google_models = providers.get('Google', [])
            required_google = ['Gemini 2.0 Flash', 'Gemini 2.5 Pro Preview 05-06', 'Gemini 2.5 Flash Preview 05-20']
            missing_google = [m for m in required_google if m not in google_models]
            
            if missing_google:
                print(f"\n⚠️  Chybějící Google modely: {missing_google}")
            else:
                print(f"\n✅ Všechny požadované Google modely jsou k dispozici!")
            
            return True
            
        else:
            print(f"❌ API chyba: {response.status_code}")
            print(f"Odpověď: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Chyba při testování API: {e}")
        return False

def test_model_functionality():
    """Testuje funkčnost modelů"""
    try:
        # Test jednoduchého modelu
        test_data = {
            "model_id": "1_gpt-4o-mini",  # Předpokládáme, že tento model existuje
            "message": "Hello, test message"
        }
        
        response = requests.post(
            "http://localhost:8001/api/db/llm/test-llm",
            json=test_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Test modelu úspěšný!")
            print(f"Model: {result.get('model', 'N/A')}")
            print(f"Odpověď: {result.get('response', {}).get('text', 'N/A')[:100]}...")
            return True
        else:
            print(f"⚠️  Test modelu selhal: {response.status_code}")
            print(f"Chyba: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Chyba při testování modelu: {e}")
        return False

if __name__ == "__main__":
    print("🧪 GENT - Test API a modelů")
    print("=" * 50)
    
    # Test API
    api_ok = test_api()
    
    if api_ok:
        print(f"\n🧪 Testování funkčnosti modelu...")
        model_ok = test_model_functionality()
        
        if model_ok:
            print(f"\n🎉 Všechny testy prošly úspěšně!")
        else:
            print(f"\n⚠️  Test modelu selhal, ale API funguje.")
    else:
        print(f"\n💥 API test selhal!")
    
    print(f"\n💡 Zkontroluj frontend na http://localhost:8000/llm-management")
    print(f"💡 Obnov stránku (F5) a zkontroluj dropdown 'Vyberte model'")
