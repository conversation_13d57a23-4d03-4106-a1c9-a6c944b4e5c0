#!/usr/bin/env python3
"""
Debug skript pro zjištění, jak<PERSON> modely máme v databázi
"""

import sys
import os
sys.path.append('/opt/gent')

from gent.db.direct_connector import get_db_connection

def debug_models():
    """Zobrazí všechny modely v databázi podle poskytovatelů"""
    try:
        print("🔍 Připojuji se k databázi...")
        conn = get_db_connection('gentdb')
        cursor = conn.cursor()
        
        # Získáme všechny poskytovatele
        cursor.execute("""
            SELECT provider_id, provider_name, is_active
            FROM llm_providers
            ORDER BY provider_name
        """)
        
        providers = cursor.fetchall()
        print(f"\n=== POSKYTOVATELÉ V DATABÁZI ===")
        for provider in providers:
            status = "✅ AKTIVNÍ" if provider[2] else "❌ NEAKTIVNÍ"
            print(f"{status}: ID={provider[0]}, Název='{provider[1]}'")
        
        # Pro každého poskytovatele zobrazíme modely
        for provider in providers:
            if not provider[2]:  # Přeskočíme neaktivní
                continue
                
            provider_id, provider_name = provider[0], provider[1]
            
            cursor.execute("""
                SELECT model_name, model_identifier, is_active, is_default
                FROM llm_models 
                WHERE provider_id = %s
                ORDER BY model_name
            """, (provider_id,))
            
            models = cursor.fetchall()
            print(f"\n=== MODELY POSKYTOVATELE '{provider_name}' (ID={provider_id}) ===")
            
            if not models:
                print("  Žádné modely nenalezeny!")
                continue
                
            for model in models:
                status = "✅ AKTIVNÍ" if model[2] else "❌ NEAKTIVNÍ"
                default = " (VÝCHOZÍ)" if model[3] else ""
                print(f"  {status}{default}: {model[0]} | ID: {model[1]}")
        
        # Speciálně se podívejme na OpenAI modely
        print(f"\n=== DETAILNÍ ANALÝZA OPENAI MODELŮ ===")
        cursor.execute("""
            SELECT m.model_name, m.model_identifier, m.is_active, p.provider_name
            FROM llm_models m
            JOIN llm_providers p ON m.provider_id = p.provider_id
            WHERE LOWER(p.provider_name) = 'openai'
            ORDER BY m.model_name
        """)
        
        openai_models = cursor.fetchall()
        if openai_models:
            for model in openai_models:
                status = "✅ AKTIVNÍ" if model[2] else "❌ NEAKTIVNÍ"
                print(f"  {status}: {model[0]} | ID: {model[1]}")
        else:
            print("  ❌ Žádné OpenAI modely nenalezeny!")
        
        # Speciálně se podívejme na Google modely
        print(f"\n=== DETAILNÍ ANALÝZA GOOGLE MODELŮ ===")
        cursor.execute("""
            SELECT m.model_name, m.model_identifier, m.is_active, p.provider_name
            FROM llm_models m
            JOIN llm_providers p ON m.provider_id = p.provider_id
            WHERE LOWER(p.provider_name) = 'google'
            ORDER BY m.model_name
        """)
        
        google_models = cursor.fetchall()
        if google_models:
            for model in google_models:
                status = "✅ AKTIVNÍ" if model[2] else "❌ NEAKTIVNÍ"
                print(f"  {status}: {model[0]} | ID: {model[1]}")
        else:
            print("  ❌ Žádné Google modely nenalezeny!")
        
        # Test našeho SQL filtru
        print(f"\n=== TEST NAŠEHO SQL FILTRU ===")
        cursor.execute("""
            SELECT p.provider_name, m.model_name, m.model_identifier
            FROM llm_models m
            JOIN llm_providers p ON m.provider_id = p.provider_id
            WHERE m.is_active = TRUE AND p.is_active = TRUE
            AND (
                -- Anthropic: pouze claude-3-7-sonnet-latest
                (LOWER(p.provider_name) = 'anthropic' AND (
                    m.model_identifier LIKE '%claude-3-7-sonnet-latest%'
                    OR m.model_identifier LIKE '%claude-3.7-sonnet-latest%'
                    OR m.model_name LIKE '%claude-3-7-sonnet-latest%'
                    OR m.model_name LIKE '%claude-3.7-sonnet-latest%'
                    OR m.model_name LIKE '%claude 3.7 sonnet%'
                ))
                -- Google: pouze vybrané Gemini modely
                OR (LOWER(p.provider_name) = 'google' AND (
                    m.model_identifier LIKE '%gemini-2.0-flash%'
                    OR m.model_identifier LIKE '%gemini-2.5-pro-preview-05-06%'
                    OR m.model_identifier LIKE '%gemini-2.5-flash-preview-05-20%'
                    OR m.model_name LIKE '%gemini-2.0-flash%'
                    OR m.model_name LIKE '%gemini-2.5-pro-preview-05-06%'
                    OR m.model_name LIKE '%gemini-2.5-flash-preview-05-20%'
                ))
                -- OpenAI: pouze vybrané GPT modely
                OR (LOWER(p.provider_name) = 'openai' AND (
                    m.model_identifier IN ('gpt-4o', 'gpt-4o-mini', 'gpt-4.5', 'gpt-4-1', 'gpt-4-1-mini', 'gpt-4-1-nano', 'o3', 'o3-mini')
                    OR m.model_name IN ('gpt-4o', 'gpt-4o-mini', 'gpt-4.5', 'gpt-4-1', 'gpt-4-1-mini', 'gpt-4-1-nano', 'o3', 'o3-mini')
                    OR m.model_identifier LIKE '%gpt-4o%'
                    OR m.model_identifier LIKE '%gpt-4.5%'
                    OR m.model_identifier LIKE '%gpt-4-1%'
                    OR m.model_identifier LIKE '%o3%'
                ))
                -- Ostatní poskytovatelé: všechny modely
                OR (LOWER(p.provider_name) NOT IN ('anthropic', 'google', 'openai'))
            )
            ORDER BY p.provider_name, m.model_name
        """)
        
        filtered_models = cursor.fetchall()
        print(f"Filtrované modely ({len(filtered_models)}):")
        current_provider = None
        for model in filtered_models:
            provider_name, model_name, model_identifier = model
            if provider_name != current_provider:
                print(f"\n🏢 {provider_name}:")
                current_provider = provider_name
            print(f"  ✓ {model_name} ({model_identifier})")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 GENT - Debug modelů v databázi")
    print("=" * 50)
    
    success = debug_models()
    
    if success:
        print("\n🎉 Debug dokončen!")
    else:
        print("\n💥 Debug selhal!")
        sys.exit(1)
