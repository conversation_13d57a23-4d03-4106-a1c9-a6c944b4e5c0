#!/usr/bin/env python3
"""
Přímý test o1-preview modelu pomocí OpenAI knihovny.
"""

from openai import OpenAI

def test_o1_preview():
    """Test o1-preview modelu přímo přes OpenAI API."""
    print("🧪 Testování o1-preview modelu přímo přes OpenAI API...")
    
    try:
        # Vytvoření OpenAI klienta s API klíčem
        client = OpenAI(api_key='********************************************************************************************************************************************************************')
        
        print("📡 Odesílám test request na o1-preview...")
        
        # Test volání
        response = client.chat.completions.create(
            model="o1-preview",
            messages=[
                {"role": "user", "content": "What is 2+2? Answer briefly."}
            ]
        )
        
        print(f"✅ Odpověď od o1-preview: {response.choices[0].message.content}")
        print(f"📊 Tokeny použité: {response.usage.total_tokens}")
        print(f"🏁 Finish reason: {response.choices[0].finish_reason}")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při testování o1-preview: {e}")
        return False

def test_standard_model():
    """Test standardního modelu pro porovnání."""
    print("\n🧪 Testování standardního modelu (gpt-4o) pro porovnání...")
    
    try:
        # Vytvoření OpenAI klienta s API klíčem
        client = OpenAI(api_key='********************************************************************************************************************************************************************')
        
        print("📡 Odesílám test request na gpt-4o...")
        
        # Test volání
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "user", "content": "What is 2+2? Answer briefly."}
            ],
            max_tokens=100,
            temperature=0.7
        )
        
        print(f"✅ Odpověď od gpt-4o: {response.choices[0].message.content}")
        print(f"📊 Tokeny použité: {response.usage.total_tokens}")
        print(f"🏁 Finish reason: {response.choices[0].finish_reason}")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při testování gpt-4o: {e}")
        return False

def main():
    """Hlavní funkce."""
    print("🔬 PŘÍMÝ TEST o1-preview modelu")
    print("=" * 50)
    
    # Test o1-preview
    o1_success = test_o1_preview()
    
    # Test standardního modelu pro porovnání
    standard_success = test_standard_model()
    
    print("\n" + "=" * 50)
    print("📋 VÝSLEDKY TESTŮ:")
    print(f"  o1-preview: {'✅ FUNGUJE' if o1_success else '❌ NEFUNGUJE'}")
    print(f"  gpt-4o:     {'✅ FUNGUJE' if standard_success else '❌ NEFUNGUJE'}")
    
    if o1_success:
        print("\n🎉 o1-preview model funguje přes OpenAI API!")
        print("💡 Problém je v GENT integraci - potřebuje další úpravy")
    else:
        print("\n💥 o1-preview model nefunguje ani přímo")
        print("💡 Zkontroluj API klíč nebo model dostupnost")

if __name__ == "__main__":
    main()
