#!/usr/bin/env python3
"""
Script pro přidání Ollama2 provideru.
"""

import sys
import os

# Přidání cesty k GENT modulům
sys.path.insert(0, '/opt/gent')

def main():
    try:
        from gent.db.llm_db_service import LlmDirectDbService
        
        print("🦙 Přidávání Ollama2 provideru...")
        
        service = LlmDirectDbService()
        success = service.add_ollama_provider(
            ollama_url="http://***************:11434",
            provider_name="Ollama2"
        )
        
        if success:
            print("✅ Ollama2 provider byl úspěšně přidán!")
            
            # Zobrazíme providery
            providers = service.get_providers()
            print("\n📋 Ollama providery:")
            for p in providers:
                if 'Ollama' in p.get('name', ''):
                    print(f"  - ID: {p.get('id')}, Name: {p.get('name')}, URL: {p.get('base_url')}")
                    
        else:
            print("❌ Nepodařilo se přidat Ollama2 provider")
            
    except Exception as e:
        print(f"❌ Chyba: {e}")

if __name__ == "__main__":
    main()
