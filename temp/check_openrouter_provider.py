#!/usr/bin/env python3
"""
Kontrola OpenRouter providera v databázi.
"""

import psycopg2
import json

def check_openrouter_provider():
    """Zkontroluje OpenRouter providera v databázi."""
    try:
        # Připojení k databázi
        conn = psycopg2.connect(
            host='localhost',
            port=5432,
            user='gent_app',
            password='gent1234secure',
            database='gentdb'
        )
        
        cursor = conn.cursor()
        
        print("🔍 Kontrola OpenRouter providera v databázi")
        print("=" * 50)
        
        # Kontrola všech providerů
        cursor.execute('SELECT provider_id, provider_name, api_key, is_active FROM llm_providers ORDER BY provider_id')
        providers = cursor.fetchall()
        
        print("📊 VŠICHNI POSKYTOVATELÉ:")
        for provider_id, provider_name, api_key, is_active in providers:
            status = "✅ AKTIVNÍ" if is_active else "❌ NEAKTIVNÍ"
            key_status = f"{api_key[:10]}..." if api_key else "ŽÁDNÝ KLÍČ"
            print(f"  ID: {provider_id:2d} | {provider_name:12s} | {key_status:15s} | {status}")
        
        # Specifická kontrola OpenRouter
        cursor.execute('SELECT provider_id, provider_name, api_key, is_active FROM llm_providers WHERE provider_name = %s', ('Openrouter',))
        openrouter = cursor.fetchone()
        
        print("\n🎯 OPENROUTER PROVIDER:")
        if openrouter:
            provider_id, provider_name, api_key, is_active = openrouter
            print(f"  ✅ Nalezen v databázi")
            print(f"  📋 ID: {provider_id}")
            print(f"  📋 Název: {provider_name}")
            print(f"  🔑 API klíč: {'✅ NASTAVEN' if api_key else '❌ CHYBÍ'}")
            print(f"  🔄 Status: {'✅ AKTIVNÍ' if is_active else '❌ NEAKTIVNÍ'}")
            
            if api_key:
                print(f"  🔑 Klíč preview: {api_key[:15]}...")
            
            # Kontrola modelů pro OpenRouter
            cursor.execute('SELECT COUNT(*) FROM llm_models WHERE provider_id = %s', (provider_id,))
            model_count = cursor.fetchone()[0]
            print(f"  🤖 Počet modelů: {model_count}")
            
            if model_count > 0:
                cursor.execute('SELECT model_name, model_identifier, is_active FROM llm_models WHERE provider_id = %s ORDER BY model_name', (provider_id,))
                models = cursor.fetchall()
                print(f"  📋 MODELY:")
                for model_name, model_identifier, is_active in models:
                    status = "✅" if is_active else "❌"
                    print(f"    {status} {model_name} ({model_identifier})")
        else:
            print("  ❌ OpenRouter provider nenalezen v databázi!")
        
        cursor.close()
        conn.close()
        
        return openrouter is not None
        
    except Exception as e:
        print(f"❌ Chyba při kontrole databáze: {e}")
        return False

if __name__ == "__main__":
    check_openrouter_provider()
