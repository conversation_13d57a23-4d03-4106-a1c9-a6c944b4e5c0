#!/usr/bin/env python3
"""
Skript pro přidání nových LLM modelů do databáze GENT pomocí LlmDirectDbService.
"""

import sys
import os
from datetime import datetime

# Přidáme cestu k GENT modulu
sys.path.append('/opt/gent')

try:
    from gent.db.llm_db_service import LlmDirectDbService
    print("✓ LlmDirectDbService úspěšně importován")
except ImportError as e:
    print(f"✗ Chyba při importu LlmDirectDbService: {e}")
    sys.exit(1)

def add_models_to_provider(service, provider_name, models_data):
    """Přidá modely k existujícímu poskytovateli."""
    try:
        # Získáme současné poskytovatele
        providers = service.get_providers()

        # Najdeme poskytovatele podle názvu
        target_provider = None
        for provider in providers:
            if provider['name'].lower() == provider_name.lower():
                target_provider = provider
                break

        if not target_provider:
            print(f"✗ Poskytovatel {provider_name} nebyl nalezen")
            return False

        print(f"✓ Nalezen poskytovatel: {target_provider['name']}")

        # Přidáme nové modely k existujícím
        existing_models = target_provider.get('models', {})
        print(f"  Současné modely: {len(existing_models)}")

        # Přidáme nové modely
        for model_name, model_identifier, context_length, max_tokens in models_data:
            if model_identifier not in existing_models:
                existing_models[model_name] = {
                    "model_identifier": model_identifier,
                    "context_length": context_length,
                    "max_tokens": max_tokens,
                    "default_temperature": 0.7,
                    "capabilities": {"text": True, "chat": True},
                    "is_active": True
                }
                print(f"  + Přidávám model: {model_name} ({model_identifier})")
            else:
                print(f"  - Model {model_identifier} již existuje")

        # Aktualizujeme poskytovatele v databázi
        provider_data = {
            "name": target_provider['name'],
            "api_base_url": target_provider.get('api_base_url', ''),
            "api_key": target_provider.get('api_key', ''),
            "api_version": target_provider.get('api_version', ''),
            "models": existing_models
        }

        success = service.save_provider(provider_data)
        if success:
            print(f"✓ Poskytovatel {provider_name} úspěšně aktualizován")
            return True
        else:
            print(f"✗ Chyba při aktualizaci poskytovatele {provider_name}")
            return False

    except Exception as e:
        print(f"✗ Chyba při přidávání modelů k poskytovateli {provider_name}: {e}")
        return False

def main():
    """Hlavní funkce pro přidání nových modelů."""

    # Definice nových modelů
    new_models = {
        'openai': [
            ('GPT-4.1', 'gpt-4.1', 128000, 4096),
            ('GPT-4.1 Mini', 'gpt-4.1-mini', 128000, 4096),
            ('GPT-4.1 Nano', 'gpt-4.1-nano', 128000, 4096),
            ('GPT-4.5', 'gpt-4.5', 128000, 4096),
            ('GPT-4.5 Mini', 'gpt-4.5-mini', 128000, 4096),
            ('GPT-4o Latest', 'gpt-4o-latest', 128000, 4096),
            ('GPT-4o Mini Latest', 'gpt-4o-mini-latest', 128000, 4096),
            ('GPT-4o Audio Preview', 'gpt-4o-audio-preview', 128000, 4096),
            ('O1 Mini', 'o1-mini', 128000, 65536),
            ('O3', 'o3', 128000, 65536),
            ('O3 Mini', 'o3-mini', 128000, 65536),
            ('O3 Mini High', 'o3-mini-high', 128000, 65536),
            ('O4 Mini', 'o4-mini', 128000, 65536),
            ('O4 Mini High', 'o4-mini-high', 128000, 65536),
        ],
        'google': [
            ('Gemini 2.5 Pro Preview', 'gemini-2.5-pro-preview-05-06', 1000000, 8192),
            ('Gemini 2.5 Flash Preview', 'gemini-2.5-flash-preview-05-20', 1000000, 8192),
            ('Gemini 2.0 Flash Lite', 'gemini-2.0-flash-lite', 1000000, 8192),
            ('Gemini 2.0 Flash', 'gemini-2.0-flash', 1000000, 8192),
        ],
        'anthropic': [
            ('Claude Sonnet 4 Latest', 'claude-sonnet-4-latest', 200000, 4096),
            ('Claude 3.7 Sonnet Latest', 'claude-3-7-sonnet-latest', 200000, 4096),
        ]
    }

    try:
        # Vytvoříme instanci LlmDirectDbService
        service = LlmDirectDbService()
        print("✓ LlmDirectDbService úspěšně vytvořen")

        print("\n=== PŘIDÁVÁNÍ NOVÝCH LLM MODELŮ ===\n")

        total_success = 0

        for provider_name, models in new_models.items():
            print(f"Poskytovatel: {provider_name.upper()}")

            success = add_models_to_provider(service, provider_name, models)
            if success:
                total_success += 1
                print(f"✓ Poskytovatel {provider_name} úspěšně aktualizován\n")
            else:
                print(f"✗ Chyba při aktualizaci poskytovatele {provider_name}\n")

        print(f"=== DOKONČENO ===")
        print(f"Úspěšně aktualizováno {total_success} z {len(new_models)} poskytovatelů")

        return total_success > 0

    except Exception as e:
        print(f"✗ Chyba při přidávání modelů: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
