#!/usr/bin/env python3
"""
Skript pro aktualizaci LLM modelů v databázi podle specifikace uživatele.
"""

import sys
import os
import psycopg2
import json

def load_db_config():
    """Načte konfiguraci databáze ze souboru db.json."""
    config_path = os.path.join('/opt/gent/config', 'db.json')
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        return {
            "host": config.get("host", "localhost"),
            "port": config.get("port", 5432),
            "user": config.get("user"),
            "password": config.get("password"),
            "database": config.get("database")
        }
    except Exception as e:
        print(f"Chyba při načítání konfigurace: {str(e)}")
        return None

def check_current_models():
    """Zkontroluje současný stav modelů v databázi."""
    config = load_db_config()
    if not config:
        print("Nepodařilo se načíst konfiguraci databáze.")
        return False

    try:
        # Připojení k databázi
        print(f"Připojuji se k databázi {config['database']}...")
        conn = psycopg2.connect(
            host=config["host"],
            port=config["port"],
            user=config["user"],
            password=config["password"],
            dbname=config["database"]
        )

        cursor = conn.cursor()

        # Zobrazení poskytovatelů
        print("\n📊 SOUČASNÍ POSKYTOVATELÉ:")
        print("=" * 50)
        cursor.execute("""
            SELECT provider_id, provider_name, is_active
            FROM llm_providers
            ORDER BY provider_name
        """)
        providers = cursor.fetchall()
        for provider_id, name, is_active in providers:
            status = "✅ AKTIVNÍ" if is_active else "❌ NEAKTIVNÍ"
            print(f"ID: {provider_id:2d} | {name:15s} | {status}")

        # Zobrazení modelů podle poskytovatelů
        print("\n🤖 SOUČASNÉ MODELY:")
        print("=" * 80)

        cursor.execute("""
            SELECT
                p.provider_name,
                m.model_name,
                m.model_identifier,
                m.is_active,
                m.is_default
            FROM llm_models m
            JOIN llm_providers p ON m.provider_id = p.provider_id
            ORDER BY p.provider_name, m.model_name
        """)

        current_provider = None
        for provider_name, model_name, model_identifier, is_active, is_default in cursor.fetchall():
            if current_provider != provider_name:
                current_provider = provider_name
                print(f"\n🏢 {provider_name.upper()}:")
                print("-" * 40)

            status = "✅" if is_active else "❌"
            default = " [DEFAULT]" if is_default else ""
            print(f"  {status} {model_name:30s} ({model_identifier}){default}")

        # Statistiky
        print("\n📈 STATISTIKY:")
        print("=" * 30)
        cursor.execute("""
            SELECT
                p.provider_name,
                COUNT(*) as total_models,
                COUNT(CASE WHEN m.is_active = TRUE THEN 1 END) as active_models
            FROM llm_models m
            JOIN llm_providers p ON m.provider_id = p.provider_id
            GROUP BY p.provider_name
            ORDER BY p.provider_name
        """)

        for provider_name, total, active in cursor.fetchall():
            print(f"{provider_name:15s}: {active:2d}/{total:2d} aktivních modelů")

        cursor.close()
        conn.close()

        return True

    except Exception as e:
        print(f"❌ Chyba: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔍 GENT - Kontrola současného stavu LLM modelů")
    print("=" * 60)

    success = check_current_models()

    if not success:
        print("\n💥 Kontrola selhala!")
        sys.exit(1)
