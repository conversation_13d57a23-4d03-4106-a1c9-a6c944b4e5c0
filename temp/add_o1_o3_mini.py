#!/usr/bin/env python3
"""
Skript pro přidání o1-mini a o3-mini modelů do databáze.
Tyto modely se volají stejným způsobem jako o1-preview.
"""

import sys
import os
import psycopg2
import json

def load_db_config():
    """Načte konfiguraci databáze ze souboru db.json."""
    config_path = os.path.join('/opt/gent/config', 'db.json')
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        return {
            "host": config.get("host", "localhost"),
            "port": config.get("port", 5432),
            "user": config.get("user"),
            "password": config.get("password"),
            "database": config.get("database")
        }
    except Exception as e:
        print(f"Chyba při načítání konfigurace: {str(e)}")
        return None

def get_new_models():
    """Definuje nové modely k <PERSON>řid<PERSON>."""
    return [
        # o1-mini model
        {
            'model_name': 'o1-mini',
            'model_identifier': 'o1-mini',
            'context_length': 128000,
            'max_tokens': 65536,
            'capabilities': '{"reasoning": true, "mathematics": true, "science": true, "fast": true, "cost_effective": true}'
        },
        # o3-mini model
        {
            'model_name': 'o3-mini',
            'model_identifier': 'o3-mini',
            'context_length': 128000,
            'max_tokens': 65536,
            'capabilities': '{"reasoning": true, "mathematics": true, "science": true, "advanced": true, "next_generation": true}'
        }
    ]

def add_new_models():
    """Přidá o1-mini a o3-mini modely do databáze."""
    config = load_db_config()
    if not config:
        print("Nepodařilo se načíst konfiguraci databáze.")
        return False

    try:
        # Připojení k databázi
        print(f"Připojuji se k databázi {config['database']}...")
        conn = psycopg2.connect(
            host=config["host"],
            port=config["port"],
            user=config["user"],
            password=config["password"],
            dbname=config["database"]
        )
        
        cursor = conn.cursor()
        
        # Získání ID OpenAI poskytovatele
        print("📋 Získávání ID OpenAI poskytovatele...")
        cursor.execute("""
            SELECT provider_id 
            FROM llm_providers 
            WHERE LOWER(provider_name) = 'openai' 
            AND is_active = TRUE
        """)
        
        result = cursor.fetchone()
        if not result:
            print("❌ OpenAI poskytovatel nebyl nalezen!")
            return False
        
        openai_provider_id = result[0]
        print(f"  ✅ OpenAI: ID {openai_provider_id}")
        
        # Přidání nových modelů
        print("\n➕ Přidávání nových modelů...")
        new_models = get_new_models()
        added_count = 0
        
        for model_data in new_models:
            # Kontrola, zda model už neexistuje
            cursor.execute("""
                SELECT model_id FROM llm_models 
                WHERE provider_id = %s AND model_identifier = %s
            """, (openai_provider_id, model_data['model_identifier']))
            
            if cursor.fetchone():
                print(f"  ⚠️  Model {model_data['model_name']} už existuje v databázi!")
                continue
            
            # Přidání modelu
            cursor.execute("""
                INSERT INTO llm_models (
                    provider_id, model_name, model_identifier, 
                    context_length, max_tokens_output, default_temperature,
                    capabilities, is_active, is_default
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                openai_provider_id, 
                model_data['model_name'], 
                model_data['model_identifier'],
                model_data['context_length'], 
                model_data['max_tokens'], 
                0.7,  # Výchozí hodnota, ale nebude se používat pro o1/o3 modely
                model_data['capabilities'], 
                True, 
                False
            ))
            
            print(f"  ✅ Přidán: {model_data['model_name']}")
            added_count += 1
        
        # Potvrzení změn
        conn.commit()
        print(f"\n🎉 Úspěšně přidáno {added_count} nových modelů!")
        
        # Zobrazení aktuálního stavu OpenAI modelů
        print(f"\n📊 AKTUÁLNÍ OPENAI MODELY:")
        print("=" * 50)
        cursor.execute("""
            SELECT 
                m.model_name,
                m.model_identifier,
                m.is_default
            FROM llm_models m
            WHERE m.provider_id = %s AND m.is_active = TRUE
            ORDER BY m.model_name
        """, (openai_provider_id,))
        
        for model_name, model_identifier, is_default in cursor.fetchall():
            default = " [DEFAULT]" if is_default else ""
            print(f"  ✅ {model_name} ({model_identifier}){default}")
        
        # Zobrazení celkového počtu modelů
        cursor.execute("""
            SELECT COUNT(*) FROM llm_models m
            JOIN llm_providers p ON m.provider_id = p.provider_id
            WHERE m.is_active = TRUE AND p.is_active = TRUE
        """)
        total_models = cursor.fetchone()[0]
        print(f"\n📈 CELKEM MODELŮ V SYSTÉMU: {total_models}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_new_models():
    """Otestuje nové modely přes API."""
    import requests
    
    print("\n🧪 Testování nových modelů...")
    
    models_to_test = ['o1-mini', 'o3-mini']
    
    for model in models_to_test:
        try:
            print(f"\n  📡 Testování {model}...")
            
            # Test API volání
            url = "http://localhost:8001/api/db/llm/test-llm"
            data = {
                "provider_id": "1",  # OpenAI provider ID
                "model_identifier": model,
                "message": "What is 2+2? Answer briefly.",
                "temperature": 0.7,  # Bude ignorováno pro o1/o3 modely
                "max_tokens": 100
            }
            
            response = requests.post(url, json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if 'text' in result:
                    answer = result['text']
                    print(f"    ✅ {model} funguje! Odpověď: {answer[:100]}...")
                else:
                    print(f"    ❌ {model} - Neočekávaná struktura odpovědi: {result}")
            else:
                print(f"    ❌ {model} - API chyba: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"    ❌ {model} - Chyba při testování: {e}")

if __name__ == "__main__":
    print("➕ GENT - Přidání o1-mini a o3-mini modelů")
    print("=" * 60)
    print("⚠️  Tento skript přidá o1-mini a o3-mini modely do databáze")
    print("   Tyto modely se volají stejným způsobem jako o1-preview")
    print("   (bez temperature, s max_completion_tokens)")
    print()
    
    # Potvrzení od uživatele
    response = input("Pokračovat s přidáním o1-mini a o3-mini? (ano/ne): ").lower().strip()
    if response not in ['ano', 'a', 'yes', 'y']:
        print("❌ Operace zrušena uživatelem.")
        sys.exit(0)
    
    # Přidání do databáze
    success = add_new_models()
    
    if success:
        print("\n💡 Restartuji API server...")
        os.system("sudo systemctl restart gent-api")
        
        # Krátká pauza pro restart
        import time
        time.sleep(5)
        
        # Test funkčnosti
        test_new_models()
        
        print("\n🎉 o1-mini a o3-mini modely byly úspěšně přidány!")
        print("💡 Obnov frontend (F5) a zkontroluj dropdown menu")
        print("💡 Všechny o1/o3 modely používají speciální API volání")
    else:
        print("\n💥 Přidání modelů selhalo!")
        sys.exit(1)
