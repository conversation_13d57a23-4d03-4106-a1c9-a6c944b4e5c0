#!/usr/bin/env python3
"""
Skript pro ponechání pouze claude-3-5-sonnet-latest u Anthropic
"""

import sys
import os
sys.path.append('/opt/gent')

from gent.db.direct_connector import get_db_connection

def update_anthropic_models():
    """Deaktivuje všechny Anthropic modely kromě claude-3-5-sonnet-latest"""
    try:
        print("Připojuji se k databázi...")
        conn = get_db_connection('gentdb')
        cursor = conn.cursor()
        
        # Zobrazíme aktuální stav
        print("\n=== AKTUÁLNÍ STAV ANTHROPIC MODELŮ ===")
        cursor.execute("""
            SELECT model_id, model_name, model_identifier, is_active, is_default
            FROM llm_models 
            WHERE provider_id = 'anthropic'
            ORDER BY model_name
        """)
        
        models = cursor.fetchall()
        for model in models:
            print(f"ID: {model[0]}, Název: {model[1]}, Identifiká<PERSON>: {model[2]}, Aktivní: {model[3]}, Výchozí: {model[4]}")
        
        if not models:
            print("Žádné Anthropic modely nenalezeny!")
            return
        
        # Deaktivujeme všechny Anthropic modely
        print("\n=== DEAKTIVUJI VŠECHNY ANTHROPIC MODELY ===")
        cursor.execute("""
            UPDATE llm_models 
            SET is_active = FALSE, is_default = FALSE
            WHERE provider_id = 'anthropic'
        """)
        print(f"Deaktivováno {cursor.rowcount} modelů")
        
        # Aktivujeme pouze claude-3-5-sonnet-latest
        print("\n=== AKTIVUJI CLAUDE-3-5-SONNET-LATEST ===")
        cursor.execute("""
            UPDATE llm_models 
            SET is_active = TRUE, is_default = TRUE
            WHERE provider_id = 'anthropic' 
            AND (
                model_identifier LIKE '%claude-3-5-sonnet-latest%' 
                OR model_name LIKE '%claude-3-5-sonnet-latest%'
                OR model_identifier LIKE '%claude-3-5-sonnet-20241022%'
                OR model_name LIKE '%claude-3-5-sonnet-20241022%'
                OR model_identifier = 'claude-3-5-sonnet-latest'
                OR model_name = 'claude-3-5-sonnet-latest'
            )
        """)
        print(f"Aktivováno {cursor.rowcount} modelů")
        
        # Zobrazíme výsledek
        print("\n=== VÝSLEDNÝ STAV ANTHROPIC MODELŮ ===")
        cursor.execute("""
            SELECT model_id, model_name, model_identifier, is_active, is_default
            FROM llm_models 
            WHERE provider_id = 'anthropic'
            ORDER BY model_name
        """)
        
        models = cursor.fetchall()
        for model in models:
            status = "✅ AKTIVNÍ" if model[3] else "❌ NEAKTIVNÍ"
            default = " (VÝCHOZÍ)" if model[4] else ""
            print(f"{status}{default}: {model[1]} ({model[2]})")
        
        # Počet aktivních modelů
        cursor.execute("""
            SELECT COUNT(*) 
            FROM llm_models 
            WHERE provider_id = 'anthropic' AND is_active = TRUE
        """)
        active_count = cursor.fetchone()[0]
        print(f"\nCelkem aktivních Anthropic modelů: {active_count}")
        
        # Potvrzení změn
        conn.commit()
        print("\n✅ Změny byly uloženy do databáze!")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Chyba: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🔧 Aktualizace Anthropic modelů...")
    success = update_anthropic_models()
    if success:
        print("🎉 Hotovo!")
    else:
        print("💥 Chyba při aktualizaci!")
        sys.exit(1)
