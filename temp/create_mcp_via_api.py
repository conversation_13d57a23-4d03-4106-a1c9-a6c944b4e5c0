#!/usr/bin/env python3
"""
Vytvoření MCP tabulek pomocí GENT API endpointu
"""

import requests
import json
import time

API_BASE = "http://localhost:8001"

def execute_sql(query, description="SQL dotaz"):
    """Spustí SQL dotaz přes GENT API."""
    try:
        print(f"🔄 {description}...")
        
        # URL encode query
        import urllib.parse
        encoded_query = urllib.parse.quote(query)
        
        url = f"{API_BASE}/api/postgres/gentdb/query?query={encoded_query}"
        response = requests.get(url, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print(f"✅ {description} - úspěch")
                return True
            else:
                print(f"❌ {description} - chyba: {result}")
                return False
        else:
            print(f"❌ {description} - HTTP {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ {description} - výjimka: {e}")
        return False

def create_mcp_tables():
    """Vytvoří všechny MCP tabulky pomocí API."""
    
    print("🔧 MCP Management - Vytváření databázových tabulek přes API")
    print("=" * 60)
    
    # 1. Tabulka mcp_providers
    providers_sql = """
    CREATE TABLE IF NOT EXISTS mcp_providers (
        provider_id SERIAL PRIMARY KEY,
        provider_name VARCHAR(100) NOT NULL UNIQUE,
        provider_type VARCHAR(50) NOT NULL,
        display_name VARCHAR(200),
        description TEXT,
        command TEXT NOT NULL,
        base_url VARCHAR(500),
        is_active BOOLEAN DEFAULT true,
        is_custom BOOLEAN DEFAULT false,
        auto_start BOOLEAN DEFAULT true,
        health_check_url VARCHAR(500),
        documentation_url VARCHAR(500),
        version VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    """
    
    if not execute_sql(providers_sql, "Vytváření tabulky mcp_providers"):
        return False
    
    # Indexy pro mcp_providers
    indexes_providers = [
        "CREATE INDEX IF NOT EXISTS idx_mcp_providers_type ON mcp_providers(provider_type);",
        "CREATE INDEX IF NOT EXISTS idx_mcp_providers_active ON mcp_providers(is_active);",
        "CREATE INDEX IF NOT EXISTS idx_mcp_providers_name ON mcp_providers(provider_name);"
    ]
    
    for idx_sql in indexes_providers:
        if not execute_sql(idx_sql, "Vytváření indexu pro mcp_providers"):
            return False
    
    # 2. Tabulka mcp_tools
    tools_sql = """
    CREATE TABLE IF NOT EXISTS mcp_tools (
        tool_id SERIAL PRIMARY KEY,
        provider_id INTEGER REFERENCES mcp_providers(provider_id) ON DELETE CASCADE,
        tool_name VARCHAR(100) NOT NULL,
        tool_identifier VARCHAR(150) NOT NULL,
        display_name VARCHAR(200),
        description TEXT,
        parameters_schema JSONB,
        response_schema JSONB,
        auto_approve BOOLEAN DEFAULT false,
        is_active BOOLEAN DEFAULT true,
        is_dangerous BOOLEAN DEFAULT false,
        capabilities JSONB,
        usage_examples JSONB,
        rate_limit_per_minute INTEGER DEFAULT 60,
        timeout_seconds INTEGER DEFAULT 30,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(provider_id, tool_identifier)
    );
    """
    
    if not execute_sql(tools_sql, "Vytváření tabulky mcp_tools"):
        return False
    
    # Indexy pro mcp_tools
    indexes_tools = [
        "CREATE INDEX IF NOT EXISTS idx_mcp_tools_provider ON mcp_tools(provider_id);",
        "CREATE INDEX IF NOT EXISTS idx_mcp_tools_active ON mcp_tools(is_active);",
        "CREATE INDEX IF NOT EXISTS idx_mcp_tools_name ON mcp_tools(tool_name);",
        "CREATE INDEX IF NOT EXISTS idx_mcp_tools_capabilities ON mcp_tools USING GIN(capabilities);"
    ]
    
    for idx_sql in indexes_tools:
        if not execute_sql(idx_sql, "Vytváření indexu pro mcp_tools"):
            return False
    
    # 3. Tabulka mcp_configurations
    configs_sql = """
    CREATE TABLE IF NOT EXISTS mcp_configurations (
        config_id SERIAL PRIMARY KEY,
        provider_id INTEGER REFERENCES mcp_providers(provider_id) ON DELETE CASCADE,
        config_name VARCHAR(100) NOT NULL,
        environment_vars JSONB,
        startup_args JSONB,
        working_directory VARCHAR(500),
        timeout_seconds INTEGER DEFAULT 30,
        retry_count INTEGER DEFAULT 3,
        retry_delay_ms INTEGER DEFAULT 1000,
        max_concurrent_requests INTEGER DEFAULT 10,
        security_settings JSONB,
        resource_limits JSONB,
        is_default BOOLEAN DEFAULT false,
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(provider_id, config_name)
    );
    """
    
    if not execute_sql(configs_sql, "Vytváření tabulky mcp_configurations"):
        return False
    
    # Indexy pro mcp_configurations
    indexes_configs = [
        "CREATE INDEX IF NOT EXISTS idx_mcp_configs_provider ON mcp_configurations(provider_id);",
        "CREATE INDEX IF NOT EXISTS idx_mcp_configs_default ON mcp_configurations(is_default);"
    ]
    
    for idx_sql in indexes_configs:
        if not execute_sql(idx_sql, "Vytváření indexu pro mcp_configurations"):
            return False
    
    print("\n✅ Základní MCP tabulky úspěšně vytvořeny!")
    return True

def create_mcp_monitoring_tables():
    """Vytvoří monitoring tabulky pro MCP."""
    
    print("\n📊 Vytváření monitoring tabulek...")
    
    # 4. Tabulka mcp_requests
    requests_sql = """
    CREATE TABLE IF NOT EXISTS mcp_requests (
        request_id SERIAL PRIMARY KEY,
        tool_id INTEGER REFERENCES mcp_tools(tool_id),
        provider_id INTEGER REFERENCES mcp_providers(provider_id),
        request_data JSONB,
        response_data JSONB,
        response_time_ms FLOAT,
        status VARCHAR(20) NOT NULL CHECK (status IN ('success', 'error', 'timeout', 'cancelled')),
        error_message TEXT,
        error_code VARCHAR(50),
        user_id VARCHAR(100),
        session_id VARCHAR(100),
        request_size_bytes INTEGER,
        response_size_bytes INTEGER,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    """
    
    if not execute_sql(requests_sql, "Vytváření tabulky mcp_requests"):
        return False
    
    # Indexy pro mcp_requests
    indexes_requests = [
        "CREATE INDEX IF NOT EXISTS idx_mcp_requests_tool ON mcp_requests(tool_id);",
        "CREATE INDEX IF NOT EXISTS idx_mcp_requests_provider ON mcp_requests(provider_id);",
        "CREATE INDEX IF NOT EXISTS idx_mcp_requests_status ON mcp_requests(status);",
        "CREATE INDEX IF NOT EXISTS idx_mcp_requests_created_at ON mcp_requests(created_at);",
        "CREATE INDEX IF NOT EXISTS idx_mcp_requests_response_time ON mcp_requests(response_time_ms);"
    ]
    
    for idx_sql in indexes_requests:
        if not execute_sql(idx_sql, "Vytváření indexu pro mcp_requests"):
            return False
    
    # 5. Tabulka mcp_server_status
    status_sql = """
    CREATE TABLE IF NOT EXISTS mcp_server_status (
        status_id SERIAL PRIMARY KEY,
        provider_id INTEGER REFERENCES mcp_providers(provider_id) ON DELETE CASCADE UNIQUE,
        status VARCHAR(20) NOT NULL CHECK (status IN ('online', 'offline', 'error', 'starting', 'stopping')),
        process_id INTEGER,
        port INTEGER,
        memory_usage_mb FLOAT,
        cpu_usage_percent FLOAT,
        uptime_seconds INTEGER,
        last_health_check TIMESTAMP,
        health_check_status VARCHAR(20) CHECK (health_check_status IN ('healthy', 'unhealthy', 'unknown')),
        error_message TEXT,
        restart_count INTEGER DEFAULT 0,
        last_restart TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    """
    
    if not execute_sql(status_sql, "Vytváření tabulky mcp_server_status"):
        return False
    
    # Indexy pro mcp_server_status
    indexes_status = [
        "CREATE INDEX IF NOT EXISTS idx_mcp_status_provider ON mcp_server_status(provider_id);",
        "CREATE INDEX IF NOT EXISTS idx_mcp_status_status ON mcp_server_status(status);",
        "CREATE INDEX IF NOT EXISTS idx_mcp_status_health ON mcp_server_status(health_check_status);"
    ]
    
    for idx_sql in indexes_status:
        if not execute_sql(idx_sql, "Vytváření indexu pro mcp_server_status"):
            return False
    
    # 6. Tabulka mcp_permissions
    permissions_sql = """
    CREATE TABLE IF NOT EXISTS mcp_permissions (
        permission_id SERIAL PRIMARY KEY,
        user_id VARCHAR(100),
        tool_id INTEGER REFERENCES mcp_tools(tool_id) ON DELETE CASCADE,
        provider_id INTEGER REFERENCES mcp_providers(provider_id) ON DELETE CASCADE,
        permission_level VARCHAR(20) NOT NULL CHECK (permission_level IN ('read', 'write', 'execute', 'admin')),
        allowed_operations JSONB,
        restrictions JSONB,
        is_active BOOLEAN DEFAULT true,
        granted_by VARCHAR(100),
        granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        expires_at TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    """
    
    if not execute_sql(permissions_sql, "Vytváření tabulky mcp_permissions"):
        return False
    
    # Indexy pro mcp_permissions
    indexes_permissions = [
        "CREATE INDEX IF NOT EXISTS idx_mcp_permissions_user ON mcp_permissions(user_id);",
        "CREATE INDEX IF NOT EXISTS idx_mcp_permissions_tool ON mcp_permissions(tool_id);",
        "CREATE INDEX IF NOT EXISTS idx_mcp_permissions_provider ON mcp_permissions(provider_id);",
        "CREATE INDEX IF NOT EXISTS idx_mcp_permissions_active ON mcp_permissions(is_active);"
    ]
    
    for idx_sql in indexes_permissions:
        if not execute_sql(idx_sql, "Vytváření indexu pro mcp_permissions"):
            return False
    
    print("✅ Monitoring tabulky úspěšně vytvořeny!")
    return True

if __name__ == "__main__":
    print("🚀 Začínám vytváření MCP tabulek...")
    
    # Vytvoření základních tabulek
    if not create_mcp_tables():
        print("❌ Chyba při vytváření základních tabulek!")
        exit(1)
    
    # Vytvoření monitoring tabulek
    if not create_mcp_monitoring_tables():
        print("❌ Chyba při vytváření monitoring tabulek!")
        exit(1)
    
    print("\n🎉 Všechny MCP tabulky byly úspěšně vytvořeny!")
    print("📋 Vytvořené tabulky:")
    print("   - mcp_providers (poskytovatelé MCP serverů)")
    print("   - mcp_tools (nástroje dostupné v MCP serverech)")
    print("   - mcp_configurations (konfigurace MCP serverů)")
    print("   - mcp_requests (log všech MCP requestů)")
    print("   - mcp_server_status (aktuální stav MCP serverů)")
    print("   - mcp_permissions (oprávnění pro MCP nástroje)")
    print("\n🔄 Další krok: Spustit import existujících MCP serverů")
