#!/usr/bin/env python3
"""
Skript pro přidání GPT-4 modelů do databáze.
Tyto modely se volají stejným způsobem jako o1/o3 modely.
POZOR: Case-sensitive názvy!
"""

import sys
import os
import psycopg2
import json

def load_db_config():
    """Načte konfiguraci databáze ze souboru db.json."""
    config_path = os.path.join('/opt/gent/config', 'db.json')
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        return {
            "host": config.get("host", "localhost"),
            "port": config.get("port", 5432),
            "user": config.get("user"),
            "password": config.get("password"),
            "database": config.get("database")
        }
    except Exception as e:
        print(f"Chyba při načítání konfigurace: {str(e)}")
        return None

def get_new_models():
    """Definuje nové GPT-4 modely k přidání."""
    return [
        # gpt-4o (už existuje, ale zkontrolujeme)
        {
            'model_name': 'gpt-4o',
            'model_identifier': 'gpt-4o',
            'context_length': 128000,
            'max_tokens': 4096,
            'capabilities': '{"text": true, "vision": true, "advanced": true, "multimodal": true}'
        },
        # gpt-4o-mini (už existuje, ale zkontrolujeme)
        {
            'model_name': 'gpt-4o-mini',
            'model_identifier': 'gpt-4o-mini',
            'context_length': 128000,
            'max_tokens': 16384,
            'capabilities': '{"text": true, "vision": true, "fast": true, "cost_effective": true}'
        },
        # gpt-4.1 - CASE SENSITIVE!
        {
            'model_name': 'gpt-4.1',
            'model_identifier': 'gpt-4.1',
            'context_length': 128000,
            'max_tokens': 4096,
            'capabilities': '{"text": true, "advanced": true, "reasoning": true, "next_generation": true}'
        },
        # gpt-4.1-mini - CASE SENSITIVE!
        {
            'model_name': 'gpt-4.1-mini',
            'model_identifier': 'gpt-4.1-mini',
            'context_length': 128000,
            'max_tokens': 16384,
            'capabilities': '{"text": true, "fast": true, "cost_effective": true, "reasoning": true}'
        },
        # gpt-4.1-nano - CASE SENSITIVE!
        {
            'model_name': 'gpt-4.1-nano',
            'model_identifier': 'gpt-4.1-nano',
            'context_length': 128000,
            'max_tokens': 8192,
            'capabilities': '{"text": true, "ultra_fast": true, "ultra_cost_effective": true, "lightweight": true}'
        }
    ]

def add_new_models():
    """Přidá nové GPT-4 modely do databáze."""
    config = load_db_config()
    if not config:
        print("Nepodařilo se načíst konfiguraci databáze.")
        return False

    try:
        # Připojení k databázi
        print(f"Připojuji se k databázi {config['database']}...")
        conn = psycopg2.connect(
            host=config["host"],
            port=config["port"],
            user=config["user"],
            password=config["password"],
            dbname=config["database"]
        )
        
        cursor = conn.cursor()
        
        # Získání ID OpenAI poskytovatele
        print("📋 Získávání ID OpenAI poskytovatele...")
        cursor.execute("""
            SELECT provider_id 
            FROM llm_providers 
            WHERE LOWER(provider_name) = 'openai' 
            AND is_active = TRUE
        """)
        
        result = cursor.fetchone()
        if not result:
            print("❌ OpenAI poskytovatel nebyl nalezen!")
            return False
        
        openai_provider_id = result[0]
        print(f"  ✅ OpenAI: ID {openai_provider_id}")
        
        # Přidání nových modelů
        print("\n➕ Přidávání nových GPT-4 modelů...")
        new_models = get_new_models()
        added_count = 0
        updated_count = 0
        
        for model_data in new_models:
            # Kontrola, zda model už neexistuje
            cursor.execute("""
                SELECT model_id FROM llm_models 
                WHERE provider_id = %s AND model_identifier = %s
            """, (openai_provider_id, model_data['model_identifier']))
            
            existing = cursor.fetchone()
            if existing:
                print(f"  ⚠️  Model {model_data['model_name']} už existuje v databázi!")
                continue
            
            # Přidání modelu
            cursor.execute("""
                INSERT INTO llm_models (
                    provider_id, model_name, model_identifier, 
                    context_length, max_tokens_output, default_temperature,
                    capabilities, is_active, is_default
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                openai_provider_id, 
                model_data['model_name'], 
                model_data['model_identifier'],
                model_data['context_length'], 
                model_data['max_tokens'], 
                0.7,  # Výchozí hodnota, ale nebude se používat pro speciální modely
                model_data['capabilities'], 
                True, 
                False
            ))
            
            print(f"  ✅ Přidán: {model_data['model_name']}")
            added_count += 1
        
        # Potvrzení změn
        conn.commit()
        print(f"\n🎉 Úspěšně přidáno {added_count} nových modelů!")
        
        # Zobrazení aktuálního stavu OpenAI modelů
        print(f"\n📊 AKTUÁLNÍ OPENAI MODELY:")
        print("=" * 50)
        cursor.execute("""
            SELECT 
                m.model_name,
                m.model_identifier,
                m.is_default
            FROM llm_models m
            WHERE m.provider_id = %s AND m.is_active = TRUE
            ORDER BY m.model_name
        """, (openai_provider_id,))
        
        for model_name, model_identifier, is_default in cursor.fetchall():
            default = " [DEFAULT]" if is_default else ""
            print(f"  ✅ {model_name} ({model_identifier}){default}")
        
        # Zobrazení celkového počtu modelů
        cursor.execute("""
            SELECT COUNT(*) FROM llm_models m
            JOIN llm_providers p ON m.provider_id = p.provider_id
            WHERE m.is_active = TRUE AND p.is_active = TRUE
        """)
        total_models = cursor.fetchone()[0]
        print(f"\n📈 CELKEM MODELŮ V SYSTÉMU: {total_models}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("➕ GENT - Přidání GPT-4 modelů")
    print("=" * 60)
    print("⚠️  Tento skript přidá tyto GPT-4 modely:")
    print("   - gpt-4o (zkontroluje existenci)")
    print("   - gpt-4o-mini (zkontroluje existenci)")
    print("   - gpt-4.1 (CASE SENSITIVE!)")
    print("   - gpt-4.1-mini (CASE SENSITIVE!)")
    print("   - gpt-4.1-nano (CASE SENSITIVE!)")
    print("   Všechny se volají stejně jako o1/o3 modely")
    print("   (bez temperature, s max_completion_tokens)")
    print()
    
    # Potvrzení od uživatele
    response = input("Pokračovat s přidáním GPT-4 modelů? (ano/ne): ").lower().strip()
    if response not in ['ano', 'a', 'yes', 'y']:
        print("❌ Operace zrušena uživatelem.")
        sys.exit(0)
    
    # Přidání do databáze
    success = add_new_models()
    
    if success:
        print("\n💡 Restartuji API server...")
        os.system("sudo systemctl restart gent-api")
        
        print("\n🎉 GPT-4 modely byly úspěšně přidány!")
        print("💡 Obnov frontend (F5) a zkontroluj dropdown menu")
        print("💡 Všechny GPT-4.x modely používají speciální API volání")
        print("💡 POZOR: Názvy jsou case-sensitive!")
    else:
        print("\n💥 Přidání modelů selhalo!")
        sys.exit(1)
