#!/usr/bin/env python3
"""
Vyčištění MCP databáze - ponechat jen fetch, brave-search, tavily, perplexity
"""

import psycopg2
import json

def get_db_connection():
    """Získá připojení k databázi."""
    try:
        with open('/opt/gent/config/postgres_credentials.json', 'r') as f:
            creds = json.load(f)
        
        conn = psycopg2.connect(
            host='localhost',
            database='gentdb',
            user='postgres',
            password=creds['postgres_password']
        )
        return conn
    except Exception as e:
        print(f"❌ Chyba při připojování k databázi: {e}")
        return None

def cleanup_mcp_providers():
    """Odstraní nepotřebné MCP poskytovatele, ponechá jen hlavní 4."""
    conn = get_db_connection()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    try:
        print("🧹 Čištění MCP databáze...")
        
        # Ponechat jen tyto poskytovatele
        keep_providers = ['fetch', 'brave-search', 'tavily', 'perplexity']
        
        # Nejdříve zjistíme, co máme
        cursor.execute("SELECT provider_id, provider_name FROM mcp_providers")
        all_providers = cursor.fetchall()
        
        print("📋 Současní poskytovatelé:")
        for provider in all_providers:
            print(f"  - ID: {provider[0]}, Název: {provider[1]}")
        
        # Smazání nástrojů pro nepotřebné poskytovatele
        cursor.execute("""
            DELETE FROM mcp_tools 
            WHERE provider_id IN (
                SELECT provider_id FROM mcp_providers 
                WHERE provider_name NOT IN %s
            )
        """, (tuple(keep_providers),))
        
        deleted_tools = cursor.rowcount
        print(f"🗑️ Smazáno {deleted_tools} nástrojů")
        
        # Smazání nepotřebných poskytovatelů
        cursor.execute("""
            DELETE FROM mcp_providers 
            WHERE provider_name NOT IN %s
        """, (tuple(keep_providers),))
        
        deleted_providers = cursor.rowcount
        print(f"🗑️ Smazáno {deleted_providers} poskytovatelů")
        
        conn.commit()
        
        # Kontrola výsledku
        cursor.execute("SELECT provider_id, provider_name, provider_type FROM mcp_providers ORDER BY provider_name")
        remaining_providers = cursor.fetchall()
        
        print("\n✅ Zbývající poskytovatelé:")
        for provider in remaining_providers:
            print(f"  - ID: {provider[0]}, Název: {provider[1]}, Typ: {provider[2]}")
        
        # Kontrola nástrojů
        cursor.execute("""
            SELECT p.provider_name, COUNT(t.tool_id) as tools_count
            FROM mcp_providers p
            LEFT JOIN mcp_tools t ON p.provider_id = t.provider_id
            GROUP BY p.provider_id, p.provider_name
            ORDER BY p.provider_name
        """)
        
        tools_stats = cursor.fetchall()
        print("\n📊 Nástroje podle poskytovatelů:")
        for stat in tools_stats:
            print(f"  - {stat[0]}: {stat[1]} nástrojů")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při čištění databáze: {e}")
        conn.rollback()
        return False
    finally:
        cursor.close()
        conn.close()

def update_provider_descriptions():
    """Aktualizuje popisy poskytovatelů."""
    conn = get_db_connection()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    try:
        print("\n📝 Aktualizace popisů poskytovatelů...")
        
        descriptions = {
            'fetch': 'Stahování webových stránek a API dat',
            'brave-search': 'Vyhledávání na internetu pomocí Brave Search API',
            'tavily': 'Pokročilé AI vyhledávání s analýzou obsahu',
            'perplexity': 'AI asistent pro odpovědi na otázky s citacemi'
        }
        
        for provider_name, description in descriptions.items():
            cursor.execute("""
                UPDATE mcp_providers 
                SET description = %s
                WHERE provider_name = %s
            """, (description, provider_name))
            
            if cursor.rowcount > 0:
                print(f"✅ Aktualizován popis pro {provider_name}")
        
        conn.commit()
        return True
        
    except Exception as e:
        print(f"❌ Chyba při aktualizaci popisů: {e}")
        conn.rollback()
        return False
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    print("🧹 MCP Management - Čištění databáze")
    print("=" * 50)
    
    # Vyčištění databáze
    if not cleanup_mcp_providers():
        print("❌ Chyba při čištění databáze!")
        exit(1)
    
    # Aktualizace popisů
    if not update_provider_descriptions():
        print("❌ Chyba při aktualizaci popisů!")
        exit(1)
    
    print("\n🎉 MCP databáze byla úspěšně vyčištěna!")
    print("📋 Ponechány jen hlavní poskytovatelé:")
    print("   1. 🌐 Fetch - stahování webových stránek")
    print("   2. 🔍 Brave Search - vyhledávání na internetu")
    print("   3. 🔍 Tavily - pokročilé AI vyhledávání")
    print("   4. 🤖 Perplexity - AI asistent s citacemi")
