#!/usr/bin/env python3
"""
Script pro přidání OpenRouter modelů do GENT databáze.
Přidá pouze nejvýkonnější modely podle požadavků uživatele.
"""

import psycopg2
import json

def add_openrouter_models():
    """Přidá OpenRouter modely do databáze."""
    print("➕ GENT - Přidání OpenRouter modelů")
    print("=" * 60)
    print("⚠️  Tento skript přidá tyto OpenRouter modely:")
    print("   - nousresearch/deephermes-3-mistral-24b-preview:free")
    print("   - qwen/qwen3-235b-a22b:free")
    print("   - tngtech/deepseek-r1t-chimera:free")
    print("   - openai/codex-mini")
    print("   - google/gemini-2.5-pro-preview")
    print("   - deepseek/deepseek-prover-v2:free")
    print("   - qwen/qwen3-32b:free")
    print("   - anthropic/claude-opus-4")
    print("   - mistralai/devstral-small:free")
    print("   - deepseek/deepseek-r1-0528:free")
    print()

    response = input("Pokračovat s přidáním OpenRouter modelů? (ano/ne): ")
    if response.lower() != 'ano':
        print("❌ Operace zrušena uživatelem")
        return

    try:
        # Připojení k databázi
        print("Připojuji se k databázi gentdb...")
        conn = psycopg2.connect(
            host='localhost',
            port=5432,
            user='gent_app',
            password='gent1234secure',
            database='gentdb'
        )

        cursor = conn.cursor()

        # Získání ID OpenRouter providera
        print("📋 Získávání ID OpenRouter poskytovatele...")
        cursor.execute('SELECT provider_id FROM llm_providers WHERE provider_name = %s', ('Openrouter',))
        result = cursor.fetchone()

        if not result:
            print("❌ OpenRouter provider nenalezen v databázi!")
            return

        openrouter_id = result[0]
        print(f"  ✅ OpenRouter: ID {openrouter_id}")

        # Definice OpenRouter modelů
        openrouter_models = [
            {
                'model_name': 'deephermes-3-mistral-24b-preview',
                'model_identifier': 'nousresearch/deephermes-3-mistral-24b-preview:free',
                'context_length': 32768,
                'max_tokens': 4096,
                'capabilities': '{"text": true, "free": true, "reasoning": true}'
            },
            {
                'model_name': 'qwen3-235b-a22b',
                'model_identifier': 'qwen/qwen3-235b-a22b:free',
                'context_length': 131072,
                'max_tokens': 8192,
                'capabilities': '{"text": true, "free": true, "large": true}'
            },
            {
                'model_name': 'deepseek-r1t-chimera',
                'model_identifier': 'tngtech/deepseek-r1t-chimera:free',
                'context_length': 65536,
                'max_tokens': 4096,
                'capabilities': '{"text": true, "free": true, "reasoning": true}'
            },
            {
                'model_name': 'codex-mini',
                'model_identifier': 'openai/codex-mini',
                'context_length': 8192,
                'max_tokens': 2048,
                'capabilities': '{"text": true, "code": true}'
            },
            {
                'model_name': 'gemini-2.5-pro-preview',
                'model_identifier': 'google/gemini-2.5-pro-preview',
                'context_length': 2097152,
                'max_tokens': 8192,
                'capabilities': '{"text": true, "multimodal": true, "large_context": true}'
            },
            {
                'model_name': 'deepseek-prover-v2',
                'model_identifier': 'deepseek/deepseek-prover-v2:free',
                'context_length': 32768,
                'max_tokens': 4096,
                'capabilities': '{"text": true, "free": true, "math": true, "reasoning": true}'
            },
            {
                'model_name': 'qwen3-32b',
                'model_identifier': 'qwen/qwen3-32b:free',
                'context_length': 131072,
                'max_tokens': 8192,
                'capabilities': '{"text": true, "free": true}'
            },
            {
                'model_name': 'claude-opus-4',
                'model_identifier': 'anthropic/claude-opus-4',
                'context_length': 200000,
                'max_tokens': 8192,
                'capabilities': '{"text": true, "advanced": true}'
            },
            {
                'model_name': 'devstral-small',
                'model_identifier': 'mistralai/devstral-small:free',
                'context_length': 32768,
                'max_tokens': 4096,
                'capabilities': '{"text": true, "free": true, "code": true}'
            },
            {
                'model_name': 'deepseek-r1-0528',
                'model_identifier': 'deepseek/deepseek-r1-0528:free',
                'context_length': 65536,
                'max_tokens': 4096,
                'capabilities': '{"text": true, "free": true, "reasoning": true}'
            }
        ]

        print("\n➕ Přidávání OpenRouter modelů...")

        added_count = 0
        for model in openrouter_models:
            # Kontrola, zda model už neexistuje
            cursor.execute(
                'SELECT model_id FROM llm_models WHERE provider_id = %s AND model_identifier = %s',
                (openrouter_id, model['model_identifier'])
            )

            if cursor.fetchone():
                print(f"  ⚠️  Model {model['model_name']} už existuje v databázi!")
                continue

            # Přidání modelu
            cursor.execute('''
                INSERT INTO llm_models (
                    provider_id, model_name, model_identifier, context_length,
                    max_tokens_output, default_temperature, capabilities, is_active, is_default
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
            ''', (
                openrouter_id,
                model['model_name'],
                model['model_identifier'],
                model['context_length'],
                model['max_tokens'],
                0.7,  # default_temperature
                model['capabilities'],
                True,  # is_active
                False  # is_default
            ))

            print(f"  ✅ Přidán: {model['model_name']}")
            added_count += 1

        # Nastavení výchozího modelu (první free model)
        if added_count > 0:
            print("\n🎯 Nastavování výchozího OpenRouter modelu...")
            cursor.execute('''
                UPDATE llm_models
                SET is_default = TRUE
                WHERE provider_id = %s AND model_identifier = %s
            ''', (openrouter_id, 'nousresearch/deephermes-3-mistral-24b-preview:free'))
            print("  ✅ OpenRouter: deephermes-3-mistral-24b-preview")

        # Commit změn
        conn.commit()

        print(f"\n🎉 Úspěšně přidáno {added_count} nových OpenRouter modelů!")

        # Zobrazení aktuálního stavu
        cursor.execute('''
            SELECT model_name, model_identifier, is_default
            FROM llm_models
            WHERE provider_id = %s
            ORDER BY model_name
        ''')
        models = cursor.fetchall()

        print("\n📊 AKTUÁLNÍ OPENROUTER MODELY:")
        print("=" * 60)
        for model_name, model_identifier, is_default in models:
            default_mark = " [DEFAULT]" if is_default else ""
            print(f"  ✅ {model_name} ({model_identifier}){default_mark}")

        # Celkové statistiky
        cursor.execute('SELECT COUNT(*) FROM llm_models WHERE is_active = TRUE')
        total_models = cursor.fetchone()[0]

        print(f"\n📈 CELKEM MODELŮ V SYSTÉMU: {total_models}")

        cursor.close()
        conn.close()

        print("\n💡 Restartuji API server...")
        import subprocess
        subprocess.run(['sudo', 'systemctl', 'restart', 'gent-api'], check=False)

        print("\n🎉 OpenRouter modely byly úspěšně přidány!")
        print("💡 Obnov frontend (F5) a zkontroluj dropdown menu")
        print("💡 OpenRouter modely používají OpenAI-kompatibilní API")
        print("💡 Free modely jsou označené ':free' v názvu")

    except Exception as e:
        print(f"❌ Chyba při přidávání OpenRouter modelů: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

if __name__ == "__main__":
    add_openrouter_models()
