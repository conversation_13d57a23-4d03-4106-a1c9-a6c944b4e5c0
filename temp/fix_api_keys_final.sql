-- Oprava API klíčů v MCP databázi

-- Nastavení API klíčů
UPDATE mcp_providers SET api_key = 'BSARir7CGmpWKz5mvNgGJyYp3yV8CDn' WHERE provider_name = 'brave-search';
UPDATE mcp_providers SET api_key = 'tvly-CLEK7GE21FGF7KZBMTEJZA5S' WHERE provider_name = 'tavily';
UPDATE mcp_providers SET api_key = '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' WHERE provider_name = 'perplexity';
UPDATE mcp_providers SET api_key = NULL WHERE provider_name = 'fetch';

-- Kontrola výsledku
SELECT provider_name, 
       CASE WHEN api_key IS NOT NULL THEN 'ANO' ELSE 'NE' END as has_api_key,
       CASE WHEN api_key IS NOT NULL THEN LEFT(api_key, 8) || '...' ELSE 'NULL' END as api_key_preview
FROM mcp_providers 
ORDER BY provider_name;
