#!/usr/bin/env python3
"""
Skript pro přidání chybějíc<PERSON><PERSON> modelů do databáze
"""

import sys
import os
sys.path.append('/opt/gent')

from gent.db.direct_connector import get_db_connection

def add_missing_models():
    """Přidá chybějící modely do databáze"""
    try:
        print("🔄 Připojuji se k databázi...")
        conn = get_db_connection('gentdb')
        cursor = conn.cursor()
        
        # Nejprve zjistíme ID poskytovatelů
        cursor.execute("""
            SELECT provider_id, provider_name 
            FROM llm_providers 
            WHERE LOWER(provider_name) IN ('openai', 'google', 'anthropic')
            AND is_active = TRUE
        """)
        
        providers = {row[1].lower(): row[0] for row in cursor.fetchall()}
        print(f"📋 Nalezení poskytovatelé: {providers}")
        
        # Definice chybě<PERSON><PERSON><PERSON><PERSON><PERSON> modelů
        missing_models = []
        
        # OpenAI modely
        if 'openai' in providers:
            openai_models = [
                {
                    'name': 'GPT-4o',
                    'identifier': 'gpt-4o',
                    'context_length': 128000,
                    'max_tokens': 4096,
                    'temperature': 0.7,
                    'capabilities': '{"text": true, "code": true, "reasoning": true, "function_calling": true}'
                },
                {
                    'name': 'GPT-4.5',
                    'identifier': 'gpt-4.5',
                    'context_length': 200000,
                    'max_tokens': 4096,
                    'temperature': 0.7,
                    'capabilities': '{"text": true, "code": true, "reasoning": true, "function_calling": true, "advanced_reasoning": true}'
                },
                {
                    'name': 'GPT-4-1',
                    'identifier': 'gpt-4-1',
                    'context_length': 200000,
                    'max_tokens': 4096,
                    'temperature': 0.7,
                    'capabilities': '{"text": true, "code": true, "reasoning": true, "function_calling": true, "advanced_reasoning": true}'
                },
                {
                    'name': 'GPT-4-1 Mini',
                    'identifier': 'gpt-4-1-mini',
                    'context_length': 128000,
                    'max_tokens': 4096,
                    'temperature': 0.7,
                    'capabilities': '{"text": true, "code": true, "reasoning": true, "function_calling": true}'
                },
                {
                    'name': 'GPT-4-1 Nano',
                    'identifier': 'gpt-4-1-nano',
                    'context_length': 32000,
                    'max_tokens': 2048,
                    'temperature': 0.7,
                    'capabilities': '{"text": true, "code": true, "function_calling": true}'
                },
                {
                    'name': 'O3',
                    'identifier': 'o3',
                    'context_length': 128000,
                    'max_tokens': 4096,
                    'temperature': 0.7,
                    'capabilities': '{"reasoning": true, "mathematics": true, "science": true, "logic": true, "problem_solving": true}'
                },
                {
                    'name': 'O3 Mini',
                    'identifier': 'o3-mini',
                    'context_length': 64000,
                    'max_tokens': 2048,
                    'temperature': 0.7,
                    'capabilities': '{"reasoning": true, "mathematics": true, "logic": true, "problem_solving": true}'
                }
            ]
            
            for model in openai_models:
                missing_models.append({
                    'provider_id': providers['openai'],
                    'provider_name': 'OpenAI',
                    **model
                })
        
        # Google modely
        if 'google' in providers:
            google_models = [
                {
                    'name': 'Gemini 2.0 Flash',
                    'identifier': 'gemini-2.0-flash',
                    'context_length': 1000000,
                    'max_tokens': 8192,
                    'temperature': 0.7,
                    'capabilities': '{"text": true, "vision": true, "audio": true, "video": true, "code": true, "multimodal": true}'
                },
                {
                    'name': 'Gemini 2.5 Pro Preview 05-06',
                    'identifier': 'gemini-2.5-pro-preview-05-06',
                    'context_length': 2000000,
                    'max_tokens': 8192,
                    'temperature': 0.7,
                    'capabilities': '{"text": true, "code": true, "reasoning": true, "analysis": true, "research": true}'
                },
                {
                    'name': 'Gemini 2.5 Flash Preview 05-20',
                    'identifier': 'gemini-2.5-flash-preview-05-20',
                    'context_length': 1000000,
                    'max_tokens': 8192,
                    'temperature': 0.7,
                    'capabilities': '{"text": true, "code": true, "quick_analysis": true, "general": true}'
                }
            ]
            
            for model in google_models:
                missing_models.append({
                    'provider_id': providers['google'],
                    'provider_name': 'Google',
                    **model
                })
        
        # Přidáme modely do databáze
        added_count = 0
        
        for model in missing_models:
            # Zkontrolujeme, zda model už neexistuje
            cursor.execute("""
                SELECT COUNT(*) 
                FROM llm_models 
                WHERE provider_id = %s AND model_identifier = %s
            """, (model['provider_id'], model['identifier']))
            
            if cursor.fetchone()[0] == 0:
                # Model neexistuje, přidáme ho
                cursor.execute("""
                    INSERT INTO llm_models (
                        provider_id, model_name, model_identifier, 
                        context_length, max_tokens_output, default_temperature,
                        capabilities, is_active, is_default
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    model['provider_id'],
                    model['name'],
                    model['identifier'],
                    model['context_length'],
                    model['max_tokens'],
                    model['temperature'],
                    model['capabilities'],
                    True,  # is_active
                    False  # is_default
                ))
                
                print(f"✅ Přidán model: {model['provider_name']} - {model['name']}")
                added_count += 1
            else:
                print(f"⚠️  Model už existuje: {model['provider_name']} - {model['name']}")
        
        # Potvrzení změn
        conn.commit()
        print(f"\n🎉 Úspěšně přidáno {added_count} nových modelů!")
        
        # Zobrazíme aktuální stav
        cursor.execute("""
            SELECT p.provider_name, COUNT(*) as model_count
            FROM llm_models m
            JOIN llm_providers p ON m.provider_id = p.provider_id
            WHERE m.is_active = TRUE AND p.is_active = TRUE
            GROUP BY p.provider_name
            ORDER BY p.provider_name
        """)
        
        print(f"\n📊 Aktuální stav modelů v databázi:")
        for row in cursor.fetchall():
            print(f"  {row[0]}: {row[1]} modelů")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🤖 GENT - Přidání chybějících modelů")
    print("=" * 50)
    
    success = add_missing_models()
    
    if success:
        print("\n🎉 Modely byly úspěšně přidány!")
        print("💡 Restartuj API server: sudo systemctl restart gent-api")
    else:
        print("\n💥 Přidání modelů selhalo!")
        sys.exit(1)
