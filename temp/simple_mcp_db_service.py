#!/usr/bin/env python3
"""
Jednoduchá MCP DB služba bez závislostí na GENT modulech
"""

import json
import os
import psycopg2
from typing import Dict, List, Any, Optional

class SimpleMcpDbService:
    """Jednoduchá služba pro práci s MCP databází."""
    
    def __init__(self):
        """Inicializace služby."""
        self.config_path = '/opt/gent/config/postgres_credentials.json'
    
    def _get_connection(self):
        """Získá připojení k PostgreSQL databázi."""
        try:
            # Načtení přístupových údajů
            with open(self.config_path, "r") as f:
                creds = json.load(f)
            
            # Vytvoření připojení
            conn = psycopg2.connect(
                host="localhost",
                database="gentdb",
                user="postgres",
                password=creds["postgres_password"]
            )
            
            return conn
        except Exception as e:
            print(f"Chyba při připojování k datab<PERSON>zi: {str(e)}")
            raise
    
    def ensure_mcp_tables(self):
        """<PERSON>aj<PERSON><PERSON>, že MCP tabulky existují v databázi."""
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            print("🚀 Vytváření MCP tabulek...")
            
            # Vytvoření tabulky mcp_providers
            print("📋 Vytvářím tabulku mcp_providers...")
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS mcp_providers (
                    provider_id SERIAL PRIMARY KEY,
                    provider_name VARCHAR(100) NOT NULL UNIQUE,
                    provider_type VARCHAR(50) NOT NULL DEFAULT 'mcp',
                    display_name VARCHAR(200),
                    description TEXT,
                    command TEXT NOT NULL,
                    base_url VARCHAR(500),
                    api_key TEXT,
                    api_version VARCHAR(50),
                    auth_type VARCHAR(30) DEFAULT 'none',
                    rate_limit INTEGER,
                    is_active BOOLEAN DEFAULT true,
                    is_custom BOOLEAN DEFAULT false,
                    auto_start BOOLEAN DEFAULT true,
                    health_check_url VARCHAR(500),
                    documentation_url VARCHAR(500),
                    version VARCHAR(50),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            """)
            
            # Vytvoření indexů pro mcp_providers
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_mcp_providers_type ON mcp_providers(provider_type);")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_mcp_providers_active ON mcp_providers(is_active);")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_mcp_providers_name ON mcp_providers(provider_name);")
            
            # Vytvoření tabulky mcp_tools
            print("🔧 Vytvářím tabulku mcp_tools...")
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS mcp_tools (
                    tool_id SERIAL PRIMARY KEY,
                    provider_id INTEGER REFERENCES mcp_providers(provider_id) ON DELETE CASCADE,
                    tool_name VARCHAR(100) NOT NULL,
                    tool_identifier VARCHAR(150) NOT NULL,
                    display_name VARCHAR(200),
                    description TEXT,
                    parameters_schema JSONB,
                    response_schema JSONB,
                    auto_approve BOOLEAN DEFAULT false,
                    is_active BOOLEAN DEFAULT true,
                    is_dangerous BOOLEAN DEFAULT false,
                    is_default BOOLEAN DEFAULT false,
                    capabilities JSONB,
                    usage_examples JSONB,
                    rate_limit_per_minute INTEGER DEFAULT 60,
                    timeout_seconds INTEGER DEFAULT 30,
                    retry_attempts INTEGER DEFAULT 3,
                    retry_delay INTEGER DEFAULT 1000,
                    pricing_input DECIMAL(10,6),
                    pricing_output DECIMAL(10,6),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(provider_id, tool_identifier)
                );
            """)
            
            # Vytvoření indexů pro mcp_tools
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_mcp_tools_provider ON mcp_tools(provider_id);")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_mcp_tools_active ON mcp_tools(is_active);")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_mcp_tools_name ON mcp_tools(tool_name);")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_mcp_tools_capabilities ON mcp_tools USING GIN(capabilities);")
            
            # Vytvoření tabulky mcp_requests
            print("📝 Vytvářím tabulku mcp_requests...")
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS mcp_requests (
                    request_id SERIAL PRIMARY KEY,
                    tool_id INTEGER REFERENCES mcp_tools(tool_id),
                    provider_id INTEGER REFERENCES mcp_providers(provider_id),
                    request_data JSONB,
                    response_data JSONB,
                    response_time_ms FLOAT,
                    status VARCHAR(20) NOT NULL CHECK (status IN ('success', 'error', 'timeout', 'cancelled')),
                    error_message TEXT,
                    error_code VARCHAR(50),
                    user_id VARCHAR(100),
                    session_id VARCHAR(100),
                    request_size_bytes INTEGER,
                    response_size_bytes INTEGER,
                    tokens_used INTEGER,
                    latency FLOAT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
            """)
            
            # Vytvoření indexů pro mcp_requests
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_mcp_requests_tool ON mcp_requests(tool_id);")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_mcp_requests_provider ON mcp_requests(provider_id);")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_mcp_requests_status ON mcp_requests(status);")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_mcp_requests_created_at ON mcp_requests(created_at);")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_mcp_requests_response_time ON mcp_requests(response_time_ms);")
            
            conn.commit()
            print("✅ MCP tabulky byly úspěšně vytvořeny!")
            
        except Exception as e:
            print(f"❌ Chyba při vytváření MCP tabulek: {str(e)}")
            if conn:
                conn.rollback()
            raise
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
    
    def get_providers(self) -> List[Dict[str, Any]]:
        """Získá seznam všech MCP poskytovatelů z databáze."""
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT
                    provider_id, provider_name, provider_type, display_name, description,
                    command, base_url, is_active, is_custom, auto_start,
                    health_check_url, documentation_url, version, created_at, updated_at
                FROM mcp_providers
                ORDER BY provider_name
            """)
            
            providers = []
            for row in cursor.fetchall():
                provider = {
                    "provider_id": row[0],
                    "provider_name": row[1],
                    "provider_type": row[2],
                    "display_name": row[3],
                    "description": row[4],
                    "command": row[5],
                    "base_url": row[6],
                    "is_active": row[7],
                    "is_custom": row[8],
                    "auto_start": row[9],
                    "health_check_url": row[10],
                    "documentation_url": row[11],
                    "version": row[12],
                    "created_at": row[13].isoformat() if row[13] else None,
                    "updated_at": row[14].isoformat() if row[14] else None
                }
                providers.append(provider)
            
            return providers
            
        except Exception as e:
            print(f"Chyba při získávání MCP poskytovatelů: {str(e)}")
            raise
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
    
    def get_provider_detail(self, provider_id: int) -> Optional[Dict[str, Any]]:
        """Získá detail MCP poskytovatele včetně jeho nástrojů."""
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            
            # Získání poskytovatele
            cursor.execute("""
                SELECT
                    provider_id, provider_name, provider_type, display_name, description,
                    command, base_url, is_active, is_custom, auto_start,
                    health_check_url, documentation_url, version, created_at, updated_at
                FROM mcp_providers
                WHERE provider_id = %s
            """, (provider_id,))
            
            row = cursor.fetchone()
            if not row:
                return None
            
            provider = {
                "provider_id": row[0],
                "provider_name": row[1],
                "provider_type": row[2],
                "display_name": row[3],
                "description": row[4],
                "command": row[5],
                "base_url": row[6],
                "is_active": row[7],
                "is_custom": row[8],
                "auto_start": row[9],
                "health_check_url": row[10],
                "documentation_url": row[11],
                "version": row[12],
                "created_at": row[13].isoformat() if row[13] else None,
                "updated_at": row[14].isoformat() if row[14] else None,
                "tools": []
            }
            
            # Získání nástrojů poskytovatele
            cursor.execute("""
                SELECT
                    tool_id, tool_name, tool_identifier, display_name, description,
                    parameters_schema, response_schema, auto_approve, is_active,
                    is_dangerous, capabilities, usage_examples,
                    rate_limit_per_minute, timeout_seconds, retry_attempts,
                    retry_delay, pricing_input, pricing_output, created_at, updated_at
                FROM mcp_tools
                WHERE provider_id = %s
                ORDER BY tool_name
            """, (provider_id,))
            
            for tool_row in cursor.fetchall():
                tool = {
                    "tool_id": tool_row[0],
                    "tool_name": tool_row[1],
                    "tool_identifier": tool_row[2],
                    "display_name": tool_row[3],
                    "description": tool_row[4],
                    "parameters_schema": tool_row[5],
                    "response_schema": tool_row[6],
                    "auto_approve": tool_row[7],
                    "is_active": tool_row[8],
                    "is_dangerous": tool_row[9],
                    "capabilities": tool_row[10],
                    "usage_examples": tool_row[11],
                    "rate_limit_per_minute": tool_row[12],
                    "timeout_seconds": tool_row[13],
                    "retry_attempts": tool_row[14],
                    "retry_delay": tool_row[15],
                    "pricing_input": float(tool_row[16]) if tool_row[16] else None,
                    "pricing_output": float(tool_row[17]) if tool_row[17] else None,
                    "created_at": tool_row[18].isoformat() if tool_row[18] else None,
                    "updated_at": tool_row[19].isoformat() if tool_row[19] else None
                }
                provider["tools"].append(tool)
            
            return provider
            
        except Exception as e:
            print(f"Chyba při získávání detailu MCP poskytovatele {provider_id}: {str(e)}")
            raise
        finally:
            if cursor:
                cursor.close()
            if conn:
                conn.close()
