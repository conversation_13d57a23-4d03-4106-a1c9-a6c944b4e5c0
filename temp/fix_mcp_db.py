#!/usr/bin/env python3
"""
Oprava MCP databázové struktury
"""

import psycopg2
import json

def get_db_connection():
    """Získá připojení k databázi."""
    try:
        with open('/opt/gent/config/postgres_credentials.json', 'r') as f:
            creds = json.load(f)
        
        conn = psycopg2.connect(
            host='localhost',
            database='gentdb',
            user='postgres',
            password=creds['postgres_password']
        )
        return conn
    except Exception as e:
        print(f"❌ Chyba při připojování k databázi: {e}")
        return None

def check_mcp_tables():
    """Zkontroluje stav MCP tabulek."""
    conn = get_db_connection()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    try:
        print("🔍 Kontrola MCP tabulek...")
        
        # Kontrola existujících tabulek
        cursor.execute("""
            SELECT table_name 
            FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_name LIKE 'mcp_%'
        """)
        tables = cursor.fetchall()
        
        print("📋 Existující MCP tabulky:")
        for table in tables:
            print(f"  - {table[0]}")
        
        # Kontrola struktury mcp_providers
        if ('mcp_providers',) in tables:
            print("\n🔍 Struktura tabulky mcp_providers:")
            cursor.execute("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name = 'mcp_providers' 
                ORDER BY ordinal_position
            """)
            columns = cursor.fetchall()
            for col in columns:
                print(f"  - {col[0]}: {col[1]} (nullable: {col[2]}, default: {col[3]})")
        
        # Kontrola dat
        if ('mcp_providers',) in tables:
            cursor.execute('SELECT COUNT(*) FROM mcp_providers')
            providers_count = cursor.fetchone()[0]
            print(f"\n📊 Počet poskytovatelů: {providers_count}")
        
        if ('mcp_tools',) in tables:
            cursor.execute('SELECT COUNT(*) FROM mcp_tools')
            tools_count = cursor.fetchone()[0]
            print(f"📊 Počet nástrojů: {tools_count}")
        
        # Ukázka dat
        if ('mcp_providers',) in tables:
            print("\n📋 Ukázka poskytovatelů:")
            cursor.execute("SELECT provider_id, provider_name, provider_type FROM mcp_providers LIMIT 5")
            providers = cursor.fetchall()
            for provider in providers:
                print(f"  - ID: {provider[0]}, Název: {provider[1]}, Typ: {provider[2]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při kontrole tabulek: {e}")
        return False
    finally:
        cursor.close()
        conn.close()

def fix_mcp_tables():
    """Opraví problémy v MCP tabulkách."""
    conn = get_db_connection()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    try:
        print("🔧 Oprava MCP tabulek...")
        
        # Přidání chybějících sloupců do mcp_providers (pokud neexistují)
        missing_columns = [
            ("api_key", "TEXT"),
            ("api_version", "VARCHAR(50)"),
            ("auth_type", "VARCHAR(30) DEFAULT 'none'"),
            ("rate_limit", "INTEGER")
        ]
        
        for col_name, col_type in missing_columns:
            try:
                cursor.execute(f"""
                    ALTER TABLE mcp_providers 
                    ADD COLUMN IF NOT EXISTS {col_name} {col_type}
                """)
                print(f"✅ Přidán sloupec {col_name} do mcp_providers")
            except Exception as e:
                print(f"⚠️ Sloupec {col_name} už existuje nebo chyba: {e}")
        
        # Oprava struktury mcp_tools (přidání chybějících sloupců)
        missing_tool_columns = [
            ("pricing_input", "DECIMAL(10,6)"),
            ("pricing_output", "DECIMAL(10,6)"),
            ("retry_attempts", "INTEGER DEFAULT 3"),
            ("retry_delay", "INTEGER DEFAULT 1000")
        ]
        
        for col_name, col_type in missing_tool_columns:
            try:
                cursor.execute(f"""
                    ALTER TABLE mcp_tools 
                    ADD COLUMN IF NOT EXISTS {col_name} {col_type}
                """)
                print(f"✅ Přidán sloupec {col_name} do mcp_tools")
            except Exception as e:
                print(f"⚠️ Sloupec {col_name} už existuje nebo chyba: {e}")
        
        conn.commit()
        print("✅ Oprava MCP tabulek dokončena!")
        return True
        
    except Exception as e:
        print(f"❌ Chyba při opravě tabulek: {e}")
        conn.rollback()
        return False
    finally:
        cursor.close()
        conn.close()

def test_mcp_service():
    """Otestuje MCP službu po opravě."""
    try:
        import sys
        sys.path.insert(0, '/opt/gent/temp')
        from simple_mcp_db_service import SimpleMcpDbService
        
        print("\n🧪 Test MCP DB Service po opravě...")
        
        mcp_service = SimpleMcpDbService()
        
        # Test získání poskytovatelů
        providers = mcp_service.get_providers()
        print(f"✅ Načteno {len(providers)} MCP poskytovatelů")
        
        # Test detailu prvního poskytovatele
        if providers:
            provider_detail = mcp_service.get_provider_detail(providers[0]["provider_id"])
            if provider_detail:
                print(f"✅ Detail poskytovatele '{provider_detail['provider_name']}' s {len(provider_detail['tools'])} nástroji")
                
                # Ukázka prvního nástroje
                if provider_detail['tools']:
                    tool = provider_detail['tools'][0]
                    print(f"  📋 Ukázka nástroje: {tool['tool_name']} (ID: {tool['tool_id']})")
            else:
                print("❌ Nepodařilo se načíst detail poskytovatele")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při testování MCP DB Service: {e}")
        return False

if __name__ == "__main__":
    print("🔧 MCP Management - Oprava databázové struktury")
    print("=" * 50)
    
    # Kontrola současného stavu
    if not check_mcp_tables():
        print("❌ Chyba při kontrole tabulek!")
        exit(1)
    
    # Oprava tabulek
    if not fix_mcp_tables():
        print("❌ Chyba při opravě tabulek!")
        exit(1)
    
    # Test po opravě
    if not test_mcp_service():
        print("❌ Test po opravě selhal!")
        exit(1)
    
    print("\n🎉 MCP databázová struktura byla úspěšně opravena!")
    print("📋 Databáze je připravena pro:")
    print("   1. Vytvoření API endpointů pro MCP Management")
    print("   2. Vytvoření frontend stránek pro správu MCP")
    print("   3. Integrace s existujícím MCP systémem")
