-- Vyt<PERSON>ř<PERSON><PERSON> tabulky llm_providers
CREATE TABLE llm_providers (
    provider_id SERIAL PRIMARY KEY,
    provider_name VARCHAR(100) NOT NULL UNIQUE,
    api_base_url VARCHAR(255),
    api_key TEXT,
    api_version VARCHAR(50),
    api_key_required BOOLEAN NOT NULL DEFAULT TRUE,
    auth_type VARCHAR(30) DEFAULT 'api_key' CHECK (auth_type IN ('api_key', 'oauth', 'bearer', 'none')),
    rate_limit INTEGER,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Vytvoření indexu pro rychlé filtrování aktivních poskytovatelů
CREATE INDEX idx_llm_providers_is_active ON llm_providers(is_active);

-- <PERSON><PERSON><PERSON><PERSON><PERSON> kome<PERSON> k tabulce a sloupcům
COMMENT ON TABLE llm_providers IS 'Tabulka obsahující informace o poskytovatelích LLM služeb a jejich API';
COMMENT ON COLUMN llm_providers.provider_id IS 'Unikátní identifikátor poskytovatele';
COMMENT ON COLUMN llm_providers.provider_name IS 'Unikátní název poskytovatele (např. OpenAI, Anthropic)';
COMMENT ON COLUMN llm_providers.api_base_url IS 'Základní URL pro volání API poskytovatele';
COMMENT ON COLUMN llm_providers.api_key IS 'API klíč pro autentizaci (měl by být šifrovaný)';
COMMENT ON COLUMN llm_providers.api_version IS 'Verze API používaná pro komunikaci';
COMMENT ON COLUMN llm_providers.api_key_required IS 'Indikátor, zda poskytovatel vyžaduje API klíč';
COMMENT ON COLUMN llm_providers.auth_type IS 'Typ autentizace (api_key, oauth, bearer, none)';
COMMENT ON COLUMN llm_providers.rate_limit IS 'Omezení počtu požadavků na API za minutu';
COMMENT ON COLUMN llm_providers.is_active IS 'Indikátor, zda je poskytovatel aktivní';
COMMENT ON COLUMN llm_providers.created_at IS 'Datum a čas vytvoření záznamu';
COMMENT ON COLUMN llm_providers.updated_at IS 'Datum a čas poslední aktualizace záznamu';
