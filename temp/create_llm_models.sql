-- Vyt<PERSON><PERSON><PERSON><PERSON> tabulky llm_models
CREATE TABLE llm_models (
    model_id SERIAL PRIMARY KEY,
    provider_id INTEGER NOT NULL REFERENCES llm_providers(provider_id) ON DELETE CASCADE,
    model_name VARCHAR(100) NOT NULL,
    model_identifier VARCHAR(100) NOT NULL,
    context_length INTEGER,
    max_tokens_output INTEGER,
    default_temperature DECIMAL(3,2) DEFAULT 0.70 CHECK (default_temperature BETWEEN 0.0 AND 1.0),
    retry_attempts INTEGER DEFAULT 3,
    retry_delay INTEGER DEFAULT 1000,
    timeout INTEGER DEFAULT 30000,
    pricing_input DECIMAL(10,6),
    pricing_output DECIMAL(10,6),
    capabilities JSONB DEFAULT '{}',
    is_default BOOLEAN NOT NULL DEFAULT FALSE,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(provider_id, model_identifier)
);

-- Vytvoření indexů
CREATE INDEX idx_llm_models_provider_id ON llm_models(provider_id);
CREATE INDEX idx_llm_models_is_active ON llm_models(is_active);
CREATE UNIQUE INDEX idx_llm_models_default ON llm_models (provider_id) WHERE is_default = TRUE;
CREATE INDEX idx_llm_models_capabilities ON llm_models USING GIN (capabilities);

-- Přidání komentářů k tabulce a sloupcům
COMMENT ON TABLE llm_models IS 'Tabulka obsahující informace o LLM modelech nabízených jednotlivými poskytovateli';
COMMENT ON COLUMN llm_models.model_id IS 'Unikátní identifikátor modelu';
COMMENT ON COLUMN llm_models.provider_id IS 'Cizí klíč odkazující na poskytovatele';
COMMENT ON COLUMN llm_models.model_name IS 'Uživatelsky přívětivý název modelu';
COMMENT ON COLUMN llm_models.model_identifier IS 'Technický identifikátor modelu pro volání API';
COMMENT ON COLUMN llm_models.context_length IS 'Maximální délka kontextu v tokenech';
COMMENT ON COLUMN llm_models.max_tokens_output IS 'Maximální počet výstupních tokenů';
COMMENT ON COLUMN llm_models.default_temperature IS 'Výchozí hodnota teploty (0.0-1.0)';
COMMENT ON COLUMN llm_models.retry_attempts IS 'Počet opakovaných pokusů při selhání';
COMMENT ON COLUMN llm_models.retry_delay IS 'Pauza mezi opakovanými pokusy (ms)';
COMMENT ON COLUMN llm_models.timeout IS 'Maximální doba čekání na odpověď (ms)';
COMMENT ON COLUMN llm_models.pricing_input IS 'Cena za 1000 vstupních tokenů v USD';
COMMENT ON COLUMN llm_models.pricing_output IS 'Cena za 1000 výstupních tokenů v USD';
COMMENT ON COLUMN llm_models.capabilities IS 'JSONB objekt s podporovanými schopnostmi modelu';
COMMENT ON COLUMN llm_models.is_default IS 'Indikátor, zda je model výchozí pro poskytovatele';
COMMENT ON COLUMN llm_models.is_active IS 'Indikátor, zda je model aktivní';
COMMENT ON COLUMN llm_models.created_at IS 'Datum a čas vytvoření záznamu';
COMMENT ON COLUMN llm_models.updated_at IS 'Datum a čas poslední aktualizace záznamu';
