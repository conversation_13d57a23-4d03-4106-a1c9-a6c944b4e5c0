#!/usr/bin/env python3
"""
Vytvoření MCP tabulek pomocí GENT databázového připojení
"""

import sys
import os
import json

# Přidání GENT cesty do sys.path
sys.path.insert(0, '/opt/gent')

try:
    from api.app.db.postgres import get_postgres_connection
    print("✅ GENT databázové moduly načteny úspěšně")
except ImportError as e:
    print(f"❌ Chyba při importu GENT modulů: {e}")
    print("Zkusím přímé připojení...")
    
    # Fallback na přímé připojení
    import psycopg2
    
    def get_postgres_connection(db_name):
        try:
            with open('/opt/gent/config/postgres_credentials.json', 'r') as f:
                creds = json.load(f)
            
            return psycopg2.connect(
                host="localhost",
                database=db_name,
                user="postgres",
                password=creds["postgres_password"]
            )
        except Exception as e:
            print(f"❌ Chyba při připojování k databázi: {e}")
            return None

def create_mcp_tables():
    """Vytvoří všechny MCP tabulky."""
    
    print("🔧 MCP Management - Vytváření databázových tabulek")
    print("=" * 50)
    
    conn = get_postgres_connection("gentdb")
    if not conn:
        print("❌ Nepodařilo se připojit k databázi!")
        return False
    
    cursor = conn.cursor()
    
    try:
        print("🚀 Začínám vytváření MCP tabulek...")
        
        # 1. Tabulka mcp_providers
        print("📋 Vytvářím tabulku mcp_providers...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS mcp_providers (
                provider_id SERIAL PRIMARY KEY,
                provider_name VARCHAR(100) NOT NULL UNIQUE,
                provider_type VARCHAR(50) NOT NULL,
                display_name VARCHAR(200),
                description TEXT,
                command TEXT NOT NULL,
                base_url VARCHAR(500),
                is_active BOOLEAN DEFAULT true,
                is_custom BOOLEAN DEFAULT false,
                auto_start BOOLEAN DEFAULT true,
                health_check_url VARCHAR(500),
                documentation_url VARCHAR(500),
                version VARCHAR(50),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """)
        
        # Indexy pro mcp_providers
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_mcp_providers_type ON mcp_providers(provider_type);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_mcp_providers_active ON mcp_providers(is_active);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_mcp_providers_name ON mcp_providers(provider_name);")
        
        # 2. Tabulka mcp_tools
        print("🔧 Vytvářím tabulku mcp_tools...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS mcp_tools (
                tool_id SERIAL PRIMARY KEY,
                provider_id INTEGER REFERENCES mcp_providers(provider_id) ON DELETE CASCADE,
                tool_name VARCHAR(100) NOT NULL,
                tool_identifier VARCHAR(150) NOT NULL,
                display_name VARCHAR(200),
                description TEXT,
                parameters_schema JSONB,
                response_schema JSONB,
                auto_approve BOOLEAN DEFAULT false,
                is_active BOOLEAN DEFAULT true,
                is_dangerous BOOLEAN DEFAULT false,
                capabilities JSONB,
                usage_examples JSONB,
                rate_limit_per_minute INTEGER DEFAULT 60,
                timeout_seconds INTEGER DEFAULT 30,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(provider_id, tool_identifier)
            );
        """)
        
        # Indexy pro mcp_tools
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_mcp_tools_provider ON mcp_tools(provider_id);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_mcp_tools_active ON mcp_tools(is_active);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_mcp_tools_name ON mcp_tools(tool_name);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_mcp_tools_capabilities ON mcp_tools USING GIN(capabilities);")
        
        # 3. Tabulka mcp_configurations
        print("⚙️ Vytvářím tabulku mcp_configurations...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS mcp_configurations (
                config_id SERIAL PRIMARY KEY,
                provider_id INTEGER REFERENCES mcp_providers(provider_id) ON DELETE CASCADE,
                config_name VARCHAR(100) NOT NULL,
                environment_vars JSONB,
                startup_args JSONB,
                working_directory VARCHAR(500),
                timeout_seconds INTEGER DEFAULT 30,
                retry_count INTEGER DEFAULT 3,
                retry_delay_ms INTEGER DEFAULT 1000,
                max_concurrent_requests INTEGER DEFAULT 10,
                security_settings JSONB,
                resource_limits JSONB,
                is_default BOOLEAN DEFAULT false,
                is_active BOOLEAN DEFAULT true,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(provider_id, config_name)
            );
        """)
        
        # Indexy pro mcp_configurations
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_mcp_configs_provider ON mcp_configurations(provider_id);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_mcp_configs_default ON mcp_configurations(is_default);")
        
        # 4. Tabulka mcp_requests
        print("📝 Vytvářím tabulku mcp_requests...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS mcp_requests (
                request_id SERIAL PRIMARY KEY,
                tool_id INTEGER REFERENCES mcp_tools(tool_id),
                provider_id INTEGER REFERENCES mcp_providers(provider_id),
                request_data JSONB,
                response_data JSONB,
                response_time_ms FLOAT,
                status VARCHAR(20) NOT NULL CHECK (status IN ('success', 'error', 'timeout', 'cancelled')),
                error_message TEXT,
                error_code VARCHAR(50),
                user_id VARCHAR(100),
                session_id VARCHAR(100),
                request_size_bytes INTEGER,
                response_size_bytes INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """)
        
        # Indexy pro mcp_requests
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_mcp_requests_tool ON mcp_requests(tool_id);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_mcp_requests_provider ON mcp_requests(provider_id);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_mcp_requests_status ON mcp_requests(status);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_mcp_requests_created_at ON mcp_requests(created_at);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_mcp_requests_response_time ON mcp_requests(response_time_ms);")
        
        # 5. Tabulka mcp_server_status
        print("🖥️ Vytvářím tabulku mcp_server_status...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS mcp_server_status (
                status_id SERIAL PRIMARY KEY,
                provider_id INTEGER REFERENCES mcp_providers(provider_id) ON DELETE CASCADE UNIQUE,
                status VARCHAR(20) NOT NULL CHECK (status IN ('online', 'offline', 'error', 'starting', 'stopping')),
                process_id INTEGER,
                port INTEGER,
                memory_usage_mb FLOAT,
                cpu_usage_percent FLOAT,
                uptime_seconds INTEGER,
                last_health_check TIMESTAMP,
                health_check_status VARCHAR(20) CHECK (health_check_status IN ('healthy', 'unhealthy', 'unknown')),
                error_message TEXT,
                restart_count INTEGER DEFAULT 0,
                last_restart TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """)
        
        # Indexy pro mcp_server_status
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_mcp_status_provider ON mcp_server_status(provider_id);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_mcp_status_status ON mcp_server_status(status);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_mcp_status_health ON mcp_server_status(health_check_status);")
        
        # 6. Tabulka mcp_permissions
        print("🔐 Vytvářím tabulku mcp_permissions...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS mcp_permissions (
                permission_id SERIAL PRIMARY KEY,
                user_id VARCHAR(100),
                tool_id INTEGER REFERENCES mcp_tools(tool_id) ON DELETE CASCADE,
                provider_id INTEGER REFERENCES mcp_providers(provider_id) ON DELETE CASCADE,
                permission_level VARCHAR(20) NOT NULL CHECK (permission_level IN ('read', 'write', 'execute', 'admin')),
                allowed_operations JSONB,
                restrictions JSONB,
                is_active BOOLEAN DEFAULT true,
                granted_by VARCHAR(100),
                granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            );
        """)
        
        # Indexy pro mcp_permissions
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_mcp_permissions_user ON mcp_permissions(user_id);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_mcp_permissions_tool ON mcp_permissions(tool_id);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_mcp_permissions_provider ON mcp_permissions(provider_id);")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_mcp_permissions_active ON mcp_permissions(is_active);")
        
        print("✅ Všechny MCP tabulky vytvořeny!")
        
        conn.commit()
        return True
        
    except Exception as e:
        print(f"❌ Chyba při vytváření tabulek: {e}")
        conn.rollback()
        return False
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    if create_mcp_tables():
        print("\n🎉 Všechny MCP tabulky byly úspěšně vytvořeny!")
        print("📋 Vytvořené tabulky:")
        print("   - mcp_providers (poskytovatelé MCP serverů)")
        print("   - mcp_tools (nástroje dostupné v MCP serverech)")
        print("   - mcp_configurations (konfigurace MCP serverů)")
        print("   - mcp_requests (log všech MCP requestů)")
        print("   - mcp_server_status (aktuální stav MCP serverů)")
        print("   - mcp_permissions (oprávnění pro MCP nástroje)")
        print("\n🔄 Další krok: Spustit import existujících MCP serverů")
    else:
        print("❌ Vytváření tabulek selhalo!")
        sys.exit(1)
