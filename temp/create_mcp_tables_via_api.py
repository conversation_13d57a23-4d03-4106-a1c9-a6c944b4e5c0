#!/usr/bin/env python3
"""
Vytvoření MCP tabulek pomocí existujícího GENT API - inspirováno LLM Management
"""

import requests
import json
import time
import sys
import os

API_BASE = "http://localhost:8001"

def execute_sql_via_api(query, description="SQL dotaz"):
    """Spustí SQL dotaz přes GENT API postgres transaction endpoint."""
    try:
        print(f"🔄 {description}...")

        # Použijeme transaction endpoint pro CREATE TABLE
        url = f"{API_BASE}/api/postgres/gentdb/transaction"

        payload = {
            "operations": [
                {
                    "type": "query",
                    "query": query,
                    "params": []
                }
            ]
        }

        response = requests.post(url, json=payload, timeout=30)

        if response.status_code == 200:
            result = response.json()
            if isinstance(result, list) and len(result) > 0:
                print(f"✅ {description} - úspěch")
                return True
            else:
                print(f"❌ {description} - neočekávan<PERSON> výsledek: {result}")
                return False
        else:
            print(f"❌ {description} - HTTP {response.status_code}: {response.text}")
            return False

    except Exception as e:
        print(f"❌ {description} - výjimka: {e}")
        return False

def create_mcp_tables():
    """Vytvoří všechny MCP tabulky podle vzoru LLM Management."""
    
    print("🔧 MCP Management - Vytváření databázových tabulek podle LLM vzoru")
    print("=" * 70)
    
    # 1. Tabulka mcp_providers (podle llm_providers)
    providers_sql = """
    CREATE TABLE IF NOT EXISTS mcp_providers (
        provider_id SERIAL PRIMARY KEY,
        provider_name VARCHAR(100) NOT NULL UNIQUE,
        provider_type VARCHAR(50) NOT NULL DEFAULT 'mcp',
        display_name VARCHAR(200),
        description TEXT,
        command TEXT NOT NULL,
        base_url VARCHAR(500),
        api_key TEXT,
        api_version VARCHAR(50),
        auth_type VARCHAR(30) DEFAULT 'none',
        rate_limit INTEGER,
        is_active BOOLEAN DEFAULT true,
        is_custom BOOLEAN DEFAULT false,
        auto_start BOOLEAN DEFAULT true,
        health_check_url VARCHAR(500),
        documentation_url VARCHAR(500),
        version VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    """
    
    if not execute_sql_via_api(providers_sql, "Vytváření tabulky mcp_providers"):
        return False
    
    # Indexy pro mcp_providers
    indexes_providers = [
        "CREATE INDEX IF NOT EXISTS idx_mcp_providers_type ON mcp_providers(provider_type);",
        "CREATE INDEX IF NOT EXISTS idx_mcp_providers_active ON mcp_providers(is_active);",
        "CREATE INDEX IF NOT EXISTS idx_mcp_providers_name ON mcp_providers(provider_name);"
    ]
    
    for idx_sql in indexes_providers:
        if not execute_sql_via_api(idx_sql, "Vytváření indexu pro mcp_providers"):
            return False
    
    # 2. Tabulka mcp_tools (podle llm_models)
    tools_sql = """
    CREATE TABLE IF NOT EXISTS mcp_tools (
        tool_id SERIAL PRIMARY KEY,
        provider_id INTEGER REFERENCES mcp_providers(provider_id) ON DELETE CASCADE,
        tool_name VARCHAR(100) NOT NULL,
        tool_identifier VARCHAR(150) NOT NULL,
        display_name VARCHAR(200),
        description TEXT,
        parameters_schema JSONB,
        response_schema JSONB,
        auto_approve BOOLEAN DEFAULT false,
        is_active BOOLEAN DEFAULT true,
        is_dangerous BOOLEAN DEFAULT false,
        is_default BOOLEAN DEFAULT false,
        capabilities JSONB,
        usage_examples JSONB,
        rate_limit_per_minute INTEGER DEFAULT 60,
        timeout_seconds INTEGER DEFAULT 30,
        retry_attempts INTEGER DEFAULT 3,
        retry_delay INTEGER DEFAULT 1000,
        pricing_input DECIMAL(10,6),
        pricing_output DECIMAL(10,6),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(provider_id, tool_identifier)
    );
    """
    
    if not execute_sql_via_api(tools_sql, "Vytváření tabulky mcp_tools"):
        return False
    
    # Indexy pro mcp_tools
    indexes_tools = [
        "CREATE INDEX IF NOT EXISTS idx_mcp_tools_provider ON mcp_tools(provider_id);",
        "CREATE INDEX IF NOT EXISTS idx_mcp_tools_active ON mcp_tools(is_active);",
        "CREATE INDEX IF NOT EXISTS idx_mcp_tools_name ON mcp_tools(tool_name);",
        "CREATE INDEX IF NOT EXISTS idx_mcp_tools_capabilities ON mcp_tools USING GIN(capabilities);"
    ]
    
    for idx_sql in indexes_tools:
        if not execute_sql_via_api(idx_sql, "Vytváření indexu pro mcp_tools"):
            return False
    
    # 3. Tabulka mcp_requests (podle llm_requests pro metriky)
    requests_sql = """
    CREATE TABLE IF NOT EXISTS mcp_requests (
        request_id SERIAL PRIMARY KEY,
        tool_id INTEGER REFERENCES mcp_tools(tool_id),
        provider_id INTEGER REFERENCES mcp_providers(provider_id),
        request_data JSONB,
        response_data JSONB,
        response_time_ms FLOAT,
        status VARCHAR(20) NOT NULL CHECK (status IN ('success', 'error', 'timeout', 'cancelled')),
        error_message TEXT,
        error_code VARCHAR(50),
        user_id VARCHAR(100),
        session_id VARCHAR(100),
        request_size_bytes INTEGER,
        response_size_bytes INTEGER,
        tokens_used INTEGER,
        latency FLOAT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );
    """
    
    if not execute_sql_via_api(requests_sql, "Vytváření tabulky mcp_requests"):
        return False
    
    # Indexy pro mcp_requests
    indexes_requests = [
        "CREATE INDEX IF NOT EXISTS idx_mcp_requests_tool ON mcp_requests(tool_id);",
        "CREATE INDEX IF NOT EXISTS idx_mcp_requests_provider ON mcp_requests(provider_id);",
        "CREATE INDEX IF NOT EXISTS idx_mcp_requests_status ON mcp_requests(status);",
        "CREATE INDEX IF NOT EXISTS idx_mcp_requests_created_at ON mcp_requests(created_at);",
        "CREATE INDEX IF NOT EXISTS idx_mcp_requests_response_time ON mcp_requests(response_time_ms);"
    ]
    
    for idx_sql in indexes_requests:
        if not execute_sql_via_api(idx_sql, "Vytváření indexu pro mcp_requests"):
            return False
    
    print("\n✅ Základní MCP tabulky úspěšně vytvořeny!")
    return True

def import_mcp_config_to_db():
    """Importuje existující MCP konfiguraci z JSON do databáze."""
    
    print("\n📥 Import existujících MCP serverů z konfigurace...")
    
    # Načtení existující konfigurace
    config_path = '/opt/gent/config/mcp_config.json'
    
    if not os.path.exists(config_path):
        print(f"⚠️ Konfigurační soubor {config_path} neexistuje, přeskakuji import")
        return True
    
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # Mapování typů MCP serverů
    type_mapping = {
        'filesystem': 'file_system',
        'brave-search': 'web_search',
        'tavily': 'web_search',
        'fetch': 'web_fetch',
        'perplexity': 'ai_search',
        'sequentialthinking': 'reasoning',
        'git': 'version_control',
        'gent-project': 'project_management',
        'gent-workflow': 'workflow_management'
    }
    
    # Import poskytovatelů
    for server_name, server_config in config['servers'].items():
        provider_type = type_mapping.get(server_name, 'other')
        display_name = server_name.replace('-', ' ').title()
        
        # Vložení poskytovatele
        provider_sql = f"""
        INSERT INTO mcp_providers (
            provider_name, provider_type, display_name, description,
            command, is_active, is_custom, auto_start
        ) VALUES (
            '{server_name}', '{provider_type}', '{display_name}', 
            '{server_config.get("description", "")}',
            '{server_config.get("command", "")}', 
            {str(server_config.get("enabled", True)).lower()}, 
            {str(server_config.get("custom", False)).lower()}, 
            true
        ) ON CONFLICT (provider_name) DO NOTHING
        RETURNING provider_id;
        """
        
        if not execute_sql_via_api(provider_sql, f"Import poskytovatele {server_name}"):
            continue
        
        # Import nástrojů pro každého poskytovatele
        auto_approve_tools = server_config.get('auto_approve', [])
        for tool_name in auto_approve_tools:
            tool_identifier = f"{server_name}_{tool_name}"
            display_name_tool = tool_name.replace('_', ' ').title()
            
            tool_sql = f"""
            INSERT INTO mcp_tools (
                provider_id, tool_name, tool_identifier, display_name,
                auto_approve, is_active
            ) 
            SELECT provider_id, '{tool_name}', '{tool_identifier}', '{display_name_tool}',
                   true, true
            FROM mcp_providers 
            WHERE provider_name = '{server_name}'
            ON CONFLICT (provider_id, tool_identifier) DO NOTHING;
            """
            
            execute_sql_via_api(tool_sql, f"Import nástroje {tool_name}")
    
    print("✅ Import MCP konfigurace dokončen!")
    return True

if __name__ == "__main__":
    print("🚀 MCP Management - Vytváření databázové struktury podle LLM vzoru")
    print("=" * 70)
    
    # Vytvoření základních tabulek
    if not create_mcp_tables():
        print("❌ Chyba při vytváření základních tabulek!")
        sys.exit(1)
    
    # Import existujících dat
    if not import_mcp_config_to_db():
        print("❌ Chyba při importu existujících dat!")
        sys.exit(1)
    
    print("\n🎉 MCP Management databázová struktura byla úspěšně vytvořena!")
    print("📋 Vytvořené tabulky:")
    print("   - mcp_providers (poskytovatelé MCP serverů)")
    print("   - mcp_tools (nástroje dostupné v MCP serverech)")
    print("   - mcp_requests (log všech MCP requestů)")
    print("\n🔄 Další kroky:")
    print("   1. Vytvoření API endpointů pro MCP Management")
    print("   2. Vytvoření frontend stránek pro správu MCP")
    print("   3. Integrace s existujícím MCP systémem")
