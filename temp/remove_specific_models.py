#!/usr/bin/env python3
"""
Skript pro odstranění konkrétních modelů z databáze.
Odstraní: o1-mini, gemini-2.5-pro-preview-05-06, gpt-3.5-turbo
"""

import sys
import os
import psycopg2
import json

def load_db_config():
    """Načte konfiguraci databáze ze souboru db.json."""
    config_path = os.path.join('/opt/gent/config', 'db.json')
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        return {
            "host": config.get("host", "localhost"),
            "port": config.get("port", 5432),
            "user": config.get("user"),
            "password": config.get("password"),
            "database": config.get("database")
        }
    except Exception as e:
        print(f"Chyba při načítání konfigurace: {str(e)}")
        return None

def remove_specific_models():
    """Odstraní konkrétní modely z databáze."""
    config = load_db_config()
    if not config:
        print("Nepodařilo se načíst konfiguraci databáze.")
        return False

    # Modely k odstranění
    models_to_remove = [
        'o1-mini',
        'gemini-2.5-pro-preview-05-06', 
        'gpt-3.5-turbo'
    ]

    try:
        # Připojení k databázi
        print(f"Připojuji se k databázi {config['database']}...")
        conn = psycopg2.connect(
            host=config["host"],
            port=config["port"],
            user=config["user"],
            password=config["password"],
            dbname=config["database"]
        )
        
        cursor = conn.cursor()
        
        print("\n🗑️  Odstraňování konkrétních modelů...")
        removed_count = 0
        
        for model_identifier in models_to_remove:
            # Zkontroluj, zda model existuje
            cursor.execute("""
                SELECT model_id, model_name, p.provider_name 
                FROM llm_models m
                JOIN llm_providers p ON m.provider_id = p.provider_id
                WHERE m.model_identifier = %s
            """, (model_identifier,))
            
            result = cursor.fetchone()
            if result:
                model_id, model_name, provider_name = result
                
                # Smaž model
                cursor.execute("DELETE FROM llm_models WHERE model_id = %s", (model_id,))
                
                print(f"  ✅ Odstraněn: {provider_name} - {model_name} ({model_identifier})")
                removed_count += 1
            else:
                print(f"  ⚠️  Model {model_identifier} nebyl nalezen")
        
        # Potvrzení změn
        conn.commit()
        print(f"\n🎉 Úspěšně odstraněno {removed_count} modelů.")
        
        # Zobrazení finálního stavu
        print(f"\n📊 FINÁLNÍ STAV:")
        print("=" * 40)
        cursor.execute("""
            SELECT 
                p.provider_name,
                COUNT(*) as model_count
            FROM llm_models m
            JOIN llm_providers p ON m.provider_id = p.provider_id
            WHERE m.is_active = TRUE AND p.is_active = TRUE
            GROUP BY p.provider_name
            ORDER BY p.provider_name
        """)
        
        for provider_name, model_count in cursor.fetchall():
            print(f"{provider_name:15s}: {model_count:2d} modelů")
        
        # Zobrazení zbývajících modelů
        print(f"\n🤖 ZBÝVAJÍCÍ MODELY:")
        print("=" * 50)
        cursor.execute("""
            SELECT 
                p.provider_name,
                m.model_name,
                m.model_identifier,
                m.is_default
            FROM llm_models m
            JOIN llm_providers p ON m.provider_id = p.provider_id
            WHERE m.is_active = TRUE AND p.is_active = TRUE
            ORDER BY p.provider_name, m.model_name
        """)
        
        current_provider = None
        for provider_name, model_name, model_identifier, is_default in cursor.fetchall():
            if current_provider != provider_name:
                current_provider = provider_name
                print(f"\n🏢 {provider_name.upper()}:")
                print("-" * 30)
            
            default = " [DEFAULT]" if is_default else ""
            print(f"  ✅ {model_name} ({model_identifier}){default}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🗑️  GENT - Odstranění konkrétních modelů")
    print("=" * 50)
    print("⚠️  Tento skript odstraní tyto modely:")
    print("   - o1-mini")
    print("   - gemini-2.5-pro-preview-05-06")
    print("   - gpt-3.5-turbo")
    print()
    
    # Potvrzení od uživatele
    response = input("Pokračovat s odstraněním? (ano/ne): ").lower().strip()
    if response not in ['ano', 'a', 'yes', 'y']:
        print("❌ Operace zrušena uživatelem.")
        sys.exit(0)
    
    success = remove_specific_models()
    
    if success:
        print("\n🎉 Odstranění bylo úspěšné!")
        print("💡 Doporučuji restartovat API server: sudo systemctl restart gent-api")
        print("💡 Pak obnovit frontend (F5)")
    else:
        print("\n💥 Odstranění selhalo!")
        sys.exit(1)
