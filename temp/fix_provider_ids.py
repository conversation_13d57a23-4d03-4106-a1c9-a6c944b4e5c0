#!/usr/bin/env python3
"""
Skript pro zjištění správných provider_id hodnot v databázi
"""

import sys
import os
sys.path.append('/opt/gent')

from gent.db.direct_connector import get_db_connection

def get_provider_ids():
    """Získá seznam všech poskytovatelů a jejich ID"""
    try:
        print("Připojuji se k databázi...")
        conn = get_db_connection('gentdb')
        cursor = conn.cursor()
        
        # Získáme všechny poskytovatele
        cursor.execute("""
            SELECT provider_id, provider_name, is_active
            FROM llm_providers
            ORDER BY provider_name
        """)
        
        providers = cursor.fetchall()
        print(f"\n=== POSKYTOVATELÉ V DATABÁZI ===")
        for provider in providers:
            status = "✅ AKTIVNÍ" if provider[2] else "❌ NEAKTIVNÍ"
            print(f"{status}: ID={provider[0]}, Název='{provider[1]}'")
        
        # Získáme počet modelů pro každého poskytovatele
        print(f"\n=== POČET MODELŮ PRO KAŽDÉHO POSKYTOVATELE ===")
        for provider in providers:
            cursor.execute("""
                SELECT COUNT(*) 
                FROM llm_models 
                WHERE provider_id = %s AND is_active = TRUE
            """, (provider[0],))
            
            model_count = cursor.fetchone()[0]
            print(f"Poskytovatel '{provider[1]}' (ID={provider[0]}): {model_count} aktivních modelů")
        
        cursor.close()
        conn.close()
        
        return providers
        
    except Exception as e:
        print(f"❌ Chyba: {e}")
        return []

def show_models_by_provider(provider_id, provider_name):
    """Zobrazí modely daného poskytovatele"""
    try:
        conn = get_db_connection('gentdb')
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT model_name, model_identifier, is_active, is_default
            FROM llm_models 
            WHERE provider_id = %s
            ORDER BY model_name
        """, (provider_id,))
        
        models = cursor.fetchall()
        print(f"\n=== MODELY POSKYTOVATELE '{provider_name}' (ID={provider_id}) ===")
        for model in models:
            status = "✅ AKTIVNÍ" if model[2] else "❌ NEAKTIVNÍ"
            default = " (VÝCHOZÍ)" if model[3] else ""
            print(f"{status}{default}: {model[0]} ({model[1]})")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Chyba: {e}")

if __name__ == "__main__":
    print("🔍 Zjišťování provider_id hodnot...")
    
    providers = get_provider_ids()
    
    if providers:
        print(f"\n🎯 SQL FILTR PRO VYBRANÉ MODELY:")
        print("-- Najděte správné provider_id hodnoty:")
        
        for provider in providers:
            if provider[1].lower() in ['openai', 'anthropic', 'google']:
                print(f"-- {provider[1]}: provider_id = {provider[0]}")
                show_models_by_provider(provider[0], provider[1])
    else:
        print("💥 Nepodařilo se načíst poskytovatele!")
