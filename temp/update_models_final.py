#!/usr/bin/env python3
"""
Skript pro aktualizaci LLM modelů v databázi podle specifikace uživatele.
Odstraní všechny současné modely a přidá pouze specifikované.
"""

import sys
import os
import psycopg2
import json

def load_db_config():
    """Načte konfiguraci databáze ze souboru db.json."""
    config_path = os.path.join('/opt/gent/config', 'db.json')
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        return {
            "host": config.get("host", "localhost"),
            "port": config.get("port", 5432),
            "user": config.get("user"),
            "password": config.get("password"),
            "database": config.get("database")
        }
    except Exception as e:
        print(f"Chyba při načítání konfigurace: {str(e)}")
        return None

def get_new_models():
    """Definuje nové modely podle specifikace uživatele."""
    return {
        'openai': [
            # Specifikované OpenAI modely
            ('gpt-4.1', 'gpt-4.1', 128000, 4096, '{"text": true, "code": true, "reasoning": true}'),
            ('gpt-4.1-mini', 'gpt-4.1-mini', 128000, 4096, '{"text": true, "code": true, "fast": true}'),
            ('gpt-4.1-nano', 'gpt-4.1-nano', 128000, 4096, '{"text": true, "fast": true, "lightweight": true}'),
            ('gpt-4.5', 'gpt-4.5', 128000, 4096, '{"text": true, "code": true, "reasoning": true, "advanced": true}'),
            ('gpt-4.5-mini', 'gpt-4.5-mini', 128000, 4096, '{"text": true, "code": true, "fast": true}'),
            ('gpt-4o-latest', 'gpt-4o-latest', 128000, 4096, '{"text": true, "vision": true, "code": true, "multimodal": true}'),
            ('gpt-4o-mini-latest', 'gpt-4o-mini-latest', 128000, 4096, '{"text": true, "vision": true, "fast": true}'),
            ('gpt-4o-audio-preview', 'gpt-4o-audio-preview', 128000, 4096, '{"text": true, "audio": true, "multimodal": true}'),
            ('o1-mini', 'o1-mini', 128000, 4096, '{"reasoning": true, "mathematics": true, "science": true}'),
            ('o3', 'o3', 128000, 4096, '{"reasoning": true, "mathematics": true, "science": true, "advanced": true}'),
            ('o3-mini', 'o3-mini', 128000, 4096, '{"reasoning": true, "mathematics": true, "fast": true}'),
            ('o3-mini-high', 'o3-mini-high', 128000, 4096, '{"reasoning": true, "mathematics": true, "high_performance": true}'),
            ('o4-mini', 'o4-mini', 128000, 4096, '{"reasoning": true, "mathematics": true, "next_gen": true}'),
            ('o4-mini-high', 'o4-mini-high', 128000, 4096, '{"reasoning": true, "mathematics": true, "next_gen": true, "high_performance": true}'),
        ],
        'google': [
            # Specifikované Google modely
            ('gemini-2.5-pro-preview-05-06', 'gemini-2.5-pro-preview-05-06', 2000000, 8192, '{"text": true, "code": true, "reasoning": true, "analysis": true}'),
            ('gemini-2.5-flash-preview-05-20', 'gemini-2.5-flash-preview-05-20', 1000000, 8192, '{"text": true, "code": true, "fast": true, "multimodal": true}'),
            ('gemini-2.0-flash-lite', 'gemini-2.0-flash-lite', 1000000, 8192, '{"text": true, "fast": true, "lightweight": true}'),
            ('gemini-2.0-flash', 'gemini-2.0-flash', 1000000, 8192, '{"text": true, "vision": true, "audio": true, "video": true, "multimodal": true}'),
        ],
        'anthropic': [
            # Specifikované Anthropic modely
            ('claude-3-7-sonnet-latest', 'claude-3-7-sonnet-latest', 200000, 4096, '{"text": true, "code": true, "reasoning": true, "analysis": true}'),
            ('claude-sonnet-4-20250514', 'claude-sonnet-4-20250514', 200000, 4096, '{"text": true, "code": true, "reasoning": true, "analysis": true, "advanced": true}'),
        ]
    }

def update_models():
    """Aktualizuje modely v databázi."""
    config = load_db_config()
    if not config:
        print("Nepodařilo se načíst konfiguraci databáze.")
        return False

    try:
        # Připojení k databázi
        print(f"Připojuji se k databázi {config['database']}...")
        conn = psycopg2.connect(
            host=config["host"],
            port=config["port"],
            user=config["user"],
            password=config["password"],
            dbname=config["database"]
        )

        cursor = conn.cursor()

        # Získání ID poskytovatelů
        print("📋 Získávání ID poskytovatelů...")
        cursor.execute("""
            SELECT provider_id, LOWER(provider_name) as provider_name
            FROM llm_providers
            WHERE LOWER(provider_name) IN ('openai', 'google', 'anthropic')
            AND is_active = TRUE
        """)

        providers = {}
        for provider_id, provider_name in cursor.fetchall():
            providers[provider_name] = provider_id
            print(f"  ✅ {provider_name.capitalize()}: ID {provider_id}")

        # Kontrola, zda máme všechny potřebné poskytovatele
        required_providers = ['openai', 'google', 'anthropic']
        missing_providers = [p for p in required_providers if p not in providers]

        if missing_providers:
            print(f"❌ Chybí poskytovatelé: {', '.join(missing_providers)}")
            return False

        # Smazání všech současných modelů
        print("\n🗑️  Mazání všech současných modelů...")
        cursor.execute("SELECT COUNT(*) FROM llm_models")
        old_count = cursor.fetchone()[0]
        print(f"  📊 Současný počet modelů: {old_count}")

        cursor.execute("DELETE FROM llm_models")
        deleted_count = cursor.rowcount
        print(f"  ✅ Smazáno {deleted_count} modelů")

        # Přidání nových modelů
        print("\n➕ Přidávání nových modelů...")
        new_models = get_new_models()
        added_count = 0

        for provider_name, models in new_models.items():
            provider_id = providers[provider_name]
            print(f"\n🏢 {provider_name.upper()}:")

            for model_name, model_identifier, context_length, max_tokens, capabilities in models:
                try:
                    cursor.execute("""
                        INSERT INTO llm_models (
                            provider_id, model_name, model_identifier,
                            context_length, max_tokens_output, default_temperature,
                            capabilities, is_active, is_default
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        provider_id, model_name, model_identifier,
                        context_length, max_tokens, 0.7,
                        capabilities, True, False
                    ))

                    print(f"  ✅ {model_name}")
                    added_count += 1

                except Exception as e:
                    print(f"  ❌ Chyba při přidávání {model_name}: {e}")

        # Nastavení výchozích modelů (první model každého poskytovatele)
        print(f"\n🎯 Nastavování výchozích modelů...")
        for provider_name in ['openai', 'google', 'anthropic']:
            provider_id = providers[provider_name]
            first_model = new_models[provider_name][0]

            cursor.execute("""
                UPDATE llm_models
                SET is_default = TRUE
                WHERE provider_id = %s AND model_identifier = %s
            """, (provider_id, first_model[1]))

            print(f"  ✅ {provider_name.capitalize()}: {first_model[0]}")

        # Potvrzení změn
        conn.commit()
        print(f"\n🎉 Úspěšně aktualizováno! Přidáno {added_count} nových modelů.")

        # Zobrazení finálního stavu
        print(f"\n📊 FINÁLNÍ STAV:")
        print("=" * 40)
        cursor.execute("""
            SELECT
                p.provider_name,
                COUNT(*) as model_count
            FROM llm_models m
            JOIN llm_providers p ON m.provider_id = p.provider_id
            WHERE m.is_active = TRUE AND p.is_active = TRUE
            GROUP BY p.provider_name
            ORDER BY p.provider_name
        """)

        for provider_name, model_count in cursor.fetchall():
            print(f"{provider_name:15s}: {model_count:2d} modelů")

        cursor.close()
        conn.close()

        return True

    except Exception as e:
        print(f"❌ Chyba: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔄 GENT - Aktualizace LLM modelů")
    print("=" * 50)
    print("⚠️  VAROVÁNÍ: Tento skript smaže všechny současné modely!")
    print("   Budou přidány pouze specifikované modely.")
    print()

    # Potvrzení od uživatele
    response = input("Pokračovat? (ano/ne): ").lower().strip()
    if response not in ['ano', 'a', 'yes', 'y']:
        print("❌ Operace zrušena uživatelem.")
        sys.exit(0)

    success = update_models()

    if success:
        print("\n🎉 Aktualizace byla úspěšná!")
        print("💡 Doporučuji restartovat API server: sudo systemctl restart gent-api")
        print("💡 Pak obnovit frontend (F5) a zkontrolovat dropdown")
    else:
        print("\n💥 Aktualizace selhala!")
        sys.exit(1)
