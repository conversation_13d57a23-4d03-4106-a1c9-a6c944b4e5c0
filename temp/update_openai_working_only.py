#!/usr/bin/env python3
"""
Skript pro aktualizaci pouze OpenAI modelů s FUNKČNÍMI modely.
Google a Anthropic zůstávají beze změny.
"""

import sys
import os
import psycopg2
import json

def load_db_config():
    """Načte konfiguraci databáze ze souboru db.json."""
    config_path = os.path.join('/opt/gent/config', 'db.json')
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        return {
            "host": config.get("host", "localhost"),
            "port": config.get("port", 5432),
            "user": config.get("user"),
            "password": config.get("password"),
            "database": config.get("database")
        }
    except Exception as e:
        print(f"Chyba při načítání konfigurace: {str(e)}")
        return None

def get_working_openai_models():
    """Definuje pouze SKUTEČNĚ FUNKČNÍ OpenAI modely podle oficiálních zdrojů."""
    return [
        # POUZE OVĚŘENÉ a FUNKČNÍ OpenAI modely
        ('gpt-4o', 'gpt-4o', 128000, 4096, '{"text": true, "vision": true, "code": true, "multimodal": true}'),
        ('gpt-4o-mini', 'gpt-4o-mini', 128000, 4096, '{"text": true, "vision": true, "fast": true, "cost_effective": true}'),
        ('gpt-4-turbo', 'gpt-4-turbo', 128000, 4096, '{"text": true, "vision": true, "code": true}'),
        ('gpt-3.5-turbo', 'gpt-3.5-turbo', 16385, 4096, '{"text": true, "fast": true, "cost_effective": true}'),
        ('o1-mini', 'o1-mini', 128000, 4096, '{"reasoning": true, "mathematics": true, "science": true}'),
    ]

def update_openai_models():
    """Aktualizuje pouze OpenAI modely, Google a Anthropic zůstávají."""
    config = load_db_config()
    if not config:
        print("Nepodařilo se načíst konfiguraci databáze.")
        return False

    try:
        # Připojení k databázi
        print(f"Připojuji se k databázi {config['database']}...")
        conn = psycopg2.connect(
            host=config["host"],
            port=config["port"],
            user=config["user"],
            password=config["password"],
            dbname=config["database"]
        )
        
        cursor = conn.cursor()
        
        # Získání ID OpenAI poskytovatele
        print("📋 Získávání ID OpenAI poskytovatele...")
        cursor.execute("""
            SELECT provider_id 
            FROM llm_providers 
            WHERE LOWER(provider_name) = 'openai' 
            AND is_active = TRUE
        """)
        
        result = cursor.fetchone()
        if not result:
            print("❌ OpenAI poskytovatel nebyl nalezen!")
            return False
        
        openai_provider_id = result[0]
        print(f"  ✅ OpenAI: ID {openai_provider_id}")
        
        # Smazání pouze OpenAI modelů
        print("\n🗑️  Mazání pouze OpenAI modelů...")
        cursor.execute("SELECT COUNT(*) FROM llm_models WHERE provider_id = %s", (openai_provider_id,))
        old_count = cursor.fetchone()[0]
        print(f"  📊 Současný počet OpenAI modelů: {old_count}")
        
        cursor.execute("DELETE FROM llm_models WHERE provider_id = %s", (openai_provider_id,))
        deleted_count = cursor.rowcount
        print(f"  ✅ Smazáno {deleted_count} OpenAI modelů")
        
        # Přidání nových FUNKČNÍCH OpenAI modelů
        print("\n➕ Přidávání pouze FUNKČNÍCH OpenAI modelů...")
        working_models = get_working_openai_models()
        added_count = 0
        
        print(f"\n🏢 OPENAI:")
        
        for model_name, model_identifier, context_length, max_tokens, capabilities in working_models:
            try:
                cursor.execute("""
                    INSERT INTO llm_models (
                        provider_id, model_name, model_identifier, 
                        context_length, max_tokens_output, default_temperature,
                        capabilities, is_active, is_default
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    openai_provider_id, model_name, model_identifier,
                    context_length, max_tokens, 0.7,
                    capabilities, True, False
                ))
                
                print(f"  ✅ {model_name}")
                added_count += 1
                
            except Exception as e:
                print(f"  ❌ Chyba při přidávání {model_name}: {e}")
        
        # Nastavení výchozího OpenAI modelu (gpt-4o)
        print(f"\n🎯 Nastavování výchozího OpenAI modelu...")
        cursor.execute("""
            UPDATE llm_models 
            SET is_default = TRUE 
            WHERE provider_id = %s AND model_identifier = %s
        """, (openai_provider_id, 'gpt-4o'))
        
        print(f"  ✅ OpenAI: gpt-4o")
        
        # Potvrzení změn
        conn.commit()
        print(f"\n🎉 Úspěšně aktualizováno! Přidáno {added_count} FUNKČNÍCH OpenAI modelů.")
        
        # Zobrazení finálního stavu
        print(f"\n📊 FINÁLNÍ STAV:")
        print("=" * 40)
        cursor.execute("""
            SELECT 
                p.provider_name,
                COUNT(*) as model_count
            FROM llm_models m
            JOIN llm_providers p ON m.provider_id = p.provider_id
            WHERE m.is_active = TRUE AND p.is_active = TRUE
            GROUP BY p.provider_name
            ORDER BY p.provider_name
        """)
        
        for provider_name, model_count in cursor.fetchall():
            print(f"{provider_name:15s}: {model_count:2d} modelů")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔄 GENT - Aktualizace pouze OpenAI modelů na FUNKČNÍ")
    print("=" * 60)
    print("⚠️  Tento skript upraví pouze OpenAI modely!")
    print("   Google a Anthropic zůstávají beze změny.")
    print("   Přidá pouze OVĚŘENÉ a FUNKČNÍ OpenAI modely.")
    print()
    
    # Potvrzení od uživatele
    response = input("Pokračovat s aktualizací OpenAI modelů? (ano/ne): ").lower().strip()
    if response not in ['ano', 'a', 'yes', 'y']:
        print("❌ Operace zrušena uživatelem.")
        sys.exit(0)
    
    success = update_openai_models()
    
    if success:
        print("\n🎉 Aktualizace OpenAI modelů byla úspěšná!")
        print("💡 Doporučuji restartovat API server: sudo systemctl restart gent-api")
        print("💡 Pak obnovit frontend (F5) a otestovat OpenAI modely")
    else:
        print("\n💥 Aktualizace selhala!")
        sys.exit(1)
