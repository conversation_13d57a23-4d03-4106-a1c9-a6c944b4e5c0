#!/usr/bin/env python3
"""
Finální oprava modelů - odstranění š<PERSON>tných a přidání správných
"""

import requests
import json
import time

def fix_models():
    """Opraví modely v databázi"""
    try:
        print("🔧 Opravuji modely v databázi...")
        
        # Počkáme, až se API spustí
        print("⏳ Čekám na API...")
        time.sleep(3)
        
        response = requests.post(
            "http://localhost:8001/api/db/llm/add-missing-models",
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Úspěch!")
            print(f"📝 Zpráva: {result.get('message', 'N/A')}")
            print(f"📊 Přidáno modelů: {result.get('added_count', 0)}")
            print(f"🏢 Poskytovatelé: {result.get('providers', [])}")
            return True
        else:
            print(f"❌ API chyba: {response.status_code}")
            print(f"Odpověď: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Chyba při volání API: {e}")
        return False

def test_final_models():
    """Otestuje finální stav modelů"""
    try:
        print("\n🧪 Testování finálního stavu modelů...")
        
        response = requests.get("http://localhost:8001/api/db/llm/models")
        
        if response.status_code == 200:
            models = response.json()
            print(f"✅ Celkem modelů: {len(models)}")
            
            # Zobrazíme modely podle poskytovatelů
            providers = {}
            for model in models:
                provider = model['provider_name']
                if provider not in providers:
                    providers[provider] = []
                providers[provider].append(model['name'])
            
            print(f"\n📊 Finální stav modelů:")
            for provider, model_list in providers.items():
                print(f"\n🏢 {provider}: {len(model_list)} modelů")
                for model_name in model_list:
                    print(f"  - {model_name}")
            
            # Zkontrolujeme OpenAI modely
            openai_models = providers.get('OpenAI', [])
            expected_openai = ['gpt-4o', 'gpt-4o-mini', 'gpt-4.5', 'gpt-4-1', 'gpt-4-1-mini', 'gpt-4-1-nano', 'o1-preview', 'o1', 'o1-pro', 'o3', 'o3-mini']
            
            print(f"\n🔍 Kontrola OpenAI modelů:")
            for expected in expected_openai:
                if expected in openai_models:
                    print(f"  ✅ {expected}")
                else:
                    print(f"  ❌ {expected} - CHYBÍ")
            
            # Zkontrolujeme Google modely
            google_models = providers.get('Google', [])
            print(f"\n🔍 Google modely:")
            for model in google_models:
                if 'flash-lite' in model:
                    print(f"  ❌ {model} - MĚLO BY BÝT ODSTRANĚNO")
                else:
                    print(f"  ✅ {model}")
            
            return True
            
        else:
            print(f"❌ API chyba: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Chyba při testování: {e}")
        return False

def test_model_functionality():
    """Otestuje funkčnost jednoho modelu"""
    try:
        print("\n🧪 Testování funkčnosti modelu gpt-4o...")
        
        test_data = {
            "model_id": "1_gpt-4o",  # Předpokládáme OpenAI provider_id = 1
            "message": "Hello, this is a test message. Please respond briefly."
        }
        
        response = requests.post(
            "http://localhost:8001/api/db/llm/test-llm",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Test modelu gpt-4o úspěšný!")
            print(f"Model: {result.get('model', 'N/A')}")
            print(f"Odpověď: {result.get('response', {}).get('text', 'N/A')[:100]}...")
            return True
        else:
            print(f"⚠️  Test modelu gpt-4o selhal: {response.status_code}")
            print(f"Chyba: {response.text}")
            
            # Zkusíme gpt-4o-mini jako fallback
            print("\n🔄 Zkouším gpt-4o-mini jako fallback...")
            test_data["model_id"] = "1_gpt-4o-mini"
            
            response = requests.post(
                "http://localhost:8001/api/db/llm/test-llm",
                json=test_data,
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Test modelu gpt-4o-mini úspěšný!")
                print(f"Model: {result.get('model', 'N/A')}")
                print(f"Odpověď: {result.get('response', {}).get('text', 'N/A')[:100]}...")
                return True
            else:
                print(f"❌ Test modelu gpt-4o-mini také selhal: {response.status_code}")
                return False
            
    except Exception as e:
        print(f"❌ Chyba při testování modelu: {e}")
        return False

if __name__ == "__main__":
    print("🔧 GENT - Finální oprava modelů")
    print("=" * 60)
    
    # Opravíme modely
    success = fix_models()
    
    if success:
        # Otestujeme výsledek
        test_success = test_final_models()
        
        if test_success:
            # Otestujeme funkčnost
            func_success = test_model_functionality()
            
            print(f"\n🎉 Oprava dokončena!")
            print(f"💡 Obnov frontend (F5) a zkontroluj dropdown 'Vyberte model'")
            print(f"💡 Měl bys vidět správné názvy modelů bez velkých písmen!")
            print(f"💡 Gemini 2.0 Flash Lite by měl být odstraněn!")
            
            if func_success:
                print(f"✅ Modely jsou funkční a testovatelné!")
            else:
                print(f"⚠️  Modely jsou v databázi, ale testování selhalo.")
        else:
            print(f"\n⚠️  Oprava proběhla, ale test selhal.")
    else:
        print(f"\n💥 Oprava modelů selhala!")
