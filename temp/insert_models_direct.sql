-- <PERSON><PERSON><PERSON><PERSON> vložení chy<PERSON>ě<PERSON><PERSON><PERSON><PERSON><PERSON> modelů do databáze GENT

-- Nejprve zjistíme ID poskytovatelů
\echo 'Zjišťování ID poskytovatelů...'
\echo 'OpenAI provider_id:'
SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'openai' AND is_active = TRUE;

\echo 'Google provider_id:'
SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'google' AND is_active = TRUE;

-- Vložení OpenAI modelů
\echo 'Vkládání OpenAI modelů...'

-- GPT-4o
INSERT INTO llm_models (
    provider_id, model_name, model_identifier, 
    context_length, max_tokens_output, default_temperature,
    capabilities, is_active, is_default
) VALUES (
    (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'openai' AND is_active = TRUE LIMIT 1),
    'GPT-4o',
    'gpt-4o',
    128000,
    4096,
    0.7,
    '{"text": true, "code": true, "reasoning": true, "function_calling": true}',
    TRUE,
    FALSE
) ON CONFLICT DO NOTHING;

-- GPT-4.5
INSERT INTO llm_models (
    provider_id, model_name, model_identifier, 
    context_length, max_tokens_output, default_temperature,
    capabilities, is_active, is_default
) VALUES (
    (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'openai' AND is_active = TRUE LIMIT 1),
    'GPT-4.5',
    'gpt-4.5',
    200000,
    4096,
    0.7,
    '{"text": true, "code": true, "reasoning": true, "function_calling": true, "advanced_reasoning": true}',
    TRUE,
    FALSE
) ON CONFLICT DO NOTHING;

-- GPT-4-1
INSERT INTO llm_models (
    provider_id, model_name, model_identifier, 
    context_length, max_tokens_output, default_temperature,
    capabilities, is_active, is_default
) VALUES (
    (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'openai' AND is_active = TRUE LIMIT 1),
    'GPT-4-1',
    'gpt-4-1',
    200000,
    4096,
    0.7,
    '{"text": true, "code": true, "reasoning": true, "function_calling": true, "advanced_reasoning": true}',
    TRUE,
    FALSE
) ON CONFLICT DO NOTHING;

-- GPT-4-1 Mini
INSERT INTO llm_models (
    provider_id, model_name, model_identifier, 
    context_length, max_tokens_output, default_temperature,
    capabilities, is_active, is_default
) VALUES (
    (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'openai' AND is_active = TRUE LIMIT 1),
    'GPT-4-1 Mini',
    'gpt-4-1-mini',
    128000,
    4096,
    0.7,
    '{"text": true, "code": true, "reasoning": true, "function_calling": true}',
    TRUE,
    FALSE
) ON CONFLICT DO NOTHING;

-- GPT-4-1 Nano
INSERT INTO llm_models (
    provider_id, model_name, model_identifier, 
    context_length, max_tokens_output, default_temperature,
    capabilities, is_active, is_default
) VALUES (
    (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'openai' AND is_active = TRUE LIMIT 1),
    'GPT-4-1 Nano',
    'gpt-4-1-nano',
    32000,
    2048,
    0.7,
    '{"text": true, "code": true, "function_calling": true}',
    TRUE,
    FALSE
) ON CONFLICT DO NOTHING;

-- O3
INSERT INTO llm_models (
    provider_id, model_name, model_identifier, 
    context_length, max_tokens_output, default_temperature,
    capabilities, is_active, is_default
) VALUES (
    (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'openai' AND is_active = TRUE LIMIT 1),
    'O3',
    'o3',
    128000,
    4096,
    0.7,
    '{"reasoning": true, "mathematics": true, "science": true, "logic": true, "problem_solving": true}',
    TRUE,
    FALSE
) ON CONFLICT DO NOTHING;

-- O3 Mini
INSERT INTO llm_models (
    provider_id, model_name, model_identifier, 
    context_length, max_tokens_output, default_temperature,
    capabilities, is_active, is_default
) VALUES (
    (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'openai' AND is_active = TRUE LIMIT 1),
    'O3 Mini',
    'o3-mini',
    64000,
    2048,
    0.7,
    '{"reasoning": true, "mathematics": true, "logic": true, "problem_solving": true}',
    TRUE,
    FALSE
) ON CONFLICT DO NOTHING;

-- Vložení Google modelů
\echo 'Vkládání Google modelů...'

-- Gemini 2.0 Flash
INSERT INTO llm_models (
    provider_id, model_name, model_identifier, 
    context_length, max_tokens_output, default_temperature,
    capabilities, is_active, is_default
) VALUES (
    (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'google' AND is_active = TRUE LIMIT 1),
    'Gemini 2.0 Flash',
    'gemini-2.0-flash',
    1000000,
    8192,
    0.7,
    '{"text": true, "vision": true, "audio": true, "video": true, "code": true, "multimodal": true}',
    TRUE,
    FALSE
) ON CONFLICT DO NOTHING;

-- Gemini 2.5 Pro Preview 05-06
INSERT INTO llm_models (
    provider_id, model_name, model_identifier, 
    context_length, max_tokens_output, default_temperature,
    capabilities, is_active, is_default
) VALUES (
    (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'google' AND is_active = TRUE LIMIT 1),
    'Gemini 2.5 Pro Preview 05-06',
    'gemini-2.5-pro-preview-05-06',
    2000000,
    8192,
    0.7,
    '{"text": true, "code": true, "reasoning": true, "analysis": true, "research": true}',
    TRUE,
    FALSE
) ON CONFLICT DO NOTHING;

-- Gemini 2.5 Flash Preview 05-20
INSERT INTO llm_models (
    provider_id, model_name, model_identifier, 
    context_length, max_tokens_output, default_temperature,
    capabilities, is_active, is_default
) VALUES (
    (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'google' AND is_active = TRUE LIMIT 1),
    'Gemini 2.5 Flash Preview 05-20',
    'gemini-2.5-flash-preview-05-20',
    1000000,
    8192,
    0.7,
    '{"text": true, "code": true, "quick_analysis": true, "general": true}',
    TRUE,
    FALSE
) ON CONFLICT DO NOTHING;

-- Zobrazení výsledků
\echo 'Zobrazení aktuálního stavu...'
SELECT 
    p.provider_name, 
    COUNT(*) as model_count
FROM llm_models m
JOIN llm_providers p ON m.provider_id = p.provider_id
WHERE m.is_active = TRUE AND p.is_active = TRUE
GROUP BY p.provider_name
ORDER BY p.provider_name;

\echo 'Všechny OpenAI modely:'
SELECT 
    m.model_name, 
    m.model_identifier
FROM llm_models m
JOIN llm_providers p ON m.provider_id = p.provider_id
WHERE LOWER(p.provider_name) = 'openai' AND m.is_active = TRUE
ORDER BY m.model_name;

\echo 'Všechny Google modely:'
SELECT 
    m.model_name, 
    m.model_identifier
FROM llm_models m
JOIN llm_providers p ON m.provider_id = p.provider_id
WHERE LOWER(p.provider_name) = 'google' AND m.is_active = TRUE
ORDER BY m.model_name;
