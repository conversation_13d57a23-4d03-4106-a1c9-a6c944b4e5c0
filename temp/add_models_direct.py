#!/usr/bin/env python3
"""
<PERSON>řid<PERSON><PERSON> chyběj<PERSON><PERSON><PERSON><PERSON> modelů přímo přes GENT databázové připojení
"""

import sys
import os
import json
sys.path.append('/opt/gent')

# Import GENT databázov<PERSON><PERSON> služeb
from gent.db.services.llm_direct_db_service import LlmDirectDbService

def add_missing_models():
    """Přidá chybějící modely do databáze"""
    try:
        print("🔄 Připojuji se k databázi přes GENT službu...")
        service = LlmDirectDbService()
        conn = service._get_connection()
        cursor = conn.cursor()
        
        # Zjistíme ID poskytovatelů
        cursor.execute("""
            SELECT provider_id, provider_name 
            FROM llm_providers 
            WHERE LOWER(provider_name) IN ('openai', 'google') 
            AND is_active = TRUE
        """)
        
        providers = {row[1].lower(): row[0] for row in cursor.fetchall()}
        print(f"📋 Nalezení poskytovatelé: {providers}")
        
        if not providers:
            print("❌ Žádní poskytovatelé nenalezeni!")
            return False
        
        added_count = 0
        
        # OpenAI modely
        if 'openai' in providers:
            openai_models = [
                ('GPT-4o', 'gpt-4o', 128000, 4096, '{"text": true, "code": true, "reasoning": true, "function_calling": true}'),
                ('GPT-4.5', 'gpt-4.5', 200000, 4096, '{"text": true, "code": true, "reasoning": true, "function_calling": true, "advanced_reasoning": true}'),
                ('GPT-4-1', 'gpt-4-1', 200000, 4096, '{"text": true, "code": true, "reasoning": true, "function_calling": true, "advanced_reasoning": true}'),
                ('GPT-4-1 Mini', 'gpt-4-1-mini', 128000, 4096, '{"text": true, "code": true, "reasoning": true, "function_calling": true}'),
                ('GPT-4-1 Nano', 'gpt-4-1-nano', 32000, 2048, '{"text": true, "code": true, "function_calling": true}'),
                ('O3', 'o3', 128000, 4096, '{"reasoning": true, "mathematics": true, "science": true, "logic": true, "problem_solving": true}'),
                ('O3 Mini', 'o3-mini', 64000, 2048, '{"reasoning": true, "mathematics": true, "logic": true, "problem_solving": true}')
            ]
            
            for name, identifier, context, max_tokens, capabilities in openai_models:
                # Zkontrolujeme, zda model už neexistuje
                cursor.execute("""
                    SELECT COUNT(*) 
                    FROM llm_models 
                    WHERE provider_id = %s AND model_identifier = %s
                """, (providers['openai'], identifier))
                
                if cursor.fetchone()[0] == 0:
                    # Model neexistuje, přidáme ho
                    cursor.execute("""
                        INSERT INTO llm_models (
                            provider_id, model_name, model_identifier, 
                            context_length, max_tokens_output, default_temperature,
                            capabilities, is_active, is_default
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        providers['openai'], name, identifier,
                        context, max_tokens, 0.7,
                        capabilities, True, False
                    ))
                    
                    print(f"✅ Přidán OpenAI model: {name}")
                    added_count += 1
                else:
                    print(f"⚠️  OpenAI model už existuje: {name}")
        
        # Google modely
        if 'google' in providers:
            google_models = [
                ('Gemini 2.0 Flash', 'gemini-2.0-flash', 1000000, 8192, '{"text": true, "vision": true, "audio": true, "video": true, "code": true, "multimodal": true}'),
                ('Gemini 2.5 Pro Preview 05-06', 'gemini-2.5-pro-preview-05-06', 2000000, 8192, '{"text": true, "code": true, "reasoning": true, "analysis": true, "research": true}'),
                ('Gemini 2.5 Flash Preview 05-20', 'gemini-2.5-flash-preview-05-20', 1000000, 8192, '{"text": true, "code": true, "quick_analysis": true, "general": true}')
            ]
            
            for name, identifier, context, max_tokens, capabilities in google_models:
                # Zkontrolujeme, zda model už neexistuje
                cursor.execute("""
                    SELECT COUNT(*) 
                    FROM llm_models 
                    WHERE provider_id = %s AND model_identifier = %s
                """, (providers['google'], identifier))
                
                if cursor.fetchone()[0] == 0:
                    # Model neexistuje, přidáme ho
                    cursor.execute("""
                        INSERT INTO llm_models (
                            provider_id, model_name, model_identifier, 
                            context_length, max_tokens_output, default_temperature,
                            capabilities, is_active, is_default
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        providers['google'], name, identifier,
                        context, max_tokens, 0.7,
                        capabilities, True, False
                    ))
                    
                    print(f"✅ Přidán Google model: {name}")
                    added_count += 1
                else:
                    print(f"⚠️  Google model už existuje: {name}")
        
        # Potvrzení změn
        conn.commit()
        print(f"\n🎉 Úspěšně přidáno {added_count} nových modelů!")
        
        # Zobrazíme aktuální stav
        cursor.execute("""
            SELECT p.provider_name, COUNT(*) as model_count
            FROM llm_models m
            JOIN llm_providers p ON m.provider_id = p.provider_id
            WHERE m.is_active = TRUE AND p.is_active = TRUE
            GROUP BY p.provider_name
            ORDER BY p.provider_name
        """)
        
        print(f"\n📊 Aktuální stav modelů v databázi:")
        for row in cursor.fetchall():
            print(f"  {row[0]}: {row[1]} modelů")
        
        cursor.close()
        
        # Uzavření připojení
        if hasattr(service, "_close_connection"):
            service._close_connection()
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🤖 GENT - Přidání chybějících modelů")
    print("=" * 50)
    
    success = add_missing_models()
    
    if success:
        print("\n🎉 Modely byly úspěšně přidány!")
        print("💡 Restartuj API server: sudo systemctl restart gent-api")
        print("💡 Pak obnov frontend (F5) a zkontroluj dropdown")
    else:
        print("\n💥 Přidání modelů selhalo!")
        sys.exit(1)
