#!/usr/bin/env python3
"""
Skript pro aktualizaci popisů modelů v databázi
Zapíše detailní informace o účelu a schopnostech jednotlivých modelů
"""

import sys
import os
import json
sys.path.append('/opt/gent')

from gent.db.direct_connector import get_db_connection
from model_descriptions import MODEL_DESCRIPTIONS

def update_model_descriptions():
    """Aktualizuje popisy modelů v databázi"""
    try:
        print("🔄 Připojuji se k databázi...")
        conn = get_db_connection('gentdb')
        cursor = conn.cursor()
        
        # Nejprve zkontrolujeme, zda tabulka má sloupec pro popis
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'llm_models' 
            AND column_name IN ('description', 'model_description', 'use_cases', 'suitable_for')
        """)
        
        existing_columns = [row[0] for row in cursor.fetchall()]
        print(f"📋 Existující sloupce pro popisy: {existing_columns}")
        
        # Pokud neexistuje sloupec description, přid<PERSON>me ho
        if 'description' not in existing_columns:
            print("➕ Přidávám sloupec 'description' do tabulky llm_models...")
            cursor.execute("""
                ALTER TABLE llm_models 
                ADD COLUMN IF NOT EXISTS description TEXT
            """)
        
        if 'use_cases' not in existing_columns:
            print("➕ Přidávám sloupec 'use_cases' do tabulky llm_models...")
            cursor.execute("""
                ALTER TABLE llm_models 
                ADD COLUMN IF NOT EXISTS use_cases JSONB
            """)
        
        if 'suitable_for' not in existing_columns:
            print("➕ Přidávám sloupec 'suitable_for' do tabulky llm_models...")
            cursor.execute("""
                ALTER TABLE llm_models 
                ADD COLUMN IF NOT EXISTS suitable_for JSONB
            """)
        
        # Získáme všechny modely z databáze
        cursor.execute("""
            SELECT m.model_id, m.model_name, m.model_identifier, p.provider_name
            FROM llm_models m
            JOIN llm_providers p ON m.provider_id = p.provider_id
            WHERE m.is_active = TRUE
            ORDER BY p.provider_name, m.model_name
        """)
        
        models = cursor.fetchall()
        print(f"📊 Nalezeno {len(models)} aktivních modelů v databázi")
        
        updated_count = 0
        
        for model in models:
            model_id, model_name, model_identifier, provider_name = model
            
            # Hledáme popis modelu podle identifikátoru
            description_data = None
            
            # Zkusíme najít přesnou shodu
            if model_identifier in MODEL_DESCRIPTIONS:
                description_data = MODEL_DESCRIPTIONS[model_identifier]
            else:
                # Zkusíme najít částečnou shodu
                for desc_key, desc_value in MODEL_DESCRIPTIONS.items():
                    if (desc_key.lower() in model_identifier.lower() or 
                        model_identifier.lower() in desc_key.lower() or
                        desc_key.lower() in model_name.lower()):
                        description_data = desc_value
                        break
            
            if description_data:
                print(f"✅ Aktualizuji model: {provider_name} - {model_name}")
                
                # Aktualizujeme model s popisem
                cursor.execute("""
                    UPDATE llm_models 
                    SET 
                        description = %s,
                        use_cases = %s,
                        suitable_for = %s
                    WHERE model_id = %s
                """, (
                    description_data.get('description', ''),
                    json.dumps(description_data.get('use_cases', [])),
                    json.dumps(description_data.get('suitable_for', []))
                ))
                
                updated_count += 1
            else:
                print(f"⚠️  Popis nenalezen pro: {provider_name} - {model_name} ({model_identifier})")
        
        # Potvrzení změn
        conn.commit()
        print(f"\n🎉 Úspěšně aktualizováno {updated_count} modelů!")
        
        # Zobrazíme výsledek
        cursor.execute("""
            SELECT 
                p.provider_name,
                m.model_name,
                m.description,
                m.suitable_for
            FROM llm_models m
            JOIN llm_providers p ON m.provider_id = p.provider_id
            WHERE m.is_active = TRUE AND m.description IS NOT NULL
            ORDER BY p.provider_name, m.model_name
        """)
        
        updated_models = cursor.fetchall()
        print(f"\n📋 Modely s aktualizovanými popisy ({len(updated_models)}):")
        
        current_provider = None
        for model in updated_models:
            provider_name, model_name, description, suitable_for = model
            
            if provider_name != current_provider:
                print(f"\n🏢 {provider_name}:")
                current_provider = provider_name
            
            suitable_list = json.loads(suitable_for) if suitable_for else []
            suitable_str = ", ".join(suitable_list) if suitable_list else "obecné"
            
            print(f"  ✓ {model_name}")
            print(f"    📝 {description[:100]}...")
            print(f"    🎯 Vhodné pro: {suitable_str}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba: {e}")
        return False

if __name__ == "__main__":
    print("🤖 GENT - Aktualizace popisů modelů")
    print("=" * 50)
    
    success = update_model_descriptions()
    
    if success:
        print("\n🎉 Aktualizace dokončena úspěšně!")
        print("💡 GENT nyní ví, které modely použít pro jaké účely.")
    else:
        print("\n💥 Aktualizace selhala!")
        sys.exit(1)
