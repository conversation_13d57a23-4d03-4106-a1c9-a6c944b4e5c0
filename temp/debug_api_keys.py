#!/usr/bin/env python3
"""
Debug API klíčů v MCP databázi
"""

import psycopg2
import json
import requests

def get_db_connection():
    """Získá připojení k databázi."""
    try:
        with open('/opt/gent/config/postgres_credentials.json', 'r') as f:
            creds = json.load(f)
        
        conn = psycopg2.connect(
            host='localhost',
            database='gentdb',
            user='postgres',
            password=creds['postgres_password']
        )
        return conn
    except Exception as e:
        print(f"❌ Chyba při připojování k databázi: {e}")
        return None

def debug_database():
    """Debuguje API klíče v databázi."""
    conn = get_db_connection()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    try:
        print("🔍 Debug API klíčů v databázi...")
        
        cursor.execute("""
            SELECT provider_id, provider_name, api_key, 
                   CASE WHEN api_key IS NULL THEN 'NULL'
                        WHEN api_key = '' THEN 'EMPTY'
                        ELSE 'OK'
                   END as status
            FROM mcp_providers 
            ORDER BY provider_name
        """)
        
        providers = cursor.fetchall()
        print("\n📋 Stav v databázi:")
        for provider in providers:
            provider_id, name, api_key, status = provider
            if api_key:
                masked = api_key[:8] + '...' if len(api_key) > 8 else api_key
                print(f"  ID {provider_id}: {name} -> {status} ({masked})")
            else:
                print(f"  ID {provider_id}: {name} -> {status} ({api_key})")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při debugování: {e}")
        return False
    finally:
        cursor.close()
        conn.close()

def debug_api():
    """Debuguje API odpověď."""
    try:
        print("\n🌐 Debug API odpovědi...")
        
        response = requests.get('http://localhost:8001/api/mcp/providers')
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API vrátilo {len(data)} poskytovatelů")
            
            for provider in data:
                name = provider.get('provider_name', 'N/A')
                api_key = provider.get('api_key', None)
                
                if api_key:
                    masked = api_key[:8] + '...' if len(api_key) > 8 else api_key
                    print(f"  {name}: ✅ {masked}")
                else:
                    print(f"  {name}: ❌ {api_key} (type: {type(api_key)})")
        else:
            print(f"❌ API chyba: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Chyba při volání API: {e}")

def fix_api_keys():
    """Opraví API klíče v databázi."""
    conn = get_db_connection()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    try:
        print("\n🔧 Oprava API klíčů...")
        
        # Správné API klíče
        api_keys = {
            'brave-search': 'BSARir7CGmpWKz5mvNgGJyYp3yV8CDn',
            'tavily': '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',
            'perplexity': '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',
            'fetch': ''  # Fetch nepotřebuje API klíč
        }
        
        # Aktualizace API klíčů
        for provider_name, api_key in api_keys.items():
            if api_key:  # Jen pokud má API klíč
                cursor.execute("""
                    UPDATE mcp_providers 
                    SET api_key = %s, auth_type = 'api_key'
                    WHERE provider_name = %s
                """, (api_key, provider_name))
            else:  # Pro fetch nastavíme NULL
                cursor.execute("""
                    UPDATE mcp_providers 
                    SET api_key = NULL, auth_type = 'none'
                    WHERE provider_name = %s
                """, (provider_name,))
            
            if cursor.rowcount > 0:
                print(f"✅ Aktualizován {provider_name}")
            else:
                print(f"⚠️ Poskytovatel {provider_name} nebyl nalezen")
        
        conn.commit()
        return True
        
    except Exception as e:
        print(f"❌ Chyba při opravě: {e}")
        conn.rollback()
        return False
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    print("🔧 Debug MCP API klíčů")
    print("=" * 40)
    
    # 1. Debug databáze
    debug_database()
    
    # 2. Debug API
    debug_api()
    
    # 3. Oprava API klíčů
    print("\n" + "=" * 40)
    if fix_api_keys():
        print("✅ API klíče opraveny!")
        
        # 4. Znovu debug
        print("\n" + "=" * 40)
        debug_database()
        debug_api()
    else:
        print("❌ Chyba při opravě!")
