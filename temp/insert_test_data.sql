-- GENT v10 - <PERSON><PERSON><PERSON><PERSON><PERSON> testovacích dat pro funkčnost webu

-- <PERSON><PERSON>žení skupin agentů
INSERT INTO agent_groups (name, description) VALUES
('Research', 'Skupina agentů pro výzkum a analýzu'),
('Development', 'Skupina agentů pro vývoj a programování'),
('Support', 'Skupina agentů pro podporu uživatelů'),
('Management', 'Skupina agentů pro řízení projektů')
ON CONFLICT DO NOTHING;

-- Vložení LLM providerů
INSERT INTO llm_providers (provider_name, api_base_url, api_key_required, is_active) VALUES
('OpenAI', 'https://api.openai.com/v1', true, true),
('Anthropic', 'https://api.anthropic.com', true, true),
('Google', 'https://generativelanguage.googleapis.com/v1', true, true),
('Ollama', 'http://localhost:11434', false, true)
ON CONFLICT (provider_name) DO NOTHING;

-- <PERSON><PERSON><PERSON><PERSON><PERSON> LLM modelů
INSERT INTO llm_models (provider_id, model_name, model_identifier, context_length, max_tokens_output, is_active, is_default) VALUES
(1, 'GPT-4o Mini', 'gpt-4o-mini', 128000, 16384, true, true),
(2, 'Claude 3 Haiku', 'claude-3-haiku-20240307', 200000, 4096, true, false),
(2, 'Claude 3.5 Sonnet', 'claude-3-5-sonnet-20241022', 200000, 8192, true, false),
(3, 'Gemini Pro', 'gemini-pro', 32768, 8192, true, false),
(4, 'Llama 3.1 70B', 'llama3.1:70b', 131072, 4096, true, false)
ON CONFLICT (provider_id, model_identifier) DO NOTHING;

-- Vložení agentů
INSERT INTO agents (name, agent_type, description, llm_model_id, system_message, group_id, is_active, metadata) VALUES
(
    'SystemAdmin',
    'system_admin',
    'Agent specializovaný na správu systému, monitorování výkonu a údržbu.',
    1,
    'Jsi SystemAdmin agent specializovaný na správu systému GENT. Tvým úkolem je monitorovat výkon, provádět údržbu a zajišťovat stabilní chod systému.',
    4,
    true,
    '{"capabilities": ["system_monitoring", "performance_analysis", "maintenance"], "priority": "high"}'
),
(
    'CodeDeveloper',
    'developer',
    'Agent specializovaný na vývoj kódu, refaktoring a optimalizaci.',
    2,
    'Jsi CodeDeveloper agent specializovaný na vývoj kódu. Tvým úkolem je psát kvalitní kód, provádět refaktoring a optimalizace.',
    2,
    true,
    '{"capabilities": ["coding", "refactoring", "optimization"], "languages": ["python", "javascript", "sql"]}'
),
(
    'DataAnalyst',
    'analyst',
    'Agent specializovaný na analýzu dat, vizualizaci a reporting.',
    3,
    'Jsi DataAnalyst agent specializovaný na analýzu dat. Tvým úkolem je analyzovat data, vytvářet vizualizace a sestavovat reporty.',
    1,
    true,
    '{"capabilities": ["data_analysis", "visualization", "reporting"], "tools": ["pandas", "matplotlib", "sql"]}'
),
(
    'ResearchAgent',
    'researcher',
    'Agent specializovaný na výzkum, sběr informací a analýzu zdrojů.',
    1,
    'Jsi ResearchAgent specializovaný na výzkum. Tvým úkolem je vyhledávat informace, analyzovat zdroje a připravovat výzkumné materiály.',
    1,
    true,
    '{"capabilities": ["research", "information_gathering", "source_analysis"], "domains": ["technology", "science", "business"]}'
),
(
    'SupportAgent',
    'support',
    'Agent specializovaný na podporu uživatelů a řešení problémů.',
    4,
    'Jsi SupportAgent specializovaný na podporu uživatelů. Tvým úkolem je pomáhat uživatelům, řešit jejich problémy a poskytovat technickou podporu.',
    3,
    true,
    '{"capabilities": ["user_support", "problem_solving", "documentation"], "response_time": "fast"}'
)
ON CONFLICT DO NOTHING;

-- Vložení testovacích konverzací
INSERT INTO conversations (title, agent_id, user_id) VALUES
('Diskuze o systémové architektuře', 1, 'user_001'),
('Vývoj nové funkcionality', 2, 'user_001'),
('Analýza výkonnostních dat', 3, 'user_002'),
('Výzkum AI technologií', 4, 'user_001'),
('Technická podpora - problém s API', 5, 'user_003')
ON CONFLICT DO NOTHING;

-- Vložení testovacích zpráv
INSERT INTO messages (conversation_id, content, role, agent_id, user_id, metadata) VALUES
(1, 'Potřebuji pomoct s návrhem architektury pro nový modul.', 'user', null, 'user_001', '{"timestamp": "2025-05-27T06:00:00Z"}'),
(1, 'Rád vám pomohu s návrhem architektury. Můžete mi říct více o požadavcích na nový modul?', 'assistant', 1, null, '{"timestamp": "2025-05-27T06:01:00Z"}'),
(2, 'Jak bychom měli implementovat novou chat funkci?', 'user', null, 'user_001', '{"timestamp": "2025-05-27T06:05:00Z"}'),
(2, 'Pro implementaci chat funkce doporučuji použít WebSocket pro real-time komunikaci a PostgreSQL pro ukládání zpráv.', 'assistant', 2, null, '{"timestamp": "2025-05-27T06:06:00Z"}'),
(3, 'Můžete analyzovat výkonnostní data z posledního týdne?', 'user', null, 'user_002', '{"timestamp": "2025-05-27T06:10:00Z"}'),
(3, 'Samozřejmě! Provedu analýzu výkonnostních dat. Potřebuji přístup k metrikám serveru a databáze.', 'assistant', 3, null, '{"timestamp": "2025-05-27T06:11:00Z"}')
ON CONFLICT DO NOTHING;

-- Vložení testovacích úkolů
INSERT INTO tasks (title, description, status, priority, assigned_agent_id, user_id) VALUES
('Optimalizace databázových dotazů', 'Zlepšit výkon pomalých SQL dotazů v systému', 'in_progress', 2, 3, 'user_001'),
('Implementace nového API endpointu', 'Vytvořit API endpoint pro správu agentů', 'pending', 1, 2, 'user_001'),
('Aktualizace dokumentace', 'Aktualizovat dokumentaci pro nové funkce', 'pending', 1, 5, 'user_002'),
('Monitoring systému', 'Nastavit monitoring pro kritické komponenty', 'completed', 3, 1, 'user_001'),
('Výzkum nových LLM modelů', 'Prozkoumat nové dostupné jazykové modely', 'in_progress', 2, 4, 'user_003')
ON CONFLICT DO NOTHING;

-- Vložení testovacích knowledge items
INSERT INTO knowledge_items (title, content, source, tags) VALUES
('GENT Architektura', 'GENT v10 je postavený na modulární architektuře s PostgreSQL databází, FastAPI backendem a Vue.js frontendem.', 'internal_docs', '["architecture", "documentation", "system"]'),
('API Dokumentace', 'Kompletní dokumentace všech API endpointů včetně příkladů použití.', 'api_docs', '["api", "documentation", "reference"]'),
('Databázové schéma', 'Popis všech tabulek a jejich vztahů v PostgreSQL databázi.', 'database_docs', '["database", "schema", "postgresql"]'),
('Deployment Guide', 'Návod na nasazení GENT systému do produkčního prostředí.', 'deployment_docs', '["deployment", "production", "guide"]'),
('Troubleshooting', 'Časté problémy a jejich řešení při provozu GENT systému.', 'support_docs', '["troubleshooting", "support", "problems"]')
ON CONFLICT DO NOTHING;
