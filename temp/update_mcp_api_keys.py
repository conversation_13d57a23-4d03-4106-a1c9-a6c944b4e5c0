#!/usr/bin/env python3
"""
Aktualizace API klíčů pro MCP poskytovatele
"""

import psycopg2
import json

def get_db_connection():
    """Získá připojení k datab<PERSON>zi."""
    try:
        with open('/opt/gent/config/postgres_credentials.json', 'r') as f:
            creds = json.load(f)
        
        conn = psycopg2.connect(
            host='localhost',
            database='gentdb',
            user='postgres',
            password=creds['postgres_password']
        )
        return conn
    except Exception as e:
        print(f"❌ Chyba při připojování k databázi: {e}")
        return None

def update_api_keys():
    """Aktualizuje API klíče pro MCP poskytovatele."""
    conn = get_db_connection()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    try:
        print("🔑 Aktualizace API klíčů pro MCP poskytovatele...")
        
        # API klíče
        api_keys = {
            'brave-search': 'BSARir7CGmpWKz5mvNgGJyYp3yV8CDn',
            'tavily': 'CLEK7GE21FGF7KZBMTEJZA5S',
            'perplexity': '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'
        }
        
        # Aktualizace API klíčů
        for provider_name, api_key in api_keys.items():
            cursor.execute("""
                UPDATE mcp_providers 
                SET api_key = %s, auth_type = 'api_key'
                WHERE provider_name = %s
            """, (api_key, provider_name))
            
            if cursor.rowcount > 0:
                print(f"✅ Aktualizován API klíč pro {provider_name}")
            else:
                print(f"⚠️ Poskytovatel {provider_name} nebyl nalezen")
        
        # Aktualizace base_url pro poskytovatele
        base_urls = {
            'brave-search': 'https://api.search.brave.com/res/v1',
            'tavily': 'https://api.tavily.com',
            'perplexity': 'https://api.perplexity.ai'
        }
        
        for provider_name, base_url in base_urls.items():
            cursor.execute("""
                UPDATE mcp_providers 
                SET base_url = %s
                WHERE provider_name = %s
            """, (base_url, provider_name))
            
            if cursor.rowcount > 0:
                print(f"✅ Aktualizována base_url pro {provider_name}")
        
        conn.commit()
        print("✅ API klíče byly úspěšně aktualizovány!")
        
        # Výpis aktuálního stavu
        print("\n📋 Aktuální stav poskytovatelů:")
        cursor.execute("""
            SELECT provider_name, provider_type, base_url, 
                   CASE WHEN api_key IS NOT NULL THEN 'Nastaven' ELSE 'Není nastaven' END as api_key_status,
                   auth_type, is_active
            FROM mcp_providers 
            WHERE provider_name IN ('brave-search', 'tavily', 'fetch', 'perplexity')
            ORDER BY provider_name
        """)
        
        providers = cursor.fetchall()
        for provider in providers:
            print(f"  - {provider[0]}: {provider[1]}, URL: {provider[2]}, API klíč: {provider[3]}, Auth: {provider[4]}, Aktivní: {provider[5]}")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při aktualizaci API klíčů: {e}")
        conn.rollback()
        return False
    finally:
        cursor.close()
        conn.close()

def test_provider_config():
    """Otestuje konfiguraci poskytovatelů."""
    conn = get_db_connection()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    try:
        print("\n🧪 Test konfigurace poskytovatelů...")
        
        # Test načtení poskytovatelů s nástroji
        cursor.execute("""
            SELECT p.provider_name, p.provider_type, p.base_url, p.api_key,
                   COUNT(t.tool_id) as tools_count
            FROM mcp_providers p
            LEFT JOIN mcp_tools t ON p.provider_id = t.provider_id
            WHERE p.provider_name IN ('brave-search', 'tavily', 'fetch')
            GROUP BY p.provider_id, p.provider_name, p.provider_type, p.base_url, p.api_key
            ORDER BY p.provider_name
        """)
        
        providers = cursor.fetchall()
        for provider in providers:
            provider_name, provider_type, base_url, api_key, tools_count = provider
            api_key_masked = api_key[:8] + '...' if api_key else 'Není nastaven'
            print(f"✅ {provider_name}: {provider_type}, URL: {base_url}, API: {api_key_masked}, Nástroje: {tools_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při testování konfigurace: {e}")
        return False
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    print("🔑 MCP Management - Aktualizace API klíčů")
    print("=" * 50)
    
    # Aktualizace API klíčů
    if not update_api_keys():
        print("❌ Chyba při aktualizaci API klíčů!")
        exit(1)
    
    # Test konfigurace
    if not test_provider_config():
        print("❌ Chyba při testování konfigurace!")
        exit(1)
    
    print("\n🎉 API klíče byly úspěšně aktualizovány a otestovány!")
    print("📋 Nyní můžete testovat:")
    print("   1. Brave Search s API klíčem BSARir7CGmpWKz5mvNgGJyYp3yV8CDn")
    print("   2. Tavily Search s API klíčem CLEK7GE21FGF7KZBMTEJZA5S")
    print("   3. Fetch nástroj pro stahování webových stránek")
