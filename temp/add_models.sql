-- Přidán<PERSON> nových LLM modelů do databáze GENT

-- Nejprve zjistíme ID poskytovatelů
\echo 'Zjišťování ID poskytovatelů...'
SELECT provider_id, provider_name FROM llm_providers WHERE LOWER(provider_name) IN ('openai', 'google', 'anthropic') AND is_active = TRUE;

-- Přidání OpenAI modelů (předpokládáme provider_id = 1 pro OpenAI)
\echo 'Přidávání OpenAI modelů...'

-- GPT-4o (pokud neexistuje)
INSERT INTO llm_models (
    provider_id, model_name, model_identifier,
    context_length, max_tokens_output, default_temperature,
    capabilities, is_active, is_default
)
SELECT
    (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'openai' AND is_active = TRUE LIMIT 1),
    'GPT-4o',
    'gpt-4o',
    128000,
    4096,
    0.7,
    '{"text": true, "code": true, "reasoning": true, "function_calling": true}',
    TRUE,
    FALSE
WHERE NOT EXISTS (
    SELECT 1 FROM llm_models m
    JOIN llm_providers p ON m.provider_id = p.provider_id
    WHERE LOWER(p.provider_name) = 'openai' AND m.model_identifier = 'gpt-4o'
);

-- GPT-4.5
INSERT INTO llm_models (
    provider_id, model_name, model_identifier,
    context_length, max_tokens_output, default_temperature,
    capabilities, is_active, is_default
)
SELECT
    (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'openai' AND is_active = TRUE LIMIT 1),
    'GPT-4.5',
    'gpt-4.5',
    200000,
    4096,
    0.7,
    '{"text": true, "code": true, "reasoning": true, "function_calling": true, "advanced_reasoning": true}',
    TRUE,
    FALSE
WHERE NOT EXISTS (
    SELECT 1 FROM llm_models m
    JOIN llm_providers p ON m.provider_id = p.provider_id
    WHERE LOWER(p.provider_name) = 'openai' AND m.model_identifier = 'gpt-4.5'
);

-- GPT-4-1
INSERT INTO llm_models (
    provider_id, model_name, model_identifier,
    context_length, max_tokens_output, default_temperature,
    capabilities, is_active, is_default
)
SELECT
    (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'openai' AND is_active = TRUE LIMIT 1),
    'GPT-4-1',
    'gpt-4-1',
    200000,
    4096,
    0.7,
    '{"text": true, "code": true, "reasoning": true, "function_calling": true, "advanced_reasoning": true}',
    TRUE,
    FALSE
WHERE NOT EXISTS (
    SELECT 1 FROM llm_models m
    JOIN llm_providers p ON m.provider_id = p.provider_id
    WHERE LOWER(p.provider_name) = 'openai' AND m.model_identifier = 'gpt-4-1'
);

-- GPT-4-1 Mini
INSERT INTO llm_models (
    provider_id, model_name, model_identifier,
    context_length, max_tokens_output, default_temperature,
    capabilities, is_active, is_default
)
SELECT
    (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'openai' AND is_active = TRUE LIMIT 1),
    'GPT-4-1 Mini',
    'gpt-4-1-mini',
    128000,
    4096,
    0.7,
    '{"text": true, "code": true, "reasoning": true, "function_calling": true}',
    TRUE,
    FALSE
WHERE NOT EXISTS (
    SELECT 1 FROM llm_models m
    JOIN llm_providers p ON m.provider_id = p.provider_id
    WHERE LOWER(p.provider_name) = 'openai' AND m.model_identifier = 'gpt-4-1-mini'
);

-- GPT-4-1 Nano
INSERT INTO llm_models (
    provider_id, model_name, model_identifier,
    context_length, max_tokens_output, default_temperature,
    capabilities, is_active, is_default
)
SELECT
    (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'openai' AND is_active = TRUE LIMIT 1),
    'GPT-4-1 Nano',
    'gpt-4-1-nano',
    32000,
    2048,
    0.7,
    '{"text": true, "code": true, "function_calling": true}',
    TRUE,
    FALSE
WHERE NOT EXISTS (
    SELECT 1 FROM llm_models m
    JOIN llm_providers p ON m.provider_id = p.provider_id
    WHERE LOWER(p.provider_name) = 'openai' AND m.model_identifier = 'gpt-4-1-nano'
);

-- O3
INSERT INTO llm_models (
    provider_id, model_name, model_identifier,
    context_length, max_tokens_output, default_temperature,
    capabilities, is_active, is_default
)
SELECT
    (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'openai' AND is_active = TRUE LIMIT 1),
    'O3',
    'o3',
    128000,
    4096,
    0.7,
    '{"reasoning": true, "mathematics": true, "science": true, "logic": true, "problem_solving": true}',
    TRUE,
    FALSE
WHERE NOT EXISTS (
    SELECT 1 FROM llm_models m
    JOIN llm_providers p ON m.provider_id = p.provider_id
    WHERE LOWER(p.provider_name) = 'openai' AND m.model_identifier = 'o3'
);

-- O3 Mini
INSERT INTO llm_models (
    provider_id, model_name, model_identifier,
    context_length, max_tokens_output, default_temperature,
    capabilities, is_active, is_default
)
SELECT
    (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'openai' AND is_active = TRUE LIMIT 1),
    'O3 Mini',
    'o3-mini',
    64000,
    2048,
    0.7,
    '{"reasoning": true, "mathematics": true, "logic": true, "problem_solving": true}',
    TRUE,
    FALSE
WHERE NOT EXISTS (
    SELECT 1 FROM llm_models m
    JOIN llm_providers p ON m.provider_id = p.provider_id
    WHERE LOWER(p.provider_name) = 'openai' AND m.model_identifier = 'o3-mini'
);

\echo 'Přidávání Google modelů...'

-- Gemini 2.0 Flash
INSERT INTO llm_models (
    provider_id, model_name, model_identifier,
    context_length, max_tokens_output, default_temperature,
    capabilities, is_active, is_default
)
SELECT
    (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'google' AND is_active = TRUE LIMIT 1),
    'Gemini 2.0 Flash',
    'gemini-2.0-flash',
    1000000,
    8192,
    0.7,
    '{"text": true, "vision": true, "audio": true, "video": true, "code": true, "multimodal": true}',
    TRUE,
    FALSE
WHERE NOT EXISTS (
    SELECT 1 FROM llm_models m
    JOIN llm_providers p ON m.provider_id = p.provider_id
    WHERE LOWER(p.provider_name) = 'google' AND m.model_identifier = 'gemini-2.0-flash'
);

-- Gemini 2.5 Pro Preview 05-06
INSERT INTO llm_models (
    provider_id, model_name, model_identifier,
    context_length, max_tokens_output, default_temperature,
    capabilities, is_active, is_default
)
SELECT
    (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'google' AND is_active = TRUE LIMIT 1),
    'Gemini 2.5 Pro Preview 05-06',
    'gemini-2.5-pro-preview-05-06',
    2000000,
    8192,
    0.7,
    '{"text": true, "code": true, "reasoning": true, "analysis": true, "research": true}',
    TRUE,
    FALSE
WHERE NOT EXISTS (
    SELECT 1 FROM llm_models m
    JOIN llm_providers p ON m.provider_id = p.provider_id
    WHERE LOWER(p.provider_name) = 'google' AND m.model_identifier = 'gemini-2.5-pro-preview-05-06'
);

-- Gemini 2.5 Flash Preview 05-20
INSERT INTO llm_models (
    provider_id, model_name, model_identifier,
    context_length, max_tokens_output, default_temperature,
    capabilities, is_active, is_default
)
SELECT
    (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'google' AND is_active = TRUE LIMIT 1),
    'Gemini 2.5 Flash Preview 05-20',
    'gemini-2.5-flash-preview-05-20',
    1000000,
    8192,
    0.7,
    '{"text": true, "code": true, "quick_analysis": true, "general": true}',
    TRUE,
    FALSE
WHERE NOT EXISTS (
    SELECT 1 FROM llm_models m
    JOIN llm_providers p ON m.provider_id = p.provider_id
    WHERE LOWER(p.provider_name) = 'google' AND m.model_identifier = 'gemini-2.5-flash-preview-05-20'
);

-- Gemini 2.0 Flash Lite
INSERT INTO llm_models (
    provider_id, model_name, model_identifier,
    context_length, max_tokens_output, default_temperature,
    capabilities, is_active, is_default
)
SELECT
    (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'google' AND is_active = TRUE LIMIT 1),
    'Gemini 2.0 Flash Lite',
    'gemini-2.0-flash-lite',
    1000000,
    8192,
    0.7,
    '{"text": true, "vision": true, "code": true, "multimodal": true}',
    TRUE,
    FALSE
WHERE NOT EXISTS (
    SELECT 1 FROM llm_models m
    JOIN llm_providers p ON m.provider_id = p.provider_id
    WHERE LOWER(p.provider_name) = 'google' AND m.model_identifier = 'gemini-2.0-flash-lite'
);

\echo 'Přidávání Anthropic modelů...'

-- Claude Sonnet 4 Latest
INSERT INTO llm_models (
    provider_id, model_name, model_identifier,
    context_length, max_tokens_output, default_temperature,
    capabilities, is_active, is_default
)
SELECT
    (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'anthropic' AND is_active = TRUE LIMIT 1),
    'Claude Sonnet 4 Latest',
    'claude-sonnet-4-latest',
    200000,
    4096,
    0.7,
    '{"text": true, "code": true, "reasoning": true, "analysis": true, "writing": true}',
    TRUE,
    FALSE
WHERE NOT EXISTS (
    SELECT 1 FROM llm_models m
    JOIN llm_providers p ON m.provider_id = p.provider_id
    WHERE LOWER(p.provider_name) = 'anthropic' AND m.model_identifier = 'claude-sonnet-4-latest'
);

-- Claude 3.7 Sonnet Latest
INSERT INTO llm_models (
    provider_id, model_name, model_identifier,
    context_length, max_tokens_output, default_temperature,
    capabilities, is_active, is_default
)
SELECT
    (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'anthropic' AND is_active = TRUE LIMIT 1),
    'Claude 3.7 Sonnet Latest',
    'claude-3-7-sonnet-latest',
    200000,
    4096,
    0.7,
    '{"text": true, "code": true, "reasoning": true, "analysis": true, "writing": true}',
    TRUE,
    FALSE
WHERE NOT EXISTS (
    SELECT 1 FROM llm_models m
    JOIN llm_providers p ON m.provider_id = p.provider_id
    WHERE LOWER(p.provider_name) = 'anthropic' AND m.model_identifier = 'claude-3-7-sonnet-latest'
);

\echo 'Přidávání dalších OpenAI modelů...'

-- GPT-4.1
INSERT INTO llm_models (
    provider_id, model_name, model_identifier,
    context_length, max_tokens_output, default_temperature,
    capabilities, is_active, is_default
)
SELECT
    (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'openai' AND is_active = TRUE LIMIT 1),
    'GPT-4.1',
    'gpt-4.1',
    128000,
    4096,
    0.7,
    '{"text": true, "code": true, "reasoning": true, "function_calling": true}',
    TRUE,
    FALSE
WHERE NOT EXISTS (
    SELECT 1 FROM llm_models m
    JOIN llm_providers p ON m.provider_id = p.provider_id
    WHERE LOWER(p.provider_name) = 'openai' AND m.model_identifier = 'gpt-4.1'
);

-- GPT-4.1 Mini
INSERT INTO llm_models (
    provider_id, model_name, model_identifier,
    context_length, max_tokens_output, default_temperature,
    capabilities, is_active, is_default
)
SELECT
    (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'openai' AND is_active = TRUE LIMIT 1),
    'GPT-4.1 Mini',
    'gpt-4.1-mini',
    128000,
    4096,
    0.7,
    '{"text": true, "code": true, "reasoning": true, "function_calling": true}',
    TRUE,
    FALSE
WHERE NOT EXISTS (
    SELECT 1 FROM llm_models m
    JOIN llm_providers p ON m.provider_id = p.provider_id
    WHERE LOWER(p.provider_name) = 'openai' AND m.model_identifier = 'gpt-4.1-mini'
);

-- GPT-4.1 Nano
INSERT INTO llm_models (
    provider_id, model_name, model_identifier,
    context_length, max_tokens_output, default_temperature,
    capabilities, is_active, is_default
)
SELECT
    (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'openai' AND is_active = TRUE LIMIT 1),
    'GPT-4.1 Nano',
    'gpt-4.1-nano',
    128000,
    4096,
    0.7,
    '{"text": true, "code": true, "function_calling": true}',
    TRUE,
    FALSE
WHERE NOT EXISTS (
    SELECT 1 FROM llm_models m
    JOIN llm_providers p ON m.provider_id = p.provider_id
    WHERE LOWER(p.provider_name) = 'openai' AND m.model_identifier = 'gpt-4.1-nano'
);

-- GPT-4.5 Mini
INSERT INTO llm_models (
    provider_id, model_name, model_identifier,
    context_length, max_tokens_output, default_temperature,
    capabilities, is_active, is_default
)
SELECT
    (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'openai' AND is_active = TRUE LIMIT 1),
    'GPT-4.5 Mini',
    'gpt-4.5-mini',
    128000,
    4096,
    0.7,
    '{"text": true, "code": true, "reasoning": true, "function_calling": true}',
    TRUE,
    FALSE
WHERE NOT EXISTS (
    SELECT 1 FROM llm_models m
    JOIN llm_providers p ON m.provider_id = p.provider_id
    WHERE LOWER(p.provider_name) = 'openai' AND m.model_identifier = 'gpt-4.5-mini'
);

-- GPT-4o Latest
INSERT INTO llm_models (
    provider_id, model_name, model_identifier,
    context_length, max_tokens_output, default_temperature,
    capabilities, is_active, is_default
)
SELECT
    (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'openai' AND is_active = TRUE LIMIT 1),
    'GPT-4o Latest',
    'gpt-4o-latest',
    128000,
    4096,
    0.7,
    '{"text": true, "code": true, "reasoning": true, "function_calling": true, "vision": true}',
    TRUE,
    FALSE
WHERE NOT EXISTS (
    SELECT 1 FROM llm_models m
    JOIN llm_providers p ON m.provider_id = p.provider_id
    WHERE LOWER(p.provider_name) = 'openai' AND m.model_identifier = 'gpt-4o-latest'
);

-- GPT-4o Mini Latest
INSERT INTO llm_models (
    provider_id, model_name, model_identifier,
    context_length, max_tokens_output, default_temperature,
    capabilities, is_active, is_default
)
SELECT
    (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'openai' AND is_active = TRUE LIMIT 1),
    'GPT-4o Mini Latest',
    'gpt-4o-mini-latest',
    128000,
    4096,
    0.7,
    '{"text": true, "code": true, "reasoning": true, "function_calling": true, "vision": true}',
    TRUE,
    FALSE
WHERE NOT EXISTS (
    SELECT 1 FROM llm_models m
    JOIN llm_providers p ON m.provider_id = p.provider_id
    WHERE LOWER(p.provider_name) = 'openai' AND m.model_identifier = 'gpt-4o-mini-latest'
);

-- GPT-4o Audio Preview
INSERT INTO llm_models (
    provider_id, model_name, model_identifier,
    context_length, max_tokens_output, default_temperature,
    capabilities, is_active, is_default
)
SELECT
    (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'openai' AND is_active = TRUE LIMIT 1),
    'GPT-4o Audio Preview',
    'gpt-4o-audio-preview',
    128000,
    4096,
    0.7,
    '{"text": true, "code": true, "reasoning": true, "function_calling": true, "vision": true, "audio": true}',
    TRUE,
    FALSE
WHERE NOT EXISTS (
    SELECT 1 FROM llm_models m
    JOIN llm_providers p ON m.provider_id = p.provider_id
    WHERE LOWER(p.provider_name) = 'openai' AND m.model_identifier = 'gpt-4o-audio-preview'
);

-- O1 Mini
INSERT INTO llm_models (
    provider_id, model_name, model_identifier,
    context_length, max_tokens_output, default_temperature,
    capabilities, is_active, is_default
)
SELECT
    (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'openai' AND is_active = TRUE LIMIT 1),
    'O1 Mini',
    'o1-mini',
    128000,
    65536,
    0.7,
    '{"reasoning": true, "mathematics": true, "science": true, "logic": true, "problem_solving": true}',
    TRUE,
    FALSE
WHERE NOT EXISTS (
    SELECT 1 FROM llm_models m
    JOIN llm_providers p ON m.provider_id = p.provider_id
    WHERE LOWER(p.provider_name) = 'openai' AND m.model_identifier = 'o1-mini'
);

-- O3 Mini High
INSERT INTO llm_models (
    provider_id, model_name, model_identifier,
    context_length, max_tokens_output, default_temperature,
    capabilities, is_active, is_default
)
SELECT
    (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'openai' AND is_active = TRUE LIMIT 1),
    'O3 Mini High',
    'o3-mini-high',
    128000,
    65536,
    0.7,
    '{"reasoning": true, "mathematics": true, "science": true, "logic": true, "problem_solving": true}',
    TRUE,
    FALSE
WHERE NOT EXISTS (
    SELECT 1 FROM llm_models m
    JOIN llm_providers p ON m.provider_id = p.provider_id
    WHERE LOWER(p.provider_name) = 'openai' AND m.model_identifier = 'o3-mini-high'
);

-- O4 Mini
INSERT INTO llm_models (
    provider_id, model_name, model_identifier,
    context_length, max_tokens_output, default_temperature,
    capabilities, is_active, is_default
)
SELECT
    (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'openai' AND is_active = TRUE LIMIT 1),
    'O4 Mini',
    'o4-mini',
    128000,
    65536,
    0.7,
    '{"reasoning": true, "mathematics": true, "science": true, "logic": true, "problem_solving": true}',
    TRUE,
    FALSE
WHERE NOT EXISTS (
    SELECT 1 FROM llm_models m
    JOIN llm_providers p ON m.provider_id = p.provider_id
    WHERE LOWER(p.provider_name) = 'openai' AND m.model_identifier = 'o4-mini'
);

-- O4 Mini High
INSERT INTO llm_models (
    provider_id, model_name, model_identifier,
    context_length, max_tokens_output, default_temperature,
    capabilities, is_active, is_default
)
SELECT
    (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'openai' AND is_active = TRUE LIMIT 1),
    'O4 Mini High',
    'o4-mini-high',
    128000,
    65536,
    0.7,
    '{"reasoning": true, "mathematics": true, "science": true, "logic": true, "problem_solving": true}',
    TRUE,
    FALSE
WHERE NOT EXISTS (
    SELECT 1 FROM llm_models m
    JOIN llm_providers p ON m.provider_id = p.provider_id
    WHERE LOWER(p.provider_name) = 'openai' AND m.model_identifier = 'o4-mini-high'
);

\echo 'Zobrazení aktuálního stavu...'
SELECT
    p.provider_name,
    COUNT(*) as model_count
FROM llm_models m
JOIN llm_providers p ON m.provider_id = p.provider_id
WHERE m.is_active = TRUE AND p.is_active = TRUE
GROUP BY p.provider_name
ORDER BY p.provider_name;
