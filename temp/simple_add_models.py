#!/usr/bin/env python3
"""
Jednoduchý skript pro přidání nových LLM modelů do databáze GENT.
"""

import os
import sys
import subprocess
from datetime import datetime

def run_sql_command(sql_command):
    """Spustí SQL příkaz pomocí sudo -u postgres psql."""
    try:
        cmd = [
            'sudo', '-u', 'postgres', 'psql', '-d', 'gentdb', '-c', sql_command
        ]
        
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            cwd='/opt/gent'
        )
        
        if result.returncode == 0:
            print(f"✓ SQL příkaz úspěšně proveden")
            if result.stdout.strip():
                print(f"  Výstup: {result.stdout.strip()}")
            return True
        else:
            print(f"✗ Chyba při provádění SQL příkazu:")
            print(f"  Chyba: {result.stderr.strip()}")
            return False
            
    except Exception as e:
        print(f"✗ Chyba při spouštění SQL příkazu: {e}")
        return False

def add_openai_models():
    """Přidá OpenAI modely."""
    print("Přidávání OpenAI modelů...")
    
    models = [
        ('GPT-4.1', 'gpt-4.1', 128000, 4096),
        ('GPT-4.1 Mini', 'gpt-4.1-mini', 128000, 4096),
        ('GPT-4.1 Nano', 'gpt-4.1-nano', 128000, 4096),
        ('GPT-4.5', 'gpt-4.5', 128000, 4096),
        ('GPT-4.5 Mini', 'gpt-4.5-mini', 128000, 4096),
        ('GPT-4o Latest', 'gpt-4o-latest', 128000, 4096),
        ('GPT-4o Mini Latest', 'gpt-4o-mini-latest', 128000, 4096),
        ('GPT-4o Audio Preview', 'gpt-4o-audio-preview', 128000, 4096),
        ('O1 Mini', 'o1-mini', 128000, 65536),
        ('O3', 'o3', 128000, 65536),
        ('O3 Mini', 'o3-mini', 128000, 65536),
        ('O3 Mini High', 'o3-mini-high', 128000, 65536),
        ('O4 Mini', 'o4-mini', 128000, 65536),
        ('O4 Mini High', 'o4-mini-high', 128000, 65536),
    ]
    
    success_count = 0
    
    for model_name, model_identifier, context_length, max_tokens in models:
        sql = f"""
        INSERT INTO llm_models (
            provider_id, model_name, model_identifier, context_length, 
            max_tokens_output, default_temperature, retry_attempts, 
            retry_delay, timeout, capabilities, is_default, is_active, 
            created_at, updated_at
        ) 
        SELECT 
            (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'openai' AND is_active = TRUE LIMIT 1),
            '{model_name}',
            '{model_identifier}',
            {context_length},
            {max_tokens},
            0.7,
            3,
            1000,
            30000,
            '{{"text": true, "chat": true}}',
            FALSE,
            TRUE,
            NOW(),
            NOW()
        WHERE NOT EXISTS (
            SELECT 1 FROM llm_models m 
            JOIN llm_providers p ON m.provider_id = p.provider_id 
            WHERE LOWER(p.provider_name) = 'openai' AND m.model_identifier = '{model_identifier}'
        );
        """
        
        print(f"  Přidávám model: {model_name} ({model_identifier})")
        if run_sql_command(sql):
            success_count += 1
    
    print(f"OpenAI: Úspěšně přidáno {success_count} z {len(models)} modelů")
    return success_count

def add_google_models():
    """Přidá Google modely."""
    print("Přidávání Google modelů...")
    
    models = [
        ('Gemini 2.5 Pro Preview', 'gemini-2.5-pro-preview-05-06', 1000000, 8192),
        ('Gemini 2.5 Flash Preview', 'gemini-2.5-flash-preview-05-20', 1000000, 8192),
        ('Gemini 2.0 Flash Lite', 'gemini-2.0-flash-lite', 1000000, 8192),
        ('Gemini 2.0 Flash', 'gemini-2.0-flash', 1000000, 8192),
    ]
    
    success_count = 0
    
    for model_name, model_identifier, context_length, max_tokens in models:
        sql = f"""
        INSERT INTO llm_models (
            provider_id, model_name, model_identifier, context_length, 
            max_tokens_output, default_temperature, retry_attempts, 
            retry_delay, timeout, capabilities, is_default, is_active, 
            created_at, updated_at
        ) 
        SELECT 
            (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'google' AND is_active = TRUE LIMIT 1),
            '{model_name}',
            '{model_identifier}',
            {context_length},
            {max_tokens},
            0.7,
            3,
            1000,
            30000,
            '{{"text": true, "chat": true, "vision": true}}',
            FALSE,
            TRUE,
            NOW(),
            NOW()
        WHERE NOT EXISTS (
            SELECT 1 FROM llm_models m 
            JOIN llm_providers p ON m.provider_id = p.provider_id 
            WHERE LOWER(p.provider_name) = 'google' AND m.model_identifier = '{model_identifier}'
        );
        """
        
        print(f"  Přidávám model: {model_name} ({model_identifier})")
        if run_sql_command(sql):
            success_count += 1
    
    print(f"Google: Úspěšně přidáno {success_count} z {len(models)} modelů")
    return success_count

def add_anthropic_models():
    """Přidá Anthropic modely."""
    print("Přidávání Anthropic modelů...")
    
    models = [
        ('Claude Sonnet 4 Latest', 'claude-sonnet-4-latest', 200000, 4096),
        ('Claude 3.7 Sonnet Latest', 'claude-3-7-sonnet-latest', 200000, 4096),
    ]
    
    success_count = 0
    
    for model_name, model_identifier, context_length, max_tokens in models:
        sql = f"""
        INSERT INTO llm_models (
            provider_id, model_name, model_identifier, context_length, 
            max_tokens_output, default_temperature, retry_attempts, 
            retry_delay, timeout, capabilities, is_default, is_active, 
            created_at, updated_at
        ) 
        SELECT 
            (SELECT provider_id FROM llm_providers WHERE LOWER(provider_name) = 'anthropic' AND is_active = TRUE LIMIT 1),
            '{model_name}',
            '{model_identifier}',
            {context_length},
            {max_tokens},
            0.7,
            3,
            1000,
            30000,
            '{{"text": true, "chat": true, "reasoning": true}}',
            FALSE,
            TRUE,
            NOW(),
            NOW()
        WHERE NOT EXISTS (
            SELECT 1 FROM llm_models m 
            JOIN llm_providers p ON m.provider_id = p.provider_id 
            WHERE LOWER(p.provider_name) = 'anthropic' AND m.model_identifier = '{model_identifier}'
        );
        """
        
        print(f"  Přidávám model: {model_name} ({model_identifier})")
        if run_sql_command(sql):
            success_count += 1
    
    print(f"Anthropic: Úspěšně přidáno {success_count} z {len(models)} modelů")
    return success_count

def show_current_state():
    """Zobrazí současný stav modelů v databázi."""
    print("Zobrazení současného stavu...")
    
    sql = """
    SELECT 
        p.provider_name, 
        COUNT(*) as model_count
    FROM llm_models m
    JOIN llm_providers p ON m.provider_id = p.provider_id
    WHERE m.is_active = TRUE AND p.is_active = TRUE
    GROUP BY p.provider_name
    ORDER BY p.provider_name;
    """
    
    run_sql_command(sql)

def main():
    """Hlavní funkce."""
    print("=== PŘIDÁVÁNÍ NOVÝCH LLM MODELŮ ===\n")
    
    # Zkontrolujeme, zda jsme v správném adresáři
    if not os.path.exists('/opt/gent'):
        print("✗ Adresář /opt/gent neexistuje")
        return False
    
    os.chdir('/opt/gent')
    
    total_added = 0
    
    # Přidáme modely pro každého poskytovatele
    total_added += add_openai_models()
    print()
    
    total_added += add_google_models()
    print()
    
    total_added += add_anthropic_models()
    print()
    
    # Zobrazíme současný stav
    show_current_state()
    
    print(f"\n=== DOKONČENO ===")
    print(f"Celkem přidáno {total_added} nových modelů")
    
    return total_added > 0

if __name__ == "__main__":
    main()
