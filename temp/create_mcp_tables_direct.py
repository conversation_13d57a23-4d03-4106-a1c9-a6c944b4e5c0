#!/usr/bin/env python3
"""
Vytvoření MCP tabulek pomocí MCP DB Service
"""

import sys
import os
import json

# Přidání GENT cesty do sys.path
sys.path.insert(0, '/opt/gent')

try:
    from gent.db.mcp_db_service import McpDirectDbService
    print("✅ MCP DB Service načten úspěšně")
except ImportError as e:
    print(f"❌ Chyba při importu MCP DB Service: {e}")
    sys.exit(1)

def create_mcp_tables():
    """Vytvoří MCP tabulky pomocí MCP DB Service."""

    print("🔧 MCP Management - Vytváření databázových tabulek")
    print("=" * 50)

    try:
        # Inicializace MCP DB Service
        mcp_service = McpDirectDbService()

        # Vytvoření tabulek
        print("🚀 Vytváření MCP tabulek...")
        mcp_service.ensure_mcp_tables()

        print("✅ MCP tabulky byly úspěšně vytvořeny!")
        return True

    except Exception as e:
        print(f"❌ Chyba při vytváření MCP tabulek: {e}")
        return False

def import_mcp_config_to_db():
    """Importuje existující MCP konfiguraci z JSON do databáze."""

    print("\n📥 Import existujících MCP serverů z konfigurace...")

    # Načtení existující konfigurace
    config_path = '/opt/gent/config/mcp_config.json'

    if not os.path.exists(config_path):
        print(f"⚠️ Konfigurační soubor {config_path} neexistuje, přeskakuji import")
        return True

    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)

        # Inicializace MCP DB Service
        mcp_service = McpDirectDbService()
        conn = mcp_service._get_connection()
        cursor = conn.cursor()

        # Mapování typů MCP serverů
        type_mapping = {
            'filesystem': 'file_system',
            'brave-search': 'web_search',
            'tavily': 'web_search',
            'fetch': 'web_fetch',
            'perplexity': 'ai_search',
            'sequentialthinking': 'reasoning',
            'git': 'version_control',
            'gent-project': 'project_management',
            'gent-workflow': 'workflow_management'
        }

        # Import poskytovatelů
        for server_name, server_config in config['servers'].items():
            provider_type = type_mapping.get(server_name, 'other')
            display_name = server_name.replace('-', ' ').title()

            # Vložení poskytovatele
            cursor.execute("""
                INSERT INTO mcp_providers (
                    provider_name, provider_type, display_name, description,
                    command, is_active, is_custom, auto_start
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                ON CONFLICT (provider_name) DO NOTHING
                RETURNING provider_id
            """, (
                server_name,
                provider_type,
                display_name,
                server_config.get("description", ""),
                server_config.get("command", ""),
                server_config.get("enabled", True),
                server_config.get("custom", False),
                True
            ))

            result = cursor.fetchone()
            if result:
                provider_id = result[0]
                print(f"✅ Importován provider: {server_name} (ID: {provider_id})")
            else:
                # Provider už existuje, získáme jeho ID
                cursor.execute("SELECT provider_id FROM mcp_providers WHERE provider_name = %s", (server_name,))
                result = cursor.fetchone()
                if result:
                    provider_id = result[0]
                    print(f"ℹ️ Provider {server_name} už existuje (ID: {provider_id})")
                else:
                    print(f"❌ Nepodařilo se najít/vytvořit provider {server_name}")
                    continue

            # Import nástrojů pro každého poskytovatele
            auto_approve_tools = server_config.get('auto_approve', [])
            for tool_name in auto_approve_tools:
                tool_identifier = f"{server_name}_{tool_name}"
                display_name_tool = tool_name.replace('_', ' ').title()

                cursor.execute("""
                    INSERT INTO mcp_tools (
                        provider_id, tool_name, tool_identifier, display_name,
                        auto_approve, is_active
                    ) VALUES (%s, %s, %s, %s, %s, %s)
                    ON CONFLICT (provider_id, tool_identifier) DO NOTHING
                """, (
                    provider_id,
                    tool_name,
                    tool_identifier,
                    display_name_tool,
                    True,
                    True
                ))

                print(f"  ✅ Importován nástroj: {tool_name}")

        conn.commit()
        print("✅ Import MCP konfigurace dokončen!")

        # Výpis statistik
        cursor.execute("SELECT COUNT(*) FROM mcp_providers")
        providers_count = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM mcp_tools")
        tools_count = cursor.fetchone()[0]

        print(f"📊 Importováno: {providers_count} poskytovatelů, {tools_count} nástrojů")

        return True

    except Exception as e:
        print(f"❌ Chyba při importu MCP konfigurace: {e}")
        if conn:
            conn.rollback()
        return False
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def test_mcp_service():
    """Otestuje MCP DB Service."""

    print("\n🧪 Test MCP DB Service...")

    try:
        # Inicializace MCP DB Service
        mcp_service = McpDirectDbService()

        # Test získání poskytovatelů
        providers = mcp_service.get_providers()
        print(f"✅ Načteno {len(providers)} MCP poskytovatelů")

        # Test detailu prvního poskytovatele
        if providers:
            provider_detail = mcp_service.get_provider_detail(providers[0]["provider_id"])
            if provider_detail:
                print(f"✅ Detail poskytovatele '{provider_detail['provider_name']}' s {len(provider_detail['tools'])} nástroji")
            else:
                print("❌ Nepodařilo se načíst detail poskytovatele")

        return True

    except Exception as e:
        print(f"❌ Chyba při testování MCP DB Service: {e}")
        return False

if __name__ == "__main__":
    print("🚀 MCP Management - Vytváření databázové struktury")
    print("=" * 60)

    # Vytvoření základních tabulek
    if not create_mcp_tables():
        print("❌ Chyba při vytváření základních tabulek!")
        sys.exit(1)

    # Import existujících dat
    if not import_mcp_config_to_db():
        print("❌ Chyba při importu existujících dat!")
        sys.exit(1)

    # Test služby
    if not test_mcp_service():
        print("❌ Chyba při testování MCP DB Service!")
        sys.exit(1)

    print("\n🎉 MCP Management databázová struktura byla úspěšně vytvořena!")
    print("📋 Vytvořené tabulky:")
    print("   - mcp_providers (poskytovatelé MCP serverů)")
    print("   - mcp_tools (nástroje dostupné v MCP serverech)")
    print("   - mcp_requests (log všech MCP requestů)")
    print("\n🔄 Další kroky:")
    print("   1. Vytvoření API endpointů pro MCP Management")
    print("   2. Vytvoření frontend stránek pro správu MCP")
    print("   3. Integrace s existujícím MCP systémem")
