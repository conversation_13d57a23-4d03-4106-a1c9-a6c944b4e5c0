#!/usr/bin/env python3
"""
Oprava Tavily API klíče v databázi
"""

import psycopg2
import json

def get_db_connection():
    """Získá připojení k databázi."""
    try:
        with open('/opt/gent/config/postgres_credentials.json', 'r') as f:
            creds = json.load(f)
        
        conn = psycopg2.connect(
            host='localhost',
            database='gentdb',
            user='postgres',
            password=creds['postgres_password']
        )
        return conn
    except Exception as e:
        print(f"❌ Chyba při připojování k databázi: {e}")
        return None

def fix_api_keys():
    """Opraví API klíče v databázi."""
    conn = get_db_connection()
    if not conn:
        return False
    
    cursor = conn.cursor()
    
    try:
        print("🔑 Oprava API klíčů...")
        
        # Správné API klíče
        api_keys = {
            'brave-search': 'BSARir7CGmpWKz5mvNgGJyYp3yV8CDn',
            'tavily': '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',
            'perplexity': '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'
        }
        
        # Aktualizace API klíčů
        for provider_name, api_key in api_keys.items():
            cursor.execute("""
                UPDATE mcp_providers 
                SET api_key = %s, auth_type = 'api_key'
                WHERE provider_name = %s
            """, (api_key, provider_name))
            
            if cursor.rowcount > 0:
                print(f"✅ Aktualizován API klíč pro {provider_name}")
            else:
                print(f"⚠️ Poskytovatel {provider_name} nebyl nalezen")
        
        conn.commit()
        
        # Kontrola výsledku
        cursor.execute("""
            SELECT provider_name, 
                   CASE WHEN api_key IS NOT NULL AND api_key != '' 
                        THEN CONCAT(LEFT(api_key, 8), '...') 
                        ELSE 'CHYBÍ' 
                   END as api_key_status
            FROM mcp_providers 
            ORDER BY provider_name
        """)
        
        providers = cursor.fetchall()
        print("\n📋 Aktuální stav API klíčů:")
        for provider in providers:
            name, status = provider
            print(f"  - {name}: {status}")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při aktualizaci API klíčů: {e}")
        conn.rollback()
        return False
    finally:
        cursor.close()
        conn.close()

if __name__ == "__main__":
    print("🔧 Oprava API klíčů v MCP databázi")
    print("=" * 40)
    
    if fix_api_keys():
        print("\n🎉 API klíče byly úspěšně opraveny!")
    else:
        print("\n❌ Chyba při opravě API klíčů!")
        exit(1)
