#!/usr/bin/env python3
"""
Skript pro přidání OpenAI o1-preview modelu do databáze.
"""

import sys
import os
import psycopg2
import json

def load_db_config():
    """Načte konfiguraci databáze ze souboru db.json."""
    config_path = os.path.join('/opt/gent/config', 'db.json')
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        return {
            "host": config.get("host", "localhost"),
            "port": config.get("port", 5432),
            "user": config.get("user"),
            "password": config.get("password"),
            "database": config.get("database")
        }
    except Exception as e:
        print(f"Chyba při načítání konfigurace: {str(e)}")
        return None

def add_o1_preview():
    """Přidá o1-preview model do databáze."""
    config = load_db_config()
    if not config:
        print("Nepodařilo se načíst konfiguraci databáze.")
        return False

    try:
        # Připojení k databázi
        print(f"Připojuji se k databázi {config['database']}...")
        conn = psycopg2.connect(
            host=config["host"],
            port=config["port"],
            user=config["user"],
            password=config["password"],
            dbname=config["database"]
        )
        
        cursor = conn.cursor()
        
        # Získání ID OpenAI poskytovatele
        print("📋 Získávání ID OpenAI poskytovatele...")
        cursor.execute("""
            SELECT provider_id 
            FROM llm_providers 
            WHERE LOWER(provider_name) = 'openai' 
            AND is_active = TRUE
        """)
        
        result = cursor.fetchone()
        if not result:
            print("❌ OpenAI poskytovatel nebyl nalezen!")
            return False
        
        openai_provider_id = result[0]
        print(f"  ✅ OpenAI: ID {openai_provider_id}")
        
        # Kontrola, zda model už neexistuje
        cursor.execute("""
            SELECT model_id FROM llm_models 
            WHERE provider_id = %s AND model_identifier = %s
        """, (openai_provider_id, 'o1-preview'))
        
        if cursor.fetchone():
            print("⚠️  Model o1-preview už existuje v databázi!")
            return False
        
        # Přidání o1-preview modelu
        print("\n➕ Přidávání o1-preview modelu...")
        
        model_data = {
            'model_name': 'o1-preview',
            'model_identifier': 'o1-preview',
            'context_length': 128000,
            'max_tokens': 32768,
            'capabilities': '{"reasoning": true, "mathematics": true, "science": true, "complex_problems": true, "advanced": true}'
        }
        
        cursor.execute("""
            INSERT INTO llm_models (
                provider_id, model_name, model_identifier, 
                context_length, max_tokens_output, default_temperature,
                capabilities, is_active, is_default
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            openai_provider_id, 
            model_data['model_name'], 
            model_data['model_identifier'],
            model_data['context_length'], 
            model_data['max_tokens'], 
            0.7,
            model_data['capabilities'], 
            True, 
            False
        ))
        
        print(f"  ✅ Přidán: {model_data['model_name']}")
        
        # Potvrzení změn
        conn.commit()
        print(f"\n🎉 Model o1-preview byl úspěšně přidán!")
        
        # Zobrazení aktuálního stavu OpenAI modelů
        print(f"\n📊 AKTUÁLNÍ OPENAI MODELY:")
        print("=" * 40)
        cursor.execute("""
            SELECT 
                m.model_name,
                m.model_identifier,
                m.is_default
            FROM llm_models m
            WHERE m.provider_id = %s AND m.is_active = TRUE
            ORDER BY m.model_name
        """, (openai_provider_id,))
        
        for model_name, model_identifier, is_default in cursor.fetchall():
            default = " [DEFAULT]" if is_default else ""
            print(f"  ✅ {model_name} ({model_identifier}){default}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_o1_preview():
    """Otestuje o1-preview model přes API."""
    import requests
    
    print("\n🧪 Testování o1-preview modelu...")
    
    try:
        # Test API volání
        url = "http://localhost:8001/api/chat/completions"
        data = {
            "model": "o1-preview",
            "messages": [
                {"role": "user", "content": "What is 2+2? Answer briefly."}
            ],
            "max_tokens": 100
        }
        
        print("  📡 Odesílám test request...")
        response = requests.post(url, json=data, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if 'choices' in result and len(result['choices']) > 0:
                answer = result['choices'][0]['message']['content']
                print(f"  ✅ Model funguje! Odpověď: {answer[:100]}...")
                return True
            else:
                print(f"  ❌ Neočekávaná struktura odpovědi: {result}")
                return False
        else:
            print(f"  ❌ API chyba: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"  ❌ Chyba při testování: {e}")
        return False

if __name__ == "__main__":
    print("➕ GENT - Přidání OpenAI o1-preview modelu")
    print("=" * 50)
    print("⚠️  Tento skript přidá o1-preview model do databáze")
    print("   a otestuje jeho funkčnost.")
    print()
    
    # Potvrzení od uživatele
    response = input("Pokračovat s přidáním o1-preview? (ano/ne): ").lower().strip()
    if response not in ['ano', 'a', 'yes', 'y']:
        print("❌ Operace zrušena uživatelem.")
        sys.exit(0)
    
    # Přidání do databáze
    success = add_o1_preview()
    
    if success:
        print("\n💡 Restartuji API server...")
        os.system("sudo systemctl restart gent-api")
        
        # Krátká pauza pro restart
        import time
        time.sleep(5)
        
        # Test funkčnosti
        test_success = test_o1_preview()
        
        if test_success:
            print("\n🎉 o1-preview model byl úspěšně přidán a funguje!")
            print("💡 Obnov frontend (F5) a zkontroluj dropdown menu")
        else:
            print("\n⚠️  Model byl přidán, ale test selhal")
            print("💡 Zkontroluj API klíče a konfiguraci")
    else:
        print("\n💥 Přidání modelu selhalo!")
        sys.exit(1)
