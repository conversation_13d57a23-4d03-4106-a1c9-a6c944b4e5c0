#!/usr/bin/env python3
"""
Volání API endpointu pro přidání modelů
"""

import requests
import json

def add_models():
    """Zavolá API endpoint pro přidání modelů"""
    try:
        print("🔄 Volám API endpoint pro přidání modelů...")
        
        response = requests.post(
            "http://localhost:8001/api/db/llm/add-missing-models",
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Úspěch!")
            print(f"📝 Zpráva: {result.get('message', 'N/A')}")
            print(f"📊 Přidáno modelů: {result.get('added_count', 0)}")
            print(f"🏢 Poskytovatelé: {result.get('providers', [])}")
            return True
        else:
            print(f"❌ API chyba: {response.status_code}")
            print(f"Odpověď: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Chyba při volání API: {e}")
        return False

def test_models():
    """Otestuje, kolik modelů máme nyní"""
    try:
        print("\n🧪 Testování počtu modelů...")
        
        response = requests.get("http://localhost:8001/api/db/llm/models")
        
        if response.status_code == 200:
            models = response.json()
            print(f"✅ Celkem modelů: {len(models)}")
            
            # Zobrazíme modely podle poskytovatelů
            providers = {}
            for model in models:
                provider = model['provider_name']
                if provider not in providers:
                    providers[provider] = []
                providers[provider].append(model['name'])
            
            print(f"\n📊 Modely podle poskytovatelů:")
            for provider, model_list in providers.items():
                print(f"\n🏢 {provider}: {len(model_list)} modelů")
                for model_name in model_list:
                    print(f"  - {model_name}")
            
            # Zkontrolujeme OpenAI modely
            openai_models = providers.get('OpenAI', [])
            required_openai = ['GPT-4o', 'GPT-4.5', 'GPT-4-1', 'GPT-4-1 Mini', 'GPT-4-1 Nano', 'O3', 'O3 Mini']
            missing_openai = [m for m in required_openai if m not in openai_models]
            
            if missing_openai:
                print(f"\n⚠️  Stále chybějící OpenAI modely: {missing_openai}")
            else:
                print(f"\n🎉 Všechny požadované OpenAI modely jsou k dispozici!")
            
            return True
            
        else:
            print(f"❌ API chyba: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Chyba při testování: {e}")
        return False

if __name__ == "__main__":
    print("🤖 GENT - Přidání chybějících modelů přes API")
    print("=" * 60)
    
    # Přidáme modely
    success = add_models()
    
    if success:
        # Otestujeme výsledek
        test_models()
        
        print(f"\n🎉 Hotovo!")
        print(f"💡 Obnov frontend (F5) a zkontroluj dropdown 'Vyberte model'")
        print(f"💡 Měl bys vidět všechny OpenAI a Google modely!")
    else:
        print(f"\n💥 Přidání modelů selhalo!")
