#!/usr/bin/env python
"""
Test skript pro ověření LiteLLM integrace s GENT v10.

Tento skript testuje volání různých poskytovatelů LLM přes LiteLLM.
"""

import os
import sys
import asyncio
import argparse
from dotenv import load_dotenv

# P<PERSON>id<PERSON><PERSON> rootovského adresáře do cesty pro import
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

# Import naší LiteLLM služby
from micro_services.litellm.service import LiteLLMService

# Nastavení argumentů příkazové řádky
parser = argparse.ArgumentParser(description="Test LiteLLM integrace s GENT v10")
parser.add_argument("--provider", type=str, default="openai", help="Poskytovatel LLM (výchozí: openai)")
parser.add_argument("--model", type=str, default="gpt-3.5-turbo", help="Model LLM (výchozí: gpt-3.5-turbo)")
parser.add_argument("--prompt", type=str, default="Ahoj, jak se máš?", help="Testovací prompt")
args = parser.parse_args()

# Načtení proměnných prostředí (pro případ lokálního testování)
load_dotenv()

async def test_litellm():
    """Test LiteLLM integrace."""
    print(f"Testování LiteLLM s poskytovatelem {args.provider} a modelem {args.model}")
    
    try:
        # Inicializace služby
        service = LiteLLMService()
        
        # Otestování načítání poskytovatelů z DB
        print("Načítání poskytovatelů z DB...")
        success = service.refresh_providers()
        if success:
            print("Poskytovatelé úspěšně načteni z DB.")
            print(f"Dostupní poskytovatelé: {list(service.providers_cache.keys())}")
        else:
            print("CHYBA: Nepodařilo se načíst poskytovatele z DB.")
            return
        
        # Otestování volání LLM
        print(f"\nVolání LLM API ({args.provider}/{args.model})...")
        print(f"Prompt: '{args.prompt}'")
        
        response = await service.call_llm(
            provider=args.provider,
            model=args.model,
            prompt=args.prompt,
            system_prompt="Jsi užitečný a přátelský asistent."
        )
        
        # Výpis odpovědi
        print("\n--- Odpověď ---")
        if "error" in response and response["error"]:
            print(f"CHYBA: {response['error_message']}")
        else:
            print(f"Text: {response['text']}")
            print(f"Tokens: {response['tokens_used']}")
            print(f"Finish reason: {response['finish_reason']}")
        
        print("\nTest dokončen.")
    
    except Exception as e:
        print(f"CHYBA při testu: {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_litellm())
