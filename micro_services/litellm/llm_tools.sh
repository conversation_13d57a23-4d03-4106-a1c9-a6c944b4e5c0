#!/bin/bash
# Skript pro snadné spouštěn<PERSON> n<PERSON>ů liteLLM integrace

# Barvy pro výstup
BLUE='\033[0;34m'
GREEN='\033[0;32m'
YELLOW='\033[0;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Aktivace virtuálního prostředí
source /opt/gent/venv/bin/activate

# Funkce pro zobrazení nápovědy
show_help() {
    echo -e "${BLUE}GENT v10 LiteLLM Nástroje${NC}"
    echo
    echo -e "${YELLOW}Použití:${NC}"
    echo -e "  $0 ${GREEN}[příkaz]${NC} ${YELLOW}[argumenty]${NC}"
    echo
    echo -e "${YELLOW}Příkazy:${NC}"
    echo -e "  ${GREEN}test${NC}       Test připojení k LLM API s konkrétním poskytovatelem a modelem"
    echo -e "  ${GREEN}chat${NC}       Interaktivní chat s LLM modelem"
    echo -e "  ${GREEN}help${NC}       Zobrazí tuto nápovědu"
    echo
    echo -e "${YELLOW}Příklady:${NC}"
    echo -e "  $0 ${GREEN}test${NC} --provider openai --model gpt-4"
    echo -e "  $0 ${GREEN}test${NC} --provider openrouter --model mistral-7b-instruct --prompt \"Co je umělá inteligence?\""
    echo -e "  $0 ${GREEN}chat${NC} --provider anthropic --model claude-3-sonnet-20240229"
    echo -e "  $0 ${GREEN}chat${NC} --provider openai --model gpt-4o --system \"Jsi český asistent.\""
    echo
}

# Cesta k adresáři s LiteLLM nástroji
SCRIPT_DIR="/opt/gent/micro_services/litellm"

# Kontrola, zda byl zadán příkaz
if [[ $# -eq 0 ]]; then
    show_help
    exit 1
fi

# Parsování příkazu
COMMAND="$1"
shift

# Spuštění odpovídajícího skriptu podle příkazu
case "$COMMAND" in
    test)
        echo -e "${BLUE}Spouštím test LiteLLM...${NC}"
        python "$SCRIPT_DIR/test_litellm.py" "$@"
        ;;
    chat)
        echo -e "${BLUE}Spouštím LiteLLM chat...${NC}"
        python "$SCRIPT_DIR/llm_chat.py" "$@"
        ;;
    help)
        show_help
        ;;
    *)
        echo -e "${RED}Neznámý příkaz: $COMMAND${NC}"
        show_help
        exit 1
        ;;
esac
