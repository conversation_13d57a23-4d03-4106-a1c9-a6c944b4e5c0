#!/usr/bin/env python
"""
Jednoduchý chat klient pro komunikaci s LLM přes LiteLLM integraci.

Tento skript vytváří jednoduchý interaktivní chat v příkazovém řádku
pro testování komunikace s různými LLM poskytovateli.
"""

import os
import sys
import asyncio
import argparse
from typing import List, Dict, Any
from dotenv import load_dotenv

# P<PERSON><PERSON><PERSON><PERSON> rootovského adresáře do cesty pro import
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

# Import naší LiteLLM služby
from micro_services.litellm.service import LiteLLMService

# Barvy pro výstup
BLUE = '\033[94m'
GREEN = '\033[92m'
YELLOW = '\033[93m'
RED = '\033[91m'
RESET = '\033[0m'

# Nastavení argumentů příkazové řádky
parser = argparse.ArgumentParser(description="Jednoduchý chat klient pro LiteLLM")
parser.add_argument("--provider", type=str, default="openai", help="Poskytovatel LLM (výchozí: openai)")
parser.add_argument("--model", type=str, default="gpt-3.5-turbo", help="Model LLM (výchozí: gpt-3.5-turbo)")
parser.add_argument("--system", type=str, default="Jsi užitečný a přátelský asistent.", help="Systémový prompt")
args = parser.parse_args()

# Načtení proměnných prostředí (pro případ lokálního testování)
load_dotenv()

class ChatSession:
    """Třída pro správu chatovací relace s LLM."""
    
    def __init__(self, provider: str, model: str, system_prompt: str):
        """
        Inicializace chatovací relace.
        
        Args:
            provider: Název poskytovatele LLM
            model: Název modelu
            system_prompt: Systémový prompt pro nastavení chování LLM
        """
        self.provider = provider
        self.model = model
        self.system_prompt = system_prompt
        self.messages = []  # Historie zpráv
        self.service = LiteLLMService()
        
    async def initialize(self) -> bool:
        """
        Inicializace služby a kontrola dostupnosti poskytovatele.
        
        Returns:
            bool: True pokud je poskytovatel dostupný, jinak False
        """
        # Načtení poskytovatelů z DB
        success = self.service.refresh_providers()
        if not success:
            print(f"{RED}CHYBA: Nepodařilo se načíst poskytovatele z DB.{RESET}")
            return False
        
        # Kontrola, zda je požadovaný poskytovatel dostupný
        provider_info = self.service.get_provider_info(self.provider)
        if not provider_info:
            print(f"{RED}CHYBA: Poskytovatel '{self.provider}' není dostupný.{RESET}")
            print(f"{YELLOW}Dostupní poskytovatelé: {list(self.service.providers_cache.keys())}{RESET}")
            return False
        
        return True
    
    async def send_message(self, content: str) -> Dict[str, Any]:
        """
        Odeslání zprávy do LLM a získání odpovědi.
        
        Args:
            content: Text zprávy od uživatele
            
        Returns:
            Dict[str, Any]: Odpověď od LLM
        """
        # Přidání zprávy uživatele do historie
        self.messages.append({"role": "user", "content": content})
        
        # Získání odpovědi od LLM
        response = await self.service.call_llm(
            provider=self.provider,
            model=self.model,
            prompt=content,
            system_prompt=self.system_prompt,
            messages=self.messages  # Předáme celou historii pro zachování kontextu
        )
        
        # Přidání odpovědi do historie, pokud nedošlo k chybě
        if "error" not in response or not response["error"]:
            self.messages.append({"role": "assistant", "content": response["text"]})
        
        return response

async def main():
    """Hlavní funkce pro spuštění chatu."""
    print(f"{BLUE}=== GENT v10 LiteLLM Chat ==={RESET}")
    print(f"{YELLOW}Poskytovatel: {args.provider}{RESET}")
    print(f"{YELLOW}Model: {args.model}{RESET}")
    print(f"{YELLOW}Systémový prompt: '{args.system}'{RESET}")
    print(f"{BLUE}Pro ukončení chatu napiš 'konec', 'exit' nebo 'quit'{RESET}")
    print("---")
    
    # Vytvoření a inicializace chatovací relace
    chat = ChatSession(args.provider, args.model, args.system)
    if not await chat.initialize():
        return
    
    print(f"{GREEN}Chat je připraven. Můžeš začít psát zprávy.{RESET}")
    
    # Hlavní chatovací smyčka
    while True:
        # Získání vstupu od uživatele
        user_input = input(f"{BLUE}Ty:{RESET} ")
        
        # Kontrola ukončení
        if user_input.lower() in ["konec", "exit", "quit"]:
            print(f"{YELLOW}Ukončuji chat...{RESET}")
            break
        
        if not user_input.strip():
            continue
        
        try:
            # Odeslání zprávy a získání odpovědi
            print(f"{YELLOW}Čekám na odpověď...{RESET}")
            response = await chat.send_message(user_input)
            
            # Zobrazení odpovědi
            if "error" in response and response["error"]:
                print(f"{RED}CHYBA: {response['error_message']}{RESET}")
            else:
                print(f"{GREEN}Asistent:{RESET} {response['text']}")
                print(f"{YELLOW}Tokens: {response['tokens_used']}{RESET}")
        
        except Exception as e:
            print(f"{RED}CHYBA: {str(e)}{RESET}")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print(f"\n{YELLOW}Chat ukončen uživatelem.{RESET}")
