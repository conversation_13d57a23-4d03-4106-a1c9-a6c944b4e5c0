# LiteLLM Mikroslužba pro GENT v10

Tato mikroslužba poskytuje jednotné rozhraní pro volání různých LLM API poskytovatelů (OpenAI, Anthropic, OpenRouter, Cohere, atd.) s využitím knihovny LiteLLM.

## Účel

Hlavním účelem této mikroslužby je:

1. **Jednotné rozhraní pro všechny poskytovatele LLM** - stejn<PERSON> kód může volat API OpenAI, Anthropic, OpenRouter, atd.
2. **Podpora pro všechny poskytovatele v databázi** - není omezeno na hard-coded seznam poskytovatelů
3. **Řešení problémů se specifickými parametry API** - např. problém s hlavičkami pro OpenRouter
4. **Zachování struktury systému** - data v DB, kód v aplikaci, UI oddělené

## Architektura

Mikroslužba se skládá z těchto komponent:

- **`service.py`** - <PERSON>lavn<PERSON> slu<PERSON>, která poskytuje rozhraní pro volání LLM API
- **`test_litellm.py`** - Skript pro testování připojení k LLM API
- **`llm_chat.py`** - Interaktivní chat klient pro komunikaci s LLM
- **`llm_tools.sh`** - Shellový skript pro snadné spouštění nástrojů

## Integrace s databází

Služba používá třídu `LlmDirectDbService` pro načítání informací o poskytovatelích z databáze. To zahrnuje:

- API klíče
- Základní URL
- Dostupné modely
- Další parametry specifické pro daného poskytovatele

## Použití nástrojů

### Test připojení k LLM API

```bash
./llm_tools.sh test --provider openai --model gpt-4o
./llm_tools.sh test --provider openrouter --model mistral-7b --prompt "Co je umělá inteligence?"
```

### Interaktivní chat

```bash
./llm_tools.sh chat --provider openai --model gpt-4o
./llm_tools.sh chat --provider anthropic --model claude-3-sonnet-20240229 --system "Jsi užitečný a přátelský asistent, který mluví česky."
```

### Zobrazení nápovědy

```bash
./llm_tools.sh help
```

## Programové použití

```python
from micro_services.litellm.service import LiteLLMService

# Inicializace služby
service = LiteLLMService()

# Načtení poskytovatelů z DB
service.refresh_providers()

# Volání LLM API
response = await service.call_llm(
    provider="openai",
    model="gpt-4o",
    prompt="Ahoj, jak se máš?",
    system_prompt="Jsi užitečný a přátelský asistent."
)

# Zpracování odpovědi
if "error" in response and response["error"]:
    print(f"CHYBA: {response['error_message']}")
else:
    print(f"Odpověď: {response['text']}")
    print(f"Použité tokeny: {response['tokens_used']}")
```

## Řešení problémů

### Chybí poskytovatel v databázi

Pokud chybí poskytovatel v databázi, přidejte ho následujícím způsobem:

1. Otevřete administraci DB
2. Přidejte nového poskytovatele s následujícími údaji:
   - Název poskytovatele (např. "openrouter")
   - API klíč
   - Základní URL (např. "https://openrouter.ai/api/v1")
   - Případné další parametry

### Správný formát modelů v databázi

Pro správné fungování LiteLLM musí být názvy modelů v databázi ve správném formátu:

1. **Modely bez prefixu** - název modelu bez prefixu poskytovatele, např.:
   - `gpt-4o` (pro OpenAI)
   - `claude-3-sonnet-20240229` (pro Anthropic)
   - `mistral-7b-instruct` (pro OpenRouter)
   - `gemini-1.5-pro` (pro Gemini/Google)

2. **Modely s prefixem** - některé modely již mohou obsahovat prefix:
   - `gemini/gemini-1.5-pro` (pro Gemini/Google)
   - `anthropic/claude-3-opus-20240229` (pro Anthropic)

Služba automaticky detekuje modely s prefixem a neupravuje je.

### Speciální případy pro Google/Gemini modely

Pro Google/Gemini modely používáme speciální logiku, abychom zajistili, že LiteLLM použije správné API:

1. Pro poskytovatele `gemini` nebo `google` explicitně určujeme, že se má použít Google AI Studio API, nikoliv VertexAI
2. Pro model `gemini-2.5-pro-exp-03-25` provádíme automatickou korekci na `gemini/gemini-pro-latest`
3. Vždy přidáváme `api_type: "google"` pro zajištění správného routování API požadavku

Pokud přidáváte nový Gemini model, doporučujeme následující formáty:
- Pro stabilní modely: `gemini-1.5-pro`
- Pro experimentální modely: použijte raději stabilní alternativu, např. `gemini-pro-latest`

### Chyba při volání API

Pokud dojde k chybě při volání API, zkontrolujte:

1. Zda je správný API klíč v databázi
2. Zda je správná základní URL
3. Zda je podporován zadaný model a je ve správném formátu (viz výše)
4. Logy pro podrobnější informace o chybě

## Podporovaní poskytovatelé

LiteLLM podporuje více než 100 různých poskytovatelů LLM. Nejpoužívanější jsou:

- OpenAI (GPT-3.5, GPT-4, GPT-4o)
- Anthropic (Claude 2, Claude 3, Claude Instant)
- OpenRouter (poskytuje přístup k mnoha modelům)
- Google (Gemini, Palm)
- Cohere
- Mistral AI
- VertexAI
- Bedrock (Amazon)
- Azure OpenAI

Pro úplný seznam podporovaných poskytovatelů viz [dokumentace LiteLLM](https://docs.litellm.ai/docs/providers).

## Výhody použití LiteLLM

- **Jednotné rozhraní** - zjednodušení kódu, méně podmínek a special cases
- **Konsistentní formát odpovědí** - všechny odpovědi mají stejnou strukturu
- **Snadné přidávání nových poskytovatelů** - stačí přidat do DB, není potřeba upravovat kód
- **Řešení problémů se specifickými hlavičkami** - LiteLLM se stará o správné HTTP hlavičky
- **Podpora pro různé funkce** - streaming, asynchronní volání, embeddingy, atd.
