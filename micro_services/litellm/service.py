"""
GENT v10 - LiteLLM služba

Tato služba poskytuje jednotné rozhraní pro volání různých LLM API pomocí knihovny LiteLLM.
Všechna data o poskytovatelích (API klíče, modely) jsou načítána z databáze.
"""

import os
import json
import requests
from datetime import datetime
import logging
from typing import Dict, List, Any, Optional, Union

# Import knihovny LiteLLM
import litellm
from litellm import completion

# Import databázové služby pro načítání konfigurací
from gent.db.llm_db_service import LlmDirectDbService

# Nastavení loggeru
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("litellm_service")


class LiteLLMService:
    """
    Služba pro jednotný přístup k různým LLM API pomocí knihovny LiteLLM.
    
    Tato služba načítá konfigurace z databáze a poskytuje jednotné rozhraní
    pro volání různých LLM API (OpenAI, Anthropic, OpenRouter, atd.).
    """
    
    def __init__(self):
        """Inicializace služby LiteLLM."""
        self.db_service = LlmDirectDbService()
        self.providers_cache = {}
        self.cache_valid = False
        
        # Konfigurace LiteLLM
        litellm.drop_params = True  # Odstranit nepodporované parametry
        litellm.set_verbose = False  # Vypnout verbose
    
    def refresh_providers(self):
        """Načte poskytovatele z databáze a aktualizuje cache."""
        try:
            providers = self.db_service.get_providers()
            self.providers_cache = {p["name"].lower(): p for p in providers}
            self.cache_valid = True
            return True
        except Exception as e:
            logger.error(f"Chyba při načítání poskytovatelů z DB: {str(e)}")
            self.cache_valid = False
            return False
    
    def get_provider_info(self, provider_name: str) -> Optional[Dict[str, Any]]:
        """
        Získá informace o poskytovateli z cache nebo z databáze.
        
        Args:
            provider_name: Název poskytovatele
            
        Returns:
            Dict[str, Any] nebo None: Informace o poskytovateli nebo None, pokud neexistuje
        """
        if not self.cache_valid:
            self.refresh_providers()
        
        provider_name = provider_name.lower()
        return self.providers_cache.get(provider_name)
    
    def format_model_name(self, provider: str, model: str) -> str:
        """
        Formátuje název modelu pro použití s LiteLLM.
        
        Args:
            provider: Název poskytovatele
            model: Název modelu
            
        Returns:
            str: Formátovaný název modelu
        """
        provider = provider.lower()

        # Pokud model již obsahuje standardizovaný prefix, vrátíme ho přímo
        known_prefixes = [
            "openai/", "anthropic/", "openrouter/", "azure/", "huggingface/", 
            "cohere/", "gemini/", "claude/", "mistral/", "ollama/", "palm/",
            "bedrock/", "groq/", "deepinfra/"
        ]
        
        for prefix in known_prefixes:
            if model.startswith(prefix):
                logger.info(f"Model '{model}' již obsahuje standardizovaný prefix '{prefix}', vracím beze změny")
                return model
        
        # Odstraníme prefix poskytovatele, pokud existuje
        if "/" in model and model.startswith(f"{provider}/"):
            model = model.split("/", 1)[1]
            
        # Speciální případy pro poskytovatele
        if provider == "openai":
            return model  # Pro OpenAI není prefix
        elif provider == "anthropic":
            return f"anthropic/{model}"
        elif provider == "openrouter":
            return f"openrouter/{model}"
        elif provider == "azure":
            return f"azure/{model}"
        elif provider == "huggingface":
            return f"huggingface/{model}"
        elif provider == "cohere":
            return f"cohere/{model}"
        elif provider == "google":
            # Pro Google používáme prefix "gemini/"
            return f"gemini/{model}"
        else:
            # Pro ostatní použijeme prefix poskytovatele
            return f"{provider}/{model}"
    
    async def call_llm(self, provider: str, model: str, prompt: str, 
                    system_prompt: str = "Jsi užitečný a přátelský asistent.",
                    temperature: float = 0.7,
                    max_tokens: int = 1000,
                    **kwargs) -> Dict[str, Any]:
        """
        Volá LLM API pomocí LiteLLM.
        
        Args:
            provider: Název poskytovatele
            model: Název modelu
            prompt: Dotaz uživatele
            system_prompt: Systémový prompt
            temperature: Teplota generování (0.0 - 2.0)
            max_tokens: Maximální počet tokenů
            **kwargs: Další parametry pro volání API
            
        Returns:
            Dict[str, Any]: Odpověď od LLM API
        """
        try:
            # Získáme informace o poskytovateli
            provider_info = self.get_provider_info(provider)
            if not provider_info:
                return {
                    "text": f"Poskytovatel {provider} nenalezen v databázi.",
                    "model": model,
                    "provider": provider,
                    "tokens_used": 0,
                    "finish_reason": "error",
                    "error": True,
                    "error_message": "Poskytovatel nenalezen"
                }
            
            # Získáme API klíč
            api_key = provider_info.get("api_key")
            if not api_key:
                return {
                    "text": f"Chybí API klíč pro poskytovatele {provider}.",
                    "model": model,
                    "provider": provider,
                    "tokens_used": 0,
                    "finish_reason": "error",
                    "error": True,
                    "error_message": "Chybí API klíč"
                }
            
            # Získáme základní URL, pokud existuje
            base_url = provider_info.get("base_url")
            
            # Formátujeme název modelu
            model_name = self.format_model_name(provider, model)
            
            # Připravíme dodatečné parametry pro poskytovatele
            extra_params = {}
            
            # Speciální nastavení pro OpenRouter
            if provider.lower() == "openrouter":
                # OpenRouter vyžaduje HTTP referer a další hlavičky
                extra_params["extra_headers"] = {
                    "HTTP-Referer": "http://localhost:8001",
                    "X-Title": "GENT API TEST"
                }
            
            # Příprava obecného API volání
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": prompt}
            ]
            
            # Kombinujeme všechny parametry do api_params
            api_params = {
                "model": model_name,
                "messages": messages,
                "temperature": temperature,
                "max_tokens": max_tokens,
                "api_key": api_key,
                **extra_params,
                **kwargs
            }
            
                # Speciální nastavení pro Gemini API
            if provider.lower() in ["gemini", "google"]:
                # Explicitně určíme, že používáme Google Gemini API, nikoliv VertexAI
                logger.info(f"Používám Gemini API pro model {model}")
                
                # Přímé volání Google API bez LiteLLM (kvůli problémům s detekcí správného API)

                # Připravíme formát zprávy pro Google AI Studio API
                messages_content = []
                
                # Systémový prompt přidáme jako první zprávu od uživatele
                if system_prompt:
                    messages_content.append({
                        "role": "user",
                        "parts": [{"text": system_prompt}]
                    })
                    # Přidáme modelovou odpověď na systémový prompt
                    messages_content.append({
                        "role": "model",
                        "parts": [{"text": "Rozumím, budu postupovat podle těchto instrukcí."}]
                    })
                
                # Přidáme aktuální dotaz uživatele
                messages_content.append({
                    "role": "user",
                    "parts": [{"text": prompt}]
                })
                
                # Připravíme payload pro API
                payload = {
                    "contents": messages_content,
                    "generationConfig": {
                        "temperature": temperature,
                        "maxOutputTokens": max_tokens,
                        "topP": 0.8,
                        "topK": 40
                    }
                }
                
                # Připravíme URL - opravená verze pro Google AI Studio API
                # Používáme stabilní model gemini-1.5-pro jako základní
                model_endpoint = "gemini-1.5-pro"
                
                # Mapování modelů na správné endpointy - oprava problému 404
                model_mapping = {
                    "gemini-pro": "gemini-pro",
                    "gemini-pro-vision": "gemini-pro-vision",
                    "gemini-1.0-pro": "gemini-pro",
                    "gemini-1.0-pro-vision": "gemini-pro-vision",
                    "gemini-1.5-pro": "gemini-1.5-pro",
                    "gemini-1.5-flash": "gemini-1.5-flash",
                    "gemini-1.5-pro-vision": "gemini-1.5-pro-vision",
                    # Přidána podpora pro experimentální modely 2.5
                    "gemini-2.5-flash-preview-04-17": "gemini-1.5-flash", # Fallback na stabilní model
                    "gemini-2.5-pro-preview-04-17": "gemini-1.5-pro",     # Fallback na stabilní model
                    "gemini-2.5-flash-preview": "gemini-1.5-flash",       # Fallback na stabilní model
                    "gemini-2.5-pro-preview": "gemini-1.5-pro"            # Fallback na stabilní model
                }
                
                # Získáme čistý název modelu (bez prefixu)
                clean_model_name = model.split("/")[-1] if "/" in model else model
                
                # Pokud je model v mapování, použijeme jeho mapovaný endpoint
                if clean_model_name in model_mapping:
                    model_endpoint = model_mapping[clean_model_name]
                    logger.info(f"Použití mapovaného modelu: {clean_model_name} -> {model_endpoint}")
                elif "gemini-" in clean_model_name:
                    # Pokud je to jiný Gemini model, zkusíme použít jeho název
                    model_endpoint = clean_model_name
                    logger.info(f"Použití vlastního modelu: {model_endpoint}")
                
                # Vždy použijeme standartní endpoint Gemini API, který je stabilní
                # Ignorujeme base_url z databáze, protože může obsahovat nesprávnou hodnotu
                api_base_url = "https://generativelanguage.googleapis.com/v1"
                logger.info(f"Použití pevně definovaného base URL pro Google API: {api_base_url}")
                
                # Výpis původní base_url z databáze pro diagnostické účely
                if base_url and base_url.strip():
                    logger.info(f"Ignoruji base_url z databáze: {base_url}")
                
                # Sestavíme URL s API base URL
                api_url = f"{api_base_url}/models/{model_endpoint}:generateContent"
                
                # Přidáme API klíč jako query parametr
                api_url += f"?key={api_key}"
                
                # Detailní logování pro debugging
                masked_api_key = api_key[:4] + "****" + api_key[-4:] if api_key and len(api_key) > 8 else "*****"
                logger.info(f"Volám přímo Google AI Studio API na: {api_url.replace(api_key, masked_api_key)}")
                logger.info(f"Použitý model: {model_endpoint}")
                logger.info(f"Payload pro Gemini API: {json.dumps(payload)}")
                
                try:
                    # Provedeme HTTP požadavek na Google API
                    response = requests.post(
                        api_url,
                        headers={"Content-Type": "application/json"},
                        data=json.dumps(payload),
                        timeout=60
                    )
                    
                    # Zpracujeme odpověď
                    if response.status_code == 200:
                        response_data = response.json()
                        
                        # Extrahujeme text odpovědi
                        try:
                            text = response_data["candidates"][0]["content"]["parts"][0]["text"]
                            
                            # Vrátíme zpracovanou odpověď ve formátu kompatibilním s LiteLLM
                            return {
                                "text": text,
                                "model": model,
                                "provider": provider,
                                "tokens_used": 0,  # Google API nevrací počet tokenů
                                "finish_reason": "stop"
                            }
                        except (KeyError, IndexError) as e:
                            logger.error(f"Chyba při zpracování odpovědi z Google API: {str(e)}")
                            logger.error(f"Obsah odpovědi: {json.dumps(response_data)}")
                            return {
                                "text": f"Chyba při zpracování odpovědi z Google API: {str(e)}",
                                "model": model,
                                "provider": provider,
                                "tokens_used": 0,
                                "finish_reason": "error",
                                "error": True,
                                "error_message": f"Chyba při zpracování odpovědi: {str(e)}"
                            }
                    else:
                        error_detail = response.text
                        logger.error(f"Chyba při volání Google API: HTTP {response.status_code} - {error_detail}")
                        return {
                            "text": f"Chyba při volání Google API: HTTP {response.status_code} - {error_detail}",
                            "model": model,
                            "provider": provider,
                            "tokens_used": 0,
                            "finish_reason": "error",
                            "error": True,
                            "error_message": f"HTTP {response.status_code}: {error_detail}"
                        }
                except Exception as e:
                    logger.error(f"Výjimka při volání Google API: {str(e)}")
                    return {
                        "text": f"Chyba při volání Google API: {str(e)}",
                        "model": model,
                        "provider": provider,
                        "tokens_used": 0,
                        "finish_reason": "error",
                        "error": True,
                        "error_message": str(e)
                    }
            
            # Přidáme base_url, pokud existuje
            if base_url:
                api_params["api_base"] = base_url
            
            # Tracelog volání API pro debugging
            logger.info(f"Volání LiteLLM pro {provider}/{model} s parametry: {json.dumps({k: v for k, v in api_params.items() if k != 'api_key'})}")
            
            # Voláme LiteLLM
            response = completion(**api_params)
            
            # Vrátíme zpracovanou odpověď
            return {
                "text": response.choices[0].message.content,
                "model": model,
                "provider": provider,
                "tokens_used": response.usage.total_tokens if hasattr(response, 'usage') and hasattr(response.usage, 'total_tokens') else 0,
                "finish_reason": response.choices[0].finish_reason if hasattr(response.choices[0], 'finish_reason') else "stop"
            }
            
        except Exception as e:
            logger.error(f"Chyba při volání LLM API pomocí LiteLLM: {str(e)}")
            return {
                "text": f"Chyba při volání LLM API: {str(e)}",
                "model": model,
                "provider": provider,
                "tokens_used": 0,
                "finish_reason": "error",
                "error": True,
                "error_message": str(e)
            }
    
    def test_connection(self, provider: str) -> Dict[str, Any]:
        """
        Otestuje připojení k API poskytovatele.
        
        Args:
            provider: Název poskytovatele
            
        Returns:
            Dict[str, Any]: Výsledek testu
        """
        try:
            provider_info = self.get_provider_info(provider)
            if not provider_info:
                return {
                    "success": False,
                    "message": f"Poskytovatel {provider} nenalezen v databázi."
                }
            
            api_key = provider_info.get("api_key")
            if not api_key:
                return {
                    "success": False,
                    "message": f"Chybí API klíč pro poskytovatele {provider}."
                }
            
            # Zde by byl skutečný test připojení k API
            # Pro jednoduchost pouze vrátíme úspěch, pokud máme API klíč
            
            return {
                "success": True,
                "message": f"Připojení k API poskytovatele {provider} je funkční."
            }
            
        except Exception as e:
            logger.error(f"Chyba při testování připojení k {provider}: {str(e)}")
            return {
                "success": False,
                "message": f"Chyba při testování připojení: {str(e)}"
            }
