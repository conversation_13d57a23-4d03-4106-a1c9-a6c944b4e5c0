# 📋 GENT TO-DO Seznam

## 🚨 PRIORITA #1 - Oprava LLM Management

### 🐛 Kritické problémy k opravě:

#### 1. **Editace modelu nefunguje**
- ❌ **Problem:** Editace názvu modelu selhává s chybou "Nepodařilo se aktualizovat model s ID 211"
- ❌ **Problem:** Model ID 211 neexistuje nebo není správně mapován
- 🔧 **Řešení:** Identifikovat správné mapování `model.model_id` vs `model.id`
- 🔧 **Řešení:** Opravit API endpoint `/models/{model_id}` nebo mapování dat
- 📍 **Status:** Debug informace přidány, čeká se na testování

#### 2. **Nastavení výchozího modelu nefunguje**
- ❌ **Problem:** Kliknutí na ⭐ u modelu "qwen3-32b" nezmění výchozí model
- ❌ **Problem:** Zůstává výchozí "deephermes-3-mistral-24b-preview"
- 🔧 **Řešení:** API endpoint `/providers/{provider_id}/models/{model_name}/set-default` je opravený
- 🔧 **Řešení:** Frontend má debug informace pro identifikaci problému
- 📍 **Status:** Čeká se na debug logy z frontendu

### 🔍 **Potřebné kroky:**
1. ✅ Přidat debug informace do frontendu
2. 🔄 Získat debug logy z prohlížeče
3. 🔄 Identifikovat správné mapování dat
4. 🔄 Opravit API endpointy nebo mapování
5. 🔄 Otestovat funkcionalitu

---

## 🚀 PRIORITA #2 - MCP Server Management

### 📋 **Nová funkcionalita - MCP Server Management**

#### **Koncept:**
- 🎯 **Podobné jako LLM Management** - stejná struktura a design
- 🎯 **Admin sekce** - `/admin/mcp` endpoint
- 🎯 **Více karet/tabů** - podobně jako u LLM Management
- 🎯 **Kompletní CRUD** - vytváření, čtení, úpravy, mazání MCP serverů
- 🎯 **Databázové uložení** - všechny informace o MCP serverech v DB
- 🎯 **AI-readable** - jiné AI nebo GENT si může přečíst info z DB

#### **Požadované funkce:**

##### 1. **MCP Server Configuration**
- ➕ **Přidání nového MCP serveru**
  - Název serveru
  - URL/endpoint
  - Typ připojení (HTTP, WebSocket, Local)
  - Autentifikace (API key, token, none)
  - Port a konfigurace
  - Popis a dokumentace

##### 2. **MCP Server Management**
- 📖 **Zobrazení všech MCP serverů** - tabulka s přehledem
- ✏️ **Editace konfigurace** - úprava nastavení
- 🗑️ **Mazání serverů** - s potvrzením
- ⚡ **Aktivace/deaktivace** - zapnutí/vypnutí serverů
- 🔄 **Test připojení** - ověření funkčnosti

##### 3. **MCP Server Monitoring**
- 📊 **Status monitoring** - online/offline status
- 📈 **Performance metriky** - rychlost odpovědi, úspěšnost
- 📝 **Logy a historie** - záznamy o komunikaci
- 🔔 **Notifikace** - upozornění na problémy

##### 4. **MCP Tools & Functions**
- 🛠️ **Dostupné nástroje** - seznam funkcí každého serveru
- 📋 **Dokumentace nástrojů** - popis a parametry
- 🧪 **Testování nástrojů** - možnost otestovat funkce
- 🔗 **Mapování funkcí** - přiřazení k různým účelům

#### **Databázová struktura:**

##### **Tabulka: `mcp_servers`**
```sql
CREATE TABLE mcp_servers (
    server_id SERIAL PRIMARY KEY,
    server_name VARCHAR(255) NOT NULL,
    server_url VARCHAR(500),
    server_type VARCHAR(50), -- 'http', 'websocket', 'local'
    auth_type VARCHAR(50), -- 'api_key', 'token', 'none'
    auth_credentials TEXT, -- encrypted
    port INTEGER,
    is_active BOOLEAN DEFAULT true,
    is_default BOOLEAN DEFAULT false,
    configuration JSONB, -- další konfigurace
    description TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

##### **Tabulka: `mcp_tools`**
```sql
CREATE TABLE mcp_tools (
    tool_id SERIAL PRIMARY KEY,
    server_id INTEGER REFERENCES mcp_servers(server_id),
    tool_name VARCHAR(255) NOT NULL,
    tool_description TEXT,
    tool_parameters JSONB, -- schema parametrů
    tool_category VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    usage_count INTEGER DEFAULT 0,
    last_used TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);
```

##### **Tabulka: `mcp_logs`**
```sql
CREATE TABLE mcp_logs (
    log_id SERIAL PRIMARY KEY,
    server_id INTEGER REFERENCES mcp_servers(server_id),
    tool_id INTEGER REFERENCES mcp_tools(tool_id),
    request_data JSONB,
    response_data JSONB,
    response_time INTEGER, -- ms
    status VARCHAR(20), -- 'success', 'error', 'timeout'
    error_message TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);
```

#### **Frontend struktura:**

##### **Komponenta: `/admin/mcp`**
- 📁 **McpManagement.vue** - hlavní komponenta
- 🎨 **Stejný design** jako LLM Management
- 📑 **Karty/taby:**
  1. **Přehled** - seznam všech MCP serverů
  2. **Konfigurace** - nastavení serverů
  3. **Nástroje** - dostupné MCP tools
  4. **Monitoring** - výkon a statistiky
  5. **Testování** - test funkcí a připojení

##### **API endpointy:**
```
GET    /api/mcp/servers          - seznam serverů
POST   /api/mcp/servers          - vytvoření serveru
PUT    /api/mcp/servers/{id}     - úprava serveru
DELETE /api/mcp/servers/{id}     - smazání serveru
POST   /api/mcp/servers/{id}/test - test připojení

GET    /api/mcp/tools            - seznam nástrojů
GET    /api/mcp/tools/{server_id} - nástroje serveru
POST   /api/mcp/tools/{id}/test  - test nástroje

GET    /api/mcp/logs             - logy komunikace
GET    /api/mcp/stats            - statistiky výkonu
```

#### **Integrace s GENT:**
- 🤖 **AI-readable** - GENT si může přečíst dostupné MCP servery z DB
- 🔗 **Automatické připojení** - GENT se automaticky připojí k aktivním serverům
- 🛠️ **Dynamické nástroje** - GENT může používat nástroje z MCP serverů
- 📊 **Monitoring** - sledování využití nástrojů

---

## 📅 **Timeline:**

### **Fáze 1: Oprava LLM Management** (1-2 dny)
1. 🔍 Debug a identifikace problémů
2. 🔧 Oprava editace modelů
3. 🔧 Oprava nastavení výchozího modelu
4. 🧪 Testování funkcionalnosti

### **Fáze 2: MCP Management Design** (2-3 dny)
1. 📋 Detailní specifikace
2. 🗄️ Návrh databázové struktury
3. 🎨 UI/UX design
4. 📡 API design

### **Fáze 3: MCP Management Implementation** (5-7 dní)
1. 🗄️ Vytvoření databázových tabulek
2. 📡 Implementace API endpointů
3. 🎨 Frontend komponenty
4. 🔗 Integrace s GENT systémem

### **Fáze 4: Testing & Polish** (2-3 dny)
1. 🧪 Kompletní testování
2. 🐛 Oprava bugů
3. 📚 Dokumentace
4. 🚀 Nasazení

---

## 📝 **Poznámky:**

- ✅ **LLM Management** musí být plně funkční před začátkem MCP Management
- ✅ **MCP Management** bude následovat stejné vzory jako LLM Management
- ✅ **Databáze** bude centrálním úložištěm pro všechny MCP informace
- ✅ **AI-readable** design umožní jiným AI systémům číst a používat MCP data
- ✅ **GENT integrace** bude klíčová pro automatické využití MCP nástrojů

---

**📍 Aktuální status:** Čeká se na debug logy z LLM Management pro opravu kritických problémů.
