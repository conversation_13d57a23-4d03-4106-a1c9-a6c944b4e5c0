#!/usr/bin/env python3
"""
Script pro vytvoření tabulek pro aplikace.
"""

import sys
import os

# Přidání cesty k GENT modulu
sys.path.insert(0, '/opt/gent')

try:
    from sqlalchemy import create_engine, text
    from gent.db.models.apps import App, AppExecution
    from gent.db.models.base import Base
    
    # Připojení k databázi
    DATABASE_URL = "postgresql://gent_user:gent_password@localhost:5432/gent_db"
    engine = create_engine(DATABASE_URL)
    
    print("🔗 Připojuji se k databázi...")
    
    # Test připojení
    with engine.connect() as conn:
        result = conn.execute(text("SELECT 1"))
        print("✅ Připojení k databázi úspěšné")
    
    # Vytvoření tabulek
    print("📋 Vytvářím tabulky...")
    Base.metadata.create_all(engine, tables=[App.__table__, AppExecution.__table__])
    
    print("✅ Tabulky 'apps' a 'app_executions' byly úspěšně vytvořeny")
    
    # Ověření vytvoření
    with engine.connect() as conn:
        result = conn.execute(text("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name IN ('apps', 'app_executions')"))
        tables = [row[0] for row in result]
        print(f"📊 Nalezené tabulky: {tables}")

except Exception as e:
    print(f"❌ Chyba: {e}")
    sys.exit(1)
