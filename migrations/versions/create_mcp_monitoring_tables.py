"""
Vytvoření monitoring tabulek pro MCP Management systém

Revize ID: create_mcp_monitoring_tables
Vytvořeno: 2025-01-31 12:05
"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# Revize identifikátor
revision = 'create_mcp_monitoring_tables'
down_revision = 'create_mcp_tables'  # Závisí na základních MCP tabulkách
branch_labels = None
depends_on = None


def upgrade() -> None:
    """
    Vytvoření monitoring tabulek pro MCP Management systém.
    """
    
    # 4. Tabulka mcp_requests - Log všech MCP requestů (pro metriky)
    op.create_table(
        'mcp_requests',
        sa.Column('request_id', sa.Integer, primary_key=True, autoincrement=True),
        sa.Column('tool_id', sa.Integer, sa.Foreign<PERSON>ey('mcp_tools.tool_id'), nullable=True),
        sa.Column('provider_id', sa.Integer, sa.<PERSON>('mcp_providers.provider_id'), nullable=False),
        sa.Column('request_data', postgresql.JSONB),
        sa.Column('response_data', postgresql.JSONB),
        sa.Column('response_time_ms', sa.Float),
        sa.Column('status', sa.String(20), nullable=False),
        sa.Column('error_message', sa.Text),
        sa.Column('error_code', sa.String(50)),
        sa.Column('user_id', sa.String(100)),
        sa.Column('session_id', sa.String(100)),
        sa.Column('request_size_bytes', sa.Integer),
        sa.Column('response_size_bytes', sa.Integer),
        sa.Column('created_at', sa.TIMESTAMP, server_default=sa.func.current_timestamp()),
        sa.CheckConstraint("status IN ('success', 'error', 'timeout', 'cancelled')", name='ck_mcp_requests_status')
    )
    
    # Indexy pro mcp_requests (pro performance analytics)
    op.create_index('idx_mcp_requests_tool', 'mcp_requests', ['tool_id'])
    op.create_index('idx_mcp_requests_provider', 'mcp_requests', ['provider_id'])
    op.create_index('idx_mcp_requests_status', 'mcp_requests', ['status'])
    op.create_index('idx_mcp_requests_created_at', 'mcp_requests', ['created_at'])
    op.create_index('idx_mcp_requests_response_time', 'mcp_requests', ['response_time_ms'])
    
    # 5. Tabulka mcp_server_status - Aktuální stav MCP serverů
    op.create_table(
        'mcp_server_status',
        sa.Column('status_id', sa.Integer, primary_key=True, autoincrement=True),
        sa.Column('provider_id', sa.Integer, sa.ForeignKey('mcp_providers.provider_id', ondelete='CASCADE'), nullable=False, unique=True),
        sa.Column('status', sa.String(20), nullable=False),
        sa.Column('process_id', sa.Integer),
        sa.Column('port', sa.Integer),
        sa.Column('memory_usage_mb', sa.Float),
        sa.Column('cpu_usage_percent', sa.Float),
        sa.Column('uptime_seconds', sa.Integer),
        sa.Column('last_health_check', sa.TIMESTAMP),
        sa.Column('health_check_status', sa.String(20)),
        sa.Column('error_message', sa.Text),
        sa.Column('restart_count', sa.Integer, default=0),
        sa.Column('last_restart', sa.TIMESTAMP),
        sa.Column('created_at', sa.TIMESTAMP, server_default=sa.func.current_timestamp()),
        sa.Column('updated_at', sa.TIMESTAMP, server_default=sa.func.current_timestamp()),
        sa.CheckConstraint("status IN ('online', 'offline', 'error', 'starting', 'stopping')", name='ck_mcp_server_status'),
        sa.CheckConstraint("health_check_status IN ('healthy', 'unhealthy', 'unknown')", name='ck_mcp_health_status')
    )
    
    # Indexy pro mcp_server_status
    op.create_index('idx_mcp_status_provider', 'mcp_server_status', ['provider_id'])
    op.create_index('idx_mcp_status_status', 'mcp_server_status', ['status'])
    op.create_index('idx_mcp_status_health', 'mcp_server_status', ['health_check_status'])
    
    # 6. Tabulka mcp_permissions - Oprávnění pro MCP nástroje
    op.create_table(
        'mcp_permissions',
        sa.Column('permission_id', sa.Integer, primary_key=True, autoincrement=True),
        sa.Column('user_id', sa.String(100)),
        sa.Column('tool_id', sa.Integer, sa.ForeignKey('mcp_tools.tool_id', ondelete='CASCADE'), nullable=True),
        sa.Column('provider_id', sa.Integer, sa.ForeignKey('mcp_providers.provider_id', ondelete='CASCADE'), nullable=True),
        sa.Column('permission_level', sa.String(20), nullable=False),
        sa.Column('allowed_operations', postgresql.JSONB),
        sa.Column('restrictions', postgresql.JSONB),
        sa.Column('is_active', sa.Boolean, default=True),
        sa.Column('granted_by', sa.String(100)),
        sa.Column('granted_at', sa.TIMESTAMP, server_default=sa.func.current_timestamp()),
        sa.Column('expires_at', sa.TIMESTAMP),
        sa.Column('created_at', sa.TIMESTAMP, server_default=sa.func.current_timestamp()),
        sa.CheckConstraint("permission_level IN ('read', 'write', 'execute', 'admin')", name='ck_mcp_permission_level')
    )
    
    # Indexy pro mcp_permissions
    op.create_index('idx_mcp_permissions_user', 'mcp_permissions', ['user_id'])
    op.create_index('idx_mcp_permissions_tool', 'mcp_permissions', ['tool_id'])
    op.create_index('idx_mcp_permissions_provider', 'mcp_permissions', ['provider_id'])
    op.create_index('idx_mcp_permissions_active', 'mcp_permissions', ['is_active'])
    
    print("✅ MCP monitoring tabulky requests, server_status a permissions byly úspěšně vytvořeny!")


def downgrade() -> None:
    """
    Odstranění monitoring tabulek při downgrade.
    """
    # Odstranění v opačném pořadí kvůli foreign keys
    op.drop_table('mcp_permissions')
    op.drop_table('mcp_server_status')
    op.drop_table('mcp_requests')
    
    print("❌ MCP monitoring tabulky byly odstraněny")
