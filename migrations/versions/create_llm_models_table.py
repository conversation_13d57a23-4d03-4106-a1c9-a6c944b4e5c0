"""
Migrace pro vytvoření tabulky llm_models

Revize ID: create_llm_models_table
Vytvořeno: 2025-04-18 14:22
"""

from typing import Sequence, Union
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID, JSONB

# Revize identifikátor - použitý Alembic
revision = 'create_llm_models_table'
down_revision = None  # Nastav na poslední migraci, pokud je známá
branch_labels = None
depends_on = None


def upgrade() -> None:
    """
    Vytvoření nové tabulky llm_models pro ukládání modelů LLM jako samostatných záznamů
    místo vnořeného JSON v tabulce llm_configs.
    """
    op.create_table(
        'llm_models',
        sa.Column('id', UUID, primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('provider_id', UUID, sa.<PERSON>('config_entities.id', ondelete='CASCADE'), nullable=False),
        sa.Column('name', sa.String(255), nullable=False),
        sa.Column('context_window', sa.Integer, nullable=False, default=4096),
        sa.Column('max_tokens', sa.Integer, nullable=False, default=2048),
        sa.Column('is_default', sa.Boolean, nullable=False, default=False),
        sa.Column('capabilities', JSONB, default=sa.text("'[]'::jsonb")),
        sa.Column('suitable_for', JSONB, default=sa.text("'[]'::jsonb")),
        sa.Column('parameters', JSONB, default=sa.text("'{}'::jsonb")),
        sa.Column('created_at', sa.TIMESTAMP(timezone=True), nullable=False, server_default=sa.text('now()')),
        sa.Column('updated_at', sa.TIMESTAMP(timezone=True), nullable=False, server_default=sa.text('now()')),
        
        # Kompozitní unikátní klíč - poskytovatel může mít jen jeden model s daným jménem
        sa.UniqueConstraint('provider_id', 'name', name='uq_provider_model_name')
    )
    
    # Index pro rychlejší vyhledávání výchozích modelů
    op.create_index('idx_llm_models_default', 'llm_models', ['provider_id', 'is_default'])
    
    # Vytvoření triggeru pro automatickou aktualizaci updated_at při změně záznamu
    op.execute("""
    CREATE TRIGGER llm_models_updated_at_trigger
    BEFORE UPDATE ON llm_models
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamp();
    """)


def downgrade() -> None:
    """
    Odstranění tabulky llm_models při downgrade
    """
    op.drop_index('idx_llm_models_default')
    op.drop_table('llm_models')
