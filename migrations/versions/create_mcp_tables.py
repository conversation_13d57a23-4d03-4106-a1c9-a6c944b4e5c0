"""
Vytvoř<PERSON><PERSON> tabulek pro MCP Management systém

Revize ID: create_mcp_tables
Vytvořeno: 2025-01-31 12:00
"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# Revize identifikátor
revision = 'create_mcp_tables'
down_revision = None  # Nezávislá migrace
branch_labels = None
depends_on = None


def upgrade() -> None:
    """
    Vytvoření všech tabulek pro MCP Management systém.
    """
    
    # 1. Tabulka mcp_providers - Poskytovatelé MCP serverů
    op.create_table(
        'mcp_providers',
        sa.Column('provider_id', sa.Integer, primary_key=True, autoincrement=True),
        sa.Column('provider_name', sa.String(100), nullable=False, unique=True),
        sa.Column('provider_type', sa.String(50), nullable=False),
        sa.Column('display_name', sa.String(200)),
        sa.Column('description', sa.Text),
        sa.Column('command', sa.Text, nullable=False),
        sa.Column('base_url', sa.String(500)),
        sa.Column('is_active', sa.Boolean, default=True),
        sa.Column('is_custom', sa.Boolean, default=False),
        sa.Column('auto_start', sa.Boolean, default=True),
        sa.Column('health_check_url', sa.String(500)),
        sa.Column('documentation_url', sa.String(500)),
        sa.Column('version', sa.String(50)),
        sa.Column('created_at', sa.TIMESTAMP, server_default=sa.func.current_timestamp()),
        sa.Column('updated_at', sa.TIMESTAMP, server_default=sa.func.current_timestamp())
    )
    
    # Indexy pro mcp_providers
    op.create_index('idx_mcp_providers_type', 'mcp_providers', ['provider_type'])
    op.create_index('idx_mcp_providers_active', 'mcp_providers', ['is_active'])
    op.create_index('idx_mcp_providers_name', 'mcp_providers', ['provider_name'])
    
    # 2. Tabulka mcp_tools - Nástroje dostupné v MCP serverech
    op.create_table(
        'mcp_tools',
        sa.Column('tool_id', sa.Integer, primary_key=True, autoincrement=True),
        sa.Column('provider_id', sa.Integer, sa.ForeignKey('mcp_providers.provider_id', ondelete='CASCADE'), nullable=False),
        sa.Column('tool_name', sa.String(100), nullable=False),
        sa.Column('tool_identifier', sa.String(150), nullable=False),
        sa.Column('display_name', sa.String(200)),
        sa.Column('description', sa.Text),
        sa.Column('parameters_schema', postgresql.JSONB),
        sa.Column('response_schema', postgresql.JSONB),
        sa.Column('auto_approve', sa.Boolean, default=False),
        sa.Column('is_active', sa.Boolean, default=True),
        sa.Column('is_dangerous', sa.Boolean, default=False),
        sa.Column('capabilities', postgresql.JSONB),
        sa.Column('usage_examples', postgresql.JSONB),
        sa.Column('rate_limit_per_minute', sa.Integer, default=60),
        sa.Column('timeout_seconds', sa.Integer, default=30),
        sa.Column('created_at', sa.TIMESTAMP, server_default=sa.func.current_timestamp()),
        sa.Column('updated_at', sa.TIMESTAMP, server_default=sa.func.current_timestamp()),
        sa.UniqueConstraint('provider_id', 'tool_identifier', name='uq_provider_tool_identifier')
    )
    
    # Indexy pro mcp_tools
    op.create_index('idx_mcp_tools_provider', 'mcp_tools', ['provider_id'])
    op.create_index('idx_mcp_tools_active', 'mcp_tools', ['is_active'])
    op.create_index('idx_mcp_tools_name', 'mcp_tools', ['tool_name'])
    op.create_index('idx_mcp_tools_capabilities', 'mcp_tools', ['capabilities'], postgresql_using='gin')
    
    # 3. Tabulka mcp_configurations - Konfigurace MCP serverů
    op.create_table(
        'mcp_configurations',
        sa.Column('config_id', sa.Integer, primary_key=True, autoincrement=True),
        sa.Column('provider_id', sa.Integer, sa.ForeignKey('mcp_providers.provider_id', ondelete='CASCADE'), nullable=False),
        sa.Column('config_name', sa.String(100), nullable=False),
        sa.Column('environment_vars', postgresql.JSONB),
        sa.Column('startup_args', postgresql.JSONB),
        sa.Column('working_directory', sa.String(500)),
        sa.Column('timeout_seconds', sa.Integer, default=30),
        sa.Column('retry_count', sa.Integer, default=3),
        sa.Column('retry_delay_ms', sa.Integer, default=1000),
        sa.Column('max_concurrent_requests', sa.Integer, default=10),
        sa.Column('security_settings', postgresql.JSONB),
        sa.Column('resource_limits', postgresql.JSONB),
        sa.Column('is_default', sa.Boolean, default=False),
        sa.Column('is_active', sa.Boolean, default=True),
        sa.Column('created_at', sa.TIMESTAMP, server_default=sa.func.current_timestamp()),
        sa.Column('updated_at', sa.TIMESTAMP, server_default=sa.func.current_timestamp()),
        sa.UniqueConstraint('provider_id', 'config_name', name='uq_provider_config_name')
    )
    
    # Indexy pro mcp_configurations
    op.create_index('idx_mcp_configs_provider', 'mcp_configurations', ['provider_id'])
    op.create_index('idx_mcp_configs_default', 'mcp_configurations', ['is_default'])
    
    print("✅ MCP tabulky providers, tools a configurations byly úspěšně vytvořeny!")


def downgrade() -> None:
    """
    Odstranění všech MCP tabulek při downgrade.
    """
    # Odstranění v opačném pořadí kvůli foreign keys
    op.drop_table('mcp_configurations')
    op.drop_table('mcp_tools')
    op.drop_table('mcp_providers')
    
    print("❌ MCP tabulky byly odstraněny")
