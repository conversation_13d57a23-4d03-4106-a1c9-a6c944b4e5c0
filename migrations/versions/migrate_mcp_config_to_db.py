"""
Migrace existujících MCP serverů z JSON konfigurace do databáze

Revize ID: migrate_mcp_config_to_db
Vytvořeno: 2025-01-31 12:10
"""

import json
import os
from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql import text

# Revize identifikátor
revision = 'migrate_mcp_config_to_db'
down_revision = 'create_mcp_monitoring_tables'  # Závisí na všech MCP tabulkách
branch_labels = None
depends_on = None


def upgrade() -> None:
    """
    Import existujících MCP serverů z config/mcp_config.json do databáze.
    """
    
    # Cesta k JSON konfiguraci
    config_path = '/opt/gent/config/mcp_config.json'
    
    if not os.path.exists(config_path):
        print(f"⚠️ Konfigurační soubor {config_path} neexistuje, přeskakuji import")
        return
    
    # Načtení existují<PERSON><PERSON> konfigurace
    with open(config_path, 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    # Připojení k databázi
    conn = op.get_bind()
    
    # Mapování typů MCP serverů
    type_mapping = {
        'filesystem': 'file_system',
        'brave-search': 'web_search',
        'tavily': 'web_search',
        'fetch': 'web_fetch',
        'perplexity': 'ai_search',
        'sequentialthinking': 'reasoning',
        'git': 'version_control',
        'gent-project': 'project_management',
        'gent-workflow': 'workflow_management'
    }
    
    # Import poskytovatelů
    for server_name, server_config in config['servers'].items():
        provider_type = type_mapping.get(server_name, 'other')
        display_name = server_name.replace('-', ' ').title()
        
        # Vložení poskytovatele
        result = conn.execute(text("""
            INSERT INTO mcp_providers (
                provider_name, provider_type, display_name, description,
                command, is_active, is_custom, auto_start
            ) VALUES (:provider_name, :provider_type, :display_name, :description,
                     :command, :is_active, :is_custom, :auto_start)
            RETURNING provider_id
        """), {
            'provider_name': server_name,
            'provider_type': provider_type,
            'display_name': display_name,
            'description': server_config.get('description', ''),
            'command': server_config.get('command', ''),
            'is_active': server_config.get('enabled', True),
            'is_custom': server_config.get('custom', False),
            'auto_start': True
        })
        
        provider_id = result.fetchone()[0]
        print(f"✅ Importován provider: {server_name} (ID: {provider_id})")
        
        # Import nástrojů pro každého poskytovatele
        auto_approve_tools = server_config.get('auto_approve', [])
        for tool_name in auto_approve_tools:
            tool_identifier = f"{server_name}_{tool_name}"
            display_name_tool = tool_name.replace('_', ' ').title()
            
            conn.execute(text("""
                INSERT INTO mcp_tools (
                    provider_id, tool_name, tool_identifier, display_name,
                    auto_approve, is_active
                ) VALUES (:provider_id, :tool_name, :tool_identifier, :display_name,
                         :auto_approve, :is_active)
            """), {
                'provider_id': provider_id,
                'tool_name': tool_name,
                'tool_identifier': tool_identifier,
                'display_name': display_name_tool,
                'auto_approve': True,
                'is_active': True
            })
            
            print(f"  ✅ Importován nástroj: {tool_name}")
        
        # Import konfigurace
        env_vars = server_config.get('env', {})
        timeout = config.get('global_settings', {}).get('timeout', 30)
        
        conn.execute(text("""
            INSERT INTO mcp_configurations (
                provider_id, config_name, environment_vars,
                timeout_seconds, is_default, is_active
            ) VALUES (:provider_id, :config_name, :environment_vars,
                     :timeout_seconds, :is_default, :is_active)
        """), {
            'provider_id': provider_id,
            'config_name': 'default',
            'environment_vars': json.dumps(env_vars),
            'timeout_seconds': timeout,
            'is_default': True,
            'is_active': True
        })
        
        print(f"  ✅ Importována konfigurace pro: {server_name}")
        
        # Vytvoření výchozího server status
        conn.execute(text("""
            INSERT INTO mcp_server_status (
                provider_id, status, health_check_status
            ) VALUES (:provider_id, :status, :health_check_status)
        """), {
            'provider_id': provider_id,
            'status': 'offline',
            'health_check_status': 'unknown'
        })
    
    print("✅ MCP konfigurace byla úspěšně migrována do databáze!")
    
    # Výpis statistik
    providers_count = conn.execute(text("SELECT COUNT(*) FROM mcp_providers")).fetchone()[0]
    tools_count = conn.execute(text("SELECT COUNT(*) FROM mcp_tools")).fetchone()[0]
    configs_count = conn.execute(text("SELECT COUNT(*) FROM mcp_configurations")).fetchone()[0]
    
    print(f"📊 Importováno: {providers_count} poskytovatelů, {tools_count} nástrojů, {configs_count} konfigurací")


def downgrade() -> None:
    """
    Vymazání všech importovaných MCP dat při downgrade.
    """
    conn = op.get_bind()
    
    # Vymazání v opačném pořadí kvůli foreign keys
    conn.execute(text("DELETE FROM mcp_server_status"))
    conn.execute(text("DELETE FROM mcp_configurations"))
    conn.execute(text("DELETE FROM mcp_tools"))
    conn.execute(text("DELETE FROM mcp_providers"))
    
    print("❌ Všechna importovaná MCP data byla odstraněna")
