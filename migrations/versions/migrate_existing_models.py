"""
Migrace existujících modelů LLM z JSON struktury do samostatné tabulky

Revize ID: migrate_existing_models
Vytvořeno: 2025-04-18 14:22
"""

import json
import uuid
from typing import Dict, Any, List
from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql import text

# Revize identifikátor - použitý Alembic
revision = 'migrate_existing_models'
down_revision = 'create_llm_models_table'  # Tato migrace závisí na vytvoření tabulky llm_models
branch_labels = None
depends_on = None


def upgrade() -> None:
    """
    Extrahuje existující modely z JSON sloupce parameters v llm_configs
    a migruje je do samostatné tabulky llm_models.
    """
    conn = op.get_bind()
    
    # Získáme všechny poskytovatele z llm_configs
    providers = conn.execute("""
        SELECT config_id, provider, model, parameters
        FROM llm_configs
    """).fetchall()
    
    # Pro každého poskytovatele vytvoříme záznamy modelů v llm_models
    for provider in providers:
        provider_id = provider[0]  # UUID poskytovatele
        provider_name = provider[1]  # Jméno poskytovatele
        default_model = provider[2]  # Výchozí model poskytovatele
        parameters = provider[3]  # JSON parametry
        
        # Průzkum modelů v JSON parametrech
        models = {}
        if parameters and 'models' in parameters:
            if isinstance(parameters, str):
                try:
                    params = json.loads(parameters)
                    if 'models' in params:
                        models = params['models']
                except json.JSONDecodeError:
                    # Pokud nelze JSON dekódovat, pokračujeme s prázdným seznamem modelů
                    pass
            else:
                # PostgreSQL může vracet JSONB jako slovník přímo
                models = parameters.get('models', {})
        
        # Pokud poskytovatel nemá žádné modely v parametrech, ale má defaultní model,
        # vytvoříme pro něj jeden záznam modelu
        if not models and default_model:
            # Vložíme výchozí model do tabulky llm_models
            conn.execute(
                text("""
                INSERT INTO llm_models 
                (id, provider_id, name, context_window, max_tokens, is_default, capabilities, suitable_for)
                VALUES (:id, :provider_id, :name, :context_window, :max_tokens, :is_default, 
                        :capabilities, :suitable_for)
                """),
                {
                    'id': str(uuid.uuid4()),
                    'provider_id': provider_id,
                    'name': default_model,
                    'context_window': 32000,  # Standardní hodnoty
                    'max_tokens': 4096,
                    'is_default': True,
                    'capabilities': json.dumps(['text']),
                    'suitable_for': json.dumps(['general'])
                }
            )
            print(f"Migrován jeden výchozí model '{default_model}' pro poskytovatele '{provider_name}'")
        
        # Pokud poskytovatel má modely v parametrech, migrujeme je do samostatné tabulky
        elif models:
            for model_name, model_data in models.items():
                # Určíme, zda je tento model výchozí
                is_default = model_name == default_model
                
                # Získat parametry modelu
                context_window = model_data.get('context_window', 32000)
                max_tokens = model_data.get('max_tokens', 4096)
                capabilities = model_data.get('capabilities', ['text'])
                suitable_for = model_data.get('suitable_for', ['general'])
                
                # Vložíme model do tabulky llm_models
                conn.execute(
                    text("""
                    INSERT INTO llm_models 
                    (id, provider_id, name, context_window, max_tokens, is_default, capabilities, suitable_for)
                    VALUES (:id, :provider_id, :name, :context_window, :max_tokens, :is_default, 
                            :capabilities, :suitable_for)
                    """),
                    {
                        'id': str(uuid.uuid4()),
                        'provider_id': provider_id,
                        'name': model_name,
                        'context_window': context_window,
                        'max_tokens': max_tokens,
                        'is_default': is_default,
                        'capabilities': json.dumps(capabilities),
                        'suitable_for': json.dumps(suitable_for)
                    }
                )
            
            print(f"Migrováno {len(models)} modelů pro poskytovatele '{provider_name}'")


def downgrade() -> None:
    """
    Odstranění všech záznamů v llm_models
    (neobnovuje JSON v parametrech, protože původní data zůstala nezměněna)
    """
    conn = op.get_bind()
    conn.execute("TRUNCATE TABLE llm_models")
    print("Všechny migrované modely byly odstraněny z tabulky llm_models")
