# Kom mezi LLM - Middleware LLM implementace

## Přehled revoluce

Implementoval jsem **Middleware LLM** - samostatný <PERSON> mozek, který inteligentně řídí komunikaci mezi AI agenty. Místo statických pravidel nyní middleware používá AI pro dynamickou analýzu a generování instrukcí.

## Koncept

### **PŘED - Statické middleware:**
```javascript
// Pevně dané instrukce
const instructions = "Přidej pouze jeden krok";
```

### **PO - AI-řízené middleware:**
```javascript
// AI analyzuje úlohu a generuje instrukce
const instructions = await callMiddlewareLLM(task, history, agent);
```

## Implementace

### **1. Nová funkce `processMessage()` s LLM**

```javascript
async processMessage(message, fromAgent, toAgent, iteration, history, originalTask) {
  try {
    // Volání middleware LLM pro inteligentní analýzu
    const middlewareInstructions = await this.callMiddlewareLLM(
      originalTask,
      history,
      toAgent,
      iteration,
      maxIterations
    );
    
    // Sestavení obohacené zprávy s AI-generovanými instrukcemi
    const enrichedMessage = `
🚨 MIDDLEWARE INSTRUKCE (AI-GENEROVANÉ) 🚨
ITERACE: ${iteration}/${maxIterations}
${middlewareInstructions}
=== PŮVODNÍ ZPRÁVA ===
${message}
🚨 DODRŽUJ VÝŠE UVEDENÉ INSTRUKCE! 🚨
`;

    return enrichedMessage;
    
  } catch (error) {
    // Fallback na statické instrukce při chybě
    return this.processMessageFallback(message, fromAgent, toAgent, iteration, history);
  }
}
```

### **2. Middleware LLM prompt**

```javascript
const middlewarePrompt = `Jsi MIDDLEWARE AI - expert na řízení iterativní spolupráce mezi AI agenty.

ÚLOHA K ANALÝZE: "${originalTask}"

HISTORIE KONVERZACE:
${historyText}

POSLEDNÍ ZPRÁVA:
${lastMessage}

AKTUÁLNÍ SITUACE:
- Iterace: ${iteration}/${maxIterations}
- Příští agent: ${toAgent}
- Typ úlohy: ${this.detectTaskType(originalTask)}

TVŮJ ÚKOL:
Vygeneruj KONKRÉTNÍ instrukce pro ${toAgent}, aby přidal POUZE JEDNU věc k řešení.

POŽADOVANÝ FORMÁT ODPOVĚDI:
TYP ÚLOHY: [programování/kreativní/analytická/stavební]
TVŮJ ÚKOL: [konkrétní instrukce - co přesně má agent udělat]
ZAKÁZÁNO: [co nesmí opakovat nebo dělat]
FORMÁT: [jak má výstup vypadat]

KRITICKÉ PRAVIDLA:
1. Agent smí přidat POUZE JEDNU věc (1 řádek kódu, 1 větu, 1 detail)
2. ŽÁDNÉ přepisování celého řešení
3. Konkrétní instrukce, ne obecné rady
4. Zabraň opakování toho, co už bylo řečeno
5. Postupné budování řešení krok za krokem

PŘÍKLAD DOBRÉ INSTRUKCE:
TYP ÚLOHY: Programování - kalkulačka
TVŮJ ÚKOL: Přidaj řádek pro načtení druhého čísla: y = int(input("Druhé číslo: "))
ZAKÁZÁNO: Měnit už napsanou funkci def calculator(), přidávat celou logiku najednou
FORMÁT: Pouze jeden řádek kódu bez komentářů

Vygeneruj instrukce pro ${toAgent}:`;
```

### **3. Detekce typu úlohy**

```javascript
detectTaskType(task) {
  const taskLower = task.toLowerCase();
  
  if (taskLower.includes('kód') || taskLower.includes('program') || 
      taskLower.includes('kalkulačka') || taskLower.includes('python')) {
    return 'programování';
  } else if (taskLower.includes('příběh') || taskLower.includes('kreativní')) {
    return 'kreativní';
  } else if (taskLower.includes('analýza') || taskLower.includes('vyhodnoť')) {
    return 'analytická';
  } else if (taskLower.includes('postup') || taskLower.includes('návod')) {
    return 'stavební';
  } else {
    return 'obecná';
  }
}
```

### **4. Fallback mechanismus**

```javascript
processMessageFallback(message, fromAgent, toAgent, iteration, history) {
  // Statické instrukce při chybě middleware LLM
  const enrichedMessage = `
🚨 MIDDLEWARE INSTRUKCE (FALLBACK) 🚨
⚠️ KRITICKÉ INSTRUKCE ⚠️
1. PŘIDEJ POUZE JEDNU VĚC NAVÍC
2. POKUD NAPÍŠEŠ VÍC NEŽ 2 VĚTY, SELHAL JSI
3. NEPIŠ CELÉ ŘEŠENÍ ZNOVU
=== PŮVODNÍ ZPRÁVA ===
${message}
🚨 POUZE JEDEN KROK! 🚨
`;
  return enrichedMessage;
}
```

## UI změny

### **Nový výběr modelu:**
```html
<div class="control-group">
  <label for="middleware-model">Middleware LLM (Řízení komunikace):</label>
  <select id="middleware-model" v-model="communicationForm.middlewareModel">
    <option value="">-- Vyberte model pro Middleware --</option>
    <option v-for="model in allModels" :value="model.id">
      {{ model.display_name }}
    </option>
  </select>
</div>
```

### **Aktualizovaná validace:**
```javascript
canStartCommunication() {
  return this.communicationForm.ai0Model &&
         this.communicationForm.ai1Model &&
         this.communicationForm.ai2Model &&
         this.communicationForm.middlewareModel &&  // NOVÉ!
         this.communicationForm.userPrompt.trim() &&
         this.communicationForm.iterationCount > 0 &&
         !this.isProcessing;
}
```

## Výhody Middleware LLM

### ✅ **Inteligentní adaptace**
- Rozpozná typ úlohy (kód/kreativa/analýza)
- Přizpůsobí instrukce kontextu
- Reaguje na historii komunikace

### ✅ **Dynamické řízení**
- Generuje konkrétní úkoly pro každou iteraci
- Zabraňuje opakování na základě analýzy
- Směřuje komunikaci k cíli

### ✅ **Kontextové porozumění**
- Analyzuje celou historii konverzace
- Rozumí pokroku a chybějícím částem
- Generuje relevantní další kroky

### ✅ **Robustnost**
- Fallback na statické instrukce při chybě
- Nezastaví komunikaci při problému
- Logování pro debugging

## Příklad výstupu

### **Middleware LLM vygeneruje:**
```
TYP ÚLOHY: Programování - kalkulačka
TVŮJ ÚKOL: Přidaj řádek pro načtení druhého čísla: y = int(input("Druhé číslo: "))
ZAKÁZÁNO: Měnit už napsanou funkci def calculator(), přidávat celou logiku najednou
FORMÁT: Pouze jeden řádek kódu bez komentářů
```

### **Výsledná zpráva pro AI agenta:**
```
🚨 MIDDLEWARE INSTRUKCE (AI-GENEROVANÉ) 🚨
ITERACE: 3/10
TYP ÚLOHY: Programování - kalkulačka
TVŮJ ÚKOL: Přidaj řádek pro načtení druhého čísla: y = int(input("Druhé číslo: "))
ZAKÁZÁNO: Měnit už napsanou funkci def calculator(), přidávat celou logiku najednou
FORMÁT: Pouze jeden řádek kódu bez komentářů
=== PŮVODNÍ ZPRÁVA ===
[původní zpráva od předchozího agenta]
🚨 DODRŽUJ VÝŠE UVEDENÉ INSTRUKCE! 🚨
```

## Status

✅ Middleware LLM implementován
✅ UI pro výběr middleware modelu
✅ Async volání s fallback
✅ Detekce typu úlohy
✅ Strukturovaný prompt pro middleware
✅ Integrace do AI-1 a AI-2 procesů
✅ Připraveno k testování

**REVOLUCE DOKONČENA!** Middleware nyní má vlastní AI mozek pro inteligentní řízení komunikace! 🧠🚀
