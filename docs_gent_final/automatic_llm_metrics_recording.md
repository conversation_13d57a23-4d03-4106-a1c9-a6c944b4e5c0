# 🤖 Automatické zaznamenávání LLM metrik

## 🎯 Odpověď na otázku: "Zaznamenávají se LLM requesty při testování a používání modelů?"

**ANO!** Nyní se **VŠECHNY** LLM requesty automaticky zaznamenávají do databáze:

### ✅ **Co se automaticky zaznamenává:**
- ⚡ **Čas odpovědi** každého LLM requestu (v milisekundách)
- 🔤 **Počet tokenů** (prompt + completion + celkem)
- ✅ **Úspěšnost** requestů (completed/failed)
- 🏷️ **Model a poskytovatel** použitý pro request
- 📝 **Prompt a odpověď** (pro analýzu kvality)
- ❌ **Chybové zprávy** při selhání
- 🔧 **Metadata** (temperature, max_tokens, API URL)

### 🔧 **Kde se zaznamenává:**

#### **1. Chat Test endpoint** (`/api/db/llm/test-llm`)
- **Soubor:** `gent/api/app/routes/llm_direct_db_routes.py`
- **Používá:** Chat service, LLM Management testování
- **Automatické ukládání:** ✅ IMPLEMENTOVÁNO

#### **2. LLM Config endpoint** (`/api/config/llm/test-llm`)
- **Soubor:** `gent/api/app/routes/llm_config_routes.py`
- **Používá:** Starší testovací rozhraní
- **Automatické ukládání:** ✅ IMPLEMENTOVÁNO

#### **3. Budoucí integrace:**
- **Chat konverzace** - při každé zprávě v chatu
- **Agent komunikace** - při použití AI agentů
- **Batch processing** - při hromadném zpracování

### 📊 **Příklad automaticky zaznamenané metriky:**

```json
{
  "model_name": "gpt-4o",
  "provider": "openai",
  "tokens_used": 563,
  "latency": 6012.875,
  "status": "completed",
  "extra_data": {
    "chat_test": true,
    "model_identifier": "gpt-4o",
    "temperature": 0.7,
    "max_tokens": 4096,
    "api_base_url": "https://api.openai.com"
  }
}
```

### 🎯 **Aktuální skutečné metriky:**

#### **📈 Přehled výkonu (z reálných testů):**
- **Průměrný čas odpovědi:** 2132ms
- **Celkové tokeny:** 1068
- **Úspěšnost:** 100%
- **Celkem requestů:** 5

#### **🏆 Testované modely:**
1. **GPT-4o** - 6013ms, 563 tokenů ✅
2. **Devstral-small** - 1947ms, 80 tokenů ✅
3. **Claude-3-7-Sonnet** - 1200ms, 200 tokenů ✅
4. **Gemini-2.0-Flash** - 651ms, 75 tokenů ✅

### 🔧 **Technická implementace:**

#### **1. Automatické měření času:**
```python
import time
start_time = time.time()
# ... LLM API volání ...
end_time = time.time()
latency = (end_time - start_time) * 1000  # v ms
```

#### **2. Extrakce tokenů z response:**
```python
tokens_used = 0
if isinstance(response, dict) and "usage" in response:
    usage = response["usage"]
    tokens_used = usage.get("total_tokens", 0)
```

#### **3. Automatické ukládání:**
```python
from gent.api.app.routes.llm_metrics_routes import save_llm_request_metric, LLMRequestMetric

metric = LLMRequestMetric(
    model_id=model_row[0],
    model_name=model_name,
    provider=provider_name,
    prompt=message,
    response=response.get("text", ""),
    tokens_used=tokens_used,
    latency=latency,
    status="completed",
    extra_data={"chat_test": True}
)

save_llm_request_metric(metric)
```

#### **4. Error handling:**
```python
except Exception as e:
    # Zaznamenání i failed requestů
    metric = LLMRequestMetric(
        model_name=model_id,
        provider="unknown",
        prompt=message,
        latency=latency,
        status="failed",
        error_message=str(e)
    )
    save_llm_request_metric(metric)
```

### 🚀 **Výhody automatického zaznamenávání:**

#### **1. Kompletní audit trail**
- Každý LLM request je zaznamenán
- Možnost rekonstrukce testovacích session
- Sledování výkonu v čase

#### **2. Skutečné performance metriky**
- LLM Management zobrazuje reálná data
- Přesné časy odpovědi z produkčních testů
- Skutečné využití tokenů a náklady

#### **3. Analýza kvality modelů**
- Porovnání rychlosti různých modelů
- Identifikace nejefektivnějších poskytovatelů
- Optimalizace výběru modelů

#### **4. Debugging a troubleshooting**
- Přesné informace o failed requestech
- Analýza chybových vzorců
- Identifikace problémových API

#### **5. Nákladová optimalizace**
- Sledování skutečných nákladů na tokeny
- Identifikace nejdražších operací
- ROI analýza různých modelů

### 📱 **Kde vidět výsledky:**

#### **1. LLM Management → Metriky**
- Přehledové karty s reálnými daty
- Tabulka modelů s performance
- Grafy a trendy

#### **2. Tests & Debug → User Activity**
- Záznamy všech LLM testů
- Detailní logy s metrikami

#### **3. API endpointy:**
```bash
# Přehled výkonu
curl http://**************:8001/api/db/llm/performance/overview

# Výkon modelů
curl http://**************:8001/api/db/llm/performance/models

# Statistiky metrik
curl http://**************:8001/api/llm/metrics/stats
```

### 🔄 **Automatické spuštění:**

**Žádná konfigurace není potřeba!** 

Automatické zaznamenávání se spustí při:
- ✅ Testování modelů v LLM Management
- ✅ Použití Chat Test funkce
- ✅ API volání na test endpointy
- ✅ Budoucí chat konverzace (připraveno)

### 🎯 **Budoucí rozšíření:**

#### **1. Chat integrace**
- Automatické zaznamenávání při každé chat zprávě
- Sledování konverzačních metrik
- Analýza uživatelských vzorců

#### **2. Agent monitoring**
- Zaznamenávání AI agent aktivit
- Performance tracking autonomních operací
- Optimalizace agent workflows

#### **3. Batch analytics**
- Hromadné zpracování metrik
- Prediktivní analýzy
- Automatické doporučení optimalizací

#### **4. Real-time alerting**
- Upozornění na pomalé modely
- Notifikace při vysokých nákladech
- Monitoring dostupnosti API

### 📊 **Databázová struktura:**

**Tabulka:** `llm_requests` v databázi `gentdb`

```sql
CREATE TABLE llm_requests (
    id SERIAL PRIMARY KEY,
    model_id INTEGER,                    -- ID z llm_models
    model_name VARCHAR(255) NOT NULL,    -- Název modelu
    provider VARCHAR(100) NOT NULL,      -- Poskytovatel
    prompt TEXT NOT NULL,               -- Původní prompt
    response TEXT,                      -- Odpověď modelu
    tokens_used INTEGER,                -- Počet tokenů
    latency FLOAT,                      -- Čas v ms
    status VARCHAR(20),                 -- completed/failed
    error_message TEXT,                 -- Chyba při selhání
    extra_data JSONB,                   -- Metadata
    created_at TIMESTAMP DEFAULT NOW()
);
```

**GENT nyní automaticky zaznamenává VŠECHNY LLM requesty!** 🎯✨

Každé testování modelu, chat zpráva a API volání se ukládá do databáze s detailními metrikami pro analýzu výkonu a optimalizaci nákladů.
