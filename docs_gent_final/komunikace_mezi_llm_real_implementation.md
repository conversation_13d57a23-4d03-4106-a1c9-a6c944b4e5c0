# Komunikace mezi LLM - Skutečná implementace

## <PERSON><PERSON><PERSON><PERSON>ce "Komunikace mezi LLM" byla <PERSON><PERSON><PERSON> přepracována ze simulace na skutečnou implementaci používající reálné LLM modely a Mem0 paměť.

## Klíčové změny

### 1. Odstranění simulací
- ❌ **PŘED**: Všechny metody používaly `setTimeout()` simulace
- ✅ **PO**: Všechny metody volají skutečná LLM API přes `chatService.sendMessage()`

### 2. Skutečné API volání
```javascript
// AI-0 optimalizace promptu
const response = await chatService.sendMessage({
  message: optimizationPrompt,
  model_id: ai0ModelId
});

// AI-1 zpracování
const response = await chatService.sendMessage({
  message: ai1Prompt,
  model_id: ai1ModelId
});

// AI-2 analýza
const response = await chatService.sendMessage({
  message: ai2Prompt,
  model_id: ai2ModelId
});
```

### 3. Mem0 integrace
- Přidány metody `saveToMem0()` a `getFromMem0()`
- Data se ukládají mezi iteracemi pro sdílení kontextu
- Každý krok ukládá svůj výstup do Mem0 pro další AI

### 4. Logika komunikace

#### AI-0 (Prompt Optimizer)
- Vezme původní prompt od uživatele
- Optimalizuje ho pro lepší pochopení
- Uloží optimalizovaný prompt do Mem0

#### AI-1 (Primary Processor)
- V iteraci 1: zpracuje optimalizovaný prompt od AI-0
- V dalších iteracích: načte kontext z Mem0 od AI-2
- Vytvoří odpověď a uloží ji do Mem0

#### AI-2 (Secondary Processor)
- Načte odpověď od AI-1 z Mem0
- Analyzuje kvalitu a úplnost
- Navrhne vylepšení pro další iteraci
- Uloží analýzu do Mem0

#### AI-0 (Final Validator)
- Načte všechny výsledky z iterací
- Porovná s původním zadáním
- Vytvoří finální shrnutí a hodnocení

### 5. Odstranění alert dialogů
- ❌ **PŘED**: `alert()` dialogy pro chyby
- ✅ **PO**: Moderní UI feedback s error zprávami v rozhraní

### 6. Model selection
- Dropdowny pro AI-0, AI-1 a AI-2 modely
- Načítání ze skutečné databáze modelů
- Validace výběru modelů před spuštěním

## Technické detaily

### Mem0 API volání
```javascript
// Ukládání
await fetch('/api/mcp/test', {
  method: 'POST',
  body: JSON.stringify({
    server_name: 'mem0',
    tool_name: 'add_memory',
    arguments: {
      messages: [{ role: 'user', content: JSON.stringify(data) }],
      user_id: `llm_communication_${sessionId}`
    }
  })
});

// Načítání
await fetch('/api/mcp/test', {
  method: 'POST', 
  body: JSON.stringify({
    server_name: 'mem0',
    tool_name: 'search_memory',
    arguments: {
      query: key,
      user_id: `llm_communication_${sessionId}`
    }
  })
});
```

### Error handling
- Chyby se nezobrazují v alert dialozích
- Error zprávy se ukládají do `currentSession.errorMessage`
- UI zobrazuje chyby v červeném boxu pod session status

### UI vylepšení
- Opravena CSS třída konfliktu v session status
- Přidán error message box s červeným ohraničením
- Moderní feedback místo 80s alert dialogů

## Testování
1. Vyberte 3 různé LLM modely pro AI-0, AI-1, AI-2
2. Zadejte prompt (např. "udělej mi kalkulačku v pythonu")
3. Nastavte počet iterací (doporučeno 2-3)
4. Spusťte komunikaci
5. Sledujte real-time výměnu dat mezi AI modely
6. Zkontrolujte finální validovaný výsledek

## Výhody nové implementace
- ✅ Skutečné AI modely místo simulace
- ✅ Mem0 paměť pro sdílení kontextu
- ✅ Iterativní vylepšování odpovědí
- ✅ Validace finálního výsledku
- ✅ Moderní UI bez alert dialogů
- ✅ Robustní error handling
- ✅ Plná integrace s GENT systémem

## Soubory změněny
- `frontend-vue/src/views/admin/LlmManagement.vue` - hlavní implementace
- Přidány Mem0 metody
- Odstraněny simulace
- Přidáno error handling UI

## KRITICKÉ VYLEPŠENÍ - Specializované prompty pro kód

### Problém
Původní implementace používala obecné prompty, což vedlo k tomu, že AI modely se odchylovaly od zadání (např. místo kalkulačky dělaly Fibonacciho posloupnost).

### Řešení
Přidána detekce typu úkolu a specializované prompty pro programovací požadavky:

#### AI-0 (Prompt Optimizer) - Kód
```javascript
if (isCodeRequest) {
  // Specializovaný prompt pro optimalizaci kódových požadavků
  // Zdůrazňuje potřebu FUNKČNÍHO KÓDU, ne popisu
}
```

#### AI-1 (Primary Processor) - Programátor
```javascript
if (isCodeRequest) {
  ai1Prompt = `Jsi AI-1 primary processor - PROGRAMÁTOR
  ÚKOL: Napiš KOMPLETNÍ FUNKČNÍ KÓD
  DŮLEŽITÉ INSTRUKCE:
  - Napiš POUZE FUNKČNÍ KÓD, žádný popis nebo teorii
  - Kód musí být kompletní a spustitelný
  - Nepiš vysvětlení, jen čistý kód`;
}
```

#### AI-2 (Secondary Processor) - Code Reviewer
```javascript
if (isCodeRequest) {
  ai2Prompt = `Jsi AI-2 secondary processor - CODE REVIEWER
  ÚKOL - ANALÝZA KÓDU:
  1. Zkontroluj, zda kód odpovídá původnímu požadavku
  2. Ověř, že kód je kompletní a funkční
  DŮLEŽITÉ: Zaměř se na FUNKČNOST kódu, ne na teorii`;
}
```

#### AI-0 (Final Validator) - Finální Code Reviewer
```javascript
if (isCodeRequest) {
  validationPrompt = `Jsi AI-0 validator - FINÁLNÍ CODE REVIEWER
  KRITICKÁ VALIDACE KÓDU:
  1. SOULAD S POŽADAVKEM: Odpovídá finální kód přesně původnímu požadavku?
  2. FUNKČNOST: Je kód kompletní a spustitelný?
  DŮLEŽITÉ: Pokud AI modely neposkytly FUNKČNÍ KÓD, ohodnoť jako NEÚSPĚŠNÉ`;
}
```

### Detekce kódových požadavků
```javascript
const isCodeRequest = originalPrompt.toLowerCase().includes('kod') ||
                     originalPrompt.toLowerCase().includes('code') ||
                     originalPrompt.toLowerCase().includes('python') ||
                     originalPrompt.toLowerCase().includes('javascript') ||
                     originalPrompt.toLowerCase().includes('kalkulačka') ||
                     originalPrompt.toLowerCase().includes('calculator');
```

## DRUHÉ KRITICKÉ VYLEPŠENÍ - Udržení zaměření na původní zadání

### Problém
AI modely se v dalších iteracích odchylovaly od původního zadání - místo vylepšování kalkulačky dělaly sluneční soustavu nebo Conway's Game of Life.

### Řešení
Přidáno explicitní připomínání původního požadavku ve všech promptech:

#### AI-0 (Kalkulačka specificky)
```javascript
if (isCalculator) {
  optimizationPrompt = `Vytvoř optimalizovaný prompt pro AI-1, který jasně specifikuje:
  "Napiš kompletní funkční Python 3 kód pro jednoduchou kalkulačku, která:
  1. Umožňuje základní matematické operace (+, -, *, /)
  2. Má jednoduché uživatelské rozhraní (input/print)
  3. Běží v nekonečné smyčce dokud uživatel nezadá 'quit'
  4. Zpracovává chyby (dělení nulou, neplatný vstup)
  5. Je jednoduchá, rychlá a čitelná"`;
}
```

#### AI-1 (Programátor s omezením)
```javascript
ai1Prompt = `PŮVODNÍ POŽADAVEK UŽIVATELE: "${originalPrompt}"
KRITICKÉ INSTRUKCE:
- VŽDY se drž původního požadavku: "${originalPrompt}"
- Napiš POUZE kód pro KALKULAČKU, nic jiného!
- NIKDY neměň typ aplikace - vždy jen kalkulačka!`;
```

#### AI-2 (Code Reviewer s kontrolou)
```javascript
ai2Prompt = `PŮVODNÍ POŽADAVEK UŽIVATELE: "${originalPrompt}"
KRITICKÉ INSTRUKCE:
- VŽDY se drž původního požadavku: "${originalPrompt}"
- Analyzuj POUZE kód kalkulačky, nic jiného!
- Pokud AI-1 napsal jiný typ aplikace než kalkulačku, JASNĚ to řekni a požaduj opravu
- NIKDY nenavrhuj jiný typ aplikace než kalkulačku!`;
```

### Klíčové změny:
1. **Explicitní připomínání** původního požadavku v každém promptu
2. **Zakázání změny typu aplikace** - AI nesmí navrhovat jiné aplikace
3. **Kontrola souladu** - AI-2 musí kontrolovat, zda AI-1 dodržel zadání
4. **Specifické instrukce pro kalkulačku** - přesná specifikace funkcí

### Testování s Claude Sonnet 3.7
Nyní otestuj se zadáním: **"ukaz mi jednoduchy kod python3 na kalkulacku, chci jednoduchy a rychly kod!"**

Všechny AI modely nastav na **claude-3-7-sonnet-latest** a spusť test s 2-3 iteracemi.

**Očekávaný výsledek:**
- Iterace 1: Python kalkulačka
- Iterace 2: Vylepšená Python kalkulačka (lepší error handling)
- Iterace 3: Další vylepšená Python kalkulačka (lepší UI)
- Validace: "Úspěšně splněno - dodán funkční kód kalkulačky"

## TŘETÍ KRITICKÉ VYLEPŠENÍ - Obecný univerzální systém

### Problém
Předchozí implementace byla příliš specifická pro kalkulačku a nebyla univerzální pro různé typy kódu.

### Řešení - Obecný workflow
Vytvořen univerzální systém pro jakýkoliv typ programovacího úkolu:

#### Nový workflow:
1. **AI-0**: Optimalizuje původní požadavek → vytvoří strukturované zadání
2. **AI-1**: Vidí optimalizované zadání od AI-0 → napíše kód podle zadání
3. **AI-2**: Vidí optimalizované zadání od AI-0 + kód od AI-1 → analyzuje a navrhne vylepšení
4. **AI-1**: Vidí optimalizované zadání od AI-0 + doporučení od AI-2 → vylepší kód
5. **AI-0**: Validuje podle optimalizovaného zadání → hodnotí úspěch

#### AI-0 (Univerzální optimizer)
```javascript
optimizationPrompt = `Vytvoř jasný a strukturovaný prompt pro AI-1, který:
1. Jasně specifikuje programovací jazyk a typ aplikace
2. Definuje konkrétní funkcionalitu a požadavky
3. Specifikuje očekávaný výstup (kompletní funkční kód)
4. Obsahuje technické požadavky (jednoduchost, rychlost, čitelnost)
5. Zdůrazní, že výstupem má být POUZE FUNKČNÍ KÓD bez vysvětlení`;
```

#### AI-1 (Programátor s optimalizovaným zadáním)
```javascript
ai1Prompt = `PŮVODNÍ POŽADAVEK UŽIVATELE: "${originalPrompt}"
OPTIMALIZOVANÉ ZADÁNÍ OD AI-0: "${optimizedPrompt}"

KRITICKÉ INSTRUKCE:
- VŽDY se drž optimalizovaného zadání od AI-0
- Napiš POUZE funkční kód podle zadání, nic jiného!
- NIKDY neměň typ aplikace - drž se zadání od AI-0!`;
```

#### AI-2 (Code Reviewer s optimalizovaným zadáním)
```javascript
ai2Prompt = `PŮVODNÍ POŽADAVEK UŽIVATELE: "${originalPrompt}"
OPTIMALIZOVANÉ ZADÁNÍ OD AI-0: "${optimizedPrompt}"
KÓD OD AI-1: ${codeFromAI1}

KRITICKÉ INSTRUKCE:
- VŽDY se drž optimalizovaného zadání od AI-0
- Analyzuj kód podle zadání od AI-0, nic jiného!
- NIKDY nenavrhuj jiný typ aplikace než je v zadání od AI-0!`;
```

### Klíčové výhody:
1. **Univerzálnost** - funguje pro jakýkoliv typ kódu (kalkulačka, hra, web app, atd.)
2. **Konzistence** - všichni AI vidí stejné optimalizované zadání od AI-0
3. **Kontrola** - AI-2 kontroluje podle zadání od AI-0, ne podle vlastních nápadů
4. **Iterativní zlepšování** - AI-1 a AI-2 spolupracují na vylepšování podle zadání

### Testování s různými úkoly:
- **Kalkulačka**: "ukaz mi jednoduchy kod python3 na kalkulacku"
- **Hra**: "naprogramuj jednoduchou hru v pythonu"
- **Web scraper**: "vytvoř python script na stahování webových stránek"
- **Database**: "naprogramuj jednoduchou databázi v pythonu"

Všechny AI modely nastav na **claude-3-7-sonnet-latest** a spusť test s 2-3 iteracemi.

## ČTVRTÉ KRITICKÉ VYLEPŠENÍ - Zobrazení finálního kódu

### Problém
Uživatel viděl pouze finální validaci od AI-0, ale ne samotný finální kód, který je hlavním výsledkem!

### Řešení
Přidáno zobrazení finálního kódu s praktickými funkcemi:

#### Nová sekce "Finální kód"
- **Zobrazení kódu**: Finální kód z poslední iterace AI-1 v čitelném formátu
- **Metadata**: Informace o iteraci a čase vytvoření
- **Kopírování**: Tlačítko pro kopírování kódu do schránky
- **Styling**: Syntax highlighting a čitelné formátování

#### Implementace
```javascript
// Extrakce finálního kódu po dokončení
if (isCodeRequest && this.ai1Messages.length > 0) {
  const lastAI1Message = this.ai1Messages[this.ai1Messages.length - 1];
  this.finalCode = {
    content: lastAI1Message.content,
    iteration: lastAI1Message.iteration,
    timestamp: lastAI1Message.timestamp
  };
}
```

#### UI komponenty
- **Finální kód** (zelené ohraničení) - hlavní výsledek
- **Finální validace** (zlaté ohraničení) - hodnocení od AI-0

#### Funkce kopírování
```javascript
async copyCodeToClipboard() {
  await navigator.clipboard.writeText(this.finalCode.content);
  // Fallback pro starší prohlížeče
}
```

### Výsledek
Uživatel nyní vidí:
1. **💻 Finální kód** - skutečný výsledek s tlačítkem kopírování
2. **✨ Finální validace** - hodnocení kvality od AI-0

### Testování
Po dokončení komunikace mezi LLM uvidíš:
- Zelený box s finálním kódem
- Tlačítko "📋 Kopírovat" pro okamžité použití
- Zlatý box s validací kvality

## PÁTÉ KRITICKÉ VYLEPŠENÍ - Zvýšené timeouty pro Ollama

### Problém
Ollama modely potřebují více času na zpracování než cloud modely, ale měly pouze výchozí timeout, což vedlo k 500 chybám.

### Řešení
Zvýšeny timeouty pro všechny Ollama instance na 300 sekund (5 minut):

#### Před opravou
```javascript
// Žádný timeout - používal se výchozí (obvykle 30s)
async with aiohttp.ClientSession() as session:
```

#### Po opravě
```javascript
// Zvýšený timeout pro Ollama (300 sekund = 5 minut)
timeout = aiohttp.ClientTimeout(total=300)
async with aiohttp.ClientSession(timeout=timeout) as session:
```

### Opravené metody
1. **`_generate_ollama()`** - Ollama generate API
2. **`_chat_completion_ollama()`** - Ollama chat API
3. **`_generate_ollama2()`** - Ollama2 generate API
4. **`_chat_completion_ollama2()`** - Ollama2 chat API

### Konzistence s LM Studio
Nyní mají všechny lokální LLM providery stejný timeout:
- **LM Studio**: 300 sekund ✅ (už bylo)
- **Ollama**: 300 sekund ✅ (nově přidáno)
- **Ollama2**: 300 sekund ✅ (nově přidáno)

### Timeout hierarchie
1. **Frontend**: 300 sekund (`chat.service.js`)
2. **Backend Ollama**: 300 sekund (`llm_manager.py`)
3. **Cloud API**: 60-120 sekund (podle providera)

### Testování
Nyní můžeš používat pomalé Ollama modely v komunikaci mezi LLM bez timeout chyb:
- Velké modely (32B+) mají dostatek času
- Komplexní prompty se zpracují bez přerušení
- Iterativní komunikace funguje spolehlivě

Implementace je nyní plně univerzální a připravená k produkčnímu použití pro jakýkoliv typ programovacího úkolu s kompletním zobrazením výsledků a spolehlivými timeouty pro všechny LLM providery.
