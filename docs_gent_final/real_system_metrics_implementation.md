# 🔧 Implementace skutečných systémových metrik

## 🚨 Problém s hardcoded metrikami

**Původní stav v Tests & Debug:**
- CPU Usage: 45% (hardcoded)
- Memory Usage: 62% (hardcoded)  
- Disk Usage: 78% (hardcoded)
- Uptime: "5d 12h" (hardcoded)

**Skutečné hodnoty systému:**
- CPU Usage: ~0.3% (32 jader, load average 0.19)
- Memory Usage: 9.0% (4.5GB/62.9GB)
- Disk Usage: 64.0% (62.6GB/97.9GB)
- Uptime: 52d 17h 1m

## ✅ Řešení - Skutečné systémové metriky

### 1. Nový API endpoint pro systémové metriky

**Soubor:** `gent/api/app/routes/system_metrics_routes.py`

**Funkce:**
- `get_cpu_usage()` - skutečné využití CPU pomocí psutil
- `get_memory_usage()` - skute<PERSON><PERSON><PERSON> využití RAM
- `get_disk_usage()` - skute<PERSON><PERSON><PERSON> využití disku
- `get_system_uptime()` - skutečný uptime systému
- `get_process_info()` - informace o GENT procesech

**Endpointy:**
- `GET /api/system/metrics` - detailní systémové metriky
- `GET /api/system/health` - zdravotní stav systému s metrikami

### 2. Integrace do API aplikace

**Soubor:** `gent/api/app/app.py`
- Přidán import: `from gent.api.app.routes.system_metrics_routes import router as system_metrics_router`
- Registrován router: `app.include_router(system_metrics_router)`

### 3. Aktualizace frontendu

**Soubor:** `frontend-vue/src/views/admin/TestsDebug.vue`

**Změny:**
- Nahrazeny hardcoded hodnoty za 0 (načítají se z API)
- Přidána metoda `loadSystemMetrics()` pro načítání skutečných dat
- Přidána metoda `updateSystemComponents()` pro aktualizaci komponent
- Automatické načítání při mount komponenty
- Pravidelné aktualizace každých 30 sekund
- Vyčištění intervalu při unmount

## 🎯 Výsledek

### Skutečné hodnoty v Tests & Debug:
```json
{
  "cpu": {
    "usage_percent": 0.3,
    "cores": 32,
    "load_average": [0.31, 0.24, 0.18]
  },
  "memory": {
    "usage_percent": 9.0,
    "total_gb": 62.9,
    "used_gb": 4.5,
    "free_gb": 57.2
  },
  "disk": {
    "usage_percent": 64.0,
    "total_gb": 97.9,
    "used_gb": 62.6,
    "free_gb": 30.2
  },
  "uptime": {
    "uptime_formatted": "52d 17h 1m",
    "boot_time": "2025-04-08T13:22:36"
  }
}
```

### Zdravotní stav komponent:
- **CPU**: healthy (< 80%)
- **Memory**: healthy (< 80%)
- **Disk**: healthy (< 80%)
- **Overall**: healthy

### GENT procesy:
- Frontend (Vite): 2 procesy
- API Server (Python): 1 proces  
- PostgreSQL: 1 proces
- MCP Server: 2 procesy

## 🔧 Technické detaily

### Použité knihovny:
- **psutil** - pro systémové metriky (CPU, RAM, disk, procesy)
- **datetime** - pro uptime kalkulace
- **os** - pro load average

### Automatické aktualizace:
- **Interval**: 30 sekund
- **Endpoint**: `/api/system/health`
- **Fallback**: při chybě se zobrazí původní hodnoty

### Zdravotní stavy:
- **healthy**: < 80% využití
- **warning**: 80-95% využití  
- **critical**: > 95% využití

## 🚀 Použití

**Tests & Debug stránka nyní zobrazuje:**
- ✅ Skutečné CPU využití (~0.3%)
- ✅ Skutečné RAM využití (~9%)
- ✅ Skutečné disk využití (~64%)
- ✅ Skutečný uptime (52+ dní)
- ✅ Automatické aktualizace každých 30s
- ✅ Zdravotní stav komponent

**API endpoint pro externí použití:**
```bash
curl http://192.168.14.150:8001/api/system/health
```

**Diagnostika je nyní založena na skutečných datech místo hardcoded hodnot!** 🎯
