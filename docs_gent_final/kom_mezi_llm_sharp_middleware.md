# Kom mezi LLM - Ostré Middleware úpravy

## Problém identifikován

AI agenti ignorovali middleware instrukce a:
- ❌ Vyplivovali celé řešení najednou
- ❌ Nepřidávali postupně po jednom kroku
- ❌ Ignorovali "TVŮJ KONKRÉTNÍ ÚKOL PRO TUTO ITERACI"
- ❌ Finální odpověď byla stejná jako první verze

## Řešení: OSTRÉ direktivy

### **1. Middleware s 🚨 STOP signály**

#### PŘED:
```javascript
TVŮJ KONKRÉTNÍ ÚKOL PRO TUTO ITERACI:
🎯 Zaměř se na: Implementovat logiku
🔧 Vytvoř konk<PERSON>í <PERSON> (ne plány)
```

#### PO:
```javascript
🚨 STOP! PŘEČTI POZORNĚ! 🚨

ITERACE: 2/5
PŮVODNÍ ÚKOL: "Napiš kalkula<PERSON>"

🔍 PŘEČTI POUZE POSLEDNÍ ŘÁDEK OD PŘEDCHOZÍ AI:
"def calculator():"

⚠️ KRITICKÉ INSTRUKCE ⚠️
1. PŘIDEJ POUZE JEDNU VĚC NAVÍC k tomu, co už je napsáno
2. POKUD NAPÍŠEŠ VÍC NEŽ 2 VĚTY, SELHAL JSI
3. NEPIŠ CELÉ ŘEŠENÍ ZNOVU - jen JEDEN KROK DOPŘEDU

TVŮJ JEDINÝ ÚKOL PRO TUTO ITERACI:
🎯 Přidej POUZE: jeden řádek kódu (input)

🚨 PAMATUJ: POUZE JEDEN KROK! MAXIMÁLNĚ 2 VĚTY! 🚨
```

### **2. Ostré AI-1 prompty**

#### PŘED:
```javascript
ÚKOL: Napiš KOMPLETNÍ FUNKČNÍ KÓD podle zadání
Vrať pouze kompletní funkční kód podle zadání od AI-0.
```

#### PO:
```javascript
🚨 KRITICKÉ INSTRUKCE - PŘEČTI POZORNĚ! 🚨

⚠️ PRVNÍ ITERACE - ZAČNI JEDNODUŠE:
- Napiš POUZE první řádek kódu (import, def, class)
- NEPIŠ celý program najednou
- Maximálně 1-2 řádky kódu
- Žádné vysvětlení, jen kód

ABSOLUTNĚ ZAKÁZÁNO:
❌ Psát celý program najednou
❌ Více než 2 řádky kódu
❌ Vysvětlení nebo komentáře
❌ "budu pokračovat" nebo podobné fráze

🚨 POKUD NAPÍŠEŠ VÍC NEŽ 2 ŘÁDKY, SELHAL JSI! 🚨
```

### **3. Ostré AI-2 prompty**

#### PŘED:
```javascript
ÚKOL - ANALÝZA KÓDU:
1. Zkontroluj, zda kód odpovídá zadání
2. Ověř, že kód je kompletní a funkční
3. Identifikuj chybějící funkce
4. Navrhni konkrétní vylepšení
5. Doporuč, co přidat v další iteraci
```

#### PO:
```javascript
🚨 KRITICKÉ INSTRUKCE - PŘEČTI POZORNĚ! 🚨

TVŮJ JEDINÝ ÚKOL:
Řekni POUZE jednu konkrétní věc, co má AI-1 přidat v další iteraci.

PŘÍKLAD SPRÁVNÉ ODPOVĚDI:
"Přidej řádek: input_value = int(input())"
"Přidej podmínku: if x > 0:"

ABSOLUTNĚ ZAKÁZÁNO:
❌ Více než 1 věta
❌ Obecné rady typu "vylepši", "rozviň"
❌ Seznam doporučení
❌ Analýza nebo hodnocení

🚨 POKUD NAPÍŠEŠ VÍC NEŽ 1 VĚTU, SELHAL JSI! 🚨
```

### **4. Ostřejší detekce porušení**

```javascript
detectRepeatedConcepts(messages) {
  const violations = [];
  const bannedPhrases = [
    'budu pokračovat', 'implementuji doporučení', 'rozvijem',
    'v další iteraci', 'postupně', 'krok za krokem',
    'komplexní řešení', 'detailní analýza', 'kompletní'
  ];
  
  messages.forEach((msg, index) => {
    // Kontrola zakázaných frází
    bannedPhrases.forEach(phrase => {
      if (msg.toLowerCase().includes(phrase.toLowerCase())) {
        violations.push(`❌ ZAKÁZÁNO: "${phrase}"`);
      }
    });
    
    // Kontrola délky (více než 3 věty = porušení)
    const sentences = msg.split(/[.!?]+/).filter(s => s.trim().length > 0);
    if (sentences.length > 3) {
      violations.push(`❌ PŘÍLIŠ DLOUHÉ: ${sentences.length} vět (max 2)`);
    }
    
    // Kontrola opakování celých bloků
    if (index > 0 && msg.length > 100 && messages[index-1].includes(msg.substring(0, 50))) {
      violations.push(`❌ OPAKOVÁNÍ: Přepisování předchozí odpovědi`);
    }
  });
  
  return [...new Set(violations)];
}
```

### **5. Konkrétnější úkoly**

```javascript
generateNewAspects(agent, iteration, progress) {
  if (agent === 'AI-1') {
    if (iteration === 1) {
      return [`🎯 Napiš POUZE první řádek kódu/první větu odpovědi`];
    } else {
      return [`🎯 Přidej POUZE: ${progress.missing[0]} (jeden řádek)`];
    }
  } else if (agent === 'AI-2') {
    if (iteration === 1) {
      return [`🔍 Řekni POUZE jednu konkrétní věc, co chybí`];
    } else {
      return [`🔍 Navrhni POUZE jednu konkrétní změnu (ne seznam)`];
    }
  }
}
```

## Očekávané zlepšení

### **PŘED ostrými úpravami:**
```
Iterace 1: AI-1 napíše celý program
Iterace 2: AI-1 přepíše celý program s drobnými změnami
Iterace 3: AI-1 znovu přepíše celý program
Výsledek: Stejný kód jako v iteraci 1
```

### **PO ostrých úpravách:**
```
Iterace 1: AI-1 napíše "def calculator():"
Iterace 2: AI-1 přidá "    x = int(input())"
Iterace 3: AI-1 přidá "    y = int(input())"
Iterace 4: AI-1 přidá "    return x + y"
Výsledek: Postupně vybudovaný funkční kód
```

## Klíčové změny

### ✅ **Vizuální upozornění**
- 🚨 STOP signály
- ⚠️ Kritické instrukce
- ❌ Zakázané akce
- ✅ Povolené akce

### ✅ **Konkrétní limity**
- Maximálně 2 řádky kódu
- Maximálně 1-2 věty textu
- Pouze jeden krok dopředu
- Žádné přepisování

### ✅ **Ostré sankce**
- "POKUD NAPÍŠEŠ VÍC, SELHAL JSI!"
- Explicitní porušení = selhání
- Jasné hranice úspěchu/neúspěchu

### ✅ **Konkrétní příklady**
- Ukázky správných odpovědí
- Konkrétní formát výstupu
- Jasné instrukce co dělat

## Status

✅ Ostré middleware implementováno
✅ AI-1 prompty zpřísněny
✅ AI-2 prompty zpřísněny  
✅ Detekce porušení rozšířena
✅ Konkrétní úkoly definovány
✅ Připraveno k testování

Nyní by AI agenti měli skutečně postupovat krok za krokem a vytvářet postupně se zlepšující řešení!
