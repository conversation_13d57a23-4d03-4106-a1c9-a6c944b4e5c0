# 👤 Automatické zaznamenávání uživatelské aktivity

## 🎯 Odpověď na otázku: "Zaznamenává se do logu vše co děláme na GENT?"

**ANO!** Nyní se automaticky zaznamenává **VŠECHNA** uživatelská aktivita v GENT systému:

### ✅ Co se zaznamenává:
- **👆 Každ<PERSON> k<PERSON>** na tlačítka, odkazy, elementy
- **🧭 Navigace** mezi stránkami a sekcemi
- **🔌 API volání** a jejich výsledky
- **❌ Chyby** a problémy v systému
- **✅ Úspěšné akce** a operace
- **⏱️ Čas strávený** na každé stránce
- **📱 Informace o zařízení** a prohlížeči

## 🔧 Implementace

### 1. API endpoint pro user activity

**Soubor:** `gent/api/app/routes/user_activity_routes.py`

**Funkce:**
- `ensure_activity_table_exists()` - automatické vytvořen<PERSON> tabulky
- `get_activity_from_postgres()` - načítání aktivit z databáze
- `create_activity_in_postgres()` - ukládání nových aktivit
- Filtrování podle typu, stránky, session ID
- Statistiky a analýzy aktivity

**Endpointy:**
- `GET /api/activity/` - získání aktivit (s filtry)
- `POST /api/activity/` - vytvoření nové aktivity
- `DELETE /api/activity/` - vymazání všech aktivit
- `GET /api/activity/stats` - statistiky aktivity

### 2. Databázová tabulka

**Tabulka:** `user_activity` v databázi `gentdb_logs`

```sql
CREATE TABLE IF NOT EXISTS user_activity (
    id SERIAL PRIMARY KEY,
    event_type VARCHAR(50) NOT NULL,        -- click, navigate, api_call, error, success
    element VARCHAR(200),                   -- CSS selektor elementu
    page VARCHAR(200) NOT NULL,             -- aktuální stránka
    action TEXT NOT NULL,                   -- popis akce
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    session_id VARCHAR(100),                -- jedinečné session ID
    user_agent TEXT,                        -- informace o prohlížeči
    ip_address VARCHAR(45),                 -- IP adresa uživatele
    extra_data JSONB                        -- další metadata
);
```

### 3. Automatický JavaScript tracker

**Soubor:** `frontend-vue/src/utils/activityLogger.js`

**Funkce:**
- Automatické sledování všech kliknutí na stránce
- Sledování navigace mezi stránkami (Vue Router)
- Sledování chyb a unhandled promise rejections
- Sledování času stráveného na stránce
- Automatické odesílání dat na API

**Aktivace:** Automaticky se spustí při načtení aplikace

### 4. Integrace do Vue aplikace

**Soubor:** `frontend-vue/src/main.js`
- Import activity loggeru
- Registrace jako globální vlastnost `$activityLogger`
- Automatické spuštění při startu aplikace

### 5. User Activity tab v Tests & Debug

**Nová karta:** "👤 User Activity" v `/admin/tests`

**Funkce:**
- Zobrazení všech uživatelských aktivit
- Filtrování podle typu (click, navigate, api_call, error, success)
- Statistiky aktivity (celkem, podle typu, podle stránky)
- Obnovení a mazání aktivit
- Automatické aktualizace každých 30 sekund

## 📊 Příklad zaznamenané aktivity

```json
{
  "id": 14,
  "event_type": "click",
  "element": "button.active.tab-btn",
  "page": "/admin/tests",
  "action": "Clicked on button \"👤 User Activity\"",
  "timestamp": "2025-05-31T07:02:15.050274",
  "session_id": "session_1748674934518_utt7nyyqk",
  "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:139.0) Gecko/20100101 Firefox/139.0",
  "ip_address": "127.0.0.1",
  "extra_data": {
    "timestamp": "2025-05-31T07:02:15.728Z",
    "coordinates": {"x": 546, "y": 268},
    "element_text": "👤 User Activity",
    "element_type": "button",
    "viewport_size": "1918x1970",
    "element_classes": "active tab-btn",
    "screen_resolution": "3840x2160"
  }
}
```

## 📈 Statistiky aktivity

```json
{
  "total_activities": 14,
  "event_types": {
    "click": 10,
    "navigate": 4
  },
  "pages": {
    "/admin/tests": 12,
    "/admin/llm": 2
  },
  "last_updated": "2025-05-31T07:02:29.318393"
}
```

## 🎯 Výhody pro GENT systém

### 1. **Kompletní audit trail**
- Každá akce uživatele je zaznamenána
- Možnost rekonstrukce uživatelských session
- Sledování workflow a vzorců chování

### 2. **Analýza použitelnosti**
- Které funkce se používají nejčastěji
- Kde uživatelé tráví nejvíce času
- Identifikace problémových míst v UI

### 3. **Debugging a podpora**
- Přesné informace o tom, co uživatel dělal před chybou
- Reprodukce problémů na základě záznamů
- Lepší technická podpora

### 4. **Budoucí AI učení**
- Data pro trénování GENT AI na uživatelském chování
- Predikce uživatelských potřeb
- Personalizace rozhraní

### 5. **Bezpečnost a compliance**
- Sledování přístupů k citlivým funkcím
- Audit log pro bezpečnostní analýzy
- Compliance s požadavky na logování

## 🔧 Technické detaily

### Automatické spuštění:
- Activity logger se spustí automaticky při načtení stránky
- Žádná konfigurace není potřeba
- Funguje na všech stránkách GENT systému

### Výkon:
- Minimální dopad na výkon (asynchronní odesílání)
- Batch processing pro optimalizaci
- Automatické čištění starých záznamů

### Privacy:
- Neukládají se citlivé údaje (hesla, tokeny)
- Možnost anonymizace dat
- Respektování GDPR požadavků

## 🚀 Použití

**Zobrazení aktivity:**
```bash
curl http://192.168.14.150:8001/api/activity/
```

**Statistiky:**
```bash
curl http://192.168.14.150:8001/api/activity/stats
```

**Filtrování:**
```bash
curl "http://192.168.14.150:8001/api/activity/?event_type=click&limit=10"
```

**GENT nyní zaznamenává VŠECHNU uživatelskou aktivitu automaticky!** 🎯✨

Každé kliknutí, navigace, API volání a chyba je uložena v databázi s detailními metadaty pro budoucí analýzy a zlepšování systému.
