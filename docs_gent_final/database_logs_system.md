# 📝 Systém logů z databáze - Tests & Debug

## 🚨 Problém s hardcoded logy

**Původní stav v Tests & Debug:**
- Logy byly hardcoded v JavaScriptu
- Žádné připojení k databázi
- Nemožnost vytváření, mazání nebo filtrování logů
- Statická data bez možnosti aktualizace

**Hardcoded logy:**
```javascript
logs: [
  { id: 1, timestamp: new Date(), level: 'info', source: 'API', message: 'Server started successfully' },
  { id: 2, timestamp: new Date(), level: 'warning', source: 'LLM', message: 'Model response time exceeded threshold' },
  { id: 3, timestamp: new Date(), level: 'error', source: 'Database', message: 'Connection timeout' }
]
```

## ✅ Řešení - Skutečný logging systém

### 1. Nový API endpoint pro logy

**Soubor:** `gent/api/app/routes/logs_routes.py`

**Funkce:**
- `ensure_log_table_exists()` - automatické vytvořen<PERSON> tabulky
- `get_logs_from_postgres()` - načítání logů z databáze
- `create_log_in_postgres()` - vytváření nových logů
- Filtrování podle úrovně (info, warning, error, debug)
- Mazání všech logů

**Endpointy:**
- `GET /api/logs/` - získání logů (s filtrem podle úrovně)
- `POST /api/logs/` - vytvoření nového logu
- `DELETE /api/logs/` - vymazání všech logů
- `GET /api/logs/levels` - seznam dostupných úrovní

### 2. Databázová tabulka

**Tabulka:** `log_entries` v databázi `gentdb_logs`

```sql
CREATE TABLE IF NOT EXISTS log_entries (
    id SERIAL PRIMARY KEY,
    level VARCHAR(20) NOT NULL,
    message TEXT NOT NULL,
    source VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    extra_data JSONB
);

CREATE INDEX IF NOT EXISTS idx_log_entries_level ON log_entries(level);
CREATE INDEX IF NOT EXISTS idx_log_entries_created_at ON log_entries(created_at);
CREATE INDEX IF NOT EXISTS idx_log_entries_source ON log_entries(source);
```

### 3. Integrace do API aplikace

**Soubor:** `gent/api/app/app.py`
- Přidán import: `from gent.api.app.routes.logs_routes import router as logs_router`
- Registrován router: `app.include_router(logs_router)`

### 4. Aktualizace frontendu

**Soubor:** `frontend-vue/src/views/admin/TestsDebug.vue`

**Změny:**
- Nahrazeny hardcoded logy za prázdný array
- Přidána metoda `loadLogs()` pro načítání z API
- Přidána metoda `refreshLogs()` pro obnovení
- Přidána metoda `clearLogs()` pro mazání všech logů
- Přidána metoda `createTestLog()` pro vytvoření testovacího logu
- Automatické načítání při mount komponenty
- Pravidelné aktualizace každých 30 sekund
- Nové tlačítko "➕ Test Log" pro vytvoření testovacího logu

## 🎯 Výsledek

### Funkční logging systém:
```json
[
  {
    "id": 3,
    "level": "error",
    "message": "Database connection timeout",
    "source": "Database",
    "timestamp": "2025-05-31T06:51:53.563230",
    "extra_data": {"error_code": "TIMEOUT"}
  },
  {
    "id": 2,
    "level": "warning",
    "message": "High memory usage detected",
    "source": "System",
    "timestamp": "2025-05-31T06:51:47.853760",
    "extra_data": {"memory_percent": 85}
  },
  {
    "id": 1,
    "level": "info",
    "message": "API server started successfully",
    "source": "API",
    "timestamp": "2025-05-31T06:51:41.159330",
    "extra_data": null
  }
]
```

### Funkce v Tests & Debug:
- ✅ **Načítání logů** z PostgreSQL databáze
- ✅ **Filtrování** podle úrovně (all, info, warning, error, debug)
- ✅ **Vytváření** testovacích logů tlačítkem "➕ Test Log"
- ✅ **Obnovení** logů tlačítkem "🔄 Obnovit"
- ✅ **Mazání** všech logů tlačítkem "🗑️ Vymazat"
- ✅ **Automatické aktualizace** každých 30 sekund
- ✅ **Správné formátování** času v českém formátu

## 🔧 Technické detaily

### Databázové připojení:
- **Host**: localhost
- **Port**: 5432
- **Database**: gentdb_logs
- **User**: postgres
- **Password**: z `config/postgres_credentials.json`

### API endpointy:
```bash
# Získání všech logů
curl http://localhost:8001/api/logs/

# Filtrování podle úrovně
curl http://localhost:8001/api/logs/?level=error

# Vytvoření nového logu
curl -X POST http://localhost:8001/api/logs/ \
  -H "Content-Type: application/json" \
  -d '{"level": "info", "message": "Test message", "source": "API"}'

# Vymazání všech logů
curl -X DELETE http://localhost:8001/api/logs/
```

### Automatické vytvoření tabulky:
- Tabulka se vytvoří automaticky při prvním volání API
- Indexy pro optimalizaci výkonu
- JSONB pole pro extra_data

## 🚀 Použití

**Tests & Debug stránka nyní:**
- ✅ Načítá skutečné logy z databáze
- ✅ Umožňuje vytváření testovacích logů
- ✅ Podporuje filtrování podle úrovně
- ✅ Automaticky se aktualizuje
- ✅ Umožňuje mazání logů

**API endpoint pro externí použití:**
```bash
curl http://**************:8001/api/logs/
```

**Logging systém je nyní plně funkční s databázovým úložištěm!** 🎯
