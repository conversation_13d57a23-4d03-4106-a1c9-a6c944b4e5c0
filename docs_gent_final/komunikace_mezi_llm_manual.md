# Komunikace mezi LLM - Kompletní manuál

## <PERSON><PERSON><PERSON><PERSON> systému

Funkce "Komunikace mezi LLM" umožňuje orchestrovanou spolupráci mezi třemi AI modely pro vytváření kvalitního kódu nebo řešení komplexních úkolů. Systém používá iterativní přístup s validací a vylepšováním.

## Architektura systému

### Účastníci komunikace
- **AI-0 (Prompt Optimizer)** - Optimalizuje zadání pro lepší pochopení
- **AI-1 (Primary Processor)** - <PERSON><PERSON>n<PERSON> procesor, vytv<PERSON><PERSON><PERSON>
- **AI-2 (Secondary Processor)** - Analyzuje a navrhuje vylepšení
- **Mem0** - Sdílená paměť pro výměnu dat mezi iteracemi

### Workflow procesu
```
Uživatel → AI-0 → AI-1 → AI-2 → AI-1 → AI-2 → ... → AI-0 (validace)
           ↓      ↓      ↓      ↓      ↓              ↓
         Mem0   Mem0   Mem0   Mem0   Mem0           Finální výsledek
```

## Detailní proces

### Fáze 1: Optimalizace zadání (AI-0)
1. **Vstup**: Původní požadavek od uživatele
2. **Proces**: AI-0 analyzuje a strukturuje zadání
3. **Výstup**: Optimalizovaný prompt pro AI-1 a AI-2
4. **Uložení**: Optimalizovaný prompt → Mem0

### Fáze 2: Iterativní zpracování (AI-1 ↔ AI-2)
#### Iterace 1:
- **AI-1**: Zpracuje optimalizovaný prompt → vytvoří řešení
- **AI-2**: Analyzuje řešení AI-1 → navrhne vylepšení
- **Uložení**: Oba výstupy → Mem0

#### Iterace 2-N:
- **AI-1**: Načte doporučení AI-2 z Mem0 → vylepší řešení
- **AI-2**: Analyzuje nové řešení → navrhne další vylepšení
- **Uložení**: Oba výstupy → Mem0

### Fáze 3: Finální validace (AI-0)
1. **Vstup**: Všechny výsledky z iterací
2. **Proces**: AI-0 validuje soulad s původním zadáním
3. **Výstup**: Finální hodnocení a shrnutí

## Uživatelské rozhraní

### Nastavení komunikace
1. **Prompt**: Zadejte svůj požadavek (např. "naprogramuj kalkulačku v pythonu")
2. **AI-0 Model**: Vyberte model pro optimalizaci promptu
3. **AI-1 Model**: Vyberte model pro hlavní zpracování
4. **AI-2 Model**: Vyberte model pro analýzu a vylepšení
5. **Počet iterací**: Nastavte 2-5 iterací (doporučeno 3)

### Spuštění procesu
1. Klikněte "Spustit komunikaci"
2. Sledujte real-time progress
3. Čekejte na dokončení všech fází

### Výsledky
Po dokončení uvidíte:
- **💻 Finální kód** (zelený box) - hlavní výsledek s tlačítkem kopírování
- **✨ Finální validace** (zlatý box) - hodnocení kvality od AI-0

## Doporučené nastavení

### Pro programovací úkoly:
- **AI-0**: Claude Sonnet 3.7 (nejlepší pro optimalizaci)
- **AI-1**: Claude Sonnet 3.7 (silný v programování)
- **AI-2**: Claude Sonnet 3.7 (dobrá analýza kódu)
- **Iterace**: 3

### Pro kreativní úkoly:
- **AI-0**: Claude Sonnet 3.7
- **AI-1**: GPT-4o (kreativita)
- **AI-2**: Claude Sonnet 3.7 (analýza)
- **Iterace**: 2-3

### Pro lokální modely (Ollama):
- **Timeout**: Automaticky 300 sekund
- **Doporučené modely**: qwq:latest, deepseek-r1:32b
- **Iterace**: 2 (kvůli rychlosti)

## Technické specifikace

### Timeouty
- **Frontend**: 300 sekund
- **Backend Ollama**: 300 sekund
- **Cloud API**: 60-120 sekund

### Mem0 integrace
- **Ukládání**: Každý výstup AI modelu
- **Načítání**: Kontext z předchozích iterací
- **Klíče**: `ai0_optimization`, `ai1_iteration_X`, `ai2_iteration_X`

### Error handling
- Chyby se zobrazují v UI (ne alert dialogy)
- Automatické retry pro síťové chyby
- Graceful degradation při Mem0 chybách

## Příklady použití

### Jednoduchý kód
**Zadání**: "naprogramuj jednoduchou kalkulačku v pythonu"
**Očekávaný výsledek**: Kompletní Python script s UI a error handlingem

### Komplexní aplikace
**Zadání**: "vytvoř webovou aplikaci pro správu úkolů s databází"
**Očekávaný výsledek**: HTML, CSS, JavaScript + backend kód

### Optimalizace kódu
**Zadání**: "optimalizuj tento kód pro rychlost: [vložit kód]"
**Očekávaný výsledek**: Vylepšený kód s benchmark analýzou

## Troubleshooting

### Časté problémy
1. **Timeout chyby**: Použijte cloud modely nebo zvyšte počet iterací
2. **Odchýlení od zadání**: Zkontrolujte AI-0 optimalizaci
3. **Neúplný kód**: Zvyšte počet iterací nebo změňte AI-1 model

### Optimalizace výkonu
1. **Rychlost**: Méně iterací, cloud modely
2. **Kvalita**: Více iterací, silnější modely
3. **Náklady**: Lokální modely (Ollama)

## Bezpečnost

### Ochrana dat
- Všechna data zůstávají v systému GENT
- Mem0 data jsou dočasná
- Žádné sdílení s externími službami

### Validace vstupů
- Kontrola výběru modelů
- Validace počtu iterací
- Sanitizace promptů

## Monitoring

### Sledování procesu
- Real-time status každé fáze
- Časové značky všech operací
- Error logy v konzoli

### Metriky
- Doba zpracování jednotlivých AI modelů
- Úspěšnost iterací
- Kvalita finálních výsledků

## Nové funkce - Apps Management

### Přidána správa aplikací
- **Nová admin stránka**: `/admin/apps` - Apps Management
- **Databázové modely**: `apps` a `app_executions` tabulky
- **API endpoints**: `/api/apps/*` pro CRUD operace
- **Ukázková aplikace**: `calendar_benchmark.py` v `/opt/gent/apps/`

### Funkce Apps Management
- **Přehled aplikací**: Karty s detaily, statistikami a akcemi
- **Filtry a vyhledávání**: Podle kategorie, názvu, tagů
- **Spouštění aplikací**: Tlačítko pro spuštění s tracking
- **Správa**: Editace, aktivace/deaktivace aplikací
- **Statistiky**: Počet spuštění, kategorie, aktivní aplikace

### Ukázková aplikace - Calendar Benchmark
- **Soubor**: `/opt/gent/apps/calendar_benchmark.py`
- **Funkce**: Performance analýza kalendářového kódu
- **Kategorie**: benchmark
- **Tagy**: performance, calendar, benchmark, testing
- **Spuštění**: Přes Apps Management nebo přímo z terminálu

---

**Verze**: 2.0
**Datum**: 2025-06-03
**Autor**: GENT v10 System
**Changelog**: Přidána Apps Management funkcionalita
