# 🔧 Řešení problému s externím přístupem k GENT

## 🚨 Problém
Když se připojujete k GENT z jiného zařízení (telefon, jiný PC), web se načte, ale nefunguje připojení k databázi - nenačítají se modely, data z DB apod.

## 🔍 Příčina
Frontend se snažil připojit k API vždy na `localhost:8001`, ale když přistupujete z jiného zařízení na `**************:8000`, API musí být dostupné na `**************:8001`.

## ✅ Jednoduché řešení

### 1. Automatická detekce API URL
**Soubor:** `frontend-vue/src/services/api.service.js`

**Přid<PERSON>a funkce:**
```javascript
function getApiBaseUrl() {
  // Pokud je nastavena VITE_API_URL, použij ji
  if (import.meta.env.VITE_API_URL) {
    return import.meta.env.VITE_API_URL;
  }

  // Automatická detekce podle hostname
  const hostname = window.location.hostname;

  // Pokud je hostname localhost nebo 127.0.0.1, použij localhost
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    return 'http://localhost:8001';
  }

  // Jinak použij stejný hostname jako web, ale port 8001
  return `http://${hostname}:8001`;
}
```

### 2. Oprava Vite proxy
**Soubor:** `frontend-vue/vite.config.js`
- Opraveno proxy z portu 8003 na správný port 8001

### 3. Oprava hardcoded URL
**Soubory:** `frontend-vue/src/views/AgentsTest.vue`, `frontend-vue/src/views/admin/LlmManagement.vue`, `frontend-vue/src/views/admin/DatabaseViewer.vue`, `frontend-vue/src/services/chat.service.js`
- Nahrazeny všechny hardcoded URL `http://localhost:8001/api/...` za relativní URL `/api/...`
- **Klíčová oprava:** `chat.service.js` - endpoint pro odesílání zpráv do LLM
- Nyní se automaticky používá správná IP adresa podle toho, odkud se přistupuje

### 4. Odstranění VITE_API_URL
**Soubory:** `run_frontend.sh`, `gent-frontend.service`
- Zakomentována proměnná `VITE_API_URL=http://localhost:8001`
- Nyní se vždy použije automatická detekce místo hardcoded localhost

## 🎯 Jak to funguje

### Automatická logika:
- **Přístup z localhost** → API: `http://localhost:8001`
- **Přístup z **************** → API: `http://**************:8001`
- **Přístup z jakékoliv IP** → API: `http://[stejná-IP]:8001`

### Porty zůstávají stejné:
- **Web**: VŽDY port 8000
- **API**: VŽDY port 8001
- **PostgreSQL**: VŽDY port 5432

## 🚀 Použití

### Jediný způsob spuštění:
```bash
# Restart service (pokud potřeba)
systemctl restart gent-frontend

# Přístup z jakéhokoliv zařízení:
http://**************:8000
```

### Síťová konfigurace:
- **Frontend**: `0.0.0.0:8000` (dostupný z vnějšku)
- **API**: `0.0.0.0:8001` (dostupný z vnějšku)
- **PostgreSQL**: `0.0.0.0:5432` (dostupný z vnějšku)

## ✅ Výsledek

**Nyní funguje přístup z:**
- ✅ Localhost (VPS)
- ✅ Jiný PC ve stejné síti
- ✅ Telefon
- ✅ Tablet
- ✅ VSCode Port Forwarding

**Databázové připojení i chat s LLM modely funguje správně ze všech zařízení!**

## 🔧 Debug

V konzoli prohlížeče uvidíte:
```
🔗 API Base URL: http://**************:8001
```

Tím můžete ověřit, že se používá správná URL.
