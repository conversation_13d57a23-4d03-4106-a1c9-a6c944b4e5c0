# Ollama2 Integration - Kompletní implementace a opravy

## Přehled

Ollama2 provider byl úspěšně integrován do GENT v10 systému jako druh<PERSON> instance Ollama serveru s plnou funkcionalitą včetně oprav API endpointů a timeoutů pro správnou komunikaci.

## Implementované komponenty

### 1. Backend implementace

#### Rozšířená databázová služba (`gent/db/llm_db_service.py`)
- **Parametrizovaná metoda `add_ollama_provider()`**: Podporuje `provider_name` parametr pro rozlišení více Ollama instancí
- **Automatická detekce modelů**: Načítá modely z Ollama API `/api/tags` endpointu
- **Správné mapování capabilities**: Rozpoznává reasoning a code modely podle názvu

```python
def add_ollama_provider(self, ollama_url: str = "http://***************:11434", provider_name: str = "Ollama") -> bool:
    # Podporuje více Ollama instancí s různými názvy
```

#### Rozšířený LLM Manager (`gent/llm/llm_manager.py`)
- **Ollama2 konfigurace**: Samostatná konfigurace pro druhý server
- **Dedikované metody**: `_generate_ollama2()` a `_chat_completion_ollama2()`
- **Správné API volání**: Použití Ollama specifických endpointů a parametrů

```python
# Ollama2 konfigurace
ollama2_url = self.config.get("ollama2_url") or get_typed_env_var("OLLAMA2_URL", "http://***************:11434", str)
providers["ollama2"] = {
    "api_key": None,
    "base_url": ollama2_url,
    "models": self.config.get("ollama2_models", ["devstral:24b", "qwen3:32b", "gemma3:27b", "qwq:latest", "deepseek-r1:32b"])
}
```

#### Opravené API endpointy (`gent/api/app/routes/llm_direct_db_routes.py`)
- **Správný Ollama endpoint**: `/api/chat` místo `/v1/chat/completions`
- **Ollama specifický payload**: `options.num_predict` místo `max_tokens`
- **Zvýšený timeout**: 120 sekund pro pomalé modely
- **Podpora Ollama2**: Přidána podmínka pro provider "ollama2"

```python
async def call_ollama_api(api_base_url: str, model: str, message: str, temperature: float, max_tokens: int):
    url = f"{api_base_url}/api/chat"  # Správný Ollama endpoint
    
    payload = {
        "model": model,
        "messages": [{"role": "user", "content": message}],
        "stream": False,
        "options": {
            "temperature": temperature,
            "num_predict": max_tokens  # Ollama specifický parametr
        }
    }
    
    # Zvýšený timeout pro Ollama (120 sekund)
    timeout = aiohttp.ClientTimeout(total=120)
```

### 2. Frontend implementace

#### Rozšířený LLM Management (`frontend-vue/src/views/admin/LlmManagement.vue`)
- **Ollama2 tlačítko**: Fialové tlačítko "🦙 Přidat Ollama2" 
- **Automatická synchronizace**: Obnovení dat po přidání provideru
- **Dedikovaná metoda**: `addOllama2Provider()` pro druhý server

```javascript
async addOllama2Provider() {
  const response = await fetch('/api/db/llm/providers/ollama', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      ollama_url: 'http://***************:11434',
      provider_name: 'Ollama2'
    })
  });
}
```

#### Opravený Chat Service (`frontend-vue/src/services/chat.service.js`)
- **Zvýšený timeout**: 120 sekund pro LLM requesty
- **Axios konfigurace**: Explicitní timeout pro pomalé modely

```javascript
// Zvýšený timeout pro LLM requesty (120 sekund)
const response = await axios.post(endpoint, payload, {
  timeout: 120000  // 2 minuty pro pomalé modely jako Ollama
});
```

## Dostupné servery a modely

### Ollama (***************:11434) - Provider ID 35
1. **qwq:latest** (32.8B) - Reasoning model s thinking capabilities
2. **deepseek-r1:32b** (32.8B) - Advanced reasoning model
3. **qwen3:32b** (32.8B) - Code a text model
4. **gemma3:27b** (27.4B) - Základní text model
5. **devstral:latest** (23.6B) - Development focused model

### Ollama2 (***************:11434) - Provider ID 40
1. **devstral:24b** (23.6B) - ✅ **RYCHLÝ** (378ms) - Development model
2. **qwen3:32b** (32.8B) - Code a text model  
3. **gemma3:27b** (27.4B) - ⏳ **POMALÝ** (>120s) - Základní text model
4. **qwq:latest** (32.8B) - ⏳ **POMALÝ** - Reasoning model
5. **deepseek-r1:32b** (32.8B) - Advanced reasoning model

## Vyřešené problémy

### 1. Špatný API endpoint
- **❌ Před**: `/v1/chat/completions` (OpenAI kompatibilní)
- **✅ Po**: `/api/chat` (správný Ollama endpoint)

### 2. Krátký timeout
- **❌ Před**: 30 sekund (axios default)
- **✅ Po**: 120 sekund frontend + backend

### 3. Špatný payload formát
- **❌ Před**: OpenAI formát s `max_tokens`
- **✅ Po**: Ollama formát s `options.num_predict`

### 4. Chybějící Ollama2 podpora
- **❌ Před**: Pouze "ollama" provider
- **✅ Po**: Podpora pro "ollama2" provider

## Testovací výsledky

### Funkční testy
```bash
# Test Ollama2 rychlého modelu
curl -X POST "http://localhost:8001/api/db/llm/test-llm" \
  -H "Content-Type: application/json" \
  -d '{"model_id": "40_devstral:24b", "message": "Ahoj z Ollama2!"}'

# Výsledek: 378ms, 10 tokenů - RYCHLÝ ✅
```

### Výkonové charakteristiky
- **devstral:24b**: ~378ms latence - ideální pro rychlé testy
- **gemma3:27b**: >120s latence - vhodný pro komplexní úkoly
- **qwq:latest**: >120s latence - reasoning capabilities
- **deepseek-r1:32b**: >120s latence - advanced reasoning

## Použití

### 1. Přidání Ollama2 provideru
```bash
# Via API
curl -X POST "http://localhost:8001/api/db/llm/providers/ollama" \
  -H "Content-Type: application/json" \
  -d '{"ollama_url": "http://***************:11434", "provider_name": "Ollama2"}'

# Via Web UI
# Klikněte na fialové tlačítko "🦙 Přidat Ollama2" v LLM Management
```

### 2. Testování modelů
```bash
# Rychlý model pro testy
curl -X POST "http://localhost:8001/api/db/llm/test-llm" \
  -H "Content-Type: application/json" \
  -d '{"model_id": "40_devstral:24b", "message": "Test zpráva"}'

# Pomalý model pro komplexní úkoly  
curl -X POST "http://localhost:8001/api/db/llm/test-llm" \
  -H "Content-Type: application/json" \
  -d '{"model_id": "40_qwq:latest", "message": "Složitý reasoning úkol"}' \
  --max-time 180
```

### 3. Programové volání
```python
from gent.llm.llm_manager import LLMManager

llm_manager = LLMManager()

# Rychlý model
response = await llm_manager.generate_text(
    prompt="Ahoj!",
    model="devstral:24b", 
    provider="ollama2",
    max_tokens=100
)

# Reasoning model
response = await llm_manager.generate_text(
    prompt="Vyřeš matematický problém...",
    model="qwq:latest",
    provider="ollama2", 
    max_tokens=500
)
```

## Monitoring a metriky

### Automatické zaznamenávání
- **LLM metriky**: Každé volání se zaznamenává do databáze
- **Performance tracking**: Latence, tokeny, úspěšnost
- **Error logging**: Detailní logování chyb a timeoutů

### Dostupné metriky
- Průměrný čas odpovědi podle modelu
- Počet použitých tokenů
- Úspěšnost volání (rychlé vs. pomalé modely)
- Využití jednotlivých Ollama serverů

## Troubleshooting

### Časté problémy

1. **Timeout chyby**
   - **Řešení**: Použijte rychlé modely (devstral:24b) pro testy
   - **Tip**: Pomalé modely (qwq, gemma3) potřebují >120s

2. **Status code 400**
   - **Řešení**: Zkontrolujte správný model_id formát: "40_model_name"
   - **Tip**: Použijte `/api/db/llm/models` pro seznam dostupných modelů

3. **Model nedostupný**
   - **Řešení**: Ověřte, že Ollama2 server běží na ***************:11434
   - **Test**: `curl http://***************:11434/api/tags`

### Diagnostika
```bash
# Test připojení k Ollama2
curl http://***************:11434/api/tags

# Test konkrétního modelu
curl -X POST http://***************:11434/api/chat \
  -H "Content-Type: application/json" \
  -d '{"model": "devstral:24b", "messages": [{"role": "user", "content": "Hello"}], "stream": false}'

# Kontrola dostupných modelů v GENT
curl http://localhost:8001/api/db/llm/models | jq '.[] | select(.provider_name == "Ollama2")'
```

## Závěr

Ollama2 integrace je plně funkční s opravenými API endpointy a timeouty. Systém nyní podporuje:

- **Dva Ollama servery**: Ollama (152) + Ollama2 (106)
- **10 modelů celkem**: 5 na každém serveru
- **Správné API volání**: Ollama specifické endpointy
- **Dostatečné timeouty**: 120s pro pomalé modely
- **Kompletní monitoring**: Metriky a error handling

**Ollama2 je připravena pro produkční použití v GENT systému!** 🎉
