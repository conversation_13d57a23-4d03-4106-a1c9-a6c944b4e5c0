# LM Studio Integration - Kompletní implementace

## Přehled

LM Studio provider byl úspěšně integrován do GENT v10 systému s plnou funkcionalitou včetně automatického model switching, správných timeoutů a kompletní integrace do všech částí systému.

## Implementované komponenty

### 1. Backend implementace

#### Rozšířená databázová služba (`gent/db/llm_db_service.py`)
- **Metoda `add_lmstudio_provider()`**: Automatické načítání modelů z LM Studio API
- **OpenAI kompatibilní API**: Použití `/v1/models` endpointu pro detekci modelů
- **Správné mapování capabilities**: Rozpoznává code a reasoning modely podle názvu

```python
def add_lmstudio_provider(self, lmstudio_url: str = "http://***************:1234", provider_name: str = "LM Studio") -> bool:
    # <PERSON>ké načtení modelů z LM Studio API
    response = requests.get(f"{lmstudio_url}/v1/models", timeout=10)
    models_list = response.json().get("data", [])
```

#### Rozšířený LLM Manager (`gent/llm/llm_manager.py`)
- **LM Studio konfigurace**: Samostatná konfigurace pro LM Studio server
- **Dedikované metody**: `_generate_lmstudio()` a `_chat_completion_lmstudio()`
- **Zvýšené timeouty**: 300 sekund pro model switching

```python
# LM Studio konfigurace
lmstudio_url = self.config.get("lmstudio_url") or get_typed_env_var("LMSTUDIO_URL", "http://***************:1234", str)
providers["lmstudio"] = {
    "api_key": None,  # LM Studio nevyžaduje API klíč
    "base_url": lmstudio_url,
    "models": self.config.get("lmstudio_models", ["google/gemma-3-27b", "qwen/qwen2.5-coder-32b", ...])
}
```

#### API endpointy (`gent/api/app/routes/`)
- **`/api/db/llm/providers/lmstudio`**: Přidání LM Studio provideru
- **Testovací endpointy**: Podpora v `llm_config_routes.py` i `llm_direct_db_routes.py`
- **Zvýšené timeouty**: 300 sekund pro model switching

```python
async def call_lmstudio_api(api_base_url: str, model: str, message: str, temperature: float, max_tokens: int):
    # Zvýšený timeout pro LM Studio (300 sekund = 5 minut)
    # LM Studio potřebuje čas na unload/load modelů
    timeout = aiohttp.ClientTimeout(total=300)
```

### 2. Frontend implementace

#### Rozšířený LLM Management (`frontend-vue/src/views/admin/LlmManagement.vue`)
- **LM Studio tlačítko**: Zelené tlačítko "🏭 Přidat LM Studio"
- **Automatická synchronizace**: Obnovení dat po přidání provideru
- **Dedikovaná metoda**: `addLMStudioProvider()` pro LM Studio server

```javascript
async addLMStudioProvider() {
  const response = await fetch('/api/db/llm/providers/lmstudio', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      lmstudio_url: 'http://***************:1234',
      provider_name: 'LM Studio'
    })
  });
}
```

#### Zvýšené timeouty (`frontend-vue/src/services/chat.service.js`)
- **300 sekund timeout**: Pro LM Studio model switching
- **Axios konfigurace**: Explicitní timeout pro pomalé model switching

```javascript
// Zvýšený timeout pro LLM requesty (300 sekund)
// LM Studio potřebuje čas na unload/load modelů
const response = await axios.post(endpoint, payload, {
  timeout: 300000  // 5 minut pro LM Studio model switching
});
```

## Dostupné modely a výkon

### LM Studio (***************:1234) - Provider ID 41

1. **mistralai/devstral-small-2505** - ✅ **RYCHLÝ** (~15s switching)
   - Development focused model
   - Rychlé přepínání mezi modely
   - Ideální pro rychlé testy

2. **google/gemma-3-27b** - ✅ **STŘEDNÍ** (~26s switching)
   - Základní text model (27B parametrů)
   - Střední rychlost přepínání
   - Univerzální použití

3. **qwen/qwen2.5-coder-32b** - Code model (32B)
   - Specializovaný na programování
   - Velký model s delším switching časem

4. **qwen/qwen3-32b** - ✅ **POMALÝ** (~41s switching, reasoning)
   - Text model s reasoning capabilities
   - Obsahuje `<think>` reasoning proces
   - Nejpomalejší switching kvůli velikosti

5. **qwen/qwq-32b** - Reasoning model (32B)
   - Pokročilé reasoning capabilities
   - Velký model s dlouhým switching časem

## Model switching charakteristiky

### Testované přepínání modelů:
- **Gemma → Devstral**: ~15 sekund (podobné velikosti)
- **Devstral → Qwen3**: ~41 sekund (větší model s reasoning)
- **Qwen → Gemma**: ~26 sekund (střední velikost)

### Důvody různých rychlostí:
1. **Velikost modelu**: Větší modely (32B) trvají déle než menší
2. **Typ modelu**: Reasoning modely mají složitější loading
3. **Paměťové nároky**: LM Studio musí unload/load celý model
4. **Hardware**: Závisí na dostupné RAM a GPU

## Konfigurace a nastavení

### Server konfigurace
- **URL**: `http://***************:1234`
- **API formát**: OpenAI kompatibilní (`/v1/chat/completions`)
- **Autentizace**: Žádná (auth_type: "none")
- **API klíč**: Nevyžadován

### Databázové záznamy
- **Provider ID**: 41
- **Název**: "LM Studio"
- **Výchozí model**: mistralai/devstral-small-2505
- **Počet modelů**: 5 (bez embedding modelů)

### Timeout nastavení
- **Backend API**: 300 sekund (aiohttp.ClientTimeout)
- **Frontend**: 300 sekund (axios timeout)
- **Curl testy**: 320 sekund (--max-time)

## Použití

### 1. Přidání LM Studio provideru
```bash
# Via API
curl -X POST "http://localhost:8001/api/db/llm/providers/lmstudio" \
  -H "Content-Type: application/json" \
  -d '{"lmstudio_url": "http://***************:1234", "provider_name": "LM Studio"}'

# Via Web UI
# Klikněte na zelené tlačítko "🏭 Přidat LM Studio" v LLM Management
```

### 2. Testování modelů
```bash
# Rychlý model (15s switching)
curl -X POST "http://localhost:8001/api/db/llm/test-llm" \
  -H "Content-Type: application/json" \
  -d '{"model_id": "41_mistralai/devstral-small-2505", "message": "Quick test"}' \
  --max-time 60

# Reasoning model (41s switching)
curl -X POST "http://localhost:8001/api/db/llm/test-llm" \
  -H "Content-Type: application/json" \
  -d '{"model_id": "41_qwen/qwen3-32b", "message": "Complex reasoning task"}' \
  --max-time 120
```

### 3. Programové volání
```python
from gent.llm.llm_manager import LLMManager

llm_manager = LLMManager()

# Rychlý model
response = await llm_manager.generate_text(
    prompt="Hello!",
    model="mistralai/devstral-small-2505",
    provider="lmstudio",
    max_tokens=50
)

# Reasoning model
response = await llm_manager.generate_text(
    prompt="Solve this complex problem...",
    model="qwen/qwen3-32b",
    provider="lmstudio",
    max_tokens=200
)
```

## Monitoring a metriky

### Automatické zaznamenávání
- **LLM metriky**: Každé volání se zaznamenává do databáze
- **Model switching time**: Latence včetně času přepnutí modelu
- **Token usage**: Přesné počty tokenů z LM Studio API
- **Error handling**: Detailní logování chyb a timeoutů

### Dostupné metriky
- **Switching latence**: 15s-41s podle modelu
- **Token counts**: Přesné hodnoty z OpenAI kompatibilního API
- **Success rate**: Vysoká úspěšnost s dostatečnými timeouty
- **Model utilization**: Statistiky použití jednotlivých modelů

## Troubleshooting

### Časté problémy

1. **Timeout chyby**
   - **Řešení**: Použijte rychlé modely (devstral-small) pro testy
   - **Tip**: Reasoning modely (qwen3, qwq) potřebují >60s

2. **Model switching delays**
   - **Řešení**: Počkejte na dokončení předchozího requestu
   - **Tip**: LM Studio může zpracovávat jen jeden model současně

3. **Model nedostupný**
   - **Řešení**: Zkontrolujte, že model je načtený v LM Studio
   - **Test**: `curl http://***************:1234/v1/models`

### Diagnostika
```bash
# Test připojení k LM Studio
curl http://***************:1234/v1/models

# Test konkrétního modelu
curl -X POST http://***************:1234/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{"model": "mistralai/devstral-small-2505", "messages": [{"role": "user", "content": "Hello"}], "max_tokens": 10}'

# Kontrola dostupných modelů v GENT
curl http://localhost:8001/api/db/llm/models | grep -A5 -B5 "LM Studio"
```

## Optimalizace výkonu

### Doporučení pro rychlost
1. **Používejte rychlé modely**: devstral-small pro běžné úkoly
2. **Minimalizujte switching**: Seskupte requesty pro stejný model
3. **Správné timeouty**: 60s pro rychlé, 120s+ pro pomalé modely
4. **Monitoring**: Sledujte latenci v GENT metrikách

### Model selection strategie
- **Rychlé testy**: mistralai/devstral-small-2505 (15s)
- **Univerzální úkoly**: google/gemma-3-27b (26s)
- **Programování**: qwen/qwen2.5-coder-32b
- **Reasoning**: qwen/qwen3-32b (41s) nebo qwen/qwq-32b

## Závěr

LM Studio integrace je plně funkční s automatickým model switching a správnými timeouty. Systém nyní podporuje:

- **Model switching**: Automatické přepínání mezi 5 modely
- **Různé rychlosti**: 15s-41s podle velikosti modelu
- **OpenAI kompatibilita**: Standardní API formát
- **Reasoning capabilities**: Pokročilé modely s <think> procesem
- **Kompletní monitoring**: Metriky a error handling

**LM Studio je připraveno pro produkční použití v GENT systému!** 🏭✨
