# Kom mezi LLM - Middleware Prompt Update

## Úprava implementována! 🎯

Implementoval jsem úpravu middleware promptu pro generování konkrétnějších a stručnějších instrukcí podle doporučení.

## Problém před úpravou

### **🔴 PŘED - <PERSON><PERSON><PERSON><PERSON>, obecné instrukce:**
```javascript
const middlewarePrompt = `Jsi UNIVERZÁLNÍ MIDDLEWARE pro multi-agent spolupráci...

TVŮJ ÚKOL:
Vyt<PERSON><PERSON> jednodu<PERSON>, povzbuzující instrukci pro ${toAgent}, která:
1. Nechá ${toAgent} být sám sebou
2. Využi<PERSON> jeho přirozené silné stránky PRO TENTO TYP ÚLOHY
3. Povzbudí k unikátnímu pohledu na úkol
4. Zabrání opakování už řečeného

POŽADOVANÝ FORMÁT:
=== TVOJE ROLE: ${toAgent} ===
ÚKOL: ${originalTask}
TVŮJ PŘÍSPĚVEK: [co má přidat ze své perspektivy]
ZAMĚŘ SE NA: [jeho silné stránky specifické pro ${taskType}]
NEPIŠ ZNOVU: [stručně - co už bylo pokryto]`;
```

**Problém:** Middleware generoval dlouhé, obecné instrukce místo konkrétních akcí!

## Řešení - Stručný, direktivní prompt

### **🟢 PO - Konkrétní, stručné instrukce:**
```javascript
const middlewarePrompt = `
KRITICKÉ: Generuj instrukce pro ${toAgent} aby přidal POUZE JEDNU konkrétní věc.

ŠPATNÉ INSTRUKCE ❌:
"Analyzuj a vylepši systém"
"Rozpracuj do detailů"
"Navrhni komplexní řešení"
"Přidej více funkcí"

SPRÁVNÉ INSTRUKCE ✅:
"Přidej JEDNU funkci: error handling"
"Vylepši JEDEN aspekt: rychlost načítání"
"Navrhni JEDNO řešení pro: validaci vstupu"
"Přidej pouze: import math"

VYGENERUJ INSTRUKCI VE FORMÁTU:
"Přidaj pouze: [jedna konkrétní věc]"

PRAVIDLA:
- Maximum 20 slov!
- Jedna konkrétní akce
- Žádné obecnosti
- Žádné seznamy
`;
```

## Implementace

### **1. Nový middleware prompt:**

```javascript
const middlewarePrompt = `
KRITICKÉ: Generuj instrukce pro ${toAgent} aby přidal POUZE JEDNU konkrétní věc.

ÚKOL: "${originalTask}"
TYP ÚLOHY: ${taskType}
ITERACE: ${iteration}/${maxIterations}

HISTORIE:
${historyText}

POSLEDNÍ PŘÍSPĚVEK:
${lastMessage}

ŠPATNÉ INSTRUKCE ❌:
"Analyzuj a vylepši systém"
"Rozpracuj do detailů"
"Navrhni komplexní řešení"
"Přidej více funkcí"

SPRÁVNÉ INSTRUKCE ✅:
"Přidej JEDNU funkci: error handling"
"Vylepši JEDEN aspekt: rychlost načítání"
"Navrhni JEDNO řešení pro: validaci vstupu"
"Přidej pouze: import math"

INSTRUKCE PODLE TYPU (${taskType}) A AGENTA (${toAgent}):
${toAgent === 'AI-1' ? 
  (taskType === 'programování' ? 'Zaměř se na: JEDEN technický aspekt (struktura/optimalizace/logika)' :
   taskType === 'kreativní' ? 'Zaměř se na: JEDEN strukturální prvek (osnova/logika/sekvence)' :
   taskType === 'analytická' ? 'Zaměř se na: JEDNU analytickou metodu (data/výpočet/trend)' :
   'Zaměř se na: JEDEN systematický přístup') :
  (taskType === 'programování' ? 'Zaměř se na: JEDEN UX prvek (použitelnost/interface/feedback)' :
   taskType === 'kreativní' ? 'Zaměř se na: JEDEN kreativní prvek (nápad/twist/detail)' :
   taskType === 'analytická' ? 'Zaměř se na: JEDNU praktickou aplikaci (využití/doporučení)' :
   'Zaměř se na: JEDEN praktický aspekt')
}

VYGENERUJ INSTRUKCI VE FORMÁTU:
"Přidaj pouze: [jedna konkrétní věc]"

PRAVIDLA:
- Maximum 20 slov!
- Jedna konkrétní akce
- Žádné obecnosti
- Žádné seznamy

Vygeneruj instrukci pro ${toAgent}:`;
```

### **2. Klíčové změny:**

#### **Přidáno:**
- ✅ **KRITICKÉ upozornění** na začátku
- ✅ **Konkrétní příklady** špatných vs. správných instrukcí
- ✅ **Jasný formát** výstupu: "Přidaj pouze: [věc]"
- ✅ **Striktní pravidla**: max 20 slov, jedna akce
- ✅ **Specifické zaměření** podle typu úlohy a agenta

#### **Odstraněno:**
- ❌ Dlouhé filosofické úvody
- ❌ Obecné povzbuzování
- ❌ Složité formáty s více sekcemi
- ❌ Dlouhé příklady

### **3. Specifické instrukce podle typu a agenta:**

#### **Pro PROGRAMOVÁNÍ:**
- **AI-1**: "JEDEN technický aspekt (struktura/optimalizace/logika)"
- **AI-2**: "JEDEN UX prvek (použitelnost/interface/feedback)"

#### **Pro KREATIVNÍ úkoly:**
- **AI-1**: "JEDEN strukturální prvek (osnova/logika/sekvence)"
- **AI-2**: "JEDEN kreativní prvek (nápad/twist/detail)"

#### **Pro ANALYTICKÉ úkoly:**
- **AI-1**: "JEDNU analytickou metodu (data/výpočet/trend)"
- **AI-2**: "JEDNU praktickou aplikaci (využití/doporučení)"

## Očekávané výsledky

### **PŘED úpravou:**
```
Middleware generuje: "Přidej svůj analytický pohled na architekturu a zaměř se na technické řešení, optimalizaci kódu a strukturu"

AI-1 odpovídá: [dlouhý obecný text o architektuře]
```

### **PO úpravě:**
```
Middleware generuje: "Přidaj pouze: input validaci pro čísla"

AI-1 odpovídá: "if not x.isdigit(): return 'Chyba'"
```

## Výhody úpravy

### ✅ **Konkrétnost**
- Middleware generuje specifické, akční instrukce
- AI agenti dostanou jasný úkol co přesně udělat

### ✅ **Stručnost**
- Maximum 20 slov = žádné dlouhé texty
- Jedna konkrétní akce = žádné seznamy

### ✅ **Efektivita**
- AI agenti nebudou "povídat kolem"
- Každá iterace přidá konkrétní hodnotu

### ✅ **Měřitelnost**
- Jasně viditelný pokrok v každé iteraci
- Konkrétní příspěvky místo obecností

## Příklady očekávaných instrukcí

### **Programování:**
```
"Přidaj pouze: error handling pro dělení nulou"
"Přidaj pouze: input() pro druhé číslo"
"Přidaj pouze: print() výsledku"
```

### **Kreativní:**
```
"Přidaj pouze: nečekaný zvrat v polovině"
"Přidaj pouze: popis hlavní postavy"
"Přidaj pouze: dialog mezi postavami"
```

### **Analytické:**
```
"Přidaj pouze: výpočet průměru"
"Přidaj pouze: trend za Q3"
"Přidaj pouze: doporučení pro management"
```

## Status

✅ Middleware prompt upraven na stručný a direktivní
✅ Přidány konkrétní příklady špatných vs. správných instrukcí
✅ Definován jasný formát výstupu: "Přidaj pouze: [věc]"
✅ Nastavena striktní pravidla: max 20 slov, jedna akce
✅ Zachována specifičnost podle typu úlohy a agenta
✅ Připraveno k testování

**ÚPRAVA DOKONČENA!** 🚀

Middleware nyní bude generovat mnohem konkrétnější a stručnější instrukce, což povede k efektivnější spolupráci mezi AI agenty!
