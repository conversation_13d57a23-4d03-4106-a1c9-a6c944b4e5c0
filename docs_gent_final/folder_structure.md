# GENT - Struktura adresářů a organizace souborů

## 🎯 Účel dokumentu
Tento dokument definuje logickou strukturu adresářů projektu GENT a pravidla pro organizaci souborů. Slouží jako návod pro AI asistenty a vývojáře, kde ukládat jaké typy souborů.

## 📁 Hlavní struktura adresářů

### `/opt/gent/` - Root adresář projektu
**Účel:** Hlavní adresář obsahující pouze základní konfigurační soubory
**Obsah:**
- `log_rollback.md` - Hlavní log všech změn v projektu
- `run_frontend.sh` - Shell skript pro spuštění frontendu
- `gent-frontend.service` - Systemd service soubor

### `/opt/gent/temp/` - Pomocné a dočasné soubory
**Účel:** Adresář pro všechny pomocné skripty a dočasné soubory
**Obsah:**
- `*.py` - Utility skripty (add_models.py, update_*.py, fix_*.py)
- `*.sql` - SQL skripty pro databázi
- Dočasné soubory a experimenty

### `/opt/gent/docs_gent_final/` - Finální dokumentace ✅
**Účel:** Kompletní dokumentace dokončených funkcí pro GENT AI a jiné AI systémy
**Pravidla:**
- ✅ Pouze kompletně dokončené funkcionality
- ✅ Formát: `{nazev_funkce}.md`
- ✅ Slouží jako návod pro GENT AI
- ✅ Vytvářet po úplném dokončení úkolu

**Současný obsah:**
- `llm_management.md` - Kompletní návod pro LLM Management
- `folder_structure.md` - Tento dokument

**Plánovaný obsah:**
- `chat_system.md` - Návod pro chat systém
- `agent_management.md` - Správa agentů
- `database_structure.md` - Databázová struktura
- `api_reference.md` - API dokumentace
- `deployment.md` - Nasazení a konfigurace

### `/opt/gent/docs/` - Pracovní dokumentace
**Účel:** Pracovní dokumenty, návrhy, analýzy a dočasná dokumentace
**Obsah:**
- `CHANGES_SUMMARY.md` - Souhrn změn
- `WORKING_MODELS_FINAL.md` - Pracovní dokumenty modelů
- `ai_llm_management_*.md` - Pracovní dokumenty LLM
- `api_*.md` - API dokumentace v procesu
- `chat-test-dokumentace.md` - Dokumentace testování
- `db_*.md` - Databázové dokumenty
- `hlavni_pravidla.md` - Základní pravidla
- `llm-management-upgrade.md` - Upgrade dokumenty
- `images/` - Obrázky a diagramy
- `vision_v9/` - Vize projektu
- `tasklists/` - Starší task listy (přesunout)

### `/opt/gent/tasklists/` - Task listy a plány
**Účel:** Organizace úkolů, plánů a postupů práce
**Struktura:**
- `README_FINALL_TASKLISTS.md` - Přehled task listů
- `tasklist_finall_*.md` - Finální task listy (1-22)
- `tasklist*.md` - Starší task listy
- `plan_*.md` - Plány a návrhy
- `gui_reorganization_plan.md` - Plány reorganizace

### `/opt/gent/docs_finall/` - Přechodný adresář
**Účel:** Přechodný adresář pro finální dokumenty (sloučit s docs_gent_final)
**Obsah:**
- `idea.md` - Základní myšlenky

### `/opt/gent/config/` - Konfigurace
**Účel:** Konfigurační soubory systému
**Struktura:**
- `brain/` - Konfigurace GENT mozku
- `docker/` - Docker konfigurace
- `environment/` - Prostředí (dev, prod)
- `llm/` - LLM konfigurace
- `mcp/` - MCP konfigurace
- `system/` - Systémová konfigurace
- `*.json` - Konfigurační soubory

### `/opt/gent/gent/` - Zdrojový kód
**Účel:** Hlavní zdrojový kód aplikace GENT
**Struktura:**
- `agents/` - Systém agentů
- `api/` - API server
- `brain/` - GENT mozek a kognice
- `communication/` - Komunikační systém
- `db/` - Databázové služby
- `knowledge/` - Znalostní báze
- `llm/` - LLM poskytovatelé
- `modes/` - Režimy práce
- `project/` - Projektový management
- `tasks/` - Task management
- `workflow/` - Workflow systém

### `/opt/gent/frontend-vue/` - Frontend aplikace
**Účel:** Vue.js frontend aplikace
**Struktura:**
- `src/` - Zdrojový kód
- `dist/` - Build výstupy
- `public/` - Statické soubory
- `node_modules/` - NPM závislosti
- `package.json` - NPM konfigurace

### `/opt/gent/api/` - API aplikace
**Účel:** FastAPI backend aplikace
**Struktura:**
- `app/` - Aplikační logika
- `run_api.py` - Spouštěcí skript

### `/opt/gent/micro_services/` - Mikroslužby
**Účel:** Nezávislé mikroslužby
**Obsah:**
- `litellm/` - LiteLLM služba

### `/opt/gent/migrations/` - Databázové migrace
**Účel:** Alembic migrace databáze
**Obsah:**
- `versions/` - Migrační skripty
- `env.py` - Alembic konfigurace

### `/opt/gent/tests/` - Testy
**Účel:** Unit testy a testovací soubory
**Obsah:**
- `*.py` - Testovací skripty
- `grrrrrrrrr/` - Testovací soubory

### `/opt/gent/data/` - Data
**Účel:** Datové soubory a cache
**Obsah:**
- `cache/` - Cache soubory

### `/opt/gent/venv/` - Python virtuální prostředí
**Účel:** Python závislosti a virtuální prostředí
**Obsah:** Standardní venv struktura

### `/opt/gent/node_modules/` - Node.js závislosti
**Účel:** Node.js balíčky pro root projekt
**Obsah:** NPM závislosti

## 🔧 Pravidla pro organizaci souborů

### 📋 Dokumentace
1. **Finální dokumentace** → `/opt/gent/docs_gent_final/`
   - Pouze kompletně dokončené funkce
   - Formát: `{nazev_funkce}.md`
   - Slouží jako návod pro GENT AI

2. **Pracovní dokumentace** → `/opt/gent/docs/`
   - Návrhy, analýzy, work-in-progress
   - Dočasné dokumenty

3. **Task listy** → `/opt/gent/tasklists/`
   - Plány, úkoly, postupy práce

### 🔨 Skripty a utility
1. **Python skripty** → `/opt/gent/temp/`
   - Utility, pomocné skripty, experimenty
   - Formát: `{ucel}_{popis}.py`

2. **SQL skripty** → `/opt/gent/temp/`
   - Databázové skripty, migrace
   - Formát: `{ucel}_{tabulka}.sql`

3. **Shell skripty** → `/opt/gent/` (pouze základní)
   - Pouze základní spouštěcí skripty
   - Pomocné shell skripty → `/opt/gent/temp/`

### ⚙️ Konfigurace
1. **Systémová konfigurace** → `/opt/gent/config/`
   - Rozděleno podle komponent
   - JSON formát preferován

2. **Service soubory** → `/opt/gent/`
   - Systemd service definice

### 💾 Zdrojový kód
1. **Backend kód** → `/opt/gent/gent/`
   - Modulární struktura podle funkcí
   - Python balíčky

2. **Frontend kód** → `/opt/gent/frontend-vue/`
   - Vue.js aplikace
   - Standardní Vue struktura

3. **API kód** → `/opt/gent/api/`
   - FastAPI aplikace

## 🚫 Co NEPATŘÍ do root adresáře

### Zakázané v `/opt/gent/`:
- ❌ Dočasné soubory
- ❌ Log soubory (kromě log_rollback.md)
- ❌ Cache soubory
- ❌ Build artefakty
- ❌ IDE konfigurace
- ❌ Osobní poznámky
- ❌ Nedokončené dokumenty

### Kam místo toho:
- **Dočasné soubory** → `/tmp/` nebo `/opt/gent/data/cache/`
- **Log soubory** → `/var/log/gent/` nebo `/opt/gent/data/logs/`
- **Build artefakty** → `/opt/gent/frontend-vue/dist/`
- **IDE konfigurace** → `.gitignore`

## 📊 Současný stav a úklid

### ✅ Správně umístěné soubory:
- `/opt/gent/docs_gent_final/llm_management.md`
- `/opt/gent/log_rollback.md`
- `/opt/gent/gent/` - zdrojový kód
- `/opt/gent/config/` - konfigurace

### 🔄 Soubory k reorganizaci:
1. **Přesunout z root do docs/:**
   - `FINAL_MODELS_SUMMARY.md`
   - `GENT_API_MANUAL.md`
   - `GENT_LLM_MODELS_DOCUMENTATION.md`
   - `WORKING_MODELS_FINAL.md`

2. **Sloučit adresáře:**
   - `docs_finall/` → `docs_gent_final/`
   - Starší task listy z `docs/tasklists/` → `tasklists/`

3. **Vyčistit:**
   - Duplicitní dokumenty
   - Zastaralé skripty
   - Nepoužívané soubory

## 🎯 Doporučení pro AI asistenty

### Při vytváření nových souborů:
1. **Dokumentace kompletní funkce** → `/opt/gent/docs_gent_final/{nazev}.md`
2. **Pracovní dokumenty** → `/opt/gent/docs/{nazev}.md`
3. **Task listy** → `/opt/gent/tasklists/{nazev}.md`
4. **Utility skripty** → `/opt/gent/temp/{nazev}.py`
5. **Testovací soubory** → `/opt/gent/tests/{nazev}.py`
6. **SQL skripty** → `/opt/gent/temp/{nazev}.sql`
7. **Konfigurace** → `/opt/gent/config/{komponenta}/`

### Při úpravách:
1. **Vždy aktualizuj** `log_rollback.md`
2. **Respektuj** strukturu adresářů
3. **Neměň** umístění existujících souborů bez konzultace
4. **Používej** konzistentní pojmenování

---

**💡 Tip:** Tato struktura je navržena pro efektivní práci GENT AI systému a snadnou orientaci v projektu. Dodržování těchto pravidel zajistí čistotu a udržitelnost kódové báze.
