# Kom mezi LLM - Oprava koherence a duplikátů

## Problém identifikován a opraven! 🎯

Implementoval jsem řešení pro problémy s koherencí a opakováním konceptů na základě perfektní analýzy uživatele.

## Identifikované problémy

### **🔴 1. <PERSON><PERSON><PERSON> instrukce s duplikáty:**
```
3x "PvP aréna" (iterace 2, 5, 8, 10)
2x "systém guild" (iterace 6, 9)  
2x "levelování" (iterace 1, 7)
```

### **🔴 2. AI-2 mění koncept místo vylepšení:**
```
Iterace 4: "mobilní RPG s budováním města"
Iterace 10: "Sbírej, obchoduj a bojuj" - úpln<PERSON> jiná hra
```

### **🔴 3. Chybí koherence:**
```
Výsledek = RPG + budování + gacha + crafting + trading
```

### **🔴 Příčina:**
Middleware LLM nevidí celou historii a generuje instrukce bez kontextu.

## Řešení implementováno

### **1. Middleware s CELOU historií a kontrolou duplikátů:**

```javascript
// PŘED - jen poslední zpráva
POSLEDNÍ PŘÍSPĚVEK:
${lastMessage}

// PO - celá historie + detekce duplikátů
// Extrakce všech použitých konceptů z historie
const usedConcepts = [];
history.forEach(h => {
  const content = h.content.toLowerCase();
  // Detekce herních konceptů
  if (content.includes('pvp') || content.includes('aréna')) usedConcepts.push('PvP aréna');
  if (content.includes('guild') || content.includes('klany')) usedConcepts.push('systém guild');
  if (content.includes('level') || content.includes('úroveň')) usedConcepts.push('levelování');
  if (content.includes('crafting') || content.includes('výroba')) usedConcepts.push('crafting');
  if (content.includes('trading') || content.includes('obchod')) usedConcepts.push('trading');
  if (content.includes('gacha') || content.includes('lootbox')) usedConcepts.push('gacha systém');
  if (content.includes('budování') || content.includes('stavění')) usedConcepts.push('budování');
  if (content.includes('rpg') || content.includes('role')) usedConcepts.push('RPG prvky');
});

const uniqueUsedConcepts = [...new Set(usedConcepts)];

const middlewarePrompt = `
🚨 KRITICKÉ: ${toAgent} smí napsat MAXIMUM 50 SLOV! 🚨

ÚKOL: "${originalTask}"
ITERACE: ${iteration}/${maxIterations}

CELÁ HISTORIE KONVERZACE:
${historyText}

UŽ POUŽITÉ KONCEPTY (NEOPAKUJ!):
${uniqueUsedConcepts.length > 0 ? uniqueUsedConcepts.map(c => `❌ ${c}`).join('\n') : 'Žádné dosud'}

TVŮJ ÚKOL:
Vygeneruj instrukci pro ${toAgent} která:
1. NEOBSAHUJE už použité koncepty
2. NAVAZUJE na předchozí vývoj
3. ZACHOVÁVÁ žánr a směr hry
4. Přidává JEDNU novou, originální věc

ZAKÁZANÉ INSTRUKCE (už byly použity):
${uniqueUsedConcepts.map(c => `"Přidaj ${c}"`).join('\n')}

🚨 PRAVIDLA: 🚨
- Maximum 15 slov pro instrukci!
- ŽÁDNÉ opakování použitých konceptů!
- Musí navazovat na předchozí vývoj!`;
```

### **2. AI-2 s kontrolou koherence:**

```javascript
// PŘED - bez kontroly koherence
ai2Prompt = `Jsi AI-2 - kreativní myslitel.
ÚKOL: "${originalPrompt}"
PRAVIDLA:
- Přidaj JEDNU konkrétní věc`;

// PO - s kontrolou koherence
ai2Prompt = `${inputFromAI1}

🚨 KRITICKÉ: Tvoje odpověď musí být MAX 50 SLOV! 🚨

Jsi AI-2 - kreativní myslitel.

PŮVODNÍ ÚKOL: "${this.currentSession.originalPrompt}"

⚠️ ZACHOVEJ KOHERENCI: ⚠️
- NEMĚŇ žánr nebo koncept hry
- NEVYMÝŠLEJ úplně novou hru
- POUZE vylepši to, co už existuje
- NAVAZUJ na předchozí vývoj

PRAVIDLA:
- MAXIMUM 50 SLOV!
- Přidaj JEDNU konkrétní věc k tomu co udělal AI-1
- ZACHOVEJ směr a styl hry
- ŽÁDNÉ radikální změny konceptu

🚨 POKUD NAPÍŠEŠ VÍC NEŽ 50 SLOV NEBO ZMĚNÍŠ KONCEPT, SELHAL JSI! 🚨`;
```

## Klíčové vylepšení

### **1. Detekce použitých konceptů:**
```javascript
// Automatická detekce herních konceptů z historie
const conceptDetectors = {
  'PvP aréna': ['pvp', 'aréna'],
  'systém guild': ['guild', 'klany'],
  'levelování': ['level', 'úroveň'],
  'crafting': ['crafting', 'výroba'],
  'trading': ['trading', 'obchod'],
  'gacha systém': ['gacha', 'lootbox'],
  'budování': ['budování', 'stavění'],
  'RPG prvky': ['rpg', 'role']
};
```

### **2. Zakázané instrukce:**
```javascript
// Middleware vidí co už bylo použito
UŽ POUŽITÉ KONCEPTY (NEOPAKUJ!):
❌ PvP aréna
❌ systém guild  
❌ levelování

ZAKÁZANÉ INSTRUKCE (už byly použity):
"Přidaj PvP aréna"
"Přidaj systém guild"
"Přidaj levelování"
```

### **3. Kontrola koherence pro AI-2:**
```javascript
⚠️ ZACHOVEJ KOHERENCI: ⚠️
- NEMĚŇ žánr nebo koncept hry
- NEVYMÝŠLEJ úplně novou hru
- POUZE vylepši to, co už existuje
- NAVAZUJ na předchozí vývoj
```

## Očekávané výsledky

### **PŘED opravou:**
```
Iterace 1: "levelování postav"
Iterace 2: "PvP aréna"
Iterace 5: "PvP aréna" (duplikát!)
Iterace 7: "levelování" (duplikát!)
Iterace 4: "mobilní RPG s budováním města" (změna konceptu!)
```

### **PO opravě:**
```
Iterace 1: "levelování postav"
Iterace 2: "PvP aréna"
Iterace 3: "quest systém" (nový koncept)
Iterace 4: "vylepšení PvP mechanik" (navazuje, nemění koncept)
Iterace 5: "achievement systém" (nový, žádný duplikát)
```

## Výhody řešení

### ✅ **Eliminace duplikátů**
- Middleware vidí všechny použité koncepty
- Automaticky zakazuje opakování
- Nutí generovat nové nápady

### ✅ **Zachování koherence**
- AI-2 nemůže měnit žánr hry
- Musí navazovat na předchozí vývoj
- Pouze vylepšuje, nevymýšlí novou hru

### ✅ **Lepší řízení obsahu**
- Middleware má celou historii
- Generuje kontextové instrukce
- Směřuje vývoj logickým směrem

### ✅ **Kvalitní finální produkt**
- Koherentní hra jednoho žánru
- Postupné budování komplexity
- Žádné protichůdné prvky

## Příklady očekávaných instrukcí

### **Pro puzzle hru:**
```
Iterace 1: "Přidaj pouze: základní puzzle mechaniku"
Iterace 2: "Přidaj pouze: časový limit na řešení"
Iterace 3: "Přidaj pouze: bonus za rychlé řešení"
❌ NEBUDE: "Přidaj pouze: PvP souboje" (nehodí se k puzzle)
```

### **Pro RPG hru:**
```
Iterace 1: "Přidaj pouze: základní levelování"
Iterace 2: "Přidaj pouze: skill tree systém"
Iterace 3: "Přidaj pouze: equipment upgrade"
❌ NEBUDE: "Přidaj pouze: základní levelování" (duplikát)
```

## Status

✅ Middleware s celou historií implementován
✅ Detekce použitých konceptů implementována
✅ Kontrola duplikátů implementována
✅ AI-2 kontrola koherence implementována
✅ Zakázané instrukce generovány automaticky
✅ Zachování žánru a směru hry
✅ Připraveno k testování

**KOHERENCE A DUPLIKÁTY OPRAVENY!** 🎯

Systém nyní bude generovat originální, navazující instrukce bez opakování a AI-2 bude zachovávat koherenci místo měnění konceptu!
