# GENT v10 - Mem0 Memory Management - Uživatelský manuál

## 🧠 Co je Mem0?

**Mem0** je inteligentní paměťový systém pro GENT, který umožňuje AI agentům ukládat, v<PERSON><PERSON><PERSON><PERSON><PERSON> a spravovat vzpomínky. Je to jako "dlouhodob<PERSON> paměť" pro umělou inteligenci.

## 🎯 Hlavní funkce

### 💾 Ukládání vzpomínek
- Automatické ukládání důležitých informací z konverzací
- Zaznamenávání uživatelských preferencí
- Archivace poznatků a řešení problémů

### 🔍 Inteligentní vyhledávání
- Sémantick<PERSON> vyhl<PERSON>vání (najde souvislosti i bez přesných slov)
- R<PERSON>lé nalezení relevantních informací
- Kontextové vyhledávání

### 📊 Správa dat
- Organizace vzpomínek podle uživatelů
- Statistiky využití paměti
- Možnost mazání nepotřebných dat

## 🚀 Jak používat Mem0 v GENT

### 1. Přístup přes MCP Management
1. Jdi na `/admin/mcp` v GENT rozhraní
2. Najdi "Mem0 Memory Management" kartu
3. Klikni na "Test" pro testování nástrojů

### 2. Dostupné nástroje

#### 📝 Uložení vzpomínky
**Nástroj**: `store_memory`
```
Obsah: "Uživatel preferuje tmavý design a rychlé odpovědi"
Uživatel: "user123"
Metadata: {"kategorie": "preference"}
```

#### 🔍 Vyhledání vzpomínek
**Nástroj**: `search_memories`
```
Dotaz: "tmavý design"
Uživatel: "user123"
Limit: 5
```

#### 📋 Seznam všech vzpomínek
**Nástroj**: `get_all_memories`
```
Uživatel: "user123"
Limit: 50
```

#### 🗑️ Smazání vzpomínky
**Nástroj**: `delete_memory`
```
ID vzpomínky: "mem_12345"
```

#### 📊 Statistiky
**Nástroj**: `get_memory_stats`
```
Uživatel: "user123"
```

## 🔧 Pro administrátory

### Kontrola stavu služby
```bash
# Status
sudo systemctl status gent-mem0

# Restart
sudo systemctl restart gent-mem0

# Logy
tail -f /opt/gent/logs/mem0_mcp.log
```

### Manuální správa
```bash
cd /opt/gent/mcp_servers/mem0
./start_mem0_server.sh status
./start_mem0_server.sh restart
```

## 🎯 Praktické příklady použití

### Scénář 1: Ukládání preferencí
```
Uživatel: "Chci vždy tmavý design a krátké odpovědi"
AI uloží: "Uživatel preferuje tmavý design a stručné odpovědi"
```

### Scénář 2: Řešení problémů
```
Problém: "Jak nastavit HTTPS?"
AI uloží řešení a příště ho rychle najde
```

### Scénář 3: Komunikace mezi AI
```
AI-1: Uloží svou analýzu
AI-2: Vyhledá souvislosti a přidá svůj pohled
AI-3: Najde podobné případy z minulosti
```

## ⚠️ Důležité poznámky

### Bezpečnost
- Všechna data jsou uložena lokálně na serveru
- Žádné informace se neodesílají do cloudu
- Přístup pouze přes GENT systém

### Výkon
- Rychlé vyhledávání díky vektorové databázi
- Automatická optimalizace ukládání
- Pravidelné čištění starých dat

### Limity
- Maximálně 10,000 vzpomínek na uživatele
- Velikost vzpomínky max 1MB
- Automatické mazání po 365 dnech

## 🆘 Řešení problémů

### Mem0 nereaguje
1. Zkontroluj status: `systemctl status gent-mem0`
2. Restart služby: `systemctl restart gent-mem0`
3. Zkontroluj logy: `tail -f /opt/gent/logs/mem0_mcp.log`

### Pomalé vyhledávání
1. Zkontroluj počet vzpomínek
2. Použij specifičtější dotazy
3. Smaž nepotřebné vzpomínky

### Chyby při ukládání
1. Zkontroluj místo na disku
2. Ověř OpenAI API klíč
3. Restart Mem0 služby

## 📞 Podpora

Pro technickou podporu:
- Zkontroluj logy v `/opt/gent/logs/mem0_mcp.log`
- Použij MCP Testing interface pro diagnostiku
- Kontaktuj GENT administrátora

---

**Verze manuálu**: 1.0.0  
**Datum**: 2025-01-06  
**Pro GENT v10**: ✅ Kompatibilní
