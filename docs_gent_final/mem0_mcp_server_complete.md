# GENT v10 - Mem0 MCP Server - Kompletní dokumentace

## 📋 Přehled systému

**Mem0 MCP Server** je lokální memory management systém integrovaný do GENT v10 pro ukládání a správu vzpomínek AI agentů. Umožňuje komunikaci mezi LLM s persistentní pamětí.

## 🏗️ Architektura systému

### Lokální instalace
- **Adresář**: `/opt/gent/mcp_servers/mem0/`
- **Virtual Environment**: `/opt/gent/mcp_servers/mem0/venv/`
- **Port**: 8080 (SSE endpoint)
- **Protokol**: MCP přes SSE (Server-Sent Events)

### Komponenty
- **main.py** - FastMCP server s Mem0 integrací
- **config.json** - Konfigurace serveru a Mem0
- **requirements.txt** - Python dependencies
- **start_mem0_server.sh** - Startup script
- **gent-mem0.service** - Systemd service

## 🛠️ Instalace a konfigurace

### 1. Struktura souborů
```
/opt/gent/mcp_servers/mem0/
├── main.py                 # FastMCP server
├── config.json            # Konfigurace
├── requirements.txt       # Dependencies
├── start_mem0_server.sh   # Startup script
├── gent-mem0.service      # Systemd service
└── venv/                  # Virtual environment
    └── bin/python         # Python interpreter
```

### 2. Dependencies
```bash
# Hlavní dependencies
mem0ai>=0.1.104
mcp>=1.9.2
fastapi>=0.115.12
uvicorn>=0.34.3
python-dotenv>=1.1.0

# Memory a databáze
qdrant-client>=1.14.2
sqlalchemy>=2.0.41

# LLM integrace
openai>=1.83.0
```

### 3. Environment variables
```bash
# Povinné
OPENAI_API_KEY=your_openai_api_key

# Volitelné
MEM0_CONFIG_PATH=/opt/gent/mcp_servers/mem0/config.json
PYTHONPATH=/opt/gent/mcp_servers/mem0
```

## 🔧 Správa služby

### Systemd příkazy
```bash
# Spuštění
sudo systemctl start gent-mem0

# Zastavení
sudo systemctl stop gent-mem0

# Restart
sudo systemctl restart gent-mem0

# Status
sudo systemctl status gent-mem0

# Autostart
sudo systemctl enable gent-mem0

# Logy
journalctl -u gent-mem0 -f
```

### Manuální správa
```bash
# Spuštění
cd /opt/gent/mcp_servers/mem0
./start_mem0_server.sh start

# Status
./start_mem0_server.sh status

# Zastavení
./start_mem0_server.sh stop

# Restart
./start_mem0_server.sh restart
```

## 🔨 Dostupné nástroje

### 1. store_memory
**Popis**: Uloží novou vzpomínku do Mem0 systému

**Parametry**:
- `content` (string, povinný): Obsah vzpomínky
- `user_id` (string, volitelný): ID uživatele (výchozí: gent_system)
- `metadata` (string, volitelný): JSON metadata

**Příklad**:
```json
{
  "content": "Uživatel preferuje tmavý design",
  "user_id": "user123",
  "metadata": "{\"category\": \"preference\", \"priority\": \"high\"}"
}
```

### 2. search_memories
**Popis**: Vyhledá vzpomínky pomocí sémantického vyhledávání

**Parametry**:
- `query` (string, povinný): Vyhledávací dotaz
- `user_id` (string, volitelný): ID uživatele
- `limit` (int, volitelný): Max počet výsledků (výchozí: 5)

**Příklad**:
```json
{
  "query": "tmavý design preference",
  "user_id": "user123",
  "limit": 10
}
```

### 3. get_all_memories
**Popis**: Získá všechny vzpomínky pro uživatele

**Parametry**:
- `user_id` (string, volitelný): ID uživatele
- `limit` (int, volitelný): Max počet výsledků (výchozí: 50)

### 4. delete_memory
**Popis**: Smaže konkrétní vzpomínku

**Parametry**:
- `memory_id` (string, povinný): ID vzpomínky k smazání

### 5. get_memory_stats
**Popis**: Získá statistiky o vzpomínkách

**Parametry**:
- `user_id` (string, volitelný): ID uživatele

## 📊 Integrace s GENT

### MCP Management
- Server je automaticky přidán do MCP Management systému
- Dostupný v `/admin/mcp` rozhraní
- Testovatelný přes MCP Testing interface

### Konfigurace v mcp_config.json
```json
{
  "mem0": {
    "enabled": true,
    "command": "/opt/gent/mcp_servers/mem0/venv/bin/python /opt/gent/mcp_servers/mem0/main.py",
    "working_directory": "/opt/gent/mcp_servers/mem0",
    "auto_approve": [
      "store_memory",
      "retrieve_memories", 
      "search_memories",
      "delete_memory",
      "list_all_memories",
      "get_memory_stats"
    ],
    "description": "GENT Mem0 Memory Management - lokální paměť pro AI agenty a komunikaci mezi LLM",
    "custom": true,
    "type": "memory"
  }
}
```

## 🔍 Monitoring a logy

### Log soubory
- **Hlavní log**: `/opt/gent/logs/mem0_mcp.log`
- **Error log**: `/opt/gent/logs/mem0_mcp_error.log`
- **Systemd log**: `journalctl -u gent-mem0`

### Monitoring endpointy
- **Health check**: `http://localhost:8080/sse`
- **Status**: Přes startup script nebo systemctl

## 🛠️ Řešení problémů

### Časté problémy

1. **Server se nespustí**
   - Zkontroluj OPENAI_API_KEY
   - Ověř virtual environment
   - Zkontroluj port 8080

2. **Mem0 nefunguje**
   - Zkontroluj Qdrant připojení (port 6333)
   - Ověř OpenAI API klíč
   - Zkontroluj logs

3. **MCP nástroje nefungují**
   - Restart serveru
   - Zkontroluj MCP konfiguraci
   - Ověř permissions

### Debug příkazy
```bash
# Test spuštění
cd /opt/gent/mcp_servers/mem0
./venv/bin/python main.py --debug

# Kontrola portů
netstat -tlnp | grep 8080

# Test dependencies
./venv/bin/python -c "import mem0; print('OK')"
```

## 🎯 Použití pro komunikaci mezi LLM

Mem0 MCP server je připraven pro implementaci "Komunikace mezi LLM" systému:

1. **AI-0 (Prompt Optimizer)** - ukládá optimalizované prompty
2. **AI-1 & AI-2 (Processors)** - ukládají iterativní komunikaci
3. **Session Management** - ukládá celé session s metadaty
4. **Context Retrieval** - vyhledává relevantní kontext

### Příklad workflow
```python
# AI-0 uloží optimalizovaný prompt
store_memory("Optimalizovaný prompt pro AI-1", "session_123", 
             '{"type": "optimized_prompt", "iteration": 1}')

# AI-1 vyhledá kontext
search_memories("podobné problémy", "session_123")

# AI-2 uloží analýzu
store_memory("Analýza odpovědi AI-1", "session_123",
             '{"type": "analysis", "iteration": 1}')
```

## ✅ Status implementace

- ✅ **Lokální instalace** - Mem0 nainstalováno bez Docker
- ✅ **FastMCP server** - Kompletní implementace
- ✅ **Systemd service** - Automatický start/restart
- ✅ **MCP integrace** - Přidáno do GENT MCP systému
- ✅ **Dokumentace** - Kompletní manuál
- ✅ **Centralizace** - Umístěno v `/opt/gent/mcp_servers/`

**Datum dokončení**: 2025-01-06  
**Verze**: 1.0.0  
**Status**: ✅ KOMPLETNÍ A FUNKČNÍ
