# 🚨 Problém s hardcoded URL a jeho řešení

## 📱 Symptomy problému

**Na PC (localhost):**
- ✅ Modely se načítají
- ✅ Chat funguje
- ✅ Databáze funguje

**Na telefonu/tabletu (externí IP):**
- ✅ Modely se načítají  
- ❌ Chat nefunguje - "Chyba při komunikaci s modelem: network error"
- ❌ Některé databázové funkce nefungují

## 🔍 Hlavní příčina

**Hardcoded URL v kódu** - místo relativních URL byly použity absolutní URL s `localhost:8001`, které nefungují při přístupu z jiného zařízení.

### Konkrétní problémové místo:
```javascript
// frontend-vue/src/services/chat.service.js - řádek 35
const endpoint = 'http://localhost:8001/api/db/llm/test-llm';  // ❌ ŠPATNĚ
```

<PERSON><PERSON><PERSON> se uživatel připojí z telefonu na `**************:8000`, frontend se pokusí poslat chat zprávu na `localhost:8001`, ale na telefonu žádný localhost:8001 neběží!

## ✅ Řešení

### 1. Automatická detekce API URL
```javascript
// frontend-vue/src/services/api.service.js
function getApiBaseUrl() {
  const hostname = window.location.hostname;
  
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    return 'http://localhost:8001';
  }
  
  return `http://${hostname}:8001`;  // Použije stejnou IP jako web
}
```

### 2. Relativní URL místo hardcoded
```javascript
// PŘED (špatně):
const endpoint = 'http://localhost:8001/api/db/llm/test-llm';

// PO (správně):
const endpoint = '/api/db/llm/test-llm';
```

### 3. Odstranění VITE_API_URL
```bash
# run_frontend.sh - zakomentováno
# export VITE_API_URL="http://localhost:8001"
```

## 📋 Opravené soubory

1. **`frontend-vue/vite.config.js`** - proxy port 8003→8001
2. **`frontend-vue/src/views/AgentsTest.vue`** - všechny axios volání
3. **`frontend-vue/src/views/admin/LlmManagement.vue`** - všechny fetch volání  
4. **`frontend-vue/src/views/admin/DatabaseViewer.vue`** - všechny fetch volání
5. **`frontend-vue/src/services/chat.service.js`** - **KLÍČOVÝ** endpoint pro LLM chat
6. **`run_frontend.sh`** - zakomentována VITE_API_URL
7. **`gent-frontend.service`** - zakomentována VITE_API_URL

## 🎯 Výsledek

**Automatická logika:**
- **localhost** → API: `http://localhost:8001`
- ****************** → API: `http://**************:8001`
- **jakákoliv IP** → API: `http://[stejná-IP]:8001`

**Nyní funguje ze všech zařízení:**
- ✅ PC (localhost)
- ✅ Telefon (externí IP)
- ✅ Tablet (externí IP)
- ✅ VSCode Port Forwarding
- ✅ Jakékoliv zařízení ve stejné síti

## 🔧 Debug

V konzoli prohlížeče uvidíte:
```
🔗 API Base URL: http://**************:8001  (na telefonu)
🔗 API Base URL: http://localhost:8001       (na PC)
```

## 📝 Pravidlo pro budoucnost

**NIKDY nepoužívat hardcoded URL!**
- ❌ `http://localhost:8001/api/...`
- ✅ `/api/...` (relativní URL)

Relativní URL automaticky použijí správnou IP adresu podle toho, odkud se uživatel připojuje.

## 🧪 Test funkčnosti

```bash
# Test API z externího zařízení
curl -X POST http://**************:8001/api/db/llm/test-llm \
  -H "Content-Type: application/json" \
  -d '{"model_id": "1_gpt-4o-mini", "message": "ahoj"}'

# Očekávaná odpověď:
# {"success":true,"model":"openai/gpt-4o-mini","response":{"text":"Ahoj! Jak ti mohu pomoci?"}}
```

**Databázové připojení i chat s LLM modely nyní funguje správně ze všech zařízení!** 🎉
