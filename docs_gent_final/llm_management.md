# LLM Management - Kompletní návod pro GENT

## 🎯 Účel dokumentu
Tento dokument slouží jako kompletní návod pro GENT AI systém, jak používat LLM Management pro práci s jazykovými modely. Obsahuje všechny potřebné informace pro volání modelů, načítán<PERSON> z databáze a správu LLM infrastruktury.

## 📋 Přehled LLM Management systému

### Základn<PERSON> struktura
- **Webové rozhraní:** http://localhost:8003/admin/llm
- **API endpoint:** http://localhost:8001/api/db/llm/
- **Databáze:** PostgreSQL (gentdb)
- **4 hlavní sekce:** Poskytovatelé, Správa, Testování, Výkon

### Hlavní funkce
1. **🏢 Poskytovatelé** - přehled a monitoring poskytovatelů
2. **⚙️ Správa** - CRUD operace s poskytovateli a modely
3. **🧪 Testování** - interaktivní testování modelů
4. **📊 Výkon** - metriky a analytics

## 🏢 Poskytovatelé LLM

### Aktuální poskytovatelé v systému
1. **OpenAI** (ID: 1)
   - Modely: gpt-4o, gpt-4o-mini, gpt-4-turbo, o1-preview, o1-mini, o3-mini
   - API: https://api.openai.com/v1
   - Výchozí model: gpt-4o

2. **Anthropic** (ID: 2)
   - Modely: claude-sonnet-4-20250514, claude-3-7-sonnet-latest
   - API: https://api.anthropic.com
   - Výchozí model: claude-3-7-sonnet-latest

3. **Google** (ID: 28)
   - Modely: gemini-2.0-flash, gemini-2.0-flash-lite, gemini-2.5-flash-preview-05-20
   - API: https://generativelanguage.googleapis.com/v1beta/openai/
   - Context: až 1M tokenů

4. **Openrouter** (ID: 29)
   - Modely: deephermes-3-mistral-24b-preview, claude-opus-4, qwen3-235b-a22b
   - API: https://openrouter.ai/api/v1
   - Výchozí model: deephermes-3-mistral-24b-preview

5. **LMStudio** (ID: 37)
   - Lokální server: http://***************:1234
   - Modely: qwq-32b, qwen2.5-14b-deepresearch

6. **Ollama** (ID: 35)
   - Lokální server: http://***************:11434
   - Lokální modely

### API volání pro poskytovatelé
```bash
# Získání všech poskytovatelů
GET http://localhost:8001/api/db/llm/providers

# Testování poskytovatele
POST http://localhost:8001/api/db/llm/performance/test-provider/{provider_id}
```

## ⚙️ Správa poskytovatelů a modelů (CRUD)

### Přístup ke správě
1. Přejdi na http://localhost:8003/admin/llm
2. Klikni na záložku "⚙️ Správa"
3. Vyber pod-záložku "🏢 Poskytovatelé" nebo "🤖 Modely"

### CRUD operace s poskytovateli

#### Vytvoření nového poskytovatele
1. **GUI:** Klikni "➕ Nový poskytovatel"
2. **Vyplň formulář:**
   - **Název poskytovatele*** (povinné): např. "OpenAI", "Anthropic"
   - **API Base URL**: https://api.example.com/v1
   - **API Klíč**: Zadej API klíč (bude maskován)
   - **API Verze**: v1 (výchozí)
   - **Typ autentizace**: API Key / Bearer Token / Basic Auth
   - **Rate Limit**: počet požadavků za minutu
   - **API klíč je povinný**: ✅ (checkbox)
   - **Aktivní**: ✅ (checkbox)

#### API endpointy pro poskytovatele
```bash
# Vytvoření poskytovatele
POST http://localhost:8001/api/db/llm/providers
Content-Type: application/json

{
  "name": "Nový Provider",
  "base_url": "https://api.example.com/v1",
  "api_key": "sk-...",
  "api_version": "v1",
  "auth_type": "api_key",
  "rate_limit": 1000,
  "api_key_required": true,
  "is_active": true
}

# Úprava poskytovatele
PUT http://localhost:8001/api/db/llm/providers/{provider_id}

# Smazání poskytovatele
DELETE http://localhost:8001/api/db/llm/providers/{provider_id}
```

#### Úprava poskytovatele
1. **GUI:** Klikni ✏️ u poskytovatele v tabulce
2. **Formulář se předvyplní** současnými hodnotami
3. **API klíč** se nezobrazuje z bezpečnostních důvodů
4. **Ulož změny** - klikni "Uložit"

#### Smazání poskytovatele
1. **GUI:** Klikni 🗑️ u poskytovatele
2. **Potvrzení:** Systém se zeptá na potvrzení
3. **Varování:** Smaže se i všechny modely poskytovatele
4. **Akce je nevratná**

### CRUD operace s modely

#### Vytvoření nového modelu
1. **GUI:** Klikni "🤖 Nový model"
2. **Vyplň formulář:**
   - **Poskytovatel*** (povinné): Vyber z dropdown
   - **Název modelu*** (povinné): např. "GPT-4o", "Claude Sonnet"
   - **Model Identifier*** (povinné): ID pro API volání
   - **Context Length**: počet tokenů kontextu
   - **Max Output Tokens**: maximální výstupní tokeny
   - **Temperature**: 0.0 - 2.0 (výchozí 0.7)
   - **Capabilities**: Text, Code, Vision, Function Calling, Reasoning, Multimodal
   - **Výchozí model**: ⭐ (checkbox)
   - **Aktivní**: ✅ (checkbox)

#### API endpointy pro modely
```bash
# Vytvoření modelu
POST http://localhost:8001/api/db/llm/models
Content-Type: application/json

{
  "provider_id": 1,
  "name": "GPT-4o Enhanced",
  "model_identifier": "gpt-4o-enhanced",
  "context_length": 128000,
  "max_tokens": 4096,
  "temperature": 0.7,
  "capabilities": {
    "text": true,
    "code": true,
    "vision": true,
    "function_calling": true,
    "reasoning": false,
    "multimodal": true
  },
  "is_default": false,
  "is_active": true
}

# Úprava modelu
PUT http://localhost:8001/api/db/llm/models/{model_id}

# Smazání modelu
DELETE http://localhost:8001/api/db/llm/models/{model_id}

# Nastavení jako výchozí
PUT http://localhost:8001/api/db/llm/providers/{provider_id}/models/{model_identifier}/set-default
```

#### Úprava modelu
1. **GUI:** Klikni ✏️ u modelu v tabulce
2. **Formulář se předvyplní** současnými hodnotami
3. **Capabilities** se načtou jako checkboxy
4. **Ulož změny** - klikni "Uložit"

#### Smazání modelu
1. **GUI:** Klikni 🗑️ u modelu
2. **Potvrzení:** Systém se zeptá na potvrzení
3. **Akce je nevratná**

#### Nastavení výchozího modelu
1. **GUI:** Klikni ⭐ u modelu
2. **Automaticky** se nastaví jako výchozí pro poskytovatele
3. **Ostatní modely** poskytovatele ztratí výchozí status

### Tabulkové zobrazení

#### Poskytovatelé - sloupce tabulky
- **ID**: Databázové ID
- **Název**: Název poskytovatele
- **API URL**: Base URL pro API
- **API Klíč**: Maskovaný klíč nebo "❌ Chybí"
- **Aktivní**: ✅ / ❌ status
- **Modely**: Počet modelů poskytovatele
- **Akce**: ✏️ Upravit, 🗑️ Smazat, 🧪 Test

#### Modely - sloupce tabulky
- **ID**: Model identifier
- **Název**: Název modelu
- **Poskytovatel**: Název poskytovatele
- **Context**: Délka kontextu (formátováno)
- **Max Tokens**: Maximální výstupní tokeny
- **Výchozí**: ⭐ pokud je výchozí
- **Aktivní**: ✅ / ❌ status
- **Akce**: ✏️ Upravit, 🗑️ Smazat, ⭐ Nastavit výchozí

### Validace a chyby

#### Povinná pole
- **Poskytovatel:** Název je povinný
- **Model:** Název, identifier a poskytovatel jsou povinné

#### Chybové stavy
- **Duplicitní název:** Poskytovatel s tímto názvem už existuje
- **Neplatná URL:** API URL není ve správném formátu
- **Chybějící poskytovatel:** Model nemůže existovat bez poskytovatele
- **API chyba:** Problém s připojením k databázi

#### Úspěšné akce
- **✅ Poskytovatel "OpenAI" byl úspěšně vytvořen!**
- **✅ Model "GPT-4o" byl úspěšně aktualizován!**
- **✅ Model "Claude Sonnet" byl nastaven jako výchozí!**

### Bezpečnostní opatření

#### API klíče
- **Maskování:** Klíče se nezobrazují v GUI
- **Šifrování:** Uloženy šifrovaně v databázi
- **Validace:** Kontrola formátu při ukládání

#### Oprávnění
- **Admin přístup:** Pouze admin uživatelé mohou upravovat
- **Audit log:** Všechny změny se logují
- **Rollback:** Možnost vrácení změn

## 🤖 Modely LLM

### Struktura modelu v databázi
```json
{
  "id": "1_gpt-4o",
  "model_id": 194,
  "provider_id": 1,
  "name": "gpt-4o",
  "model_identifier": "gpt-4o",
  "provider_name": "OpenAI",
  "context_length": 128000,
  "max_tokens": 4096,
  "temperature": 0.7,
  "capabilities": {
    "code": true,
    "text": true,
    "vision": true,
    "multimodal": true
  },
  "is_default": true,
  "is_active": true,
  "api_key_required": true
}
```

### API volání pro modely
```bash
# Získání všech modelů
GET http://localhost:8001/api/db/llm/models

# Filtrování modelů podle poskytovatele
# Použij provider_id z odpovědi
```

## 🔧 Jak GENT používá modely

### 1. Načítání dostupných modelů
```javascript
// Frontend Vue.js
const response = await fetch('http://localhost:8001/api/db/llm/models');
const models = await response.json();

// Filtrování aktivních modelů
const activeModels = models.filter(model => model.is_active);

// Získání výchozích modelů
const defaultModels = models.filter(model => model.is_default);
```

### 2. Volání modelu pro chat
```javascript
// Použití v chat systému
const chatResponse = await chatService.sendMessage({
  message: "Tvoje zpráva",
  model_id: "1_gpt-4o"  // ID modelu z databáze
});
```

### 3. Výběr modelu podle schopností
```javascript
// Modely s podporou kódu
const codeModels = models.filter(model =>
  model.capabilities && model.capabilities.code
);

// Modely s podporou vision
const visionModels = models.filter(model =>
  model.capabilities && model.capabilities.vision
);

// Rychlé modely
const fastModels = models.filter(model =>
  model.capabilities && model.capabilities.fast
);
```

## 🧪 Testování modelů

### Webové rozhraní
1. Přejdi na http://localhost:8003/admin/llm
2. Klikni na záložku "🧪 Testování"
3. Vyber model z dropdown menu (25+ modelů dostupných)
4. Napiš testovací zprávu
5. Stiskni Enter nebo klikni "🚀 Odeslat"

### Programové testování
```bash
# Test konkrétního poskytovatele
curl -X POST "http://localhost:8001/api/db/llm/performance/test-provider/1"

# Odpověď obsahuje:
{
  "success": true,
  "provider_name": "OpenAI",
  "model_tested": "gpt-4o",
  "responseTime": 799,
  "response_text": "Test successful - OpenAI API responding"
}
```

## 📊 Výkonové metriky

### Sledované metriky
- **Čas odpovědi** (ms)
- **Úspěšnost** (%)
- **Celkové tokeny**
- **Náklady** ($)
- **Využití modelů**

### API pro metriky
```bash
# Přehled výkonu
GET http://localhost:8001/api/db/llm/performance/overview?time_range=24h

# Výkon modelů
GET http://localhost:8001/api/db/llm/performance/models?time_range=24h
```

## 🗄️ Databázová struktura

### Tabulky
- **llm_providers** - poskytovatelé LLM
- **llm_models** - modely a jejich konfigurace
- **llm_conversations** - historie konverzací (Supabase)

### Klíčové sloupce
- `provider_id` - ID poskytovatele
- `model_identifier` - identifikátor pro API volání
- `is_default` - výchozí model poskytovatele
- `is_active` - aktivní/neaktivní model
- `capabilities` - JSON s možnostmi modelu

## 🔑 API klíče

### Uložení v databázi
- API klíče jsou uloženy v tabulce `llm_providers`
- Sloupec `api_key` obsahuje skutečné klíče
- V GUI zobrazeny jako "✅ Nastaven" / "❌ Chybí"

### Bezpečnost
- Klíče jsou maskované v odpovědích API
- Přístup pouze přes autorizované endpointy
- Šifrování v databázi

## 🚀 Praktické použití pro GENT

### Doporučené postupy

1. **Výběr modelu podle úkolu:**
   - **Kód:** gpt-4o, claude-3-7-sonnet-latest
   - **Analýza:** claude-sonnet-4-20250514, o1-preview
   - **Rychlé odpovědi:** gpt-4o-mini, gemini-2.0-flash-lite
   - **Lokální:** LMStudio qwq-32b, Ollama modely

2. **Optimalizace nákladů:**
   - Použij mini verze pro jednoduché úkoly
   - Openrouter free modely pro testování
   - Lokální modely pro citlivá data

3. **Monitoring výkonu:**
   - Sleduj čas odpovědi v GUI
   - Exportuj metriky do CSV
   - Testuj poskytovatele pravidelně

### Příklad kompletního workflow
```javascript
// 1. Načti dostupné modely
const models = await fetch('http://localhost:8001/api/db/llm/models').then(r => r.json());

// 2. Vyber model podle potřeby
const codeModel = models.find(m =>
  m.capabilities?.code &&
  m.is_active &&
  m.provider_name === 'OpenAI'
);

// 3. Použij model pro chat
const response = await chatService.sendMessage({
  message: "Napiš Python funkci pro třídění",
  model_id: codeModel.id
});

// 4. Zpracuj odpověď
console.log('Model:', codeModel.name);
console.log('Odpověď:', response.text);
console.log('Tokeny:', response.usage?.total_tokens);
```

## 🔧 Troubleshooting

### Časté problémy
1. **Model se nezobrazuje v dropdown:** Zkontroluj `is_active = true`
2. **API klíč nefunguje:** Otestuj poskytovatele v GUI
3. **Pomalé odpovědi:** Zkus jiný model nebo poskytovatele
4. **Chyba připojení:** Zkontroluj dostupnost API endpointu

### Debug informace
- Konzole prohlížeče: F12 → Console
- API logy: terminál s uvicorn serverem
- Databáze: pgAdmin nebo psql

## 📈 Budoucí rozšíření

### Plánované funkce
- Automatické load balancing
- Caching odpovědí
- Batch processing
- Custom fine-tuned modely
- Real-time monitoring dashboard

## 🔄 Integrace s GENT systémem

### Chat Service integrace
```javascript
// Soubor: frontend-vue/src/services/chat.service.js
// GENT používá tento service pro komunikaci s modely

import { chatService } from '@/services/chat.service';

// Odeslání zprávy s konkrétním modelem
const response = await chatService.sendMessage({
  message: "Tvoje zpráva",
  model_id: "1_gpt-4o",  // ID z LLM Management
  conversation_id: "optional-conversation-id"
});
```

### Supabase integrace
```javascript
// Konverzace se ukládají do Supabase
// Tabulka: conversations
// Obsahuje: message, response, model_used, timestamp, tokens_used
```

### Systémové služby
```bash
# Frontend běží jako systemd služba
sudo systemctl status gent-frontend
sudo systemctl restart gent-frontend

# API server
cd /opt/gent
source venv/bin/activate
python -m uvicorn gent.api.app.app:app --host 0.0.0.0 --port 8001 --reload
```

## 📝 Konfigurace modelů

### Přidání nového modelu
1. **Databáze:** Vlož záznam do `llm_models` tabulky
2. **Provider:** Ujisti se, že provider existuje v `llm_providers`
3. **Capabilities:** Definuj JSON s možnostmi modelu
4. **Test:** Otestuj model přes GUI nebo API

### Příklad SQL pro nový model
```sql
INSERT INTO llm_models (
  provider_id,
  model_name,
  model_identifier,
  context_length,
  max_tokens,
  capabilities,
  is_active,
  is_default
) VALUES (
  1,
  'gpt-4-turbo-2024',
  'gpt-4-turbo-2024-04-09',
  128000,
  4096,
  '{"code": true, "text": true, "vision": true}',
  true,
  false
);
```

## 🎛️ Pokročilé funkce

### Model Selection Strategy
```javascript
// GENT může implementovat inteligentní výběr modelu
function selectBestModel(task, requirements) {
  const models = getAllActiveModels();

  // Filtruj podle capabilities
  let candidates = models.filter(model => {
    if (requirements.code && !model.capabilities?.code) return false;
    if (requirements.vision && !model.capabilities?.vision) return false;
    if (requirements.fast && !model.capabilities?.fast) return false;
    return true;
  });

  // Seřaď podle výkonu a nákladů
  candidates.sort((a, b) => {
    const scoreA = calculateModelScore(a, requirements);
    const scoreB = calculateModelScore(b, requirements);
    return scoreB - scoreA;
  });

  return candidates[0]; // Nejlepší model
}
```

### Batch Processing
```javascript
// Pro zpracování více zpráv najednou
async function processBatch(messages, modelId) {
  const results = [];
  for (const message of messages) {
    const response = await chatService.sendMessage({
      message: message.text,
      model_id: modelId
    });
    results.push({
      input: message,
      output: response,
      timestamp: new Date()
    });
  }
  return results;
}
```

## 🔍 Monitoring a Analytics

### Klíčové metriky pro GENT
- **Response Time:** < 2s pro rychlé úkoly, < 10s pro složité
- **Success Rate:** > 95% pro produkční modely
- **Token Usage:** Sleduj náklady na token
- **Model Availability:** Uptime jednotlivých poskytovatelů

### Export dat
```bash
# CSV export z GUI
# Obsahuje: model, provider, avg_response, success_rate, usage_count

# Programový export
curl "http://localhost:8001/api/db/llm/performance/models?time_range=7d" > performance_data.json
```

## 🛡️ Bezpečnost a Best Practices

### API klíče
- **Rotace:** Pravidelně měň API klíče
- **Monitoring:** Sleduj neobvyklé využití
- **Backup:** Uchovávej záložní klíče

### Rate Limiting
- **OpenAI:** 3,500 RPM (requests per minute)
- **Anthropic:** 4,000 RPM
- **Google:** 300 RPM
- **Lokální:** Bez omezení

### Error Handling
```javascript
// Implementuj fallback strategie
async function robustModelCall(message, preferredModelId) {
  try {
    return await chatService.sendMessage({
      message,
      model_id: preferredModelId
    });
  } catch (error) {
    console.warn(`Model ${preferredModelId} failed, trying fallback`);

    // Zkus výchozí model poskytovatele
    const fallbackModel = getFallbackModel(preferredModelId);
    return await chatService.sendMessage({
      message,
      model_id: fallbackModel.id
    });
  }
}
```

## 📊 Reporting pro GENT

### Denní report
```javascript
// Automatický report o využití modelů
async function generateDailyReport() {
  const metrics = await fetch('/api/db/llm/performance/overview?time_range=24h');
  const models = await fetch('/api/db/llm/performance/models?time_range=24h');

  return {
    date: new Date().toISOString().split('T')[0],
    totalRequests: metrics.totalRequests,
    avgResponseTime: metrics.avgResponseTime,
    totalCost: metrics.totalCost,
    topModels: models.slice(0, 5),
    recommendations: generateRecommendations(metrics, models)
  };
}
```

---

**🎯 Závěr pro GENT:**
LLM Management je tvoje centrální řídící jednotka pro všechny jazykové modely. Používej ji pro:
- ✅ Dynamický výběr nejlepšího modelu pro úkol
- ✅ Monitoring výkonu a nákladů
- ✅ Testování nových modelů a poskytovatelů
- ✅ Optimalizaci celkového systému

**🔗 Rychlé odkazy:**
- GUI: http://localhost:8003/admin/llm
- API: http://localhost:8001/api/db/llm/
- Dokumentace: /opt/gent/docs_gent_final/llm_management.md
