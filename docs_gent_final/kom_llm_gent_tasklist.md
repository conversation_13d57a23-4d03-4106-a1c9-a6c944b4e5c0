# GENT v10 - Komunikace mezi LLM - Implementační Tasklist

## 📋 Přehled projektu
Implementace nové karty "Kom mezi LLM" do stávajícího LLM Management systému pro orchestrovanou komunikaci mezi třemi AI modely s využitím Mem0 memory systému.

## 🎯 Kompatibilita s GENT systémem
- **Frontend**: Rozšíření `frontend-vue/src/views/admin/LlmManagement.vue`
- **Backend**: Nové API endpointy v `gent/api/app/routes/`
- **Database**: Využití stávajících `llm_providers` a `llm_models` tabulek
- **Memory**: Integrace s plánovaným Mem0 MCP serverem
- **Porty**: Frontend 8000, API 8001 (zachování stávající architektury)

---

## 🚀 Fáze 1: Příprava Mem0 MCP serveru

### Mem0 instalace a konfigurace
- [ ] Nainstalovat `mem0ai` package: `pip install mem0ai`
- [ ] Vytvořit Mem0 MCP server konfiguraci v `/opt/gent/config/mcp_config.json`
- [ ] Přidat Mem0 do databáze `mcp_providers` pomocí stávajícího API
- [ ] Otestovat Mem0 připojení přes MCP Management interface
- [ ] Vytvořit Mem0 schema pro komunikaci mezi LLM:
  ```json
  {
    "session_id": "uuid",
    "original_prompt": "string",
    "optimized_prompt": "string", 
    "iteration_data": [
      {
        "iteration": 1,
        "ai_sender": "AI-1",
        "ai_receiver": "AI-2",
        "prompt": "string",
        "response": "string",
        "timestamp": "datetime"
      }
    ],
    "final_validation": "boolean",
    "topic_adherence": "string"
  }
  ```

---

## 🎨 Fáze 2: Frontend rozšíření

### Nová karta v LLM Management
- [ ] Přidat nový tab "Kom mezi LLM" do `LlmManagement.vue` (řádek ~38)
- [ ] Vytvořit novou sekci v template s podmíněným zobrazením
- [ ] Implementovat vstupní formulář:
  - [ ] Model selector (dropdown z existujících modelů)
  - [ ] Prompt input (textarea)
  - [ ] Iteration counter (number input, default: 10)
  - [ ] Submit button

### Dual-panel layout
- [ ] Vytvořit CSS layout pro AI-1 a AI-2 panely
- [ ] Implementovat conversation display komponenty
- [ ] Přidat finální odpověď sekci nahoře
- [ ] Stylovat podle stávajícího tmavého designu GENT

### Real-time komunikace
- [ ] Implementovat WebSocket/SSE pro live updates
- [ ] Vytvořit progress indicator pro iterace
- [ ] Implementovat auto-scroll pro nové zprávy
- [ ] Přidat manual input možnosti pro AI panely

---

## 🔧 Fáze 3: Backend API

### Nové API endpointy
- [ ] Vytvořit `gent/api/app/routes/llm_communication_routes.py`
- [ ] Implementovat endpointy:
  - [ ] `POST /api/llm/communication/start` - spuštění komunikace
  - [ ] `GET /api/llm/communication/session/{session_id}` - stav session
  - [ ] `POST /api/llm/communication/manual-input` - manuální input
  - [ ] `GET /api/llm/communication/history/{session_id}` - historie
  - [ ] `DELETE /api/llm/communication/session/{session_id}` - cleanup

### Pydantic modely
- [ ] `CommunicationSessionCreate` model
- [ ] `CommunicationResponse` model  
- [ ] `ManualInput` model
- [ ] `SessionHistory` model

### Registrace v main app
- [ ] Přidat router do `gent/api/app/app.py` (řádek ~68)

---

## 🤖 Fáze 4: AI Model orchestrace

### AI-0 (Prompt Optimizer)
- [ ] Implementovat prompt optimization service
- [ ] Vytvořit template pro reformulaci lidských promptů
- [ ] Implementovat topic validation algoritmus
- [ ] Vytvořit final answer validation system

### AI-1 & AI-2 (Processors)
- [ ] Implementovat iterativní komunikační logiku
- [ ] Vytvořit response generation handlers
- [ ] Implementovat Mem0 integration pro načítání/ukládání
- [ ] Vytvořit conversation flow controller

### Workflow orchestrace
- [ ] Implementovat iteration loop controller
- [ ] Vytvořit prompt tagging system
- [ ] Implementovat automatic prompt distribution
- [ ] Vytvořit session management

---

## 🔄 Fáze 5: Mem0 integrace

### Memory operations
- [ ] Implementovat Mem0 client wrapper
- [ ] Vytvořit session storage/retrieval
- [ ] Implementovat conversation persistence
- [ ] Vytvořit cleanup mechanismus pro staré sessions

### Change detection
- [ ] Implementovat Mem0 change listener
- [ ] Vytvořit real-time notification systém
- [ ] Implementovat automatic UI updates

---

## 🧪 Fáze 6: Testing & optimalizace

### Unit testy
- [ ] Vytvořit testy pro AI model integrace
- [ ] Implementovat testy pro Mem0 operations
- [ ] Vytvořit testy pro workflow orchestrace
- [ ] Implementovat frontend component testy

### Integration testy
- [ ] Testovat end-to-end workflow
- [ ] Validovat memory persistence
- [ ] Testovat error handling scenarios
- [ ] Ověřit performance s vysokým počtem iterací

---

## 📚 Fáze 7: Dokumentace

### Uživatelská dokumentace
- [ ] Vytvořit user guide v `/opt/gent/docs_gent_final/`
- [ ] Dokumentovat API endpoints
- [ ] Vytvořit troubleshooting guide
- [ ] Připravit demo scenarios

### Technická dokumentace
- [ ] Dokumentovat architekturu systému
- [ ] Vytvořit API dokumentaci
- [ ] Dokumentovat Mem0 schema
- [ ] Vytvořit deployment guide

---

## ✅ Definition of Done

- [ ] Nová karta "Kom mezi LLM" je funkční v LLM Management
- [ ] Mem0 MCP server je integrován a funkční
- [ ] AI-0, AI-1, AI-2 komunikace funguje iterativně
- [ ] Real-time zobrazení konverzací funguje
- [ ] Manual input do AI panelů funguje
- [ ] Session management a cleanup funguje
- [ ] Všechny testy projdou
- [ ] Dokumentace je kompletní
- [ ] Systém je kompatibilní se stávající GENT architekturou

---

**Odhadovaný čas implementace:** 2-3 týdny  
**Priorita:** Vysoká  
**Závislosti:** Stávající LLM management, MCP systém, Mem0 instalace
