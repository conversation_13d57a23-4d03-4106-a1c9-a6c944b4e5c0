# 🔧 Oprava překrývajícího se textu v Admin System

## 🚨 Problém

Na stránce `/admin/system` se překrývalo písmo v kartách stavu:
- Text se překrýval s indikátory (🟢🔴)
- Nadpisy a hodnoty neměly dostatek místa
- Indikátory zasahovaly do textu
- Problém byl viditelný zejména na menších obrazovkách

## 🔍 Příčina

V CSS souboru `frontend-vue/src/styles/dashboard.css`:
- `.stat-card` měl `position: relative` 
- `.stat-indicator` měl `position: absolute` s `top: 1rem; right: 1rem`
- Text neměl rezervované místo pro indikátor
- Chyběly responsive úpravy pro menší obrazovky

## ✅ Řešení

### 1. Oprava `.stat-card`
```css
.stat-card {
  background-color: var(--background-secondary, #222);
  border-radius: 8px;
  padding: 1.5rem;
  padding-right: 3rem; /* Více místa pro indikátor */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  transition: all 0.3s ease;
  min-height: 120px; /* Minimální výška pro konzistenci */
}
```

### 2. Oprava `.stat-indicator`
```css
.stat-indicator {
  position: absolute;
  top: 1rem;
  right: 1rem;
  font-size: 1.5rem; /* Větší indikátor */
  line-height: 1;
  z-index: 1; /* Nad textem */
}
```

### 3. Oprava textových elementů
```css
.stat-card h3 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 1rem;
  color: var(--text-secondary, #aaa);
  padding-right: 2rem; /* Místo pro indikátor */
  line-height: 1.2;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  padding-right: 2rem; /* Místo pro indikátor */
  line-height: 1.2;
  word-wrap: break-word;
}

.stat-info {
  font-size: 0.9rem;
  color: var(--text-secondary, #aaa);
  padding-right: 2rem; /* Místo pro indikátor */
  line-height: 1.3;
}
```

### 4. Responsive úpravy
```css
@media (max-width: 768px) {
  .stat-card {
    padding: 1rem;
    padding-right: 2.5rem;
    min-height: 100px;
  }

  .stat-indicator {
    font-size: 1.2rem;
    top: 0.75rem;
    right: 0.75rem;
  }

  .stat-card h3 {
    font-size: 0.9rem;
    padding-right: 1.5rem;
  }

  .stat-value {
    font-size: 1.3rem;
    padding-right: 1.5rem;
  }

  .stat-info {
    font-size: 0.8rem;
    padding-right: 1.5rem;
  }
}
```

## 🎯 Výsledek

### Opravené problémy:
- ✅ Text se už nepřekrývá s indikátory
- ✅ Karty mají konzistentní výšku
- ✅ Indikátory jsou větší a lépe viditelné
- ✅ Responsive design pro mobily a tablety
- ✅ Lepší čitelnost na všech zařízeních

### Stránka `/admin/system` nyní zobrazuje:
- **API Server**: Status s 🟢/🔴 indikátorem
- **PostgreSQL**: Připojení s počtem databází
- **LLM Modely**: Počet modelů a poskytovatelů
- **System Health**: Zdravotní stav s uptime

### Testováno na:
- ✅ Desktop (1920x1080)
- ✅ Tablet (768x1024)
- ✅ Mobil (375x667)
- ✅ Různé velikosti oken

## 🔧 Technické detaily

### Klíčové změny:
1. **Padding-right**: Rezervované místo pro indikátory
2. **Min-height**: Konzistentní výška karet
3. **Z-index**: Správné vrstvení elementů
4. **Line-height**: Lepší řádkování textu
5. **Word-wrap**: Zalamování dlouhého textu
6. **Responsive**: Úpravy pro menší obrazovky

### Soubory změněny:
- `frontend-vue/src/styles/dashboard.css`

### Komponenty ovlivněny:
- `frontend-vue/src/views/admin/SystemMonitor.vue`
- Všechny `.stat-card` elementy

**UI problém s překrývajícím se textem je vyřešen!** 🎯
