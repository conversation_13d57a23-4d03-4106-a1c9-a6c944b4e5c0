# Oprava obecných odpovědí v Kom mezi LLM

## Problém

AI-1 vracel obecné fráze místo konkrétních řešení:
```
"Pochopeno, budu pokračovat v práci na základě analýzy od AI-2 a budu implementovat doporučená opatření. Pokračujeme s rozvojem a zlepšováním předchozí práce."
```

## Příčina

AI-1 dostával <PERSON> prompt v iteracích 2+:
- Neměl připomenutý původní úkol
- Dostal pouze obecný pokyn "pokračuj v práci"
- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CO má vlastně řešit

## Řešení

### **1. Opravený AI-1 prompt (non-code)**

#### PŘED:
```javascript
ai1Prompt = `Jsi AI-1 primary processor v iteraci ${iteration}.
${iteration === 1 ? 'Zpracuj tento optimalizovaný prompt:' : 'Pokračuj v práci na základě analýzy od AI-2:'}
${inputPrompt}
Vrať pouze svou odpověď bez dalších komentářů.`;
```

#### PO:
```javascript
ai1Prompt = `Jsi AI-1 primary processor v iteraci ${iteration}.

PŮVODNÍ ÚKOL UŽIVATELE: "${this.currentSession.originalPrompt}"
OPTIMALIZOVANÉ ZADÁNÍ OD AI-0: "${this.currentSession.optimizedPrompt}"

${iteration === 1 ? 
  `ÚKOL: Vyřeš tento problém podle optimalizovaného zadání od AI-0.
   Vytvoř kompletní, detailní odpověď na původní otázku uživatele.` 
  : 
  `ÚKOL: Vylepši své předchozí řešení na základě analýzy od AI-2, ale VŽDY se drž původního úkolu!
   
   FEEDBACK OD AI-2: ${inputPrompt}
   
   DŮLEŽITÉ:
   - Řeš stále stejný problém: "${this.currentSession.originalPrompt}"
   - Zohledni doporučení od AI-2
   - Vytvoř lepší, kompletní odpověď na původní otázku
   - NEPIŠ obecné fráze typu "budu pokračovat" - VYŘEš TO!`}

Vrať pouze konkrétní řešení původního problému.`;
```

### **2. Opravený AI-1 prompt (code)**

Přidal jsem:
```javascript
KRITICKÉ INSTRUKCE:
- VŽDY se drž původního úkolu: "${this.currentSession.originalPrompt}"
- NEPIŠ obecné fráze typu "budu pokračovat" - NAPIŠ KONKRÉTNÍ KÓD!
- Vrať pouze vylepšený kód na základě doporučení od AI-2 - KONKRÉTNÍ KÓD, ne fráze!
```

### **3. Opravený AI-2 prompt (non-code)**

#### PŘED:
```javascript
ai2Prompt = `Jsi AI-2 secondary processor v iteraci ${iteration}.
Analyzuj a rozšiř následující odpověď od AI-1:
${inputFromAI1}
Tvým úkolem je:
1. Analyzovat kvalitu a úplnost odpovědi AI-1...`;
```

#### PO:
```javascript
ai2Prompt = `Jsi AI-2 secondary processor v iteraci ${iteration}.

PŮVODNÍ ÚKOL UŽIVATELE: "${this.currentSession.originalPrompt}"
OPTIMALIZOVANÉ ZADÁNÍ OD AI-0: "${this.currentSession.optimizedPrompt}"

ODPOVĚĎ OD AI-1: ${inputFromAI1}

TVŮJ ÚKOL - ANALÝZA ODPOVĚDI:
1. Zkontroluj, zda odpověď AI-1 řeší původní úkol uživatele
2. Ověř, že odpověď odpovídá optimalizovanému zadání od AI-0
3. Analyzuj kvalitu a úplnost odpovědi
4. Identifikuj chybějící aspekty nebo možná vylepšení
5. Navrhni konkrétní doporučení pro AI-1 v další iteraci

KRITICKÉ INSTRUKCE:
- VŽDY se drž původního úkolu: "${this.currentSession.originalPrompt}"
- Pokud AI-1 neodpověděl na původní otázku, JASNĚ to řekni
- Doporuč konkrétní kroky, jak lépe vyřešit původní problém
- Nepiš obecnosti, buď specifický`;
```

## Klíčové změny

### ✅ **Vždy připomenout původní úkol**
- AI-1 i AI-2 mají vždy k dispozici původní otázku uživatele
- AI-1 i AI-2 mají vždy k dispozici optimalizované zadání od AI-0

### ✅ **Zakázat obecné fráze**
- Explicitní zákaz "budu pokračovat" a podobných frází
- Požadavek na konkrétní řešení, ne meta-komentáře

### ✅ **Jasné instrukce pro iterace**
- AI-1 v iteraci 2+ ví, že má zlepšit KONKRÉTNÍ řešení
- AI-2 kontroluje relevanci k původnímu úkolu

### ✅ **Specifické požadavky**
- "VYŘEš TO!" místo "pokračuj v práci"
- "KONKRÉTNÍ KÓD" místo obecných slibů
- "konkrétní řešení původního problému"

## Očekávaný výsledek

Po opravě by AI-1 měl vracet:
- **Konkrétní kód** (pro code požadavky)
- **Konkrétní odpovědi** (pro text požadavky)
- **Skutečná řešení** původního problému
- **Zlepšené verze** na základě feedbacku od AI-2

Místo obecných frází typu "budu pokračovat" nebo "implementuji doporučení".

## Status

✅ Opravy implementovány
✅ Prompty aktualizovány
✅ Připraveno k testování

Nyní by "Kom mezi LLM" měl vracet konkrétní, užitečné odpovědi!
