# GENT - Aplikace, služby a porty

## 🎯 Účel dokumentu
Tento dokument obsahuje kompletní přehled všech portů, služ<PERSON> a aplikací používaných v GENT projektu. Slouží jako reference pro GENT AI, jin<PERSON> AI systémy a vývojáře pro správnou konfiguraci a komunikaci mezi komponentami.

## 🌐 Hlavní aplikace a porty

### 🖥️ Frontend aplikace (Vue.js)
- **Port:** `8000`
- **URL:** `http://localhost:8000`
- **Služba:** `gent-frontend.service` (systemd)
- **Spouštěcí skript:** `/opt/gent/run_frontend.sh`
- **Adresář:** `/opt/gent/frontend-vue/`
- **Účel:** Webové uživatelské rozhraní GENT systému
- **Konfigurace:**
  - Environment: `NODE_ENV=production`
  - API URL: `VITE_API_URL=http://localhost:8001`
  - Host: `0.0.0.0` (dostupné ze všech síťových rozhraní)

### 🔧 API server (FastAPI)
- **Port:** `8001`
- **URL:** `http://localhost:8001`
- **Služba:** Python aplikace (uvicorn)
- **Spouštěcí příkaz:** `python -m uvicorn gent.api.app.app:app --host 0.0.0.0 --port 8001 --reload`
- **Adresář:** `/opt/gent/gent/api/`
- **Účel:** Hlavní API server pro komunikaci s frontendem a externími systémy
- **Konfigurace:**
  - Host: `0.0.0.0`
  - Debug: `true` (development), `false` (production)
  - Timeout: `60s` (production), `120s` (development)
  - CORS origins: `["http://localhost:8000", "http://localhost:8001"]`

## 🗄️ Databázové služby

### 🐘 PostgreSQL
- **Port:** `5432`
- **Host:** `localhost`
- **Služba:** Nativní PostgreSQL služba (ne Docker)
- **Databáze:**
  - `gentdb` - hlavní databáze
  - `gentdb_knowledge` - znalostní báze
  - `gentdb_logs` - logy
  - `gentdb_analytics` - analytika
- **Uživatel:** `gent_app`
- **Heslo:** `gent1234secure`
- **Connection string:** `postgresql://gent_app:gent1234secure@localhost:5432/gentdb`
- **Účel:** Hlavní databáze pro LLM modely, konfiguraci, uživatele

### 📊 PostgREST (volitelné)
- **Port:** `3000`
- **URL:** `http://localhost:3000`
- **Konfigurace:** `/opt/gent/config/docker/postgrest/postgrest.conf`
- **Účel:** REST API pro přímý přístup k PostgreSQL databázi
- **Status:** Konfigurováno, ale nepoužíváno v produkci

## 🔄 Cache a messaging

### 🔴 Redis
- **Port:** `6379`
- **Host:** `redis` (Docker) nebo `localhost`
- **Účel:** Cache, session storage, message queue
- **Konfigurace:**
  - DB: `0`
  - SSL: `false` (development), `true` (production)
  - Timeout: `10s` (development), `5s` (production)

## 📈 Monitoring a metriky

### 📊 Prometheus
- **Port:** `9090`
- **URL:** `http://localhost:9090`
- **Konfigurace:** `/opt/gent/config/docker/prometheus/prometheus.yml`
- **Účel:** Sběr metrik z GENT API a dalších služeb
- **Targets:**
  - `localhost:9090` - Prometheus sám
  - `api:8001` - GENT API metriky
  - `redis:6379` - Redis metriky
  - `postgres-exporter:9187` - PostgreSQL metriky (plánováno)

### 📊 Grafana (plánováno)
- **Port:** `3001` (navrhovaný)
- **Účel:** Vizualizace metrik z Prometheus

## 🤖 LLM poskytovatelé a externí služby

### 🏠 Lokální LLM služby

#### LM Studio
- **Port:** `1234`
- **Host:** `***************`
- **URL:** `http://***************:1234`
- **API endpoint:** `/v1/chat/completions`
- **Kompatibilita:** OpenAI API
- **Modely:** qwq-32b, qwen2.5-14b-deepresearch
- **Účel:** Lokální inference pro citlivá data

#### Ollama
- **Port:** `11434`
- **Host:** `***************`
- **URL:** `http://***************:11434`
- **API endpoint:** `/v1/chat/completions`
- **Kompatibilita:** OpenAI API
- **Účel:** Lokální open-source modely

### ☁️ Externí LLM API

#### OpenAI
- **URL:** `https://api.openai.com/v1`
- **Port:** `443` (HTTPS)
- **Modely:** gpt-4o, gpt-4o-mini, gpt-4-turbo, o1-preview, o1-mini, o3-mini
- **Rate limit:** 3,500 RPM

#### Anthropic
- **URL:** `https://api.anthropic.com`
- **Port:** `443` (HTTPS)
- **Modely:** claude-sonnet-4-20250514, claude-3-7-sonnet-latest
- **Rate limit:** 4,000 RPM

#### Google AI
- **URL:** `https://generativelanguage.googleapis.com/v1beta/openai/`
- **Port:** `443` (HTTPS)
- **Modely:** gemini-2.0-flash, gemini-2.0-flash-lite, gemini-2.5-flash-preview-05-20
- **Rate limit:** 300 RPM

#### OpenRouter
- **URL:** `https://openrouter.ai/api/v1`
- **Port:** `443` (HTTPS)
- **Modely:** deephermes-3-mistral-24b-preview, claude-opus-4, qwen3-235b-a22b
- **Účel:** Agregátor různých LLM modelů

## 🔌 MCP servery (Model Context Protocol)

### 📁 Filesystem MCP
- **Příkaz:** `npx -y @modelcontextprotocol/server-filesystem /opt/gent`
- **Účel:** Přístup k souborovému systému

### 🔍 Search MCP servery
- **Brave Search:** `npx -y @modelcontextprotocol/server-brave-search`
- **Tavily:** `npx -y tavily-mcp`
- **Perplexity:** `npx -y perplexity-mcp`

### 🌐 Web MCP
- **Fetch:** `npx -y fetch-mcp`
- **Účel:** Načítání obsahu webových stránek

### 🧠 Thinking MCP
- **Sequential Thinking:** `npx -y @modelcontextprotocol/server-sequentialthinking`
- **Účel:** Sekvenční myšlení a řešení problémů

### 📝 Git MCP
- **Git:** `npx -y mcp-server-git`
- **Účel:** Git operace

### 🎯 Custom GENT MCP servery
- **Project Server:** `node /opt/gent/scripts/mcp/project-server.js`
- **Workflow Server:** `node /opt/gent/scripts/mcp/workflow-server.js`

## 🔧 Mikroslužby

### 🔄 LiteLLM služba
- **Adresář:** `/opt/gent/micro_services/litellm/`
- **Účel:** Unifikované rozhraní pro různé LLM API
- **Status:** Konfigurováno, ale nepoužíváno (přímé API volání preferováno)

## 🌍 Síťová konfigurace

### 🔒 CORS nastavení
**Development:**
```json
{
  "origins": ["http://localhost:8000", "http://localhost:8001", "*"]
}
```

**Production:**
```json
{
  "origins": ["https://gent.example.com", "https://api.gent.example.com"]
}
```

### 🛡️ Rate limiting
**Development:**
- API: 100 requests/minute, burst 20

**Production:**
- API: 60 requests/minute, burst 10

## 📋 Přehled všech portů

| Port | Služba | Protokol | Status | Účel |
|------|--------|----------|--------|------|
| 8000 | GENT Frontend | HTTP | ✅ Aktivní | Webové UI |
| 8001 | GENT API | HTTP | ✅ Aktivní | REST API |
| 5432 | PostgreSQL | TCP | ✅ Aktivní | Hlavní databáze |
| 6379 | Redis | TCP | 🔄 Volitelné | Cache |
| 3000 | PostgREST | HTTP | 🔄 Volitelné | DB REST API |
| 9090 | Prometheus | HTTP | 🔄 Volitelné | Metriky |
| 3001 | Grafana | HTTP | 📋 Plánováno | Dashboardy |
| 1234 | LM Studio | HTTP | 🌐 Externí | Lokální LLM |
| 11434 | Ollama | HTTP | 🌐 Externí | Lokální LLM |
| 443 | Externí API | HTTPS | 🌐 Externí | Cloud LLM |

## 🚀 Spouštění služeb

### Systemd služby
```bash
# Frontend
sudo systemctl start gent-frontend
sudo systemctl enable gent-frontend
sudo systemctl status gent-frontend

# PostgreSQL (nativní)
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

### Manuální spouštění
```bash
# API server
cd /opt/gent
source venv/bin/activate
python -m uvicorn gent.api.app.app:app --host 0.0.0.0 --port 8001 --reload

# Frontend (development)
cd /opt/gent/frontend-vue
npm run dev -- --host 0.0.0.0 --port 8000
```

## 🔍 Diagnostika portů

### Kontrola aktivních portů
```bash
# Všechny GENT porty
sudo netstat -tlnp | grep -E ':(8000|8001|5432|6379|3000|9090)'

# Specifické služby
sudo ss -tlnp | grep :8001  # API
sudo ss -tlnp | grep :8000  # Frontend
sudo ss -tlnp | grep :5432  # PostgreSQL
```

### Testování dostupnosti
```bash
# API health check
curl http://localhost:8001/health

# Frontend
curl http://localhost:8000

# PostgreSQL
psql -h localhost -p 5432 -U gent_app -d gentdb -c "SELECT version();"
```

## 🎯 Doporučení pro GENT AI

### Při komunikaci mezi komponentami:
1. **Frontend → API:** Vždy používej `http://localhost:8001`
2. **API → Database:** Používej connection string z `config/db.json`
3. **API → LLM:** Používej URL z databáze `llm_providers` tabulky
4. **Monitoring:** Kontroluj dostupnost služeb před voláním

### Při konfiguraci nových služeb:
1. **Vyhni se konfliktům portů** - zkontroluj tabulku výše
2. **Používej CORS** pro webové služby
3. **Implementuj health checks** pro monitoring
4. **Dokumentuj nové porty** v tomto souboru

---

**💡 Tip pro GENT:** Tento dokument je tvoje mapa síťové infrastruktury. Používej ho pro správnou konfiguraci komunikace mezi všemi komponentami systému!
