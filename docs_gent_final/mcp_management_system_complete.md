# GENT v10 - MCP Management System - Kompletní dokumentace

## 📋 Přehled systému

**MCP (Model Context Protocol) Management** je kompletní systém pro správu a testování MCP serverů v GENT v10. Umož<PERSON><PERSON><PERSON>, úpravu, ma<PERSON><PERSON><PERSON> a testování různých MCP poskytovatelů s jejich nástro<PERSON>.

## 🏗️ Architektura systému

### Frontend (Vue.js)
- **Soubor**: `frontend-vue/src/views/admin/McpManagement.vue`
- **URL**: `http://localhost:8000/admin/mcp`
- **Porty**: Frontend běží na portu 8000

### Backend API (FastAPI)
- **Soubory**: 
  - `gent/api/app/routes/mcp_routes.py` - CRUD operace
  - `gent/api/app/routes/mcp_test_routes.py` - testování MCP serverů
- **Port**: API běží na portu 8001
- **Base URL**: `/api/mcp/`

### Datab<PERSON><PERSON> (PostgreSQL)
- **Port**: 5432
- **Datab<PERSON>ze**: `gentdb`
- **Hlavní tabulky**:
  - `mcp_providers` - poskytovatelé MCP serverů
  - `mcp_tools` - nástroje jednotlivých poskytovatelů

## 🗄️ Databázová struktura

### Tabulka: mcp_providers
```sql
CREATE TABLE mcp_providers (
    provider_id SERIAL PRIMARY KEY,
    provider_name VARCHAR(100) UNIQUE NOT NULL,
    display_name VARCHAR(200),
    provider_type VARCHAR(50) NOT NULL,
    command TEXT NOT NULL,
    api_key TEXT,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    is_custom BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Tabulka: mcp_tools
```sql
CREATE TABLE mcp_tools (
    tool_id SERIAL PRIMARY KEY,
    provider_id INTEGER REFERENCES mcp_providers(provider_id),
    tool_name VARCHAR(100) NOT NULL,
    tool_identifier VARCHAR(200) NOT NULL,
    display_name VARCHAR(200),
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    auto_approve BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🔧 Funkční MCP servery

### 1. Fetch (Web scraping)
- **Název**: `fetch`
- **Typ**: `web_fetch`
- **API klíč**: Nepotřebuje
- **Funkce**: Stahování obsahu z webových stránek
- **Formáty výstupu**: HTML, TEXT, MARKDOWN, JSON
- **Testování**: `https://svitsol.cz`

### 2. Brave Search
- **Název**: `brave-search`
- **Typ**: `web_search`
- **API klíč**: `BSARir7CGmpWKz5mvNgGJyYp3yV8CDn`
- **Funkce**: Webové vyhledávání
- **Typy vyhledávání**: Web, Zprávy, Obrázky, Videa
- **Testování**: `svitsol.cz`

### 3. Tavily Search
- **Název**: `tavily`
- **Typ**: `web_search`
- **API klíč**: `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`
- **Funkce**: AI-powered vyhledávání
- **Typy vyhledávání**: Web, Zprávy, Obrázky, Videa
- **Testování**: `svitsol.cz company information`

### 4. Perplexity AI
- **Název**: `perplexity`
- **Typ**: `ai_search`
- **API klíč**: `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`
- **Funkce**: AI odpovědi na otázky
- **Typy odpovědí**: Stručná, Detailní, Kreativní, Faktická
- **Testování**: `Co je svitsol.cz?`

## 🎛️ GUI rozhraní

### Hlavní sekce
1. **Poskytovatelé** - přehled všech MCP serverů
2. **Správa** - CRUD operace (přidat/upravit/smazat)
3. **Testování** - testování jednotlivých MCP serverů
4. **Statistiky** - přehled systému

### CRUD operace
- **➕ Přidat MCP server** - formulář pro nový server
- **✏️ Upravit** - editace existujícího serveru
- **🗑️ Smazat** - mazání serveru (s potvrzením)

### Testování
- **Výběr serveru** - dropdown s dostupnými servery
- **Input pole** - zadání URL/dotazu
- **Formát výstupu** - pro fetch (HTML/TEXT/MARKDOWN/JSON)
- **Typ vyhledávání** - pro search enginy
- **Výsledky** - zobrazení odpovědi v JSON formátu

## 🔌 API endpointy

### CRUD operace
```
GET    /api/mcp/providers           - seznam poskytovatelů
GET    /api/mcp/providers/{id}      - detail poskytovatele
POST   /api/mcp/providers           - přidat poskytovatele
PUT    /api/mcp/providers/{id}      - upravit poskytovatele
DELETE /api/mcp/providers/{id}      - smazat poskytovatele

GET    /api/mcp/tools               - seznam všech nástrojů
GET    /api/mcp/tools/{id}          - detail nástroje
GET    /api/mcp/stats               - statistiky systému
```

### Testování
```
POST   /api/mcp/test/fetch          - test fetch serveru
POST   /api/mcp/test/brave-search   - test Brave Search
POST   /api/mcp/test/tavily         - test Tavily
POST   /api/mcp/test/perplexity     - test Perplexity
```

## 🚀 Jak spustit systém

### 1. Spuštění služeb
```bash
sudo systemctl start gent-api
sudo systemctl start gent-frontend
sudo systemctl start postgresql
```

### 2. Přístup k rozhraní
- **Frontend**: http://localhost:8000/admin/mcp
- **API dokumentace**: http://localhost:8001/docs

### 3. Testování
1. Otevři MCP Management
2. Přejdi na tab "Testování"
3. Vyber MCP server
4. Zadej testovací input
5. Spusť test

## 🔑 API klíče

### Aktuální platné klíče
- **Brave Search**: `BSARir7CGmpWKz5mvNgGJyYp3yV8CDn`
- **Tavily**: `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`
- **Perplexity**: `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`

### Uložení v databázi
API klíče jsou uloženy v tabulce `mcp_providers` ve sloupci `api_key`.

## 🛠️ Řešení problémů

### Časté problémy
1. **API klíč nefunguje** - zkontroluj platnost a formát
2. **Server neodpovídá** - restart `gent-api` služby
3. **Databáze nedostupná** - restart `postgresql` služby
4. **Frontend se nenačte** - restart `gent-frontend` služby

### Logy
- **API logy**: `journalctl -u gent-api -f`
- **Frontend logy**: `journalctl -u gent-frontend -f`
- **Databáze logy**: `journalctl -u postgresql -f`

## 📝 Changelog

### Verze 1.0 (aktuální)
- ✅ Kompletní CRUD operace pro MCP servery
- ✅ Testování 4 MCP serverů (fetch, brave-search, tavily, perplexity)
- ✅ Tmavý design konzistentní s GENT
- ✅ Skutečné zápisy do databáze
- ✅ Rozšířené možnosti testování (formáty, typy)
- ✅ API klíče uložené v databázi

### Budoucí vylepšení
- 🔄 Automatické objevování MCP serverů
- 🔄 Mem0 Memory Server integrace
- 🔄 Batch operace pro více serverů
- 🔄 Monitoring a metriky výkonu

---

**Vytvořeno**: 2024
**Pro**: GENT v10 AI systém
**Autor**: AI Assistant s uživatelem
**Status**: Produkční verze
