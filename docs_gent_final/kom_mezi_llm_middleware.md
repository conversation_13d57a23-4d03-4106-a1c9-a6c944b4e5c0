# Kom mezi LLM - Middleware implementace

## Přehled

Implementoval jsem middleware systém, který obohacuje zprávy mezi AI agenty o metadata a instrukce. Toto řeší problém obecných odpovědí a pomáhá AI-1 a AI-2 skuteč<PERSON>ě spolupracovat na řešení.

## Problém před middleware

AI-1 a AI-2 si často "povídaly kolem" místo řešení:
- Opakování stejných frází
- Obecné sliby typu "budu pokračovat"
- Ztráta fokus na původní úkol
- Žádný sledování pokroku

## Middleware řešení

### **Funkce `processMessage(message, fromAgent, toAgent, iteration, history)`**

```javascript
// Middleware obohacuje každou zprávu o:
const enrichedMessage = `
=== MIDDLEWARE INSTRUKCE ===
ITERACE: ${iteration}/${maxIterations}
PŮVODNÍ ÚKOL: "${originalPrompt}"
AGENT: ${toAgent}

POKROK DOSUD:
✅ Základní kód napsán
🔍 Implementovat logiku

ZAKÁZÁNO OPAKOVAT:
❌ "budu pokračovat" - používáno opakovaně

TVŮJ KONKRÉTNÍ ÚKOL PRO TUTO ITERACI:
🎯 Zaměř se na: Implementovat logiku
🔧 Vytvoř konkrétní řešení (ne plány)

=== PŮVODNÍ ZPRÁVA ===
${originalMessage}

=== INSTRUKCE ===
- NEPIŠ obecné fráze nebo meta-komentáře
- ŘEŠ konkrétně jeden z aspektů výše
- NEPAKUJ to, co už bylo řečeno
- POSUŇ řešení dopředu
`;
```

## Klíčové komponenty

### **1. Detekce opakování (`detectRepeatedConcepts`)**
```javascript
const commonPhrases = [
  'budu pokračovat', 'implementuji doporučení', 'rozvijem', 'vylepším',
  'pokračujeme', 'budeme pracovat', 'zaměříme se', 'můžeme přidat'
];

// Sleduje a zakazuje opakování těchto frází
```

### **2. Analýza pokroku (`analyzeProgress`)**
```javascript
// Pro kód:
- hasCode: Základní kód napsán ✅/❌
- hasLogic: Logika implementována ✅/❌  
- hasComments: Komentáře přidány ✅/❌

// Pro text:
- hasStructure: Základní struktura odpovědi ✅/❌
- hasDetails: Konkrétní příklady ✅/❌
```

### **3. Generování úkolů (`generateNewAspects`)**
```javascript
// Pro AI-1:
🎯 Zaměř se na: [konkrétní chybějící aspekt]
🔧 Vytvoř konkrétní řešení (ne plány)
📝 Buď specifický a detailní

// Pro AI-2:
🔍 Zkontroluj kvalitu řešení od AI-1
💡 Navrhni konkrétní vylepšení
🚀 Identifikuj chybějící funkcionality
```

## Implementace v kódu

### **AI-1 proces s middleware:**
```javascript
async processWithAI1(iteration) {
  // ... získání základního promptu ...
  
  // MIDDLEWARE: Obohacení zprávy
  const history = [
    ...this.ai1Messages.map(m => ({agent: 'AI-1', content: m.content})),
    ...this.ai2Messages.map(m => ({agent: 'AI-2', content: m.content}))
  ];
  
  inputPrompt = this.processMessage(
    inputPrompt, 
    iteration === 1 ? 'AI-0' : 'AI-2', 
    'AI-1', 
    iteration, 
    history
  );
  
  // ... odeslání na LLM ...
}
```

### **AI-2 proces s middleware:**
```javascript
async processWithAI2(iteration) {
  // ... získání odpovědi od AI-1 ...
  
  // MIDDLEWARE: Obohacení zprávy
  const history = [
    ...this.ai1Messages.map(m => ({agent: 'AI-1', content: m.content})),
    ...this.ai2Messages.map(m => ({agent: 'AI-2', content: m.content}))
  ];
  
  inputFromAI1 = this.processMessage(
    inputFromAI1 + contextFromMem0, 
    'AI-1', 
    'AI-2', 
    iteration, 
    history
  );
  
  // ... odeslání na LLM ...
}
```

## Výhody middleware

### ✅ **Zabrání opakování**
- Sleduje použité fráze
- Explicitně zakazuje opakování
- Nutí hledat nová řešení

### ✅ **Dá jasný směr**
- Konkrétní úkol pro každou iteraci
- Fokus na chybějící aspekty
- Zakáže meta-komentáře

### ✅ **Sleduje pokrok**
- Ukazuje, co se už vyřešilo
- Identifikuje, co ještě chybí
- Směřuje k dokončení

### ✅ **Strukturuje komunikaci**
- Jasný formát zpráv
- Konzistentní instrukce
- Měřitelný pokrok

## Očekávané zlepšení

### **PŘED middleware:**
```
AI-1: "Pochopeno, budu pokračovat v práci na základě analýzy od AI-2..."
AI-2: "Doporučuji rozvinout předchozí práci..."
AI-1: "Budu implementovat doporučení..."
```

### **PO middleware:**
```
AI-1: [konkrétní kód/řešení podle middleware instrukcí]
AI-2: [specifická analýza s konkrétními doporučeními]
AI-1: [vylepšené řešení na základě konkrétního feedbacku]
```

## Status

✅ Middleware implementován
✅ Integrace do AI-1 a AI-2 procesů
✅ Detekce opakování
✅ Analýza pokroku
✅ Generování konkrétních úkolů
✅ Připraveno k testování

Middleware by měl výrazně zlepšit kvalitu komunikace mezi AI agenty a vést k konkrétním, užitečným výsledkům!
