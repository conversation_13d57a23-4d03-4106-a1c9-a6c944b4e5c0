# Apps Management - Kompletní manuál

## <PERSON><PERSON><PERSON>led systému

Apps Management je nová funkcionalita GENT v10 pro správu, spouštění a monitoring aplikací v systému. Umožňuje centralizovanou správu všech aplikací s detailním sledováním jej<PERSON> p<PERSON>.

## Architektura

### Databázové modely

#### Tabulka `apps`
```sql
CREATE TABLE apps (
    id VARCHAR PRIMARY KEY,                    -- Unikátní identifikátor
    name VARCHAR(255) NOT NULL,               -- Zobrazovaný název
    description TEXT,                         -- Popis aplikace
    file_path VARCHAR(500) NOT NULL,          -- Cesta k souboru
    language VARCHAR(50) DEFAULT 'python',   -- Programovací jazyk
    version VARCHAR(50) DEFAULT '1.0.0',     -- Verze aplikace
    author VARCHAR(255),                      -- Autor aplikace
    category VARCHAR(100),                    -- <PERSON><PERSON><PERSON>
    tags JSON,                               -- <PERSON>znam tagů
    command VARCHAR(500),                    -- Příkaz pro spuštění
    arguments JSON,                          -- Možné argumenty
    requirements JSON,                       -- Požadavky
    is_active BOOLEAN DEFAULT true,          -- Je aktivní
    is_public BOOLEAN DEFAULT false,         -- Je veřejná
    execution_count INTEGER DEFAULT 0,      -- Počet spuštění
    created_at TIMESTAMP DEFAULT NOW(),     -- Datum vytvoření
    updated_at TIMESTAMP DEFAULT NOW(),     -- Datum aktualizace
    last_executed_at TIMESTAMP,             -- Poslední spuštění
    ui_config JSON                          -- Konfigurace UI
);
```

#### Tabulka `app_executions`
```sql
CREATE TABLE app_executions (
    id VARCHAR PRIMARY KEY,                  -- UUID spuštění
    app_id VARCHAR NOT NULL,                -- Reference na aplikaci
    user_id VARCHAR,                        -- Kdo spustil
    arguments_used JSON,                    -- Použité argumenty
    exit_code INTEGER,                      -- Exit kód
    output TEXT,                           -- Výstup aplikace
    error_output TEXT,                     -- Chybový výstup
    execution_time INTEGER,                -- Doba běhu v ms
    status VARCHAR(50) DEFAULT 'running',  -- Status spuštění
    started_at TIMESTAMP DEFAULT NOW(),    -- Začátek spuštění
    completed_at TIMESTAMP                 -- Konec spuštění
);
```

### API Endpoints

#### Správa aplikací
- `GET /api/apps/` - Seznam všech aplikací
- `GET /api/apps/{app_id}` - Detail aplikace
- `POST /api/apps/` - Vytvoření nové aplikace
- `PUT /api/apps/{app_id}` - Aktualizace aplikace
- `DELETE /api/apps/{app_id}` - Smazání aplikace

#### Spouštění aplikací
- `POST /api/apps/{app_id}/execute` - Spuštění aplikace
- `GET /api/apps/{app_id}/executions` - Historie spuštění

#### Inicializace
- `POST /api/apps/init-tables` - Vytvoření tabulek

## Uživatelské rozhraní

### Přístup k Apps Management
1. Otevřete GENT v10 admin rozhraní
2. Klikněte na "Admin" v navigaci
3. Vyberte "📱 Apps Management"
4. URL: `http://localhost:8000/admin/apps`

### Hlavní komponenty UI

#### Statistiky (horní část)
- **Celkem aplikací**: Počet všech aplikací v systému
- **Aktivních aplikací**: Počet aktivních aplikací
- **Celkem spuštění**: Součet všech spuštění
- **Kategorií**: Počet různých kategorií

#### Filtry a vyhledávání
- **Vyhledávací pole**: Hledání podle názvu, popisu, tagů
- **Filtry kategorií**: Tlačítka pro filtrování podle kategorie
- **Aktivní filtry**: Vizuální označení aktivních filtrů

#### Karty aplikací
Každá aplikace je zobrazena jako karta obsahující:

##### Hlavička karty
- **Ikona**: Emoji podle kategorie aplikace
- **Název a verze**: Zobrazovaný název + číslo verze
- **Status badge**: Aktivní/Neaktivní

##### Obsah karty
- **Popis**: Textový popis funkcionality
- **Metadata**: Jazyk, kategorie, počet spuštění, autor
- **Tagy**: Barevné štítky pro kategorizaci

##### Akce
- **🚀 Spustit**: Spuštění aplikace (pouze pro aktivní)
- **📋 Detail**: Zobrazení detailních informací
- **✏️ Upravit**: Editace aplikace

##### Patička
- **Datum vytvoření**: Kdy byla aplikace přidána
- **Poslední spuštění**: Kdy byla naposledy spuštěna

## Ukázková aplikace - Calendar Benchmark

### Popis
Performance analýza kalendářového kódu s benchmarky a testy spolehlivosti.

### Technické detaily
- **Soubor**: `/opt/gent/apps/calendar_benchmark.py`
- **Jazyk**: Python 3
- **Kategorie**: benchmark
- **Tagy**: performance, calendar, benchmark, testing
- **Autor**: GENT System

### Funkcionalita
1. **Benchmark původního přístupu**: Měří výkon standardního kódu
2. **Benchmark optimalizovaného přístupu**: Měří výkon optimalizované verze
3. **Testy spolehlivosti**: Testuje edge cases a chybové stavy
4. **Analýza paměti**: Odhaduje spotřebu paměti

### Spuštění
```bash
# Přímé spuštění z terminálu
/opt/gent/venv/bin/python /opt/gent/apps/calendar_benchmark.py

# Nebo přes Apps Management UI
```

### Výstup
```
🔬 PERFORMANCE ANALÝZA KALENDÁŘOVÉHO KÓDU
==================================================

🚀 RYCHLOSTNÍ TESTY:
📊 Původní přístup (1000 iterací): 0.123ms
📊 Optimalizovaný přístup (1000 iterací): 0.089ms
⚡ Zrychlení (původní/optimalizovaný): 1.38x

🛡️ TESTY SPOLEHLIVOSTI:
✅ Úspěšných testů: 5/9
  ✅  1/1 - 0.02ms
  ✅ 12/9999 - 0.03ms
  ✅  2/2024 - 0.02ms
  ✅  2/2025 - 0.02ms
  ✅  6/2025 - 0.02ms
  ❌  0/2000 - CHYBA: day is out of range for month
  ❌ 13/2000 - CHYBA: month must be in 1..12
  ❌  6/0 - CHYBA: year 0 is out of range
  ❌  6/10000 - CHYBA: year 10000 is out of range

💾 SPOTŘEBA PAMĚTI:
  📦 calendar_object: 48 bytes
  📦 output_string: 218 bytes
  📦 datetime_object: 48 bytes
  📦 global_calendar_object: 48 bytes
  🔢 Celkově odhadovaná paměť: 362 bytes (0.4 KB)
```

## Správa aplikací

### Přidání nové aplikace

#### Přes API
```bash
curl -X POST http://localhost:8001/api/apps/ \
  -H "Content-Type: application/json" \
  -d '{
    "id": "my_app",
    "name": "Moje aplikace",
    "description": "Popis aplikace",
    "file_path": "/opt/gent/apps/my_app.py",
    "language": "python",
    "category": "utility",
    "tags": ["tool", "helper"],
    "command": "/opt/gent/venv/bin/python /opt/gent/apps/my_app.py"
  }'
```

#### Přes databázi
```sql
INSERT INTO apps (id, name, description, file_path, language, category, tags, command)
VALUES (
    'my_app',
    'Moje aplikace',
    'Popis aplikace',
    '/opt/gent/apps/my_app.py',
    'python',
    'utility',
    '["tool", "helper"]',
    '/opt/gent/venv/bin/python /opt/gent/apps/my_app.py'
);
```

### Kategorie aplikací
- **benchmark**: Performance testy a analýzy
- **utility**: Užitečné nástroje
- **game**: Hry a zábava
- **tool**: Pracovní nástroje
- **analysis**: Analytické aplikace
- **test**: Testovací aplikace

### Doporučené tagy
- **performance**: Výkonnostní aplikace
- **testing**: Testovací nástroje
- **automation**: Automatizační skripty
- **data**: Práce s daty
- **web**: Webové aplikace
- **cli**: Command line nástroje

## Monitoring a statistiky

### Sledované metriky
- **Počet spuštění**: Kolikrát byla aplikace spuštěna
- **Doba běhu**: Průměrná doba spuštění
- **Úspěšnost**: Poměr úspěšných/neúspěšných spuštění
- **Poslední aktivita**: Kdy byla aplikace naposledy použita

### Logy spuštění
Každé spuštění aplikace se zaznamenává do tabulky `app_executions` s:
- Časem spuštění a dokončení
- Použitými argumenty
- Výstupem aplikace
- Exit kódem
- Případnými chybami

## Bezpečnost

### Omezení spouštění
- Pouze aktivní aplikace lze spustit
- Kontrola existence souboru před spuštěním
- Validace argumentů
- Timeout pro dlouho běžící aplikace

### Izolace aplikací
- Aplikace běží v izolovaném prostředí
- Omezený přístup k systémovým prostředkům
- Logování všech aktivit

## Troubleshooting

### Časté problémy
1. **Aplikace se nespustí**: Zkontrolujte cestu k souboru a oprávnění
2. **Chybí v seznamu**: Zkontrolujte, zda je aplikace aktivní
3. **Timeout**: Zvyšte timeout pro dlouho běžící aplikace

### Diagnostika
```bash
# Kontrola existence souboru
ls -la /opt/gent/apps/

# Test spuštění
/opt/gent/venv/bin/python /opt/gent/apps/calendar_benchmark.py

# Kontrola logů
tail -f /var/log/gent/api.log
```

---

**Verze**: 1.0  
**Datum**: 2025-06-03  
**Autor**: GENT v10 System
