# API server - Podrobný task list

Tento dokument obsahuje podrobný seznam úkolů pro dokončení a optimalizaci API serveru v projektu Gent.

## 1. Analýza aktuálního stavu API serveru
- [ ] Zkontrolovat běžící službu gent-api
  - [ ] Ověřit status pomocí `systemctl status gent-api`
  - [ ] Zkontrolovat konfiguraci v `/etc/systemd/system/gent-api.service`
  - [ ] Ověřit, že služba se automaticky restartuje při selhání
- [ ] Analyzovat strukturu API aplikace
  - [ ] Prozkoumat soubor `gent/api/app/app.py`
  - [ ] Identifikovat všechny registrované routery
  - [ ] Zkontrolovat middleware a jejich konfiguraci
- [ ] Zkontrolovat dostupné endpointy
  - [ ] Spustit `curl http://localhost:8001/docs` pro zobrazení Swagger dokumentace
  - [ ] Identifikovat všechny implementované endpointy
  - [ ] Porovnat s očekávanou funkcionalitou podle dokumentace

## 2. Testování existujících API endpointů
- [ ] Otestovat endpointy pro LLM poskytovatele
  - [ ] GET `/api/db/llm/providers` - seznam poskytovatelů
  - [ ] GET `/api/db/llm/providers/{id}` - detail poskytovatele
  - [ ] POST `/api/db/llm/providers` - vytvoření poskytovatele
  - [ ] PUT `/api/db/llm/providers/{id}` - aktualizace poskytovatele
  - [ ] DELETE `/api/db/llm/providers/{id}` - smazání poskytovatele
- [ ] Otestovat endpointy pro LLM modely
  - [ ] GET `/api/db/llm/models` - seznam modelů
  - [ ] GET `/api/db/llm/models/{id}` - detail modelu
  - [ ] POST `/api/db/llm/models` - vytvoření modelu
  - [ ] PUT `/api/db/llm/models/{id}` - aktualizace modelu
  - [ ] DELETE `/api/db/llm/models/{id}` - smazání modelu
- [ ] Otestovat endpointy pro testování LLM
  - [ ] POST `/api/config/llm/test-llm` - test LLM
  - [ ] POST `/api/config/llm/test-connection` - test připojení
- [ ] Otestovat další implementované endpointy
  - [ ] Endpointy pro DB viewer
  - [ ] Endpointy pro PostgreSQL
  - [ ] Testovací endpointy

## 3. Implementace chybějících API endpointů
- [ ] Identifikovat chybějící endpointy podle dokumentace
  - [ ] Zkontrolovat dokumentaci v `docs/api_llm_db_reference.md`
  - [ ] Zkontrolovat dokumentaci v `docs/ai_llm_management_podrobna_dokumentace.md`
  - [ ] Porovnat s aktuálním stavem API
- [ ] Implementovat chybějící endpointy pro LLM
  - [ ] Endpointy pro správu cache
  - [ ] Endpointy pro správu embedding modelů
  - [ ] Další chybějící endpointy
- [ ] Implementovat chybějící endpointy pro projekt
  - [ ] Endpointy pro správu projektů
  - [ ] Endpointy pro správu úkolů
  - [ ] Endpointy pro správu agentů
- [ ] Implementovat chybějící endpointy pro kognitivní funkce
  - [ ] Endpointy pro správu znalostí
  - [ ] Endpointy pro správu myšlenek
  - [ ] Endpointy pro správu konverzací

## 4. Optimalizace výkonu API
- [ ] Analyzovat výkon existujících endpointů
  - [ ] Měřit dobu odezvy endpointů
  - [ ] Identifikovat pomalé endpointy
  - [ ] Analyzovat příčiny pomalé odezvy
- [ ] Optimalizovat databázové dotazy
  - [ ] Zkontrolovat použití indexů
  - [ ] Optimalizovat složité dotazy
  - [ ] Implementovat caching, kde je to vhodné
- [ ] Optimalizovat zpracování požadavků
  - [ ] Implementovat asynchronní zpracování pro náročné operace
  - [ ] Optimalizovat serializaci/deserializaci dat
  - [ ] Implementovat kompresi odpovědí

## 5. Implementace autentizace a autorizace
- [ ] Zkontrolovat existující implementaci autentizace
  - [ ] Analyzovat soubory související s autentizací
  - [ ] Zkontrolovat použití JWT tokenů
  - [ ] Zkontrolovat správu uživatelů
- [ ] Implementovat nebo vylepšit autentizaci
  - [ ] Implementovat JWT autentizaci, pokud chybí
  - [ ] Implementovat refresh tokenů
  - [ ] Implementovat správu uživatelů
- [ ] Implementovat nebo vylepšit autorizaci
  - [ ] Implementovat systém rolí a oprávnění
  - [ ] Implementovat kontrolu oprávnění pro endpointy
  - [ ] Implementovat logování přístupů

## 6. Implementace rate limitingu a monitoringu
- [ ] Implementovat rate limiting
  - [ ] Nastavit limity pro počet požadavků
  - [ ] Implementovat různé limity pro různé endpointy
  - [ ] Implementovat různé limity pro různé uživatele
- [ ] Implementovat monitoring
  - [ ] Implementovat sběr metrik (počet požadavků, doba odezvy, atd.)
  - [ ] Implementovat export metrik pro Prometheus
  - [ ] Implementovat dashboardy pro Grafana
- [ ] Implementovat alerting
  - [ ] Definovat pravidla pro alerty
  - [ ] Implementovat notifikace (email, Slack, atd.)
  - [ ] Otestovat alerting

## 7. Testování API
- [ ] Vytvořit unit testy pro API endpointy
  - [ ] Testy pro všechny endpointy
  - [ ] Testy pro ošetření chyb
  - [ ] Testy pro edge cases
- [ ] Vytvořit integrační testy
  - [ ] Testy pro interakci s databází
  - [ ] Testy pro interakci s LLM API
  - [ ] Testy pro autentizaci a autorizaci
- [ ] Vytvořit zátěžové testy
  - [ ] Testy pro měření výkonu
  - [ ] Testy pro měření škálovatelnosti
  - [ ] Testy pro měření stability

## 8. Dokumentace API
- [ ] Aktualizovat OpenAPI dokumentaci
  - [ ] Aktualizovat popis endpointů
  - [ ] Aktualizovat schémata požadavků a odpovědí
  - [ ] Přidat příklady použití
- [ ] Vytvořit podrobnou dokumentaci API
  - [ ] Dokumentovat všechny endpointy
  - [ ] Dokumentovat parametry a návratové hodnoty
  - [ ] Poskytnout příklady použití
- [ ] Vytvořit návod pro vývojáře
  - [ ] Postup pro přidání nového endpointu
  - [ ] Postup pro testování API
  - [ ] Postup pro nasazení změn

## 9. Nasazení a správa API
- [ ] Optimalizovat konfiguraci systemd služby
  - [ ] Nastavit správné závislosti
  - [ ] Nastavit správné limity zdrojů
  - [ ] Nastavit správné restartovací politiky
- [ ] Implementovat logování
  - [ ] Nastavit úrovně logování
  - [ ] Implementovat rotaci logů
  - [ ] Implementovat strukturované logování
- [ ] Vytvořit skripty pro správu API
  - [ ] Skript pro restart služby
  - [ ] Skript pro kontrolu stavu
  - [ ] Skript pro aktualizaci
