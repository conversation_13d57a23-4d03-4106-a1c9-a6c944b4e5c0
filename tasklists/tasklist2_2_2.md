# 📋 tasklist2_2_2.md – Navrh introspektivnich mechanizmu

## 1. <PERSON><PERSON><PERSON> metriky
- [ ] Latence kognitivni jednotky (ms)
- [ ] Memory footprint (MB)
- [ ] Success rate u problem solveru (%)
- [ ] Anomaly score (0‑1)

## 2. Proces monitoringu aktivit
- [ ] Event bus pro vsech<PERSON> jed<PERSON><PERSON>
- [ ] Schema JSON eventu
- [ ] Persist do Supabase `introspection_events`

## 3. Algoritmy analýzy vykonu
- [ ] EWMA pro trend latence
- [ ] PCA pro detekci odchylek patternu
- [ ] Clustering K‑Means na chovani agentu

## 4. Detekce anomalii v chovani
- [ ] Isolation Forest model
- [ ] Prag threshold alert = 0.85
- [ ] Notification kanal Slack

## 5. Automaticka korekce chyb
- [ ] Rule‑based fallback strategie
- [ ] Reinforcement Learning tuning parametru
- [ ] Rollback mechanismus

## 6. Zaznam introspektivnich dat
- [ ] Rotation politik logu (7 dni)
- [ ] GDPR compliant anonymizace
- [ ] Backup do S3 nightly
