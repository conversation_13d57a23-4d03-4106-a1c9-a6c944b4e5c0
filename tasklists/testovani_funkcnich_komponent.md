# Testování funkčních komponent projektu GENT

Tento dokument obsahuje plán pro testování existuj<PERSON><PERSON><PERSON><PERSON> funkčních komponent projektu GENT. Cílem je lépe pochopit jejich funkcionalitu a zajistit, že nebudou poškozeny při dalším vývoji.

## 1. Testování databáze

### 1.1. Testování tabulky `llm_providers`
- [ ] Získat seznam všech poskytovatelů
  ```sql
  SELECT * FROM llm_providers;
  ```
- [ ] Ověřit strukturu tabulky
  ```sql
  \d llm_providers
  ```
- [ ] Otestovat vložení testovacího poskytovatele
  ```sql
  INSERT INTO llm_providers (provider_name, api_key, api_base, api_version, api_type, description)
  VALUES ('TEST_PROVIDER', 'test_key', 'test_base', 'test_version', 'test_type', 'Test provider');
  ```
- [ ] Otestovat aktualizaci testovacího poskytovatele
  ```sql
  UPDATE llm_providers SET description = 'Updated test provider' WHERE provider_name = 'TEST_PROVIDER';
  ```
- [ ] Otestovat smazání testovacího poskytovatele
  ```sql
  DELETE FROM llm_providers WHERE provider_name = 'TEST_PROVIDER';
  ```

### 1.2. Testování tabulky `llm_models`
- [ ] Získat seznam všech modelů
  ```sql
  SELECT * FROM llm_models;
  ```
- [ ] Ověřit strukturu tabulky
  ```sql
  \d llm_models
  ```
- [ ] Otestovat vložení testovacího modelu
  ```sql
  INSERT INTO llm_models (model_name, provider_id, model_type, max_tokens, temperature, description)
  VALUES ('test-model', 1, 'test', 4096, 0.7, 'Test model');
  ```
- [ ] Otestovat aktualizaci testovacího modelu
  ```sql
  UPDATE llm_models SET description = 'Updated test model' WHERE model_name = 'test-model';
  ```
- [ ] Otestovat smazání testovacího modelu
  ```sql
  DELETE FROM llm_models WHERE model_name = 'test-model';
  ```

### 1.3. Testování vztahů mezi tabulkami
- [ ] Ověřit vztah mezi `llm_providers` a `llm_models`
  ```sql
  SELECT m.model_name, p.provider_name
  FROM llm_models m
  JOIN llm_providers p ON m.provider_id = p.id;
  ```

## 2. Testování API serveru

### 2.1. Testování endpointů pro LLM poskytovatele
- [ ] Otestovat získání seznamu poskytovatelů
  ```bash
  curl -s http://localhost:8001/api/db/llm/providers
  ```
- [ ] Otestovat získání konkrétního poskytovatele
  ```bash
  curl -s http://localhost:8001/api/db/llm/providers/1
  ```
- [ ] Otestovat vytvoření testovacího poskytovatele
  ```bash
  curl -s -X POST http://localhost:8001/api/db/llm/providers \
    -H "Content-Type: application/json" \
    -d '{"provider_name": "TEST_API", "api_key": "test_key", "api_base": "test_base", "api_version": "test_version", "api_type": "test_type", "description": "Test provider via API"}'
  ```
- [ ] Otestovat aktualizaci testovacího poskytovatele
  ```bash
  # Předpokládejme, že ID je 5
  curl -s -X PUT http://localhost:8001/api/db/llm/providers/5 \
    -H "Content-Type: application/json" \
    -d '{"provider_name": "TEST_API", "api_key": "test_key", "api_base": "test_base", "api_version": "test_version", "api_type": "test_type", "description": "Updated test provider via API"}'
  ```
- [ ] Otestovat smazání testovacího poskytovatele
  ```bash
  # Předpokládejme, že ID je 5
  curl -s -X DELETE http://localhost:8001/api/db/llm/providers/5
  ```

### 2.2. Testování endpointů pro LLM modely
- [ ] Otestovat získání seznamu modelů
  ```bash
  curl -s http://localhost:8001/api/db/llm/models
  ```
- [ ] Otestovat získání konkrétního modelu
  ```bash
  curl -s http://localhost:8001/api/db/llm/models/1
  ```
- [ ] Otestovat vytvoření testovacího modelu
  ```bash
  curl -s -X POST http://localhost:8001/api/db/llm/models \
    -H "Content-Type: application/json" \
    -d '{"model_name": "test-api-model", "provider_id": 1, "model_type": "test", "max_tokens": 4096, "temperature": 0.7, "description": "Test model via API"}'
  ```
- [ ] Otestovat aktualizaci testovacího modelu
  ```bash
  # Předpokládejme, že ID je 16
  curl -s -X PUT http://localhost:8001/api/db/llm/models/16 \
    -H "Content-Type: application/json" \
    -d '{"model_name": "test-api-model", "provider_id": 1, "model_type": "test", "max_tokens": 4096, "temperature": 0.7, "description": "Updated test model via API"}'
  ```
- [ ] Otestovat smazání testovacího modelu
  ```bash
  # Předpokládejme, že ID je 16
  curl -s -X DELETE http://localhost:8001/api/db/llm/models/16
  ```

### 2.3. Testování endpointů pro testování LLM
- [ ] Otestovat endpoint pro test připojení
  ```bash
  curl -s -X POST http://localhost:8001/api/config/llm/test-connection \
    -H "Content-Type: application/json" \
    -d '{"provider_id": 1}'
  ```
- [ ] Otestovat endpoint pro test LLM
  ```bash
  curl -s -X POST http://localhost:8001/api/config/llm/test-llm \
    -H "Content-Type: application/json" \
    -d '{"model_id": 1, "prompt": "Say hello in Czech"}'
  ```

## 3. Testování frontendu

### 3.1. Testování stránky Testy
- [ ] Otevřít stránku Testy v prohlížeči
  ```
  http://localhost:8000/tests
  ```
- [ ] Otestovat všechny dostupné testy na stránce
- [ ] Zaznamenat výsledky testů

### 3.2. Testování stránky Databáze
- [ ] Otevřít stránku Databáze v prohlížeči
  ```
  http://localhost:8000/db-viewer
  ```
- [ ] Otestovat zobrazení tabulek
- [ ] Otestovat filtrování a řazení
- [ ] Otestovat editaci záznamů (pokud je dostupná)

### 3.3. Testování stránky AI-LLM
- [ ] Otevřít stránku AI-LLM v prohlížeči
  ```
  http://localhost:8000/ai-llm
  ```
- [ ] Otestovat zobrazení poskytovatelů a modelů
- [ ] Otestovat přidání nového poskytovatele
- [ ] Otestovat přidání nového modelu
- [ ] Otestovat editaci existujících záznamů
- [ ] Otestovat smazání testovacích záznamů

### 3.4. Testování stránky CHAT-TEST
- [ ] Otevřít stránku CHAT-TEST v prohlížeči
  ```
  http://localhost:8000/chat-test
  ```
- [ ] Otestovat výběr modelu
- [ ] Otestovat odeslání zprávy
- [ ] Otestovat zobrazení odpovědi
- [ ] Otestovat historii konverzace

## 4. Testování LLM integrace

### 4.1. Testování integrace s OpenAI
- [ ] Otestovat připojení k API
  ```bash
  curl -s -X POST http://localhost:8001/api/config/llm/test-connection \
    -H "Content-Type: application/json" \
    -d '{"provider_name": "OpenAI"}'
  ```
- [ ] Otestovat generování textu s modelem GPT-4
  ```bash
  curl -s -X POST http://localhost:8001/api/config/llm/test-llm \
    -H "Content-Type: application/json" \
    -d '{"model_name": "gpt-4", "prompt": "Say hello in Czech"}'
  ```
- [ ] Otestovat generování textu s modelem GPT-3.5
  ```bash
  curl -s -X POST http://localhost:8001/api/config/llm/test-llm \
    -H "Content-Type: application/json" \
    -d '{"model_name": "gpt-3.5-turbo", "prompt": "Say hello in Czech"}'
  ```

### 4.2. Testování integrace s Anthropic
- [ ] Otestovat připojení k API
  ```bash
  curl -s -X POST http://localhost:8001/api/config/llm/test-connection \
    -H "Content-Type: application/json" \
    -d '{"provider_name": "Anthropic"}'
  ```
- [ ] Otestovat generování textu s modelem Claude
  ```bash
  curl -s -X POST http://localhost:8001/api/config/llm/test-llm \
    -H "Content-Type: application/json" \
    -d '{"model_name": "claude-3-opus-20240229", "prompt": "Say hello in Czech"}'
  ```

### 4.3. Testování integrace s Google
- [ ] Otestovat připojení k API
  ```bash
  curl -s -X POST http://localhost:8001/api/config/llm/test-connection \
    -H "Content-Type: application/json" \
    -d '{"provider_name": "Google"}'
  ```
- [ ] Otestovat generování textu s modelem Gemini
  ```bash
  curl -s -X POST http://localhost:8001/api/config/llm/test-llm \
    -H "Content-Type: application/json" \
    -d '{"model_name": "gemini-pro", "prompt": "Say hello in Czech"}'
  ```

### 4.4. Testování integrace s Openrouter
- [ ] Otestovat připojení k API
  ```bash
  curl -s -X POST http://localhost:8001/api/config/llm/test-connection \
    -H "Content-Type: application/json" \
    -d '{"provider_name": "Openrouter"}'
  ```
- [ ] Otestovat generování textu s dostupným modelem
  ```bash
  curl -s -X POST http://localhost:8001/api/config/llm/test-llm \
    -H "Content-Type: application/json" \
    -d '{"model_name": "openrouter/auto", "prompt": "Say hello in Czech"}'
  ```

## 5. Testování MCP serverů

### 5.1. Testování filesystem serveru
- [ ] Otestovat výpis adresáře
- [ ] Otestovat čtení souboru
- [ ] Otestovat zápis do souboru (pokud je implementováno)

### 5.2. Testování brave-search serveru
- [ ] Otestovat vyhledávání
- [ ] Otestovat zpracování výsledků

### 5.3. Testování tavily serveru
- [ ] Otestovat vyhledávání
- [ ] Otestovat zpracování výsledků

### 5.4. Testování perplexity serveru
- [ ] Otestovat vyhledávání
- [ ] Otestovat získávání dokumentace

### 5.5. Testování fetch serveru
- [ ] Otestovat načítání HTML
- [ ] Otestovat načítání Markdown
- [ ] Otestovat načítání TXT

### 5.6. Testování sequentialthinking serveru
- [ ] Otestovat sekvenční myšlení
- [ ] Otestovat zpracování výsledků

### 5.7. Testování git serveru
- [ ] Otestovat git status
- [ ] Otestovat git diff
- [ ] Otestovat git log

## 6. Dokumentace výsledků testů

Po provedení všech testů je potřeba zdokumentovat:

1. **Funkční komponenty** - které komponenty fungují správně
2. **Problémy** - které komponenty nefungují nebo fungují nesprávně
3. **Chybějící funkce** - které funkce chybí nebo nejsou plně implementovány
4. **Doporučení** - jak postupovat dále při vývoji

Tato dokumentace bude sloužit jako základ pro další vývoj projektu GENT.
