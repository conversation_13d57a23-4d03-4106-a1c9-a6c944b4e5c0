# Aktuální stav projektu GENT

Tento dokument obsahuje přehled aktuálního stavu projektu GENT, v<PERSON><PERSON><PERSON><PERSON> funkčních komponent a částí, které je potřeba zachovat při dalším vývoji.

## Základní myšlenka projektu GENT

GENT (Generativní Extra Neuronová Technologie) je inteligentní partner pro realiza<PERSON>, který:
- Spolupracuje s uživatelem na formulaci a upřesnění myšlenek
- Autonomně realizuje schválené plány pomocí specializovaných AI agentů
- Učí se a adaptuje na základě zkušeností a zpětné vazby
- Optimalizuje své vlastní fungování

Klíčové principy:
- **Kolaborace**: Aktivní spolupráce s uživatelem
- **Autonomie**: Samostatné jednání po schválení plánu
- **Adaptabilita**: Přizpůsobení se povaze úkolu a dostupným zdrojům
- **Modularita**: Využití specializovaných agentů pro různé úkoly

## Funkční komponenty

### 1. Databáze
- PostgreSQL databáze `gentdb` je funkční
- Tabulky `llm_providers` a `llm_models` jsou vytvořeny a obsahují data
- Obsahuje 4 poskytovatele (OpenAI, Anthropic, Google, Openrouter)
- Obsahuje 15 modelů od těchto poskytovatelů
- **DŮLEŽITÉ**: Zachovat existující strukturu a data

### 2. API server
- Běží jako systemd služba `gent-api` na portu 8001
- Implementovány endpointy pro LLM poskytovatele a modely
- Implementovány endpointy pro testování LLM
- **DŮLEŽITÉ**: Zachovat funkční endpointy

### 3. Frontend
- Běží na portu 8000
- Funkční stránky:
  - **Testy**: Stránka pro testování různých funkcí
  - **Databáze**: Stránka pro zobrazení a správu databáze
  - **AI-LLM**: Stránka pro správu LLM poskytovatelů a modelů
  - **CHAT-TEST**: Stránka pro testování chatování s LLM
- **DŮLEŽITÉ**: Zachovat funkční stránky a jejich funkcionalitu

### 4. LLM integrace
- Funkční integrace s poskytovateli OpenAI, Anthropic, Google a Openrouter
- Konfigurace modelů v databázi
- **DŮLEŽITÉ**: Zachovat funkční integraci s LLM poskytovateli

### 5. MCP servery
- Běží několik MCP serverů pro různé funkce:
  - filesystem: pro práci se soubory
  - brave-search: pro vyhledávání
  - tavily: pro vyhledávání
  - perplexity: pro vyhledávání a získávání dokumentace
  - fetch: pro načítání webových stránek
  - sequentialthinking: pro sekvenční myšlení
  - git: pro práci s git repozitáři
- **DŮLEŽITÉ**: Zachovat funkční MCP servery

## Postup pro další vývoj

Při dalším vývoji projektu GENT je potřeba:

1. **Testovat změny izolovaně** před jejich integrací do hlavního kódu
2. **Vytvářet zálohy** před prováděním významných změn
3. **Postupovat inkrementálně** - implementovat a testovat jednu změnu v jednom čase
4. **Dokumentovat změny** - zaznamenávat, co bylo změněno a proč
5. **Pravidelně testovat** funkční komponenty, aby se zajistilo, že nebyly poškozeny

## Priority pro další vývoj

Na základě analýzy aktuálního stavu a základní myšlenky projektu GENT doporučujeme následující priority:

1. **Analýza a testování** existujících komponent pro lepší pochopení jejich funkcionality
2. **Dokončení kognitivní architektury** - implementace "mozku" systému
3. **Dokončení agentního systému** - implementace specializovaných agentů a jejich týmů
4. **Dokončení operačních módů** - implementace PLAN, ACT, RESEARCH a IMPROVE módů
5. **Integrace komponent** - propojení kognitivní architektury, agentů a módů

Tyto priority respektují základní myšlenku projektu GENT a staví na existujících funkčních komponentách.
