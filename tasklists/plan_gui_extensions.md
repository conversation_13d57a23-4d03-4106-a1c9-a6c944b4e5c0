# 🎨 ROZŠÍŘENÍ EXISTUJÍCÍCH GUI STRÁNEK - Implementační plán

## 🎯 CÍLE:
- Zachovat všechny funkční komponenty
- Přidat monitoring a real-time features
- Žádná mock data - pouze reálná data z DB
- Postupné r<PERSON>ní bez narušení funk<PERSON>

---

## 📊 1. DASHBOARD - Nová hlavní stránka

### Komponenty k přidání:
```vue
<!-- /src/views/Dashboard.vue -->
<template>
  <div class="dashboard">
    <!-- System Overview Cards -->
    <div class="overview-cards">
      <SystemHealthCard />
      <DatabaseStatusCard />
      <LLMModelsCard />
      <AgentStatusCard />
    </div>
    
    <!-- Real-time Activity Feed -->
    <div class="activity-section">
      <ActivityFeed />
    </div>
    
    <!-- Quick Actions -->
    <div class="quick-actions">
      <QuickTestButton />
      <DatabaseBrowserButton />
      <ChatTestButton />
    </div>
  </div>
</template>
```

### API endpointy k implementaci:
```javascript
// /src/services/dashboard.service.js
export const dashboardService = {
  getSystemHealth() {
    return apiService.get('/api/system/health');
  },
  
  getDatabaseStatus() {
    return apiService.get('/api/system/database-status');
  },
  
  getLLMModelsCount() {
    return apiService.get('/api/db/llm/models/count');
  },
  
  getRecentActivity() {
    return apiService.get('/api/system/activity');
  }
};
```

---

## 🤖 2. ROZŠÍŘENÍ CHAT-TEST STRÁNKY

### Přidat monitoring features:
```vue
<!-- Rozšíření ChatTest.vue -->
<template>
  <div class="chat-test-enhanced">
    <!-- Existující chat interface -->
    <div class="existing-chat">
      <!-- Zachovat současný kód -->
    </div>
    
    <!-- Nové monitoring panely -->
    <div class="chat-monitoring">
      <div class="model-performance">
        <h3>Performance Metrics</h3>
        <div class="metrics">
          <div class="metric">
            <span>Response Time:</span>
            <span>{{ responseTime }}ms</span>
          </div>
          <div class="metric">
            <span>Tokens Used:</span>
            <span>{{ tokensUsed }}</span>
          </div>
          <div class="metric">
            <span>Cost:</span>
            <span>${{ estimatedCost }}</span>
          </div>
        </div>
      </div>
      
      <div class="conversation-history">
        <h3>Conversation Stats</h3>
        <div class="stats">
          <div>Messages: {{ messageCount }}</div>
          <div>Duration: {{ conversationDuration }}</div>
          <div>Avg Response: {{ avgResponseTime }}ms</div>
        </div>
      </div>
    </div>
  </div>
</template>
```

### Backend API rozšíření:
```python
# Přidat do chat API
@app.post("/api/chat/message")
async def send_message_with_metrics(request: ChatRequest):
    start_time = time.time()
    
    # Existující chat logika
    response = await process_chat_message(request)
    
    # Přidat metriky
    end_time = time.time()
    response_time = (end_time - start_time) * 1000
    
    # Uložit metriky do DB
    await save_chat_metrics({
        "response_time": response_time,
        "tokens_used": response.tokens_used,
        "model_id": request.model_id,
        "timestamp": datetime.now()
    })
    
    return {
        "response": response,
        "metrics": {
            "response_time": response_time,
            "tokens_used": response.tokens_used,
            "estimated_cost": calculate_cost(response.tokens_used, request.model_id)
        }
    }
```

---

## 🗄️ 3. ROZŠÍŘENÍ DATABASE VIEWER

### Přidat real-time monitoring:
```vue
<!-- Rozšíření DbViewer.vue -->
<template>
  <div class="db-viewer-enhanced">
    <!-- Existující DB viewer -->
    <div class="existing-viewer">
      <!-- Zachovat současný kód -->
    </div>
    
    <!-- Nové monitoring features -->
    <div class="db-monitoring" v-if="selectedDb">
      <div class="connection-status">
        <h3>Connection Status</h3>
        <div class="status-indicator" :class="connectionStatus">
          {{ connectionStatus === 'connected' ? '🟢' : '🔴' }}
          {{ connectionStatusText }}
        </div>
        <div class="connection-metrics">
          <div>Active Connections: {{ activeConnections }}</div>
          <div>Query Time: {{ avgQueryTime }}ms</div>
          <div>Last Update: {{ lastUpdate }}</div>
        </div>
      </div>
      
      <div class="table-stats" v-if="selectedTable">
        <h3>Table Statistics</h3>
        <div class="stats-grid">
          <div>Rows: {{ tableStats.rowCount }}</div>
          <div>Size: {{ tableStats.size }}</div>
          <div>Last Modified: {{ tableStats.lastModified }}</div>
          <div>Indexes: {{ tableStats.indexCount }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
```

---

## 🧪 4. ROZŠÍŘENÍ TESTS STRÁNKY

### Přidat system diagnostics:
```vue
<!-- Rozšíření Tests.vue -->
<template>
  <div class="tests-enhanced">
    <!-- Existující PostgreSQL test -->
    <div class="existing-tests">
      <!-- Zachovat PostgresTest komponentu -->
    </div>
    
    <!-- Nové diagnostic tests -->
    <div class="system-diagnostics">
      <div class="test-section">
        <h2>System Health Tests</h2>
        <SystemHealthTest />
      </div>
      
      <div class="test-section">
        <h2>API Connectivity Tests</h2>
        <ApiConnectivityTest />
      </div>
      
      <div class="test-section">
        <h2>LLM Models Tests</h2>
        <LLMModelsTest />
      </div>
      
      <div class="test-section">
        <h2>Performance Tests</h2>
        <PerformanceTest />
      </div>
    </div>
  </div>
</template>
```

---

## ⚡ 5. REAL-TIME FEATURES

### WebSocket implementace:
```javascript
// /src/services/websocket.service.js
class WebSocketService {
  constructor() {
    this.ws = null;
    this.listeners = new Map();
  }
  
  connect() {
    this.ws = new WebSocket('ws://localhost:8001/ws');
    
    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      this.notifyListeners(data.type, data.payload);
    };
  }
  
  subscribe(eventType, callback) {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, []);
    }
    this.listeners.get(eventType).push(callback);
  }
  
  notifyListeners(eventType, data) {
    const callbacks = this.listeners.get(eventType) || [];
    callbacks.forEach(callback => callback(data));
  }
}

export const wsService = new WebSocketService();
```

### Backend WebSocket server:
```python
# Přidat do FastAPI
from fastapi import WebSocket

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()
    
    while True:
        # Posílat real-time updates
        system_status = await get_system_status()
        await websocket.send_json({
            "type": "system_status",
            "payload": system_status
        })
        
        await asyncio.sleep(5)  # Update každých 5 sekund
```

---

## 📋 IMPLEMENTAČNÍ KROKY:

### Fáze 1: Systemd Service (Dnes)
1. ✅ Vytvořit systemd service soubor
2. ✅ Otestovat automatický start
3. ✅ Ověřit funkčnost po restartu

### Fáze 2: Dashboard (Zítra ráno)
1. ✅ Vytvořit Dashboard.vue komponentu
2. ✅ Implementovat SystemHealthCard
3. ✅ Přidat do router
4. ✅ Otestovat s reálnými daty

### Fáze 3: Real-time Features (Zítra odpoledne)
1. ✅ Implementovat WebSocket service
2. ✅ Přidat real-time updates do Dashboard
3. ✅ Rozšířit ChatTest o metriky
4. ✅ Otestovat live updates

### Fáze 4: Monitoring Extensions (Další den)
1. ✅ Rozšířit DbViewer o monitoring
2. ✅ Přidat diagnostic tests
3. ✅ Implementovat performance tracking
4. ✅ Kompletní testování

---

## 🎯 SUCCESS CRITERIA:

✅ **Systemd service** - Frontend běží automaticky jako služba
✅ **Zachované funkce** - Všechny existující stránky fungují
✅ **Real-time data** - Žádná mock data, pouze reálná z DB
✅ **Visual feedback** - Okamžitě viditelný pokrok
✅ **Performance monitoring** - Metriky výkonu v real-time
✅ **System health** - Přehled o stavu systému

**Výsledek: Plně funkční GUI s real-time monitoring a zachovanými funkcemi!** 🚀
