# Databázová integrace - Podrobný task list

Tento dokument obsahuje podrobný seznam úkolů pro dokončení databázové integrace v projektu Gent.

## 1. Analýza aktuálního stavu databáze
- [ ] Zkontrolovat existující tabulky v databázi
  - [ ] Spustit `\dt` v PostgreSQL pro zobrazení všech tabulek
  - [ ] Porovnat existující tabulky s oček<PERSON>vanou strukturou
  - [ ] Identifikovat chybějící tabulky
- [ ] Zkontrolovat schéma existujících tabulek
  - [ ] Spustit `\d llm_providers` pro zobrazení struktury tabulky
  - [ ] Spustit `\d llm_models` pro zobrazení struktury tabulky
  - [ ] Zkontrolovat, zda existují všechny potřebné sloupce
  - [ ] Zkontrolovat datové typy sloupců
  - [ ] Zkontrolovat omezení (constraints) a indexy
- [ ] Analyzovat existující data
  - [ ] Zkontrolovat počet záznamů v tabulkách
  - [ ] Zkontrolovat kvalitu dat (žádná chybějící nebo neplatná data)
  - [ ] Identifikovat, zda jsou data reálná nebo mock data

## 2. Implementace chybějících tabulek
- [ ] Identifikovat chybějící tabulky podle dokumentace
  - [ ] Zkontrolovat dokumentaci v `docs/db_llms.md`
  - [ ] Zkontrolovat dokumentaci v `docs/ai_llm_management_podrobna_dokumentace.md`
  - [ ] Porovnat s aktuálním stavem databáze
- [ ] Vytvořit SQL skripty pro chybějící tabulky
  - [ ] Připravit CREATE TABLE příkazy
  - [ ] Definovat primární a cizí klíče
  - [ ] Definovat indexy pro optimalizaci výkonu
  - [ ] Přidat komentáře k tabulkám a sloupcům
- [ ] Spustit SQL skripty pro vytvoření tabulek
  - [ ] Otestovat skripty v testovacím prostředí
  - [ ] Aplikovat skripty v produkčním prostředí
  - [ ] Ověřit, že tabulky byly správně vytvořeny

## 3. Kontrola a optimalizace databázových konektorů
- [ ] Zkontrolovat implementaci databázových konektorů
  - [ ] Analyzovat soubor `gent/db/connector.py`
  - [ ] Analyzovat soubor `gent/db/direct_connector.py`
  - [ ] Zkontrolovat správu připojení k databázi
  - [ ] Zkontrolovat ošetření chyb
- [ ] Optimalizovat výkon databázových konektorů
  - [ ] Zkontrolovat nastavení connection pool
  - [ ] Optimalizovat dotazy pro lepší výkon
  - [ ] Implementovat caching, kde je to vhodné
- [ ] Implementovat chybějící funkce v konektorech
  - [ ] Identifikovat chybějící funkce
  - [ ] Implementovat a otestovat tyto funkce

## 4. Kontrola a optimalizace databázových služeb
- [ ] Zkontrolovat implementaci `llm_db_service.py`
  - [ ] Analyzovat všechny metody a jejich funkčnost
  - [ ] Zkontrolovat správu připojení k databázi
  - [ ] Zkontrolovat ošetření chyb
- [ ] Optimalizovat výkon databázových služeb
  - [ ] Optimalizovat dotazy pro lepší výkon
  - [ ] Implementovat caching, kde je to vhodné
  - [ ] Zkontrolovat uzavírání připojení
- [ ] Implementovat chybějící funkce v službách
  - [ ] Identifikovat chybějící funkce podle dokumentace
  - [ ] Implementovat a otestovat tyto funkce

## 5. Testování databázové vrstvy
- [ ] Vytvořit unit testy pro databázové konektory
  - [ ] Testy pro základní CRUD operace
  - [ ] Testy pro ošetření chyb
  - [ ] Testy pro edge cases
- [ ] Vytvořit unit testy pro databázové služby
  - [ ] Testy pro všechny veřejné metody
  - [ ] Testy pro ošetření chyb
  - [ ] Testy pro edge cases
- [ ] Vytvořit integrační testy
  - [ ] Testy pro interakci s reálnou databází
  - [ ] Testy pro transakce
  - [ ] Testy pro konkurenční přístup

## 6. Dokumentace databázové vrstvy
- [ ] Aktualizovat dokumentaci schématu databáze
  - [ ] Aktualizovat `docs/db_llms.md`
  - [ ] Vytvořit ER diagram
  - [ ] Dokumentovat všechny tabulky a jejich vztahy
- [ ] Dokumentovat API databázových služeb
  - [ ] Dokumentovat všechny veřejné metody
  - [ ] Dokumentovat parametry a návratové hodnoty
  - [ ] Poskytnout příklady použití
- [ ] Vytvořit návod pro správu databáze
  - [ ] Postup pro zálohování a obnovu
  - [ ] Postup pro migraci schématu
  - [ ] Postup pro řešení běžných problémů

## 7. Implementace migračního systému
- [ ] Navrhnout systém pro správu migrací databáze
  - [ ] Vybrat vhodný nástroj (Alembic, vlastní řešení, atd.)
  - [ ] Definovat proces pro vytváření a aplikování migrací
- [ ] Implementovat migrační systém
  - [ ] Nastavit základní strukturu
  - [ ] Vytvořit první migraci pro aktuální schéma
  - [ ] Otestovat proces migrace
- [ ] Dokumentovat migrační systém
  - [ ] Postup pro vytvoření nové migrace
  - [ ] Postup pro aplikování migrací
  - [ ] Postup pro řešení konfliktů

## 8. Implementace zálohovacího systému
- [ ] Navrhnout systém pro zálohování databáze
  - [ ] Definovat frekvenci záloh
  - [ ] Definovat typ záloh (plné, inkrementální)
  - [ ] Definovat retenci záloh
- [ ] Implementovat zálohovací systém
  - [ ] Vytvořit skripty pro zálohování
  - [ ] Nastavit automatické spouštění záloh
  - [ ] Otestovat proces zálohování a obnovy
- [ ] Dokumentovat zálohovací systém
  - [ ] Postup pro manuální zálohování
  - [ ] Postup pro obnovu ze zálohy
  - [ ] Postup pro kontrolu integrity záloh
