# 📋 tasklist1_1_1_1.md – Analyza potreb uzivatelu bez technickeho zazemi

> Ultra detailni kroky pro pochopeni realnych potreb cilove skupiny.

1. **Definice cilove persony**
   - [ ] Persona: „Kreativni podnikatel Karel”, zkusenosti s IT: minimalni
   - [ ] Persona: „Ucitelka Jana”, chce interaktivni web pro vyuku
2. **Nastroje zberu dat**
   - [ ] Online dotaznik Google Forms (15 otazek)
   - [ ] Polostrukturovany rozhovor (30 minut, skript v PDF)
   - [ ] Analytika existujicich ticketu podpory
3. **Plan rekrutace respondentů**
   - [ ] Seznam 20 potencialnich uzivatelu z komunity
   - [ ] Pozvanky e‑mailem, pobidka 10 € voucher
   - [ ] Kalendly sloty pro rozhovory
4. **Realizace rozhovoru**
   - [ ] Zaznamy v MP3, transkripce Whisper
   - [ ] Kódovaní odpovědi v nástroji Atlas.ti
5. **Kvantitativni analyza**
   - [ ] Pivot tabulka frekvence problemu
   - [ ] Graf nejpalcivejsich barier
6. **Kvalitativni syntéza**
   - [ ] Affinity diagram v Miro
   - [ ] Identifikace „pain points” TOP 5
7. **Vypracovani reportu**
   - [ ] Executive summary (1 strana)
   - [ ] Plny report (10 stran, Markdown)
   - [ ] Prezentace v PowerPoint (10 slidu)
8. **Validace zaveru**
   - [ ] Workshop s 5 nezavislymi UX designery
   - [ ] Zapracovat feedback
9. **Mapovani potreb na funkcni reseni**
   - [ ] Traceability matrix problem → funkce GENT
10. **Publikace a sdileni znalosti**
    - [ ] Ulozit report do Supabase knowledge base
    - [ ] Interni meetup k prezentaci vysledku
