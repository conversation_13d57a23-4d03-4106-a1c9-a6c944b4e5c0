# 📋 TASKLIST_FINALL_22 - Integrace dokumentace a zachování funkčních komponent

> **Konsolidace:** Všechny dokumenty z /opt/gent/docs + zachování existujících funkcí
> **Zaměření:** Integrace existující dokumentace do implementace a zachování funkčních částí

---

## 📚 HLAVNÍ PRAVIDLA A POSTUPY (z docs/hlavni_pravidla.md)

### 1. Pracovní postup
- [ ] **Strukturované task listy**
  - Vždy vytvořit seznam úkolů před zahájením práce
  - Rozdělit složité úkoly na menší kroky
  - Přiřadit prioritu každému úkolu
  - Postupovat systematicky podle seznamu
  - Označovat úkoly jako dokončené
  - Pravidelně aktualizovat seznam

- [ ] **Dokumentace po dokončení**
  - Detailní dokumentace v Markdown formátu
  - Samostatný Markdown soubor s návodem na použití
  - Vysvětlující dokument s popisem funkcí a principů

### 2. Stylování a design
- [ ] **Tmavý design standard**
  - Style musí být vždy tmavý
  - Style nikdy nepatří do kódu - zvlášť CSS soubor
  - JavaScript nikdy nepatří do komponenty - zvlášť JS soubor
  - Konzistentní barevné schéma a typografie
  - Responzivní design pro různé velikosti obrazovek
  - Sémantické HTML elementy pro přístupnost

### 3. Prostředí a nástroje
- [ ] **Virtuální prostředí**
  - Vždy aktivovat: `source /opt/gent/venv/bin/activate`
  - Kontrolovat verze knihoven a kompatibilitu
  - Používat virtuální prostředí pro izolaci závislostí
  - Dodržovat princip nejmenších oprávnění
  - **ŽÁDNÁ ZE SLUŽEB NEPOUŽÍVÁ DOCKER** - vše běží nativně

### 4. Systémové služby
- [ ] **Service management**
  - API server (gent-api) je systemd služba - restart: `systemctl restart gent-api`
  - PostgreSQL je systemd služba - restart: `systemctl restart postgresql`
  - Frontend spouštěn pomocí skriptu `./run_frontend.sh`
  - Detailní dokumentace v `docs/system_services.md`

### 5. Architektura projektu
- [ ] **Třívrstvá architektura**
  - **DATOVÁ VRSTVA** - pouze v databázi, nikdy v kódu
  - **APLIKAČNÍ VRSTVA** - byznys logika a zpracování dat
  - **PREZENTAČNÍ VRSTVA** - uživatelské rozhraní a zobrazení

- [ ] **Struktura adresářů**
  - `/micro_services/` - pro všechny mikroslužby
  - `/gent/` - pro hlavní aplikační kód
  - `/config/` - pouze pro konfigurační soubory (ne data)
  - `/docs/` - pro veškerou dokumentaci
  - `/frontend-vue/` - pro UI komponenty
  - `/tests/` - pro všechny testy
  - **NEUKLÁDEJ SOUBORY DO KOŘENOVÉHO ADRESÁŘE** (/opt/gent)

### 6. Datové práce
- [ ] **Zásady práce s daty**
  - **V KÓDU NESMÍ BÝT NIKDY ULOŽENA DATA** - data pouze v databázi
  - Kritická konfigurace pouze v .ENV souborech nebo proměnných prostředí
  - **MOCK DATA JSOU PŘÍSNĚ ZAKÁZÁNA**
  - V JSON nesmí být komentáře
  - Konfigurační soubory pouze pro nastavení aplikace, nikdy pro data
  - Všechny entity a informace ukládej do databáze
  - Při načítání dat vždy používej databázové služby
  - Validuj vstupní data před zpracováním
  - Používej parametrizované dotazy
  - Šifruj citlivá data při přenosu i ukládání

### 7. Povinné MCP servery
- [ ] **Vždy používej tyto MCP servery:**
  - fetch
  - perplexity-ask
  - tavily
  - filesystem
  - memory
  - sequentialthinking
  - brave-search

---

## 🤖 AGENTNÍ SYSTÉM (z docs/agents_documentation.md)

### 1. Základní principy agentního systému
- [ ] **Autonomie** - Agenti mohou samostatně vykonávat úkoly
- [ ] **Specializace** - Každý agent se specializuje na určitý typ úkolů
- [ ] **Spolupráce** - Agenti mohou spolupracovat v týmech
- [ ] **Adaptabilita** - Agenti se mohou učit a zlepšovat

### 2. Komponenty agenta
- [ ] **AgentContext** - Kontext, ve kterém agent pracuje
- [ ] **AgentMemory** - Paměť agenta pro informace a zkušenosti
- [ ] **AgentCapability** - Schopnosti agenta
- [ ] **AgentStatus** - Aktuální stav agenta

### 3. Životní cyklus agenta
- [ ] **Inicializace** - Vytvoření a inicializace s parametry
- [ ] **Aktivace** - Připravenost přijímat úkoly
- [ ] **Zpracování úkolů** - Zpracování přidělených úkolů
- [ ] **Deaktivace** - Deaktivace když není potřeba
- [ ] **Ukončení** - Ukončení a uvolnění zdrojů

### 4. Typy agentů
- [ ] **Developer Agent** - Vývoj a refaktorování kódu
  - Psaní kódu v různých jazycích
  - Refaktorování existujícího kódu
  - Optimalizace kódu
  - Implementace nových funkcí
  - Doporučené LLM: GPT-4, Claude 3 Opus

- [ ] **Test Agent** - Psaní a spouštění testů
  - Unit testy, integrační testy
  - Spouštění testů
  - Analýza výsledků testů
  - Identifikace chyb a problémů
  - Doporučené LLM: GPT-4, Claude 3 Opus

- [ ] **Analyst Agent** - Analýza dat a požadavků
  - Analýza dat, identifikace vzorů
  - Analýza požadavků
  - Vytváření reportů
  - Doporučené LLM: GPT-4, Claude 3 Opus, Gemini Pro

- [ ] **Research Agent** - Vyhledávání a zpracování informací
  - Vyhledávání informací
  - Analýza a syntéza informací
  - Ověřování faktů, vytváření souhrnů
  - Doporučené LLM: Claude 3 Opus, GPT-4, Gemini Pro

- [ ] **Creative Agent** - Generování textů a nápadů
  - Generování textů a nápadů
  - Vytváření obsahu, brainstorming
  - Doporučené LLM: Claude 3 Opus, GPT-4

### 5. Skupiny agentů
- [ ] **Výzkumná skupina** - Agenti zaměření na výzkum a analýzu
- [ ] **Vývojová skupina** - Agenti zaměření na vývoj a testování
- [ ] **Kreativní skupina** - Agenti zaměření na generování obsahu
- [ ] **Projektová skupina** - Agenti přiřazení ke konkrétnímu projektu

### 6. Konfigurace agentů
- [ ] **Základní konfigurace**
  - Název (jedinečný identifikátor)
  - Účel (popis účelu agenta)
  - LLM Model (model pro myšlení)
  - Skupina (skupina agenta)
  - Stav (online, offline, busy)

- [ ] **Pokročilá konfigurace**
  - Parametry LLM modelu (temperature, max tokens, top-p)
  - Přístupová práva (MCP servery, soubory, API)
  - Omezení (časové limity, limity zdrojů)

### 7. Komunikace mezi agenty
- [ ] **Mechanismy komunikace**
  - Přímá komunikace (zprávy mezi agenty)
  - Sdílená paměť (sdílení informací)
  - Blackboard (centrální tabule)

- [ ] **Protokoly komunikace**
  - Požadavek-odpověď
  - Publikace-odběr
  - Kontrakty na splnění úkolů

### 8. Testování agentů
- [ ] **Testovací rozhraní AGENTI-TEST**
  - Testování jednotlivých agentů
  - Testování skupin agentů
  - Testování komunikace mezi agenty
  - Testování výkonu agentů

- [ ] **Metriky hodnocení**
  - Úspěšnost (poměr úspěšně dokončených úkolů)
  - Rychlost (čas potřebný k dokončení)
  - Kvalita (kvalita výstupů agenta)
  - Efektivita (využití zdrojů)

---

## 🧠 LLM MANAGEMENT (z docs/ai_llm_management_podrobna_dokumentace_final.md)

### 1. Databázové schéma
- [ ] **Tabulka llm_providers**
  - provider_id (SERIAL PRIMARY KEY)
  - provider_name (VARCHAR(100) NOT NULL)
  - api_base_url (VARCHAR(255))
  - api_key (TEXT)
  - api_version (VARCHAR(50))
  - api_key_required (BOOLEAN DEFAULT TRUE)
  - auth_type (VARCHAR(30) DEFAULT 'api_key')
  - rate_limit (INTEGER)
  - is_active (BOOLEAN DEFAULT TRUE)
  - created_at, updated_at (TIMESTAMP)

- [ ] **Tabulka llm_models**
  - model_id (SERIAL PRIMARY KEY)
  - provider_id (INTEGER REFERENCES llm_providers)
  - model_name (VARCHAR(100) NOT NULL)
  - model_identifier (VARCHAR(100) NOT NULL)
  - context_length (INTEGER)
  - max_tokens_output (INTEGER)
  - default_temperature (DECIMAL(3,2) DEFAULT 0.70)
  - retry_attempts (INTEGER DEFAULT 3)
  - retry_delay (INTEGER DEFAULT 1000)
  - timeout (INTEGER DEFAULT 30000)
  - pricing_input, pricing_output (DECIMAL(10,6))
  - capabilities (JSONB DEFAULT '{}')
  - is_default, is_active (BOOLEAN)
  - created_at, updated_at (TIMESTAMP)

### 2. API endpointy
- [ ] **Poskytovatelé**
  - GET /api/db/llm/providers - Seznam poskytovatelů
  - POST /api/db/llm/providers - Vytvoření poskytovatele
  - GET /api/db/llm/providers/{id} - Detail poskytovatele
  - PUT /api/db/llm/providers/{id} - Aktualizace poskytovatele
  - DELETE /api/db/llm/providers/{id} - Smazání poskytovatele

- [ ] **Modely**
  - PUT /api/db/llm/models/{id} - Aktualizace modelu
  - DELETE /api/db/llm/models/{id} - Smazání modelu

### 3. Schopnosti modelů
- [ ] **Dostupné schopnosti**
  - text, images, code, reasoning, planning, search
  - math, embeddings, function_calling, vision

### 4. Omezení a známé problémy
- [ ] **Omezení**
  - Nelze nastavit více výchozích modelů pro jednoho poskytovatele
  - Nelze mít dva modely se stejným model_identifier pro jednoho poskytovatele
  - Při změně názvu modelu je třeba generovat nový model_identifier

---

## 🌟 VIZE v9 PRINCIPY (z docs/vision_v9/cz/)

### 1. Základní filozofie
- [ ] **Účel GENT v9**
  - Inteligentní partner a akcelerátor pro realizaci lidských myšlenek
  - Demokratizace tvorby pro uživatele bez technických znalostí
  - Zesílení kreativity aktivním brainstormingem
  - Efektivní realizace autonomním řízením implementace
  - Kontinuální zlepšování učením z každé interakce

### 2. Klíčové principy
- [ ] **Kolaborace** - Aktivní spolupráce s uživatelem, dialog a vzájemné porozumění
- [ ] **Autonomie po schválení** - Po schválení myšlenky jedná GENT autonomně
- [ ] **Adaptabilita** - Dynamické přizpůsobení povaze úkolu a dostupným zdrojům
- [ ] **Modularita** - Agentní přístup s dynamicky sestavenými týmy
- [ ] **Transparentnost** - Poskytování vhledu do rozhodování a postupu
- [ ] **Efektivita a Optimalizace** - Efektivní využití zdrojů a aktivní hledání optimalizací
- [ ] **Organizace a Čistota** - Udržování pořádku v kódu a struktuře
- [ ] **Robustní Testování** - Kvalita zajišťována průběžným testováním
- [ ] **Práce s Reálnými Daty** - Minimalizace mock dat, práce s reálnými systémy

### 3. Hodnoty a etické zásady
- [ ] **Užitečnost a Prospěšnost** - Být užitečný a přinášet hodnotu uživateli
- [ ] **Bezpečnost a Spolehlivost** - Minimalizovat rizika, chránit data
- [ ] **Respekt k Uživateli** - Respektovat autonomii a záměry uživatele
- [ ] **Zodpovědnost** - Mechanismy pro sledovatelnost a zodpovědnost
- [ ] **Důsledná Dokumentace** - Pečlivá dokumentace všech procesů
- [ ] **Objektivita a Nestrannost** - Objektivní analýza, minimalizace předsudků
- [ ] **Ochrana Soukromí** - Zpracování dat s ohledem na soukromí
- [ ] **Neustálé Učení** - Závazek k vlastnímu zlepšování

### 4. Identita GENT v9
- [ ] **Partner, ne jen nástroj** - Aktivně se zapojuje, přemýšlí, navrhuje
- [ ] **Systém s "vědomím"** - Chování řízené definovaným "mozkem"
- [ ] **Dirigent inteligence** - Koordinuje práci specializovaných AI agentů
- [ ] **Učící se entita** - Vyvíjí se a zlepšuje v průběhu času

---

## 🔄 AKTUÁLNÍ STAV PROJEKTU (z docs/tasklists/aktualni_stav.md)

### 1. Funkční komponenty - ZACHOVAT!
- [ ] **Databáze PostgreSQL**
  - Databáze `gentdb` je funkční
  - Tabulky `llm_providers` a `llm_models` obsahují data
  - 4 poskytovatelé: OpenAI, Anthropic, Google, Openrouter
  - 15 modelů od těchto poskytovatelů
  - **DŮLEŽITÉ: Zachovat existující strukturu a data**

- [ ] **API server**
  - Běží jako systemd služba `gent-api` na portu 8001
  - Implementovány endpointy pro LLM poskytovatele a modely
  - Implementovány endpointy pro testování LLM
  - **DŮLEŽITÉ: Zachovat funkční endpointy**

- [ ] **Frontend**
  - Běží na portu 8000
  - Funkční stránky: Testy, Databáze, AI-LLM, CHAT-TEST
  - **DŮLEŽITÉ: Zachovat funkční stránky a jejich funkcionalitu**

- [ ] **LLM integrace**
  - Funkční integrace s OpenAI, Anthropic, Google, Openrouter
  - Konfigurace modelů v databázi
  - **DŮLEŽITÉ: Zachovat funkční integraci**

- [ ] **MCP servery**
  - filesystem, brave-search, tavily, perplexity, fetch
  - sequentialthinking, git
  - **DŮLEŽITÉ: Zachovat funkční MCP servery**

### 2. Postup pro další vývoj
- [ ] **Testovat změny izolovaně** před integrací do hlavního kódu
- [ ] **Vytvářet zálohy** před významými změnami
- [ ] **Postupovat inkrementálně** - jedna změna v jednom čase
- [ ] **Dokumentovat změny** - zaznamenávat co a proč bylo změněno
- [ ] **Pravidelně testovat** funkční komponenty

### 3. Priority pro další vývoj
- [ ] **Analýza a testování** existujících komponent
- [ ] **Dokončení kognitivní architektury** - implementace "mozku"
- [ ] **Dokončení agentního systému** - specializovaní agenti a týmy
- [ ] **Dokončení operačních módů** - PLAN, ACT, RESEARCH, IMPROVE
- [ ] **Integrace komponent** - propojení architektury, agentů a módů

---

## 🧠 KOMPLETNÍ MYŠLENKA INTELIGENCE (z docs_finall/idea.md)

### 1. Proaktivní iniciativa GENTa
- [ ] **Kontinuální monitoring** - GENT nepřetržitě sleduje uživatelovu činnost
- [ ] **Autonomní analýza** - Sám rozpoznává neefektivity a potenciální optimalizace
- [ ] **Proaktivní komunikace** - Aktivně oslovuje uživatele s konkrétními návrhy
- [ ] **Okamžitá realizace** - Po schválení ihned zahajuje implementaci
- [ ] **Učení z iniciativy** - Vyhodnocuje úspěšnost proaktivních návrhů

### 2. Kognitivní jednotky - Detailní mechanismy
- [ ] **ExecutiveControl (Výkonná kontrola)**
  - Attention Manager - řídí zaměření
  - Priority Queue - dynamické přeřazování úkolů
  - Resource Allocator - rozhoduje o aktivaci jednotek
  - Context Switcher - přepínání mezi úkoly
  - Proactive Monitor - sleduje prostředí
  - Initiative Engine - generuje proaktivní návrhy

- [ ] **PerceptionUnit (Vnímání)**
  - Pattern Recognition Engine - identifikuje vzorce
  - Semantic Parser - extrahuje význam
  - Context Mapper - mapuje vztahy
  - Anomaly Detector - rozpoznává neobvyklé prvky
  - Behavior Analyzer - analyzuje vzorce chování
  - Need Detector - identifikuje potřeby uživatele

- [ ] **ReasoningUnit (Uvažování)**
  - Deductive Reasoner - logické odvozování
  - Inductive Reasoner - zobecňování
  - Abductive Reasoner - hledání vysvětlení
  - Analogical Reasoner - uvažování na základě analogií
  - Causal Reasoner - analýza příčin a následků
  - Probabilistic Reasoner - práce s nejistotou

- [ ] **PlanningUnit (Plánování)**
  - Goal Decomposer - rozkládá cíle na podcíle
  - Dependency Analyzer - identifikuje závislosti
  - Resource Estimator - odhaduje potřebné zdroje
  - Timeline Generator - vytváří časové plány
  - Risk Assessor - hodnotí rizika
  - Optimization Engine - optimalizuje plány

- [ ] **ExecutionUnit (Provádění)**
  - Agent Orchestrator - koordinuje práci agentů
  - Task Dispatcher - přiděluje úkoly agentům
  - Progress Monitor - sleduje pokrok v reálném čase
  - Problem Resolver - řeší vzniklé problémy
  - Adaptation Engine - upravuje plány
  - Quality Controller - kontroluje kvalitu výstupů

- [ ] **ReflectionUnit (Reflexe)**
  - Performance Analyzer - analyzuje vlastní výkon
  - Decision Reviewer - přehodnocuje rozhodnutí
  - Pattern Extractor - extrahuje vzorce z chování
  - Bias Detector - identifikuje předsudky
  - Improvement Identifier - navrhuje zlepšení

- [ ] **LearningUnit (Učení)**
  - Experience Encoder - kóduje zkušenosti
  - Pattern Learner - učí se nové vzorce
  - Strategy Optimizer - optimalizuje strategie
  - Knowledge Integrator - integruje nové znalosti
  - Skill Acquirer - získává nové dovednosti
  - Memory Consolidator - konsoliduje vzpomínky
  - Initiative Evaluator - hodnotí proaktivní návrhy
  - Feedback Processor - zpracovává zpětnou vazbu

- [ ] **CommunicationUnit (Komunikace)**
  - Language Processor - zpracovává přirozený jazyk
  - Context Maintainer - udržuje kontext konverzace
  - Intent Recognizer - rozpoznává záměry
  - Response Generator - generuje odpovědi
  - Emotion Detector - rozpoznává emocionální stavy
  - Initiative Communicator - specializovaný na proaktivní komunikaci
  - Proposal Formatter - formátuje návrhy řešení

### 3. Hierarchická paměťová architektura
- [ ] **Pracovní paměť (Working Memory)**
  - Attention Buffer - aktuálně zpracovávané informace
  - Goal Stack - hierarchie aktivních cílů
  - Context Cache - relevantní kontextové informace
  - Temporary Variables - dočasné výpočetní proměnné

- [ ] **Krátkodobá paměť (Short-term Memory)**
  - Episodické vzpomínky - konkrétní události
  - Procedurální vzpomínky - nedávno naučené postupy
  - Sémantické fragmenty - nové koncepty
  - Emocionální asociace - hodnocení úspěchů/neúspěchů

- [ ] **Dlouhodobá paměť (Long-term Memory)**
  - Sémantická paměť (konceptuální síť, faktické znalosti)
  - Episodická paměť (projektové historie, interakční historie)
  - Procedurální paměť (automatizované postupy, optimalizované algoritmy)

### 4. Introspektivní mechanismy
- [ ] **Self-monitoring (Sebesledování)**
  - Rychlost zpracování
  - Přesnost rozhodování
  - Efektivita zdrojů
  - Kvalita výstupů
  - Konzistence

- [ ] **Meta-kognitivní procesy**
  - Meta-memory - vědomí o vlastní paměti
  - Meta-reasoning - uvažování o vlastním uvažování
  - Meta-learning - učení se o vlastním učení
  - Meta-planning - plánování vlastního plánování
  - Meta-communication - reflexe komunikačních vzorců

- [ ] **Bias detection a korekce**
  - Confirmation bias, Anchoring bias
  - Availability bias, Overconfidence bias
  - Recency bias

### 5. Vytváření vlastních nástrojů
- [ ] **Identifikace potřeby nástroje**
  - Frekvence použití
  - Komplexnost vývoje vs. přínos
  - Jedinečnost (neexistence alternativy)
  - ROI (návratnost investice)
  - Udržitelnost

- [ ] **Typy nástrojů**
  - Kognitivní nástroje (specializované reasonery, pattern matchers)
  - Komunikační nástroje (protocol adapters, data transformers)
  - Vývojové nástroje (code generators, test frameworks)

### 6. Dvojí databázový systém
- [ ] **PostgreSQL - Pouze konfigurace systému**
  - LLM modely a poskytovatelé
  - Systémová nastavení
  - Uživatelské profily
  - Přístupová oprávnění

- [ ] **Supabase - Veškerá pracovní data**
  - Konverzace a komunikace
  - Projekty a úkoly
  - Týmy a agenti
  - Znalostní báze
  - Analytická data
  - Vlastní nástroje a rozšíření

### 7. Operační módy
- [ ] **PLAN** - Kolaborativní definování a plánování úkolů
- [ ] **ACT** - Autonomní realizace schválených plánů
- [ ] **RESEARCH** - Systematický sběr a analýza informací
- [ ] **IMPROVE** - Optimalizace vlastního systému nebo procesů
- [ ] **COLLABORATE** - Intenzivní spolupráce s uživatelem

### 8. Emergentní chování a komplexní interakce
- [ ] **Kreativní syntéza**
  - Konceptuální blending
  - Analogické přemostění
  - Kontrafaktuální uvažování
  - Serendipitní objevy
  - Paradigmatické posuny

- [ ] **Adaptivní inteligence**
  - Rozpoznání novosti
  - Aktivace adaptačních mechanismů
  - Experimentální přístupy
  - Učení z výsledků
  - Aktualizace strategií

- [ ] **Kolektivní řešení problémů**
  - Distribuované zpracování
  - Paralelní přístupy
  - Syntéza perspektiv
  - Konsenzuální řešení
  - Kolektivní učení

### 9. Personalizace a adaptace
- [ ] **Profilování uživatele**
  - Komunikační styl
  - Kognitivní preference
  - Doménové zájmy
  - Pracovní vzorce
  - Rozhodovací styl

- [ ] **Adaptivní komunikace**
  - Výběr komunikačního stylu
  - Generování odpovědi
  - Monitoring reakce
  - Adaptace stylu

- [ ] **Prediktivní asistence**
  - Predikce potřeb
  - Proaktivní návrhy
  - Validace užitečnosti
  - Refinace predikčních modelů

---

**Status:** 🔄 PŘIPRAVENO K IMPLEMENTACI
**Závislosti:** Všechny existující funkční komponenty musí být zachovány
**Další:** Implementace podle priorit s respektováním existujících funkcí a integrace kompletní vize
