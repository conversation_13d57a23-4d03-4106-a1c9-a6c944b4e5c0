# Task List pro dokončení projektu Gent

## 1. <PERSON><PERSON><PERSON>za aktuálního stavu
- [x] Zkontrolovat běžíc<PERSON> služby (gent-api, PostgreSQL, frontend)
- [x] Prozkoumat strukturu projektu
- [x] Identifikovat hlavní komponenty systému
- [x] Ověřit stav databáze
- [x] Vytvořit task list pro dalš<PERSON> postup

## 2. Databázová integrace
- [ ] Ověřit strukturu databázových tabulek
  - [ ] Zkontrolovat tabulku `llm_providers`
  - [ ] Zkontrolovat tabulku `llm_models`
  - [ ] Zkontrolovat další tabulky podle schématu
- [ ] Ověřit existující data v databázi
  - [ ] Zkontrolovat, zda existují reálná data (ne mock data)
  - [ ] Identifikovat chybějící data
- [ ] Otestovat připojení k databázi z API
  - [ ] Zkontrolovat konfiguraci připojení
  - [ ] Otestovat základní CRUD operace
- [ ] Implementovat chybějící databázové funkce
  - [ ] Identifikovat chybějící funkce
  - [ ] Implementovat a otestovat

## 3. API server
- [ ] Zkontrolovat všechny API endpointy
  - [ ] Ověřit, že všechny endpointy jsou funkční
  - [ ] Otestovat odpovědi endpointů
- [ ] Optimalizovat výkon API
  - [ ] Identifikovat pomalé endpointy
  - [ ] Optimalizovat databázové dotazy
- [ ] Implementovat chybějící API funkce
  - [ ] Identifikovat chybějící funkce podle dokumentace
  - [ ] Implementovat a otestovat
- [ ] Zajistit správné logování
  - [ ] Zkontrolovat nastavení logování
  - [ ] Ověřit, že důležité události jsou logovány

## 4. Frontend
- [ ] Zkontrolovat všechny stránky a komponenty
  - [ ] Ověřit, že všechny stránky se správně načítají
  - [ ] Zkontrolovat responzivní design
- [ ] Otestovat komunikaci s API
  - [ ] Ověřit, že frontend správně komunikuje s API
  - [ ] Zkontrolovat zpracování chyb
- [ ] Implementovat chybějící frontend funkce
  - [ ] Identifikovat chybějící funkce podle dokumentace
  - [ ] Implementovat a otestovat
- [ ] Optimalizovat výkon frontendu
  - [ ] Zkontrolovat načítání stránek
  - [ ] Optimalizovat velikost balíčků

## 5. LLM integrace
- [ ] Zkontrolovat konfiguraci LLM poskytovatelů
  - [ ] Ověřit, že všichni poskytovatelé jsou správně nakonfigurováni
  - [ ] Zkontrolovat API klíče
- [ ] Otestovat komunikaci s LLM API
  - [ ] Otestovat každého poskytovatele
  - [ ] Zkontrolovat zpracování odpovědí
- [ ] Implementovat chybějící LLM funkce
  - [ ] Identifikovat chybějící funkce
  - [ ] Implementovat a otestovat

## 6. MCP servery
- [ ] Zkontrolovat běžící MCP servery
  - [ ] Ověřit, že všechny potřebné MCP servery běží
  - [ ] Zkontrolovat konfiguraci
- [ ] Otestovat komunikaci s MCP servery
  - [ ] Otestovat každý MCP server
  - [ ] Zkontrolovat zpracování odpovědí
- [ ] Implementovat chybějící MCP funkce
  - [ ] Identifikovat chybějící funkce
  - [ ] Implementovat a otestovat

## 7. Testování
- [ ] Vytvořit a spustit unit testy
  - [ ] Napsat testy pro API
  - [ ] Napsat testy pro databázové funkce
- [ ] Vytvořit a spustit integrační testy
  - [ ] Otestovat komunikaci mezi komponentami
  - [ ] Otestovat end-to-end scénáře
- [ ] Provést manuální testování
  - [ ] Otestovat uživatelské scénáře
  - [ ] Identifikovat a opravit chyby

## 8. Dokumentace
- [ ] Aktualizovat technickou dokumentaci
  - [ ] Dokumentovat API endpointy
  - [ ] Dokumentovat databázové schéma
- [ ] Vytvořit uživatelskou dokumentaci
  - [ ] Napsat návody pro používání systému
  - [ ] Vytvořit FAQ
- [ ] Dokumentovat proces nasazení
  - [ ] Popsat kroky pro nasazení
  - [ ] Dokumentovat konfiguraci

## 9. Zabezpečení
- [ ] Zkontrolovat autentizaci a autorizaci
  - [ ] Ověřit, že přístup k API je zabezpečen
  - [ ] Zkontrolovat správu uživatelských rolí
- [ ] Provést bezpečnostní audit
  - [ ] Identifikovat potenciální zranitelnosti
  - [ ] Implementovat opravy
- [ ] Zabezpečit citlivá data
  - [ ] Zkontrolovat šifrování citlivých dat
  - [ ] Ověřit bezpečné ukládání API klíčů

## 10. Produkční nasazení
- [ ] Připravit produkční prostředí
  - [ ] Zkontrolovat systemd služby
  - [ ] Nastavit produkční proměnné prostředí
- [ ] Implementovat zálohovací systém
  - [ ] Nastavit pravidelné zálohování databáze
  - [ ] Implementovat zálohu konfiguračních souborů
- [ ] Nastavit monitoring
  - [ ] Implementovat monitoring služeb
  - [ ] Nastavit alerting
- [ ] Provést finální testy v produkčním prostředí
  - [ ] Otestovat výkon
  - [ ] Ověřit stabilitu

## 11. Předání a školení
- [ ] Připravit předávací dokumentaci
  - [ ] Shrnout provedené změny
  - [ ] Dokumentovat známé problémy
- [ ] Provést školení uživatelů
  - [ ] Připravit školící materiály
  - [ ] Provést školení
- [ ] Nastavit proces podpory
  - [ ] Definovat proces hlášení chyb
  - [ ] Nastavit SLA pro řešení problémů
