# 🔧 MCP Management System - Kompletní Tasklist

## 🎯 Cíl: Vytvoření MCP Management podobného LLM Management

Vytvořit kompletní systém pro správu MCP (Model Context Protocol) serverů v GENT, který umožní:
- **Databázové ukládání** MCP serverů a jejich konfigurací
- **GUI pro správu** MCP serverů (přidávání, editace, testování)
- **<PERSON>k<PERSON> o<PERSON>jevování** vhodných MCP serverů pro úkoly
- **Performance monitoring** a metriky MCP serverů
- **Inteligentní výběr** MCP nástrojů podle kontextu

---

## 📋 **FÁZE 1: Databázová struktura pro MCP servery**

### 1.1 Vytvoření databázových tabulek
- [ ] **Tabulka `mcp_providers`**
  - [ ] `provider_id` (SERIAL PRIMARY KEY)
  - [ ] `provider_name` (VARCHAR) - filesystem, brave-search, fetch, mem0
  - [ ] `provider_type` (VARCHAR) - web_search, file_system, memory, fetch
  - [ ] `description` (TEXT)
  - [ ] `command` (TEXT) - npx command nebo custom script
  - [ ] `is_active` (BOOLEAN)
  - [ ] `is_custom` (BOOLEAN) - true pro vlastní MCP servery
  - [ ] `created_at`, `updated_at`

- [ ] **Tabulka `mcp_tools`**
  - [ ] `tool_id` (SERIAL PRIMARY KEY)
  - [ ] `provider_id` (FK na mcp_providers)
  - [ ] `tool_name` (VARCHAR) - brave_web_search, read_file, fetch_html
  - [ ] `tool_identifier` (VARCHAR) - jedinečný identifikátor
  - [ ] `description` (TEXT)
  - [ ] `parameters_schema` (JSONB) - JSON schema pro parametry
  - [ ] `auto_approve` (BOOLEAN) - automatické schválení
  - [ ] `is_active` (BOOLEAN)
  - [ ] `capabilities` (JSONB) - co nástroj umí

- [ ] **Tabulka `mcp_configurations`**
  - [ ] `config_id` (SERIAL PRIMARY KEY)
  - [ ] `provider_id` (FK na mcp_providers)
  - [ ] `config_name` (VARCHAR)
  - [ ] `environment_vars` (JSONB) - API klíče, konfigurace
  - [ ] `timeout` (INTEGER)
  - [ ] `retry_count` (INTEGER)
  - [ ] `security_settings` (JSONB)
  - [ ] `is_default` (BOOLEAN)

### 1.2 Migrace existujících MCP serverů
- [ ] **Skript pro import z `config/mcp_config.json`**
  - [ ] Načtení existující konfigurace
  - [ ] Převod na databázovou strukturu
  - [ ] Import všech 8 existujících serverů
  - [ ] Zachování všech nastavení a API klíčů

---

## 📋 **FÁZE 2: API endpointy pro MCP Management**

### 2.1 CRUD operace pro MCP providers
- [ ] **`GET /api/mcp/providers`** - seznam všech poskytovatelů
- [ ] **`GET /api/mcp/providers/{id}`** - detail poskytovatele
- [ ] **`POST /api/mcp/providers`** - vytvoření nového poskytovatele
- [ ] **`PUT /api/mcp/providers/{id}`** - aktualizace poskytovatele
- [ ] **`DELETE /api/mcp/providers/{id}`** - smazání poskytovatele

### 2.2 CRUD operace pro MCP tools
- [ ] **`GET /api/mcp/tools`** - seznam všech nástrojů
- [ ] **`GET /api/mcp/tools/by-provider/{provider_id}`** - nástroje podle poskytovatele
- [ ] **`POST /api/mcp/tools`** - přidání nového nástroje
- [ ] **`PUT /api/mcp/tools/{id}`** - aktualizace nástroje
- [ ] **`DELETE /api/mcp/tools/{id}`** - smazání nástroje

### 2.3 MCP server management
- [ ] **`GET /api/mcp/status`** - stav všech MCP serverů
- [ ] **`POST /api/mcp/start/{provider_id}`** - spuštění MCP serveru
- [ ] **`POST /api/mcp/stop/{provider_id}`** - zastavení MCP serveru
- [ ] **`POST /api/mcp/restart/{provider_id}`** - restart MCP serveru
- [ ] **`POST /api/mcp/test/{provider_id}`** - test MCP serveru

### 2.4 MCP tool execution
- [ ] **`POST /api/mcp/execute`** - spuštění MCP nástroje
- [ ] **`GET /api/mcp/tools/search`** - vyhledání vhodného nástroje
- [ ] **`POST /api/mcp/tools/recommend`** - doporučení nástroje pro úkol

---

## 📋 **FÁZE 3: Frontend MCP Management GUI**

### 3.1 Hlavní MCP Management stránka
- [ ] **Route `/admin/mcp`** - hlavní stránka MCP Management
- [ ] **Komponenta `McpManagement.vue`**
  - [ ] Taby: Providers, Tools, Configurations, Metrics, Testing
  - [ ] Responsive design podobný LLM Management
  - [ ] Real-time status indikátory

### 3.2 Providers tab
- [ ] **Seznam MCP poskytovatelů**
  - [ ] Tabulka s provider_name, type, status, tools_count
  - [ ] Filtry podle typu (web_search, file_system, memory)
  - [ ] Vyhledávání podle názvu
  - [ ] Status indikátory (online/offline/error)

- [ ] **Přidání nového poskytovatele**
  - [ ] Modal dialog s formulářem
  - [ ] Pole: name, type, description, command
  - [ ] Validace vstupů
  - [ ] Test připojení před uložením

- [ ] **Editace poskytovatele**
  - [ ] Inline editace nebo modal
  - [ ] Možnost změny všech parametrů
  - [ ] Preview změn před uložením

### 3.3 Tools tab
- [ ] **Seznam MCP nástrojů**
  - [ ] Tabulka s tool_name, provider, description, auto_approve
  - [ ] Filtry podle poskytovatele
  - [ ] Vyhledávání podle názvu nebo popisu
  - [ ] Možnost aktivace/deaktivace nástrojů

- [ ] **Detail nástroje**
  - [ ] Zobrazení parameters_schema
  - [ ] Capabilities a omezení
  - [ ] Historie použití
  - [ ] Test nástroje s ukázkovými parametry

### 3.4 Configurations tab
- [ ] **Konfigurace MCP serverů**
  - [ ] Environment variables (API klíče)
  - [ ] Timeout a retry nastavení
  - [ ] Security settings
  - [ ] Export/import konfigurací

### 3.5 Metrics tab
- [ ] **Performance metriky MCP serverů**
  - [ ] Přehledové karty: Total Tools, Active Servers, Success Rate
  - [ ] Tabulka serverů s metrikami (response time, usage count)
  - [ ] Grafy využití v čase
  - [ ] Top performing tools

### 3.6 Testing tab
- [ ] **Testování MCP nástrojů**
  - [ ] Výběr nástroje z dropdown
  - [ ] Formulář pro parametry
  - [ ] Spuštění testu a zobrazení výsledku
  - [ ] Historie testů
  - [ ] Export výsledků

---

## 📋 **FÁZE 4: Inteligentní MCP Discovery**

### 4.1 MCP Recommendation Engine
- [ ] **Analýza úkolů a doporučení nástrojů**
  - [ ] Klíčová slova → MCP nástroje mapping
  - [ ] "potřebujem na web" → brave-search, fetch
  - [ ] "načti soubor" → filesystem
  - [ ] "zapamatuj si" → mem0 (až bude implementován)

- [ ] **Kontext-aware výběr**
  - [ ] Analýza aktuálního kontextu konverzace
  - [ ] Preference uživatele
  - [ ] Performance historie nástrojů

### 4.2 Auto-discovery API
- [ ] **`POST /api/mcp/discover`** - najdi vhodný nástroj
  - [ ] Input: popis úkolu, kontext
  - [ ] Output: doporučené nástroje s confidence score
  - [ ] Učení z úspěšných použití

### 4.3 Integration s GENT AI
- [ ] **Automatické použití MCP nástrojů**
  - [ ] GENT rozpozná potřebu nástroje
  - [ ] Automaticky vybere a spustí vhodný MCP
  - [ ] Zpracuje výsledek a pokračuje

---

## 📋 **FÁZE 5: Přidání nových MCP serverů**

### 5.1 Mem0 Memory Server
- [ ] **Instalace a konfigurace Mem0**
  - [ ] `pip install mem0ai` nebo Docker container
  - [ ] Konfigurace pro GENT
  - [ ] API klíče a nastavení

- [ ] **Integrace do GENT**
  - [ ] Přidání do databáze mcp_providers
  - [ ] Definice nástrojů (store_memory, retrieve_memory, search_memory)
  - [ ] Test funkčnosti

### 5.2 Další užitečné MCP servery
- [ ] **Weather MCP** - informace o počasí
- [ ] **Calendar MCP** - správa kalendáře
- [ ] **Email MCP** - odesílání emailů
- [ ] **Database MCP** - přístup k databázím
- [ ] **Docker MCP** - správa kontejnerů

### 5.3 Custom GENT MCP servery
- [ ] **GENT Knowledge MCP** - přístup k znalostní bázi
- [ ] **GENT Analytics MCP** - analýzy a reporty
- [ ] **GENT Workflow MCP** - správa workflow

---

## 📋 **FÁZE 6: Performance Monitoring**

### 6.1 MCP Metrics Collection
- [ ] **Tabulka `mcp_requests`** pro metriky
  - [ ] tool_id, provider_id, request_data
  - [ ] response_time, success, error_message
  - [ ] tokens_used (pokud relevantní)
  - [ ] created_at

- [ ] **Automatické zaznamenávání**
  - [ ] Middleware pro všechna MCP volání
  - [ ] Měření response time
  - [ ] Sledování úspěšnosti

### 6.2 Performance Analytics
- [ ] **API endpointy pro metriky**
  - [ ] `GET /api/mcp/metrics/overview`
  - [ ] `GET /api/mcp/metrics/tools`
  - [ ] `GET /api/mcp/metrics/providers`

- [ ] **Dashboard v GUI**
  - [ ] Grafy využití nástrojů
  - [ ] Performance trendy
  - [ ] Error rate monitoring

---

## 📋 **FÁZE 7: Security a Permissions**

### 7.1 MCP Security Framework
- [ ] **Tabulka `mcp_permissions`**
  - [ ] user_id, tool_id, permission_level
  - [ ] allowed_operations, restrictions
  - [ ] created_at, expires_at

- [ ] **API Security**
  - [ ] Ověření oprávnění před spuštěním nástroje
  - [ ] Rate limiting pro MCP volání
  - [ ] Audit log všech MCP operací

### 7.2 Safe Execution Environment
- [ ] **Sandbox pro MCP nástroje**
  - [ ] Omezení přístupu k souborům
  - [ ] Network restrictions
  - [ ] Resource limits (CPU, memory)

---

## 📋 **FÁZE 8: Testing a Documentation**

### 8.1 Automated Testing
- [ ] **Unit testy pro MCP API**
- [ ] **Integration testy pro MCP servery**
- [ ] **E2E testy pro GUI**
- [ ] **Performance testy**

### 8.2 Documentation
- [ ] **API dokumentace** - Swagger/OpenAPI
- [ ] **User guide** - jak používat MCP Management
- [ ] **Developer guide** - jak přidat nový MCP server
- [ ] **Troubleshooting guide**

---

## 🎯 **Prioritní pořadí implementace:**

### **Sprint 1 (Týden 1-2):**
- Fáze 1: Databázová struktura
- Fáze 2: Základní API endpointy

### **Sprint 2 (Týden 3-4):**
- Fáze 3.1-3.2: Frontend Providers tab
- Fáze 5.1: Mem0 integration

### **Sprint 3 (Týden 5-6):**
- Fáze 3.3-3.4: Tools a Configurations tabs
- Fáze 6.1: Metrics collection

### **Sprint 4 (Týden 7-8):**
- Fáze 3.5-3.6: Metrics a Testing tabs
- Fáze 4: Inteligentní discovery

**Výsledek:** Kompletní MCP Management systém, který umožní GENT inteligentně vybírat a používat MCP nástroje podle potřeby! 🚀
