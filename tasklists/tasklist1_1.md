# 📋 tasklist1_1.md – Definice celkove vize a cile projektu

> Rozpracovani prvni polozky z `tasklist1.md`.  
> Cilem je vytvorit jednoznacny, meritelny a sdileny popis vize GENT.

## 1. Syntéza klicovych piliru GENT
- [ ] Vyextrahovat 4 pilire (Demokratizace tvorby, Autonomni realizace, Proaktivni iniciativa, Kontinualni evoluce) z dokumentu vize
- [ ] Zpracovat shrnuti kazdeho pilire do 1 odstavce pro vedeni
- [ ] Prelozit shrnuti do kratke „elevator pitch” verze (max 30 slov)
- [ ] Odsouhlasit wording s product ownery

## 2. Definice meritelnych kriterii uspechu
- [ ] Navrhnout KPI pro casovou uspory uzivatele (h/měsíc)
- [ ] KPI pro miru automatizace (pocet automatizovanych ukolu / celkove ukoly)
- [ ] KPI pro kvalitu proaktivnich navrhu (schvalene / navrzene)
- [ ] KPI pro spokojenost uzivatele (NPS, CES)
- [ ] Schválit metriky s vedením a datovým analytikem

## 3. Vypracovani dokumentu Vision Document
- [ ] Struktura: Context, Goals, Non‑Goals, Success Metrics
- [ ] Priprava draftu v Markdownu
- [ ] Review s klicovymi stakeholdery
- [ ] Finalizace a publikace do knowledge base

## 4. Workflow schvalovani ukolu uzivatelem
- [ ] Model stavu: Draft → Pending Approval → Approved → In Progress → Done
- [ ] Definice JSON schematu pro stav v Supabase
- [ ] Implementace REST endpointu pro zmenu stavu
- [ ] Test end‑to‑end se dvema fiktivnimi ukoly

## 5. Sablony pro proaktivni komunikaci
- [ ] Markdown sablona s placeholdery {problem}, {solution}, {benefit}
- [ ] Unit testy na validator sablony
- [ ] Ukladani sablon do Supabase table `proactive_templates`
- [ ] UX text review s copywriterem
