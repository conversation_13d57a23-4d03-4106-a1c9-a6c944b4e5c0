# 🗄️ MCP Database Schema - Detailní návrh

## 🎯 Cíl: Databázová struktura pro MCP Management

Vytvořit kompletní databázovou strukturu pro správu MCP serverů, podobnou LLM Management, ale optimalizovanou pro MCP specifika.

---

## 📊 **Datab<PERSON><PERSON><PERSON><PERSON> tabulky**

### 1. **`mcp_providers`** - Poskytovatelé MCP serverů
```sql
CREATE TABLE mcp_providers (
    provider_id SERIAL PRIMARY KEY,
    provider_name VARCHAR(100) NOT NULL UNIQUE,     -- filesystem, brave-search, fetch, mem0
    provider_type VARCHAR(50) NOT NULL,             -- web_search, file_system, memory, fetch, git
    display_name VARCHAR(200),                      -- "Brave Search", "File System Access"
    description TEXT,                               -- <PERSON><PERSON>
    command TEXT NOT NULL,                          -- npx command nebo custom script
    base_url VARCHAR(500),                          -- URL pro HTTP MCP servery
    is_active BOOLEAN DEFAULT true,                 -- Aktivní/neaktivní
    is_custom BOOLEAN DEFAULT false,                -- <PERSON><PERSON><PERSON><PERSON> vs. standardní MCP
    auto_start BOOLEAN DEFAULT true,                -- Automatické spuštění
    health_check_url VARCHAR(500),                  -- URL pro health check
    documentation_url VARCHAR(500),                 -- Link na dokumentaci
    version VARCHAR(50),                            -- Verze MCP serveru
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexy pro rychlé vyhledávání
CREATE INDEX idx_mcp_providers_type ON mcp_providers(provider_type);
CREATE INDEX idx_mcp_providers_active ON mcp_providers(is_active);
CREATE INDEX idx_mcp_providers_name ON mcp_providers(provider_name);
```

### 2. **`mcp_tools`** - Nástroje dostupné v MCP serverech
```sql
CREATE TABLE mcp_tools (
    tool_id SERIAL PRIMARY KEY,
    provider_id INTEGER REFERENCES mcp_providers(provider_id) ON DELETE CASCADE,
    tool_name VARCHAR(100) NOT NULL,                -- brave_web_search, read_file, fetch_html
    tool_identifier VARCHAR(150) NOT NULL,          -- Jedinečný identifikátor
    display_name VARCHAR(200),                      -- "Web Search", "Read File"
    description TEXT,                               -- Popis funkčnosti nástroje
    parameters_schema JSONB,                        -- JSON schema pro parametry
    response_schema JSONB,                          -- JSON schema pro odpověď
    auto_approve BOOLEAN DEFAULT false,             -- Automatické schválení
    is_active BOOLEAN DEFAULT true,                 -- Aktivní/neaktivní
    is_dangerous BOOLEAN DEFAULT false,             -- Nebezpečný nástroj (write, delete)
    capabilities JSONB,                             -- Co nástroj umí {"search": true, "write": false}
    usage_examples JSONB,                           -- Příklady použití
    rate_limit_per_minute INTEGER DEFAULT 60,       -- Rate limit
    timeout_seconds INTEGER DEFAULT 30,             -- Timeout
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(provider_id, tool_identifier)
);

-- Indexy
CREATE INDEX idx_mcp_tools_provider ON mcp_tools(provider_id);
CREATE INDEX idx_mcp_tools_active ON mcp_tools(is_active);
CREATE INDEX idx_mcp_tools_name ON mcp_tools(tool_name);
CREATE INDEX idx_mcp_tools_capabilities ON mcp_tools USING GIN(capabilities);
```

### 3. **`mcp_configurations`** - Konfigurace MCP serverů
```sql
CREATE TABLE mcp_configurations (
    config_id SERIAL PRIMARY KEY,
    provider_id INTEGER REFERENCES mcp_providers(provider_id) ON DELETE CASCADE,
    config_name VARCHAR(100) NOT NULL,             -- "production", "development", "testing"
    environment_vars JSONB,                        -- {"BRAVE_API_KEY": "xxx", "TIMEOUT": "30"}
    startup_args JSONB,                            -- Argumenty pro spuštění
    working_directory VARCHAR(500),                -- Pracovní adresář
    timeout_seconds INTEGER DEFAULT 30,            -- Timeout pro operace
    retry_count INTEGER DEFAULT 3,                 -- Počet opakování
    retry_delay_ms INTEGER DEFAULT 1000,           -- Zpoždění mezi pokusy
    max_concurrent_requests INTEGER DEFAULT 10,    -- Max současných requestů
    security_settings JSONB,                       -- Bezpečnostní nastavení
    resource_limits JSONB,                         -- {"memory": "512MB", "cpu": "0.5"}
    is_default BOOLEAN DEFAULT false,              -- Výchozí konfigurace
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(provider_id, config_name)
);

-- Indexy
CREATE INDEX idx_mcp_configs_provider ON mcp_configurations(provider_id);
CREATE INDEX idx_mcp_configs_default ON mcp_configurations(is_default);
```

### 4. **`mcp_requests`** - Log všech MCP requestů (pro metriky)
```sql
CREATE TABLE mcp_requests (
    request_id SERIAL PRIMARY KEY,
    tool_id INTEGER REFERENCES mcp_tools(tool_id),
    provider_id INTEGER REFERENCES mcp_providers(provider_id),
    request_data JSONB,                            -- Parametry requestu
    response_data JSONB,                           -- Odpověď
    response_time_ms FLOAT,                        -- Čas odpovědi v ms
    status VARCHAR(20) NOT NULL,                   -- success, error, timeout
    error_message TEXT,                            -- Chybová zpráva
    error_code VARCHAR(50),                        -- Kód chyby
    user_id VARCHAR(100),                          -- ID uživatele (pokud relevantní)
    session_id VARCHAR(100),                       -- Session ID
    request_size_bytes INTEGER,                    -- Velikost requestu
    response_size_bytes INTEGER,                   -- Velikost odpovědi
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CHECK (status IN ('success', 'error', 'timeout', 'cancelled'))
);

-- Indexy pro performance analytics
CREATE INDEX idx_mcp_requests_tool ON mcp_requests(tool_id);
CREATE INDEX idx_mcp_requests_provider ON mcp_requests(provider_id);
CREATE INDEX idx_mcp_requests_status ON mcp_requests(status);
CREATE INDEX idx_mcp_requests_created_at ON mcp_requests(created_at);
CREATE INDEX idx_mcp_requests_response_time ON mcp_requests(response_time_ms);
```

### 5. **`mcp_server_status`** - Aktuální stav MCP serverů
```sql
CREATE TABLE mcp_server_status (
    status_id SERIAL PRIMARY KEY,
    provider_id INTEGER REFERENCES mcp_providers(provider_id) ON DELETE CASCADE,
    status VARCHAR(20) NOT NULL,                   -- online, offline, error, starting, stopping
    process_id INTEGER,                            -- PID procesu
    port INTEGER,                                  -- Port na kterém běží
    memory_usage_mb FLOAT,                         -- Využití paměti
    cpu_usage_percent FLOAT,                       -- Využití CPU
    uptime_seconds INTEGER,                        -- Doba běhu
    last_health_check TIMESTAMP,                  -- Poslední health check
    health_check_status VARCHAR(20),              -- healthy, unhealthy, unknown
    error_message TEXT,                            -- Chybová zpráva
    restart_count INTEGER DEFAULT 0,              -- Počet restartů
    last_restart TIMESTAMP,                       -- Poslední restart
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CHECK (status IN ('online', 'offline', 'error', 'starting', 'stopping')),
    CHECK (health_check_status IN ('healthy', 'unhealthy', 'unknown')),
    UNIQUE(provider_id)
);

-- Indexy
CREATE INDEX idx_mcp_status_provider ON mcp_server_status(provider_id);
CREATE INDEX idx_mcp_status_status ON mcp_server_status(status);
CREATE INDEX idx_mcp_status_health ON mcp_server_status(health_check_status);
```

### 6. **`mcp_permissions`** - Oprávnění pro MCP nástroje
```sql
CREATE TABLE mcp_permissions (
    permission_id SERIAL PRIMARY KEY,
    user_id VARCHAR(100),                          -- ID uživatele nebo role
    tool_id INTEGER REFERENCES mcp_tools(tool_id) ON DELETE CASCADE,
    provider_id INTEGER REFERENCES mcp_providers(provider_id) ON DELETE CASCADE,
    permission_level VARCHAR(20) NOT NULL,        -- read, write, execute, admin
    allowed_operations JSONB,                      -- Povolené operace
    restrictions JSONB,                            -- Omezení {"max_requests_per_hour": 100}
    is_active BOOLEAN DEFAULT true,
    granted_by VARCHAR(100),                       -- Kdo udělil oprávnění
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,                          -- Vypršení oprávnění
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    CHECK (permission_level IN ('read', 'write', 'execute', 'admin'))
);

-- Indexy
CREATE INDEX idx_mcp_permissions_user ON mcp_permissions(user_id);
CREATE INDEX idx_mcp_permissions_tool ON mcp_permissions(tool_id);
CREATE INDEX idx_mcp_permissions_provider ON mcp_permissions(provider_id);
CREATE INDEX idx_mcp_permissions_active ON mcp_permissions(is_active);
```

---

## 🔄 **Migrace existujících dat**

### Script pro import z `config/mcp_config.json`:

```python
# /opt/gent/scripts/migrate_mcp_to_db.py

import json
import psycopg2
from datetime import datetime

def migrate_mcp_config_to_db():
    """Migrace MCP konfigurace z JSON do databáze."""
    
    # Načtení existující konfigurace
    with open('/opt/gent/config/mcp_config.json', 'r') as f:
        config = json.load(f)
    
    # Připojení k databázi
    conn = psycopg2.connect(
        host="localhost",
        database="gentdb",
        user="postgres",
        password="Ostrov201252"
    )
    cursor = conn.cursor()
    
    # Mapování typů MCP serverů
    type_mapping = {
        'filesystem': 'file_system',
        'brave-search': 'web_search',
        'tavily': 'web_search',
        'fetch': 'web_fetch',
        'perplexity': 'ai_search',
        'sequentialthinking': 'reasoning',
        'git': 'version_control',
        'gent-project': 'project_management',
        'gent-workflow': 'workflow_management'
    }
    
    # Import poskytovatelů
    for server_name, server_config in config['servers'].items():
        provider_type = type_mapping.get(server_name, 'other')
        
        cursor.execute("""
            INSERT INTO mcp_providers (
                provider_name, provider_type, display_name, description,
                command, is_active, is_custom, auto_start
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            RETURNING provider_id
        """, (
            server_name,
            provider_type,
            server_name.replace('-', ' ').title(),
            server_config.get('description', ''),
            server_config.get('command', ''),
            server_config.get('enabled', True),
            server_config.get('custom', False),
            True
        ))
        
        provider_id = cursor.fetchone()[0]
        
        # Import nástrojů pro každého poskytovatele
        auto_approve_tools = server_config.get('auto_approve', [])
        for tool_name in auto_approve_tools:
            cursor.execute("""
                INSERT INTO mcp_tools (
                    provider_id, tool_name, tool_identifier, display_name,
                    auto_approve, is_active
                ) VALUES (%s, %s, %s, %s, %s, %s)
            """, (
                provider_id,
                tool_name,
                f"{server_name}_{tool_name}",
                tool_name.replace('_', ' ').title(),
                True,
                True
            ))
        
        # Import konfigurace
        env_vars = server_config.get('env', {})
        cursor.execute("""
            INSERT INTO mcp_configurations (
                provider_id, config_name, environment_vars,
                timeout_seconds, is_default, is_active
            ) VALUES (%s, %s, %s, %s, %s, %s)
        """, (
            provider_id,
            'default',
            json.dumps(env_vars),
            config['global_settings'].get('timeout', 30),
            True,
            True
        ))
    
    conn.commit()
    cursor.close()
    conn.close()
    
    print("✅ MCP konfigurace byla úspěšně migrována do databáze!")

if __name__ == "__main__":
    migrate_mcp_config_to_db()
```

---

## 🎯 **Výhody této struktury:**

### **1. Flexibilita**
- Snadné přidávání nových MCP serverů
- Dynamická konfigurace bez restartů
- Podpora custom MCP serverů

### **2. Performance monitoring**
- Detailní metriky všech MCP volání
- Analýza výkonu nástrojů
- Identifikace bottlenecků

### **3. Security**
- Granulární oprávnění
- Audit trail všech operací
- Rate limiting a resource limits

### **4. Scalability**
- Podpora více instancí MCP serverů
- Load balancing
- Health monitoring

### **5. Intelligence**
- Data pro AI doporučení nástrojů
- Učení z usage patterns
- Automatická optimalizace

**Tato struktura umožní GENT inteligentně spravovat a používat MCP nástroje!** 🚀
