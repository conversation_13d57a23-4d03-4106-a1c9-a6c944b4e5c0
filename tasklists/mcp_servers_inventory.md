# 🔧 MCP Servery - Invent<PERSON>ř a plán rozšíření

## 📊 **Aktuální stav MCP serverů v GENT**

### ✅ **Existující MCP servery (8 serverů)**

#### 1. **Filesystem MCP**
- **Command:** `npx -y @modelcontextprotocol/server-filesystem /opt/gent`
- **Type:** `file_system`
- **Tools:** `read_file`, `list_directory`, `write_file`
- **Status:** ✅ Aktivní
- **Použití:** Přístup k souborovému systému GENT

#### 2. **Brave Search MCP**
- **Command:** `npx -y @modelcontextprotocol/server-brave-search`
- **Type:** `web_search`
- **Tools:** `brave_web_search`
- **API Key:** `BRAVE_API_KEY`
- **Status:** ✅ Aktivní
- **Použití:** Vyhledávání na webu

#### 3. **Tavily Search MCP**
- **Command:** `npx -y tavily-mcp`
- **Type:** `web_search`
- **Tools:** `tavily-search`
- **API Key:** `TAVILY_API_KEY`
- **Status:** ✅ Aktivní
- **Použití:** AI-powered vyhledávání

#### 4. **Fetch MCP**
- **Command:** `npx -y fetch-mcp`
- **Type:** `web_fetch`
- **Tools:** `fetch_html`, `fetch_markdown`, `fetch_txt`
- **Status:** ✅ Aktivní
- **Použití:** Načítání obsahu webových stránek

#### 5. **Perplexity MCP**
- **Command:** `npx -y perplexity-mcp`
- **Type:** `ai_search`
- **Tools:** `search`, `get_documentation`
- **API Key:** `PERPLEXITY_API_KEY`
- **Status:** ✅ Aktivní
- **Použití:** Pokročilé vyhledávání a dokumentace

#### 6. **Sequential Thinking MCP**
- **Command:** `npx -y @modelcontextprotocol/server-sequentialthinking`
- **Type:** `reasoning`
- **Tools:** `sequentialthinking`
- **Status:** ✅ Aktivní
- **Použití:** Sekvenční řešení problémů

#### 7. **Git MCP**
- **Command:** `npx -y mcp-server-git`
- **Type:** `version_control`
- **Tools:** `git_status`, `git_diff_unstaged`, `git_diff_staged`, `git_log`
- **Status:** ✅ Aktivní
- **Použití:** Git operace

#### 8. **GENT Project MCP** (Custom)
- **Command:** `node /opt/gent/scripts/mcp/project-server.js`
- **Type:** `project_management`
- **Tools:** `list_projects`, `get_project`, `project_status`
- **Status:** ✅ Aktivní (Custom)
- **Použití:** Správa GENT projektů

#### 9. **GENT Workflow MCP** (Custom)
- **Command:** `node /opt/gent/scripts/mcp/workflow-server.js`
- **Type:** `workflow_management`
- **Tools:** `get_workflow_status`, `list_workflows`, `get_workflow_logs`
- **Status:** ✅ Aktivní (Custom)
- **Použití:** Správa GENT workflow

---

## 🚀 **Plánované nové MCP servery**

### 🧠 **1. Mem0 Memory MCP** (Priorita #1)
- **GitHub:** https://github.com/mem0ai/mem0
- **Command:** `python -m mem0.mcp_server` nebo custom wrapper
- **Type:** `memory`
- **Tools:** 
  - `store_memory` - uložení vzpomínky
  - `retrieve_memory` - načtení vzpomínek
  - `search_memory` - vyhledání v paměti
  - `delete_memory` - smazání vzpomínky
  - `list_memories` - seznam všech vzpomínek
- **API Keys:** Mem0 API key (pokud cloud verze)
- **Použití:** Dlouhodobá paměť pro AI agenty
- **Implementace:**
  ```bash
  pip install mem0ai
  # Nebo Docker: docker run -p 8000:8000 mem0ai/mem0
  ```

### 📧 **2. Email MCP**
- **Command:** `npx -y @modelcontextprotocol/server-email` nebo custom
- **Type:** `communication`
- **Tools:**
  - `send_email` - odeslání emailu
  - `read_emails` - čtení emailů
  - `search_emails` - vyhledání emailů
- **API Keys:** SMTP credentials
- **Použití:** Email komunikace pro GENT

### 🌤️ **3. Weather MCP**
- **Command:** `npx -y weather-mcp`
- **Type:** `information`
- **Tools:**
  - `get_weather` - aktuální počasí
  - `get_forecast` - předpověď
  - `get_weather_alerts` - výstrahy
- **API Keys:** OpenWeatherMap API
- **Použití:** Informace o počasí

### 📅 **4. Calendar MCP**
- **Command:** `npx -y calendar-mcp`
- **Type:** `productivity`
- **Tools:**
  - `create_event` - vytvoření události
  - `list_events` - seznam událostí
  - `update_event` - aktualizace události
  - `delete_event` - smazání události
- **API Keys:** Google Calendar API
- **Použití:** Správa kalendáře

### 🗄️ **5. Database MCP**
- **Command:** `npx -y database-mcp`
- **Type:** `data_access`
- **Tools:**
  - `execute_query` - spuštění SQL dotazu
  - `get_schema` - získání schématu
  - `list_tables` - seznam tabulek
- **Credentials:** Database connection strings
- **Použití:** Přístup k databázím

### 🐳 **6. Docker MCP**
- **Command:** `npx -y docker-mcp`
- **Type:** `infrastructure`
- **Tools:**
  - `list_containers` - seznam kontejnerů
  - `start_container` - spuštění kontejneru
  - `stop_container` - zastavení kontejneru
  - `get_logs` - získání logů
- **Použití:** Správa Docker kontejnerů

### 📊 **7. Analytics MCP** (Custom GENT)
- **Command:** `node /opt/gent/scripts/mcp/analytics-server.js`
- **Type:** `analytics`
- **Tools:**
  - `get_user_stats` - statistiky uživatelů
  - `get_llm_metrics` - LLM metriky
  - `get_system_health` - zdraví systému
  - `generate_report` - generování reportů
- **Použití:** GENT analytics a reporty

### 🧠 **8. Knowledge Base MCP** (Custom GENT)
- **Command:** `node /opt/gent/scripts/mcp/knowledge-server.js`
- **Type:** `knowledge`
- **Tools:**
  - `search_knowledge` - vyhledání ve znalostní bázi
  - `add_knowledge` - přidání znalosti
  - `update_knowledge` - aktualizace znalosti
  - `get_related` - související znalosti
- **Použití:** GENT znalostní báze

---

## 🎯 **MCP Discovery Mapping**

### **Klíčová slova → MCP nástroje**

#### **Web & Search:**
- "vyhledej", "najdi na webu", "search" → `brave-search`, `tavily`
- "načti stránku", "fetch", "download" → `fetch`
- "dokumentace", "research" → `perplexity`

#### **Files & System:**
- "načti soubor", "read file" → `filesystem`
- "ulož soubor", "write file" → `filesystem`
- "seznam souborů", "list directory" → `filesystem`

#### **Memory & Knowledge:**
- "zapamatuj si", "remember", "store" → `mem0` (až bude)
- "co si pamatuješ", "recall", "retrieve" → `mem0`
- "znalostní báze", "knowledge" → `knowledge-base` (custom)

#### **Development:**
- "git status", "commit", "diff" → `git`
- "docker", "container" → `docker` (až bude)
- "databáze", "SQL" → `database` (až bude)

#### **Communication:**
- "pošli email", "send email" → `email` (až bude)
- "kalendář", "meeting" → `calendar` (až bude)

#### **Analytics:**
- "statistiky", "metriky", "analytics" → `analytics` (custom)
- "report", "přehled" → `analytics`

#### **Reasoning:**
- "mysli postupně", "krok za krokem" → `sequentialthinking`
- "analyzuj problém" → `sequentialthinking`

---

## 📈 **Implementační plán**

### **Fáze 1: Databáze a základní API (Týden 1-2)**
- Vytvoření databázových tabulek
- Migrace existujících 9 MCP serverů
- Základní CRUD API endpointy

### **Fáze 2: Mem0 integrace (Týden 3)**
- Instalace a konfigurace Mem0
- Přidání do databáze
- Testování memory funkcí

### **Fáze 3: Frontend GUI (Týden 4-5)**
- MCP Management interface
- Providers, Tools, Configurations tabs
- Testing interface

### **Fáze 4: Intelligence (Týden 6)**
- MCP Discovery engine
- Auto-recommendation
- Context-aware selection

### **Fáze 5: Další MCP servery (Týden 7-8)**
- Email, Weather, Calendar MCP
- Custom GENT MCP servery
- Performance monitoring

### **Fáze 6: Advanced features (Týden 9-10)**
- Security & permissions
- Load balancing
- Advanced analytics

---

## 🎯 **Očekávané výsledky**

### **Pro GENT AI:**
- **Inteligentní výběr nástrojů** podle kontextu
- **Automatické použití** vhodných MCP serverů
- **Dlouhodobá paměť** díky Mem0
- **Rozšířené schopnosti** (email, kalendář, počasí)

### **Pro uživatele:**
- **Jednoduchá správa** MCP serverů
- **Přehledné testování** nástrojů
- **Performance monitoring**
- **Bezpečnostní kontrola**

### **Pro vývojáře:**
- **Snadné přidávání** nových MCP serverů
- **API pro integraci**
- **Dokumentace a příklady**
- **Automated testing**

**Výsledek: GENT s 15+ MCP servery a inteligentním výběrem nástrojů!** 🚀
