# 🔧 SYSTEMD SERVICE PRO FRONTEND - Implementační plán

## 1. Vytvoření systemd service souboru

```bash
# Vytvoříme service soubor
sudo nano /etc/systemd/system/gent-frontend.service
```

**Obsah service souboru:**
```ini
[Unit]
Description=GENT Frontend Vue.js Application
After=network.target
Wants=gent-api.service

[Service]
Type=simple
User=root
Group=root
WorkingDirectory=/opt/gent/frontend-vue
Environment=NODE_ENV=production
Environment=VITE_API_URL=http://localhost:8001
ExecStartPre=/bin/bash -c 'source /opt/gent/venv/bin/activate && cd /opt/gent/frontend-vue && npm install'
ExecStart=/bin/bash -c 'source /opt/gent/venv/bin/activate && cd /opt/gent/frontend-vue && npm run dev'
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=gent-frontend

[Install]
WantedBy=multi-user.target
```

## 2. Aktivace a spuštění service

```bash
# Reload systemd daemon
sudo systemctl daemon-reload

# Povolit service při startu systému
sudo systemctl enable gent-frontend

# Spustit service
sudo systemctl start gent-frontend

# Zkontrolovat stav
sudo systemctl status gent-frontend

# Sledovat logy
sudo journalctl -u gent-frontend -f
```

## 3. Správa service

```bash
# Restart service (při změnách kódu)
sudo systemctl restart gent-frontend

# Stop service
sudo systemctl stop gent-frontend

# Disable service
sudo systemctl disable gent-frontend

# Zobrazit logy
sudo journalctl -u gent-frontend --since "1 hour ago"
```

## 4. Výhody systemd service

✅ **Automatický start** při restartu serveru
✅ **Automatický restart** při pádu aplikace  
✅ **Centralizované logování** přes journald
✅ **Dependency management** - čeká na síť a gent-api
✅ **Jednoduché ovládání** přes systemctl
✅ **Monitoring** stavu služby

## 5. Testování

```bash
# Test restartu systému
sudo reboot

# Po restartu zkontrolovat
sudo systemctl status gent-frontend
sudo systemctl status gent-api

# Test dostupnosti
curl http://localhost:8000
```
