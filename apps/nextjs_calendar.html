<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Next.js Calendar</title>
    <style>
        body { 
            font-family: sans-serif; 
            background-color: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .calendar { 
            width: 350px; 
            margin: 20px auto; 
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .calendar-header {
            background: #2563eb;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .calendar-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .nav-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .nav-btn:hover {
            background: rgba(255,255,255,0.3);
        }
        .calendar table { 
            width: 100%; 
            border-collapse: collapse; 
        }
        .calendar th, .calendar td { 
            border: 1px solid #e5e7eb; 
            padding: 8px; 
            text-align: center; 
            vertical-align: top;
            height: 40px;
        }
        .calendar th { 
            background-color: #f9fafb; 
            font-weight: 600;
            color: #374151;
        }
        .calendar td {
            position: relative;
            cursor: pointer;
        }
        .calendar td:hover {
            background-color: #f3f4f6;
        }
        .other-month {
            color: #9ca3af;
        }
        .today {
            background-color: #dbeafe;
            font-weight: bold;
            color: #1d4ed8;
        }
        .event { 
            background-color: #3b82f6; 
            color: white;
            padding: 2px 4px; 
            margin: 1px; 
            display: inline-block; 
            border-radius: 2px;
            font-size: 10px;
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #6b7280;
        }
        .error {
            color: #dc2626;
            text-align: center;
            padding: 10px;
            background: #fee2e2;
            margin: 10px;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="calendar">
        <div class="calendar-header">
            <div class="calendar-nav">
                <button class="nav-btn" onclick="changeMonth(-1)">‹ Prev</button>
                <h2 id="month-year">Loading...</h2>
                <button class="nav-btn" onclick="changeMonth(1)">Next ›</button>
            </div>
        </div>
        <table>
            <thead>
                <tr>
                    <th>Sun</th>
                    <th>Mon</th>
                    <th>Tue</th>
                    <th>Wed</th>
                    <th>Thu</th>
                    <th>Fri</th>
                    <th>Sat</th>
                </tr>
            </thead>
            <tbody id="calendar-body">
                <!-- Days will be inserted here -->
            </tbody>
        </table>
    </div>

    <script>
        // Global variables for current date
        let currentYear = new Date().getFullYear();
        let currentMonth = new Date().getMonth(); // 0-based (0 = January)
        let eventsData = {};

        const monthYearElement = document.getElementById('month-year');
        const calendarBodyElement = document.getElementById('calendar-body');
        const monthNames = [
            'January', 'February', 'March', 'April', 'May', 'June',
            'July', 'August', 'September', 'October', 'November', 'December'
        ];

        // OPRAVA: Simulace API pro demonstraci (v reálné aplikaci by volalo skutečné API)
        async function fetchEvents(year, month) {
            try {
                // Simulace API volání s mock daty
                await new Promise(resolve => setTimeout(resolve, 300)); // Simulace síťového zpoždění
                
                // Mock data pro demonstraci
                const mockEvents = {
                    [`${year}-${month}-15`]: ['Meeting', 'Lunch'],
                    [`${year}-${month}-22`]: ['Conference'],
                    [`${year}-${month}-8`]: ['Birthday'],
                    [`${year}-${month}-25`]: ['Holiday']
                };
                
                return mockEvents;
            } catch (error) {
                console.error('Error fetching events:', error);
                throw error;
            }
        }

        // OPRAVA: Kompletní implementace renderCalendar funkce
        function renderCalendar(year, month, events = {}) {
            // Update header
            monthYearElement.textContent = `${monthNames[month]} ${year}`;
            
            // Clear previous calendar
            calendarBodyElement.innerHTML = '';
            
            // Get first day of month and number of days
            const firstDay = new Date(year, month, 1);
            const lastDay = new Date(year, month + 1, 0);
            const daysInMonth = lastDay.getDate();
            const startingDayOfWeek = firstDay.getDay(); // 0 = Sunday
            
            // Get today's date for highlighting
            const today = new Date();
            const isCurrentMonth = today.getFullYear() === year && today.getMonth() === month;
            const todayDate = today.getDate();
            
            let date = 1;
            let nextMonthDate = 1;
            
            // Create 6 weeks (42 days total)
            for (let week = 0; week < 6; week++) {
                const row = document.createElement('tr');
                
                for (let day = 0; day < 7; day++) {
                    const cell = document.createElement('td');
                    let cellDate;
                    let isCurrentMonthDate = true;
                    
                    if (week === 0 && day < startingDayOfWeek) {
                        // Previous month dates
                        const prevMonth = month === 0 ? 11 : month - 1;
                        const prevYear = month === 0 ? year - 1 : year;
                        const daysInPrevMonth = new Date(prevYear, prevMonth + 1, 0).getDate();
                        cellDate = daysInPrevMonth - (startingDayOfWeek - day - 1);
                        cell.className = 'other-month';
                        isCurrentMonthDate = false;
                    } else if (date > daysInMonth) {
                        // Next month dates
                        cellDate = nextMonthDate++;
                        cell.className = 'other-month';
                        isCurrentMonthDate = false;
                    } else {
                        // Current month dates
                        cellDate = date++;
                        
                        // Highlight today
                        if (isCurrentMonth && cellDate === todayDate) {
                            cell.className = 'today';
                        }
                    }
                    
                    cell.innerHTML = `<div>${cellDate}</div>`;
                    
                    // Add events for current month dates
                    if (isCurrentMonthDate && events) {
                        const dateKey = `${year}-${month + 1}-${cellDate}`;
                        const dayEvents = events[dateKey];
                        
                        if (dayEvents && Array.isArray(dayEvents)) {
                            dayEvents.forEach(event => {
                                const eventDiv = document.createElement('div');
                                eventDiv.className = 'event';
                                eventDiv.textContent = event;
                                eventDiv.title = event; // Tooltip
                                cell.appendChild(eventDiv);
                            });
                        }
                    }
                    
                    row.appendChild(cell);
                }
                
                calendarBodyElement.appendChild(row);
                
                // Stop if we've filled all days and we're past the current month
                if (date > daysInMonth && week >= 4) break;
            }
        }

        // OPRAVA: Správné volání renderCalendar s events parametrem
        async function loadEvents() {
            try {
                calendarBodyElement.innerHTML = '<tr><td colspan="7" class="loading">Loading calendar...</td></tr>';
                
                const events = await fetchEvents(currentYear, currentMonth + 1); // API expects 1-based month
                eventsData = events;
                renderCalendar(currentYear, currentMonth, events);
            } catch (error) {
                console.error('Failed to load events:', error);
                calendarBodyElement.innerHTML = '<tr><td colspan="7" class="error">Failed to load calendar. Please try again.</td></tr>';
            }
        }

        // PŘIDÁNO: Navigace mezi měsíci
        function changeMonth(direction) {
            currentMonth += direction;
            
            if (currentMonth > 11) {
                currentMonth = 0;
                currentYear++;
            } else if (currentMonth < 0) {
                currentMonth = 11;
                currentYear--;
            }
            
            loadEvents();
        }

        // PŘIDÁNO: Keyboard navigation
        document.addEventListener('keydown', function(event) {
            if (event.key === 'ArrowLeft') {
                changeMonth(-1);
            } else if (event.key === 'ArrowRight') {
                changeMonth(1);
            }
        });

        // Initialize calendar
        loadEvents();
    </script>
</body>
</html>
