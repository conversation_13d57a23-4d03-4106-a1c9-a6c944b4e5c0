import time
import calendar
from datetime import datetime
import sys

# Globální instance kalendáře pro optimalizovaný přístup a testy spolehlivosti
# Vytvoření instance je drahá operace, proto ji optimalizovaný přístup předpokládá předvytvořenou.
GLOBAL_CALENDAR_INSTANCE = calendar.TextCalendar(firstweekday=6)

def benchmark_original_approach():
    """Benchmark původního přístupu."""
    start = time.perf_counter()
    
    # Simulace původního kódu, kde se TextCalendar vytváří uvnitř
    if True:  # Simulace len(sys.argv) == 3
        month = 6
        year = 2025
        
        # Validace
        if not (1 <= month <= 12):
            raise ValueError("Měsíc musí být mezi 1 a 12")
        if not (1 <= year <= 9999):
            raise ValueError("Rok musí být mezi 1 a 9999")
    else:
        current_month = datetime.now().month
        current_year = datetime.now().year
        month, year = current_month, current_year
    
    # Vyt<PERSON><PERSON><PERSON><PERSON> kalen<PERSON> (uv<PERSON><PERSON><PERSON>, simulace původního přístupu)
    cal = calendar.TextCalendar(firstweekday=6)
    result = cal.formatmonth(year, month)
    
    end = time.perf_counter()
    return end - start, len(result)

def benchmark_optimized_approach():
    """Benchmark optimalizovaného přístupu."""
    start = time.perf_counter()
    
    # Optimalizace: Jedna instance datetime (pokud by se používala)
    # Pro tento konkrétní benchmark s if True je toto menší faktor
    now = datetime.now() 
    
    if True:  # Simulace argumentů
        month = 6
        year = 2025
        
        # Rychlejší validace s and operátorem
        if not (1 <= month <= 12 and 1 <= year <= 9999):
            raise ValueError("Neplatné hodnoty")
    else:
        month, year = now.month, now.year
    
    # Pre-vytvořený kalendář (použití globální instance)
    result = GLOBAL_CALENDAR_INSTANCE.formatmonth(year, month)
    
    end = time.perf_counter()
    return end - start, len(result)

def reliability_test():
    """Test spolehlivosti edge cases."""
    test_cases = [
        (1, 1),      # Minimální hodnoty
        (12, 9999),  # Maximální hodnoty  
        (2, 2024),   # Přestupný rok
        (2, 2025),   # Nepřestupný rok
        (6, 2025),   # Normální případ
        (0, 2000),   # Neplatný měsíc (očekáváme chybu)
        (13, 2000),  # Neplatný měsíc (očekáváme chybu)
        (6, 0),      # Neplatný rok (očekáváme chybu)
        (6, 10000),  # Neplatný rok (očekáváme chybu)
    ]
    
    results = []
    
    for month, year in test_cases:
        try:
            start = time.perf_counter()
            # Použití globální instance pro testy spolehlivosti
            result = GLOBAL_CALENDAR_INSTANCE.formatmonth(year, month)
            end = time.perf_counter()
            
            results.append({
                'month': month,
                'year': year,
                'success': True,
                'time': end - start,
                'output_length': len(result)
            })
        except Exception as e:
            results.append({
                'month': month,
                'year': year,
                'success': False,
                'error': str(e),
                'time': 0 # Pro chyby nemá čas smysl
            })
    
    return results

def memory_usage_estimate():
    """Odhad spotřeby paměti."""
    # Měření objektů
    # Vytvoření dočasných instancí pro přesné měření jejich velikosti
    temp_cal = calendar.TextCalendar(firstweekday=6)
    temp_result = temp_cal.formatmonth(2025, 6)
    temp_datetime = datetime.now()
    
    return {
        'calendar_object': sys.getsizeof(temp_cal),
        'output_string': sys.getsizeof(temp_result),
        'datetime_object': sys.getsizeof(temp_datetime),
        'global_calendar_object': sys.getsizeof(GLOBAL_CALENDAR_INSTANCE) # Měření globální instance
    }

if __name__ == "__main__":
    print("🔬 PERFORMANCE ANALÝZA KALENDÁŘOVÉHO KÓDU")
    print("=" * 50)
    
    # Rychlostní benchmark
    print("\n🚀 RYCHLOSTNÍ TESTY:")
    
    original_times = []
    optimized_times = []
    
    # 1000 iterací pro přesnost
    NUM_ITERATIONS = 1000
    for _ in range(NUM_ITERATIONS):
        orig_time, _ = benchmark_original_approach()
        opt_time, _ = benchmark_optimized_approach()
        original_times.append(orig_time)
        optimized_times.append(opt_time)
    
    avg_original = sum(original_times) / len(original_times) * 1000
    avg_optimized = sum(optimized_times) / len(optimized_times) * 1000
    
    print(f"📊 Původní přístup ({NUM_ITERATIONS} iterací): {avg_original:.3f}ms")
    print(f"📊 Optimalizovaný přístup ({NUM_ITERATIONS} iterací): {avg_optimized:.3f}ms")
    if avg_optimized > 0:
        print(f"⚡ Zrychlení (původní/optimalizovaný): {(avg_original/avg_optimized):.2f}x")
    else:
        print("⚡ Optimalizovaný přístup je extrémně rychlý, nelze vypočítat zrychlení.")
    
    # Test spolehlivosti
    print("\n🛡️  TESTY SPOLEHLIVOSTI:")
    reliability_results = reliability_test()
    
    success_count = sum(1 for r in reliability_results if r['success'])
    print(f"✅ Úspěšných testů: {success_count}/{len(reliability_results)}")
    
    for result in reliability_results:
        if result['success']:
            print(f"  ✅ {result['month']:2d}/{result['year']} - {result['time']*1000:.2f}ms")
        else:
            print(f"  ❌ {result['month']:2d}/{result['year']} - CHYBA: {result['error']}")
    
    # Spotřeba paměti
    print("\n💾 SPOTŘEBA PAMĚTI:")
    memory = memory_usage_estimate()
    for key, value in memory.items():
        print(f"  📦 {key}: {value} bytes")
    
    total_memory = sum(memory.values())
    print(f"  🔢 Celkově odhadovaná paměť pro zobrazené objekty: {total_memory} bytes ({total_memory/1024:.1f} KB)")
