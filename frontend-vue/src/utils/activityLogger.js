/**
 * GENT v10 - <PERSON>k<PERSON> zaznamenávání uživatelské aktivity
 * 
 * Tento modul automaticky zaznamenává všechny uživatelské akce
 * jako jsou klik<PERSON>, navigace, API volání a chyby do databáze.
 */

class ActivityLogger {
  constructor() {
    this.sessionId = this.generateSessionId();
    this.currentPage = window.location.pathname;
    this.pageStartTime = Date.now();
    this.isEnabled = true;
    this.apiEndpoint = '/api/activity/';
    
    // Inicializace automatického trackingu
    this.initializeTracking();
  }

  /**
   * Generuje jedinečné session ID
   */
  generateSessionId() {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Inicializuje automatické sledování událostí
   */
  initializeTracking() {
    if (!this.isEnabled) return;

    // Sledování klikání na všechny elementy
    document.addEventListener('click', (event) => {
      this.logClickEvent(event);
    });

    // Sledování změn stránky (Vue Router)
    this.setupRouterTracking();

    // Sledování opuštění stránky
    window.addEventListener('beforeunload', () => {
      this.logPageLeave();
    });

    // Sledování chyb
    window.addEventListener('error', (event) => {
      this.logError(event);
    });

    // Sledování unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.logError(event, 'promise_rejection');
    });

    // Zaznamenání načtení stránky
    this.logActivity('navigate', null, this.currentPage, 'Page loaded');
  }

  /**
   * Nastavení sledování Vue Router navigace
   */
  setupRouterTracking() {
    // Pokud je dostupný Vue Router, sledujeme změny route
    if (window.Vue && window.Vue.config && window.Vue.config.globalProperties) {
      const router = window.Vue.config.globalProperties.$router;
      if (router) {
        router.afterEach((to, from) => {
          this.logNavigation(to.path, from.path);
        });
      }
    }

    // Fallback - sledování změn URL
    let lastUrl = location.href;
    new MutationObserver(() => {
      const url = location.href;
      if (url !== lastUrl) {
        this.logNavigation(location.pathname, this.currentPage);
        lastUrl = url;
      }
    }).observe(document, { subtree: true, childList: true });
  }

  /**
   * Zaznamenává kliknutí na element
   */
  logClickEvent(event) {
    const element = event.target;
    const elementInfo = this.getElementInfo(element);
    
    this.logActivity(
      'click',
      elementInfo.selector,
      this.currentPage,
      `Clicked on ${elementInfo.description}`,
      {
        element_type: element.tagName.toLowerCase(),
        element_text: elementInfo.text,
        element_classes: elementInfo.classes,
        coordinates: { x: event.clientX, y: event.clientY }
      }
    );
  }

  /**
   * Získává informace o elementu
   */
  getElementInfo(element) {
    const text = element.textContent?.trim().substring(0, 50) || '';
    const classes = Array.from(element.classList).join(' ');
    const id = element.id;
    const tagName = element.tagName.toLowerCase();
    
    // Vytvoření CSS selektoru
    let selector = tagName;
    if (id) {
      selector += `#${id}`;
    } else if (classes) {
      selector += `.${classes.split(' ').join('.')}`;
    }
    
    // Popis elementu
    let description = tagName;
    if (text) {
      description += ` "${text}"`;
    } else if (id) {
      description += ` #${id}`;
    } else if (classes) {
      description += ` .${classes.split(' ')[0]}`;
    }
    
    return {
      selector,
      description,
      text,
      classes,
      id
    };
  }

  /**
   * Zaznamenává navigaci mezi stránkami
   */
  logNavigation(newPage, oldPage) {
    // Zaznamenání opuštění předchozí stránky
    if (oldPage && oldPage !== newPage) {
      const timeSpent = Date.now() - this.pageStartTime;
      this.logActivity(
        'navigate',
        null,
        oldPage,
        `Left page after ${Math.round(timeSpent / 1000)}s`,
        { time_spent_ms: timeSpent, destination: newPage }
      );
    }

    // Zaznamenání příchodu na novou stránku
    this.currentPage = newPage;
    this.pageStartTime = Date.now();
    
    this.logActivity(
      'navigate',
      null,
      newPage,
      'Navigated to page',
      { previous_page: oldPage }
    );
  }

  /**
   * Zaznamenává opuštění stránky
   */
  logPageLeave() {
    const timeSpent = Date.now() - this.pageStartTime;
    this.logActivity(
      'navigate',
      null,
      this.currentPage,
      `Left page after ${Math.round(timeSpent / 1000)}s`,
      { time_spent_ms: timeSpent, action: 'page_leave' }
    );
  }

  /**
   * Zaznamenává chyby
   */
  logError(event, type = 'javascript_error') {
    let errorInfo = {};
    
    if (event.error) {
      errorInfo = {
        message: event.error.message,
        stack: event.error.stack,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      };
    } else if (event.reason) {
      errorInfo = {
        message: event.reason.toString(),
        type: 'promise_rejection'
      };
    }
    
    this.logActivity(
      'error',
      null,
      this.currentPage,
      `${type}: ${errorInfo.message || 'Unknown error'}`,
      errorInfo
    );
  }

  /**
   * Zaznamenává API volání
   */
  logApiCall(method, url, status, responseTime, error = null) {
    const eventType = error ? 'error' : 'success';
    const action = error 
      ? `API ${method} ${url} failed (${status})`
      : `API ${method} ${url} success (${status})`;
    
    this.logActivity(
      eventType,
      null,
      this.currentPage,
      action,
      {
        api_method: method,
        api_url: url,
        status_code: status,
        response_time_ms: responseTime,
        error: error
      }
    );
  }

  /**
   * Zaznamenává úspěšné akce
   */
  logSuccess(action, details = {}) {
    this.logActivity(
      'success',
      null,
      this.currentPage,
      action,
      details
    );
  }

  /**
   * Hlavní metoda pro zaznamenání aktivity
   */
  async logActivity(eventType, element, page, action, extraData = {}) {
    if (!this.isEnabled) return;

    try {
      const activityData = {
        event_type: eventType,
        element: element,
        page: page,
        action: action,
        session_id: this.sessionId,
        extra_data: {
          timestamp: new Date().toISOString(),
          user_agent: navigator.userAgent,
          screen_resolution: `${screen.width}x${screen.height}`,
          viewport_size: `${window.innerWidth}x${window.innerHeight}`,
          ...extraData
        }
      };

      // Odeslání na API endpoint
      const response = await fetch(this.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(activityData)
      });

      if (!response.ok) {
        console.warn('Failed to log activity:', response.status);
      }
    } catch (error) {
      console.warn('Error logging activity:', error);
    }
  }

  /**
   * Povolí/zakáže zaznamenávání aktivity
   */
  setEnabled(enabled) {
    this.isEnabled = enabled;
  }

  /**
   * Získá statistiky aktuální session
   */
  getSessionStats() {
    return {
      sessionId: this.sessionId,
      currentPage: this.currentPage,
      timeOnCurrentPage: Date.now() - this.pageStartTime,
      isEnabled: this.isEnabled
    };
  }
}

// Vytvoření globální instance
const activityLogger = new ActivityLogger();

// Export pro použití v komponentách
export default activityLogger;

// Globální dostupnost
window.activityLogger = activityLogger;
