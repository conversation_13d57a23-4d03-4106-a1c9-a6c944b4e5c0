/**
 * <PERSON><PERSON><PERSON> pro stránku CHAT-TEST
 * <PERSON><PERSON><PERSON><PERSON> re<PERSON> pod<PERSON> p<PERSON>vků projektu
 */

.chat-test-container {
  display: flex;
  flex-direction: row;
  height: 80vh;
  background-color: #1e1e2d;
  color: #e4e6ef;
  border-radius: 8px;
  overflow: hidden;
  margin: 1rem;
  gap: 1rem;
}

.chat-main-section {
  flex: 2;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* <PERSON>kce pro výběr poskytovatele a modelu */
.provider-model-section {
  display: flex;
  flex-direction: column;
  padding: 1rem;
  background-color: #151521;
  border-bottom: 1px solid #2b2b40;
}

.selector-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.selector-container {
  flex: 1;
}

.selector-label {
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #9899ac;
}

.provider-select,
.model-select {
  width: 100%;
  padding: 0.75rem;
  border-radius: 4px;
  border: 1px solid #323248;
  background-color: #1e1e2d;
  color: #e4e6ef;
  font-size: 1rem;
  outline: none;
}

/* Sekce chatu */
.chat-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.chat-messages {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  background-color: #1a1a27;
  display: flex;
  flex-direction: column;
}

.chat-empty {
  margin: auto;
  text-align: center;
  color: #9899ac;
}

.chat-empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

/* Zprávy */
.message {
  display: flex;
  margin-bottom: 1rem;
  max-width: 85%;
}

.message.user {
  margin-left: auto;
  flex-direction: row-reverse;
}

.message.assistant {
  margin-right: auto;
}

.message-bubble {
  padding: 0.75rem 1rem;
  border-radius: 12px;
  position: relative;
}

.message.user .message-bubble {
  background-color: #3699ff;
  color: #ffffff;
  border-top-right-radius: 2px;
}

.message.assistant .message-bubble {
  background-color: #323248;
  color: #e4e6ef;
  border-top-left-radius: 2px;
}

.message-time {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 0.25rem;
  text-align: right;
}

/* Indikátor psaní */
.typing-indicator {
  display: flex;
  padding: 0.5rem 1rem;
  background-color: #323248;
  border-radius: 12px;
  margin-left: 0.5rem;
}

.typing-dot {
  width: 8px;
  height: 8px;
  margin: 0 2px;
  background-color: #9899ac;
  border-radius: 50%;
  animation: bounce 1.3s linear infinite;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes bounce {
  0%, 60%, 100% { transform: translateY(0); }
  30% { transform: translateY(-4px); }
}

/* Vstupní pole */
.message-input-container {
  display: flex;
  padding: 1rem;
  background-color: #151521;
  border-top: 1px solid #2b2b40;
}

.message-input {
  flex: 1;
  padding: 0.75rem;
  border-radius: 4px;
  border: 1px solid #323248;
  background-color: #1e1e2d;
  color: #e4e6ef;
  font-size: 1rem;
  resize: none;
  outline: none;
  height: 40px;
  min-height: 40px;
}

.send-button {
  margin-left: 0.5rem;
  width: 40px;
  height: 40px;
  border-radius: 4px;
  border: none;
  background-color: #3699ff;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.send-button:disabled {
  background-color: #3f4254;
  cursor: not-allowed;
}

/* Akční tlačítka */
.action-buttons {
  display: flex;
  padding: 1rem;
  gap: 0.5rem;
  background-color: #151521;
  border-top: 1px solid #2b2b40;
}

.btn-action {
  flex: 1;
  padding: 0.75rem;
  border-radius: 4px;
  border: none;
  font-weight: 600;
  cursor: pointer;
}

.btn-clear {
  background-color: #3f4254;
  color: #ffffff;
}

.btn-test {
  background-color: #3699ff;
  color: #ffffff;
}

.btn-clear:disabled,
.btn-test:disabled {
  background-color: #2b2b40;
  cursor: not-allowed;
  opacity: 0.7;
}

/* Chybová zpráva */
.error-message {
  padding: 0.75rem;
  margin: 0.5rem 1rem;
  background-color: rgba(246, 78, 96, 0.1);
  border-left: 3px solid #f64e60;
  color: #f64e60;
  border-radius: 4px;
}

/* Stavy načítání */
.loading-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.25rem;
  color: #9899ac;
  font-size: 0.9rem;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #3699ff;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-models-message {
  margin-top: 0.5rem;
  font-size: 0.85rem;
  color: #f64e60;
}

/* Monitoring Panel */
.monitoring-panel {
  flex: 1;
  background-color: #151521;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.monitoring-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: #1e1e2d;
  border-bottom: 1px solid #2b2b40;
}

.monitoring-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: #e4e6ef;
}

.toggle-monitoring {
  background: none;
  border: none;
  color: #9899ac;
  cursor: pointer;
  font-size: 1.2rem;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.toggle-monitoring:hover {
  background-color: #323248;
}

.monitoring-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

/* Stats Section */
.stats-section {
  margin-bottom: 1.5rem;
}

.stats-section h4 {
  margin: 0 0 0.75rem 0;
  font-size: 0.9rem;
  color: #9899ac;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
}

.stat-item {
  background-color: #1e1e2d;
  padding: 0.75rem;
  border-radius: 6px;
  border: 1px solid #2b2b40;
}

.stat-label {
  font-size: 0.8rem;
  color: #9899ac;
  margin-bottom: 0.25rem;
}

.stat-value {
  font-size: 1.1rem;
  font-weight: 600;
  color: #e4e6ef;
}

/* Metrics Section */
.metrics-section {
  margin-bottom: 1.5rem;
}

.metrics-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.5rem;
}

.metric-item {
  display: flex;
  align-items: center;
  background-color: #1e1e2d;
  padding: 0.75rem;
  border-radius: 6px;
  border: 1px solid #2b2b40;
  transition: border-color 0.2s;
}

.metric-item.fast {
  border-color: #1bc5bd;
}

.metric-item.medium {
  border-color: #ffa800;
}

.metric-item.slow {
  border-color: #f64e60;
}

.metric-icon {
  font-size: 1.2rem;
  margin-right: 0.75rem;
}

.metric-content {
  flex: 1;
}

.metric-label {
  font-size: 0.8rem;
  color: #9899ac;
  margin-bottom: 0.25rem;
}

.metric-value {
  font-size: 1rem;
  font-weight: 600;
  color: #e4e6ef;
}

/* Chart Section */
.chart-section {
  margin-bottom: 1.5rem;
}

.response-time-chart {
  display: flex;
  align-items: end;
  height: 60px;
  background-color: #1e1e2d;
  border-radius: 6px;
  padding: 0.5rem;
  gap: 2px;
  margin-bottom: 0.5rem;
}

.chart-bar {
  flex: 1;
  background-color: #3699ff;
  border-radius: 2px 2px 0 0;
  min-height: 2px;
  transition: height 0.3s ease;
}

.chart-labels {
  text-align: center;
  font-size: 0.8rem;
  color: #9899ac;
}

/* Performance Section */
.performance-section {
  margin-bottom: 1.5rem;
}

.performance-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.performance-item {
  background-color: #1e1e2d;
  padding: 0.75rem;
  border-radius: 6px;
  border: 1px solid #2b2b40;
}

.performance-model {
  font-weight: 600;
  color: #e4e6ef;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
}

.performance-stats {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.performance-stat {
  font-size: 0.8rem;
  color: #9899ac;
  background-color: #323248;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

/* Health Section */
.health-section {
  margin-bottom: 1rem;
}

.health-indicators {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.health-item {
  display: flex;
  align-items: center;
  background-color: #1e1e2d;
  padding: 0.75rem;
  border-radius: 6px;
  border: 1px solid #2b2b40;
}

.health-item.healthy {
  border-color: #1bc5bd;
}

.health-icon {
  font-size: 1rem;
  margin-right: 0.75rem;
}

.health-label {
  flex: 1;
  font-size: 0.9rem;
  color: #9899ac;
}

.health-value {
  font-size: 0.9rem;
  font-weight: 600;
  color: #e4e6ef;
}

/* Responzivní design */
@media (max-width: 1200px) {
  .chat-test-container {
    flex-direction: column;
    height: auto;
  }

  .monitoring-panel {
    flex: none;
    max-height: 400px;
  }

  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .selector-row {
    flex-direction: column;
    gap: 0.5rem;
  }

  .message {
    max-width: 95%;
  }

  .stats-grid {
    grid-template-columns: 1fr 1fr;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .performance-stats {
    flex-direction: column;
    gap: 0.25rem;
  }
}
