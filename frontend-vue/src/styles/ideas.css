/* Ideas Page Styles */
.ideas-container {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  color: var(--text-color, #e4e6ef);
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, #1e1e2d 0%, #2a2a3e 100%);
  border-radius: 16px;
  padding: 3rem;
  margin-bottom: 3rem;
  text-align: center;
}

.hero-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #e4e6ef;
}

.hero-icon {
  font-size: 3rem;
  display: block;
  margin-bottom: 1rem;
}

.hero-subtitle {
  font-size: 1.2rem;
  color: #9899ac;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Quick Input */
.quick-input-section {
  max-width: 800px;
  margin: 0 auto;
}

.input-container {
  background: #151521;
  border-radius: 12px;
  padding: 1.5rem;
  border: 2px solid #2b2b40;
  transition: border-color 0.3s;
}

.input-container:focus-within {
  border-color: #3699ff;
}

.idea-input {
  width: 100%;
  background: transparent;
  border: none;
  color: #e4e6ef;
  font-size: 1.1rem;
  line-height: 1.6;
  resize: vertical;
  min-height: 120px;
  outline: none;
}

.idea-input::placeholder {
  color: #9899ac;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
  gap: 1rem;
}

.submit-btn {
  background: linear-gradient(135deg, #3699ff 0%, #1bc5bd 100%);
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 1rem;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(54, 153, 255, 0.3);
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.template-btn {
  background: #323248;
  color: #e4e6ef;
  border: 1px solid #2b2b40;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.template-btn:hover {
  background: #3a3a52;
  border-color: #3699ff;
}

/* Templates Section */
.templates-section {
  margin-bottom: 3rem;
}

.templates-section h3 {
  margin-bottom: 1.5rem;
  color: #e4e6ef;
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.template-card {
  background: #1e1e2d;
  border: 1px solid #2b2b40;
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.template-card:hover {
  border-color: #3699ff;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.template-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.template-content h4 {
  margin: 0 0 0.5rem 0;
  color: #e4e6ef;
}

.template-content p {
  margin: 0;
  color: #9899ac;
  font-size: 0.9rem;
}

/* Section Headers */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.section-header h2 {
  margin: 0;
  color: #e4e6ef;
}

.section-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.refresh-btn {
  background: #323248;
  color: #e4e6ef;
  border: 1px solid #2b2b40;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.refresh-btn:hover {
  background: #3a3a52;
  border-color: #3699ff;
}

.filter-select {
  background: #323248;
  color: #e4e6ef;
  border: 1px solid #2b2b40;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
}

/* Projects Grid */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.project-card {
  background: #1e1e2d;
  border: 1px solid #2b2b40;
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s;
}

.project-card:hover {
  border-color: #3699ff;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.project-status {
  font-size: 1.2rem;
}

.project-meta {
  font-size: 0.8rem;
  color: #9899ac;
}

.project-content h3 {
  margin: 0 0 0.5rem 0;
  color: #e4e6ef;
}

.project-content p {
  margin: 0 0 1rem 0;
  color: #9899ac;
  line-height: 1.5;
}

.project-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.project-progress {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex: 1;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: #323248;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3699ff 0%, #1bc5bd 100%);
  transition: width 0.3s;
}

.progress-text {
  font-size: 0.8rem;
  color: #9899ac;
  min-width: 35px;
}

.team-count {
  font-size: 0.8rem;
  color: #9899ac;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem;
  color: #9899ac;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.empty-state h3 {
  margin-bottom: 0.5rem;
  color: #e4e6ef;
}

/* History List */
.history-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 3rem;
}

.history-item {
  background: #1e1e2d;
  border: 1px solid #2b2b40;
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.history-item:hover {
  border-color: #3699ff;
  background: #252538;
}

.history-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.history-content {
  flex: 1;
}

.history-content h4 {
  margin: 0 0 0.25rem 0;
  color: #e4e6ef;
}

.history-content p {
  margin: 0 0 0.5rem 0;
  color: #9899ac;
  font-size: 0.9rem;
}

.history-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.8rem;
  color: #9899ac;
}

/* Stats Section */
.stats-section {
  margin-bottom: 2rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.stat-card {
  background: #1e1e2d;
  border: 1px solid #2b2b40;
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.8rem;
  font-weight: 700;
  color: #e4e6ef;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.9rem;
  color: #9899ac;
}

/* Responsive Design */
@media (max-width: 768px) {
  .ideas-container {
    padding: 1rem;
  }
  
  .hero-section {
    padding: 2rem;
  }
  
  .hero-title {
    font-size: 2rem;
  }
  
  .input-actions {
    flex-direction: column;
    align-items: stretch;
  }
  
  .templates-grid {
    grid-template-columns: 1fr;
  }
  
  .projects-grid {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .section-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
}
