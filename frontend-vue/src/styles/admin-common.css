/* Admin Common Styles */
.admin-header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #2b2b40;
}

.admin-header h1 {
  margin: 0 0 0.5rem 0;
  color: #e4e6ef;
  font-size: 2rem;
}

.admin-header p {
  margin: 0;
  color: #9899ac;
  font-size: 1.1rem;
}

/* Tab Navigation */
.tab-navigation {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid #2b2b40;
}

.tab-btn {
  background: transparent;
  border: none;
  color: #9899ac;
  padding: 1rem 1.5rem;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.3s;
  font-size: 1rem;
}

.tab-btn:hover {
  color: #e4e6ef;
  background: #252538;
}

.tab-btn.active {
  color: #3699ff;
  border-bottom-color: #3699ff;
  background: #252538;
}

/* Tab Content */
.tab-content {
  min-height: 400px;
}

/* Section Headers */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.section-header h2 {
  margin: 0;
  color: #e4e6ef;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.refresh-btn, .test-btn, .export-btn {
  background: #323248;
  color: #e4e6ef;
  border: 1px solid #2b2b40;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
  font-size: 0.9rem;
}

.refresh-btn:hover {
  background: #3a3a52;
  border-color: #3699ff;
}

.test-btn {
  background: #ffa800;
  border-color: #ffa800;
}

.test-btn:hover:not(:disabled) {
  background: #e6950e;
}

.test-btn:disabled {
  background: #4a4a5a;
  border-color: #4a4a5a;
  cursor: not-allowed;
  opacity: 0.6;
}

.export-btn {
  background: #8950fc;
  border-color: #8950fc;
}

.export-btn:hover {
  background: #7239ea;
}

/* Providers Grid */
.providers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.provider-card {
  background: #1e1e2d;
  border: 1px solid #2b2b40;
  border-radius: 8px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s;
}

.provider-card:hover {
  border-color: #3699ff;
  transform: translateY(-2px);
}

.provider-card.selected {
  border-color: #3699ff;
  background: #252538;
}

.provider-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.provider-header h3 {
  margin: 0;
  color: #e4e6ef;
}

.provider-status {
  font-size: 1.2rem;
}

.provider-info p {
  margin: 0 0 1rem 0;
  color: #9899ac;
  line-height: 1.5;
}

.provider-stats {
  display: flex;
  gap: 1rem;
  font-size: 0.9rem;
  color: #9899ac;
  margin-bottom: 1rem;
}

.provider-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.test-provider-btn, .edit-provider-btn {
  background: #3699ff;
  color: white;
  border: none;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s;
}

.test-provider-btn:hover:not(:disabled) {
  background: #2d7dd2;
}

.test-provider-btn:disabled {
  background: #4a4a5a;
  cursor: not-allowed;
  opacity: 0.6;
}

.edit-provider-btn {
  background: #6c757d;
}

.edit-provider-btn:hover {
  background: #5a6268;
}

/* Provider Details */
.provider-details {
  background: #1e1e2d;
  border: 1px solid #2b2b40;
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 2rem;
}

.provider-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
}

.provider-detail-header h3 {
  margin: 0;
  color: #e4e6ef;
}

.provider-detail-stats {
  display: flex;
  gap: 1.5rem;
}

.stat-item {
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 0.8rem;
  color: #9899ac;
  margin-bottom: 0.25rem;
}

.stat-value {
  display: block;
  font-size: 1.1rem;
  font-weight: 600;
  color: #e4e6ef;
}

/* Models Grid */
.models-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
}

.model-card {
  background: #151521;
  border: 1px solid #2b2b40;
  border-radius: 6px;
  padding: 1rem;
  transition: all 0.3s;
}

.model-card:hover {
  background: #1a1a2e;
  border-color: #3699ff;
}

.model-card.model-default {
  border-color: #ffa800;
  background: #2a2416;
}

.model-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.model-header h4 {
  margin: 0;
  color: #e4e6ef;
  font-size: 0.9rem;
}

.model-badges {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.badge {
  padding: 0.125rem 0.375rem;
  border-radius: 3px;
  font-size: 0.7rem;
  font-weight: 600;
}

.badge.default {
  background: #ffa800;
  color: #1e1e2d;
}

.model-status {
  font-size: 1rem;
}

.model-specs {
  display: flex;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: #9899ac;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
}

.model-specs span {
  background: #323248;
  padding: 0.125rem 0.375rem;
  border-radius: 3px;
  border: 1px solid #2b2b40;
}

.model-capabilities {
  display: flex;
  gap: 0.25rem;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
}

.capability-tag {
  background: #3699ff;
  color: white;
  padding: 0.125rem 0.375rem;
  border-radius: 3px;
  font-size: 0.7rem;
}

.model-description {
  margin: 0;
  color: #9899ac;
  font-size: 0.8rem;
  line-height: 1.4;
}

/* Testing Section */
.test-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.model-selector {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.model-selector label {
  color: #e4e6ef;
  font-weight: 500;
}

.model-select {
  background: #323248;
  color: #e4e6ef;
  border: 1px solid #2b2b40;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  min-width: 250px;
}

/* Chat Interface */
.chat-interface {
  background: #1e1e2d;
  border-radius: 8px;
  overflow: hidden;
  height: 500px;
  display: flex;
  flex-direction: column;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.message {
  margin-bottom: 1rem;
}

.message.user .message-content {
  background: #3699ff;
  color: white;
  margin-left: 20%;
}

.message.assistant .message-content {
  background: #323248;
  color: #e4e6ef;
  margin-right: 20%;
}

.message-content {
  padding: 1rem;
  border-radius: 8px;
}

.message-text {
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.message-meta {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  opacity: 0.7;
}

.chat-input {
  border-top: 1px solid #2b2b40;
  padding: 1rem;
  display: flex;
  gap: 1rem;
}

.chat-input textarea {
  flex: 1;
  background: #323248;
  border: 1px solid #2b2b40;
  color: #e4e6ef;
  padding: 0.75rem;
  border-radius: 6px;
  resize: none;
  outline: none;
}

.send-btn {
  background: #3699ff;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.send-btn:hover:not(:disabled) {
  background: #2d7dd2;
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Performance Section */
.time-range-selector {
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.time-range-selector label {
  color: #e4e6ef;
  font-weight: 500;
}

.time-range-selector select {
  background: #323248;
  color: #e4e6ef;
  border: 1px solid #2b2b40;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
}

.performance-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.metric-card {
  background: #1e1e2d;
  border: 1px solid #2b2b40;
  border-radius: 8px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  position: relative;
}

.metric-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #e4e6ef;
  margin-bottom: 0.25rem;
}

.metric-label {
  font-size: 0.9rem;
  color: #9899ac;
}

.metric-trend {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  font-size: 0.8rem;
}

.metric-trend.up {
  color: #1bc5bd;
}

.metric-trend.down {
  color: #f64e60;
}

.metric-trend.stable {
  color: #9899ac;
}

/* Model Comparison Table */
.comparison-table {
  background: #1e1e2d;
  border-radius: 8px;
  overflow: hidden;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1fr;
  gap: 1rem;
  padding: 1rem;
  background: #323248;
  font-weight: 600;
  color: #e4e6ef;
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr 1fr;
  gap: 1rem;
  padding: 1rem;
  border-bottom: 1px solid #2b2b40;
  align-items: center;
  transition: all 0.3s;
}

.table-row:hover {
  background: #252538;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row.top-performer {
  border-left: 3px solid #1bc5bd;
  background: #1a2e2a;
}

.model-name {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.model-provider {
  font-size: 0.8rem;
  color: #9899ac;
}

.model-title {
  color: #e4e6ef;
  font-weight: 500;
}

.response-time.fast {
  color: #1bc5bd;
  font-weight: 600;
}

.response-time.medium {
  color: #ffa800;
  font-weight: 600;
}

.response-time.slow {
  color: #f64e60;
  font-weight: 600;
}

.success-rate.excellent {
  color: #1bc5bd;
  font-weight: 600;
}

.success-rate.good {
  color: #3699ff;
}

.success-rate.fair {
  color: #ffa800;
}

.success-rate.poor {
  color: #f64e60;
}

.cost,
.usage,
.last-used {
  color: #9899ac;
}

/* Performance Charts */
.performance-charts {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.chart-container {
  background: #1e1e2d;
  border: 1px solid #2b2b40;
  border-radius: 8px;
  padding: 1.5rem;
}

.chart-container h4 {
  margin: 0 0 1rem 0;
  color: #e4e6ef;
}

.chart-placeholder {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #151521;
  border-radius: 6px;
  color: #9899ac;
  font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .tab-navigation {
    flex-wrap: wrap;
  }

  .section-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .header-actions {
    justify-content: center;
  }

  .provider-detail-header {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .provider-detail-stats {
    justify-content: space-around;
  }

  .test-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .model-selector {
    flex-direction: column;
    align-items: stretch;
  }

  .chat-input {
    flex-direction: column;
  }

  .time-range-selector {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }

  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .performance-overview {
    grid-template-columns: repeat(2, 1fr);
  }

  .performance-charts {
    grid-template-columns: 1fr;
  }

  .providers-grid {
    grid-template-columns: 1fr;
  }

  .models-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .performance-overview {
    grid-template-columns: 1fr;
  }

  .provider-detail-stats {
    flex-direction: column;
    gap: 1rem;
  }

  .provider-stats {
    flex-direction: column;
    gap: 0.5rem;
  }

  .model-specs {
    flex-direction: column;
    gap: 0.25rem;
  }

  .model-capabilities {
    justify-content: flex-start;
  }
}

/* === Management Tab Styles === */

/* Sub-tab navigation */
.sub-tab-navigation {
  display: flex;
  gap: 10px;
  margin: 20px 0;
  border-bottom: 2px solid #2b2b40;
  padding-bottom: 10px;
}

.sub-tab-btn {
  padding: 8px 16px;
  border: none;
  background: #323248;
  color: #9899ac;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.sub-tab-btn:hover {
  background: #2b2b40;
  color: #e4e6ef;
}

.sub-tab-btn.active {
  background: #3699ff;
  color: white;
}

/* Management tables */
.management-table {
  background: #1e1e2d;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #2b2b40;
  margin-top: 20px;
}

.management-table .table-header {
  grid-template-columns: 60px 1fr 1fr 100px 80px 80px 120px;
  background: #323248;
  padding: 15px;
  font-weight: 600;
  color: #e4e6ef;
  border-bottom: 2px solid #2b2b40;
}

.models-management .management-table .table-header {
  grid-template-columns: 80px 1fr 120px 100px 100px 80px 80px 120px;
}

.management-table .table-row {
  grid-template-columns: 60px 1fr 1fr 100px 80px 80px 120px;
  padding: 15px;
  border-bottom: 1px solid #2b2b40;
  align-items: center;
  transition: background-color 0.2s ease;
  color: #e4e6ef;
}

.models-management .management-table .table-row {
  grid-template-columns: 80px 1fr 120px 100px 100px 80px 80px 120px;
}

.management-table .table-row:hover {
  background: #252538;
}

.management-table .table-row:last-child {
  border-bottom: none;
}

/* Table cell styles */
.url-cell {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #9899ac;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.actions-cell {
  display: flex;
  gap: 5px;
  justify-content: flex-end;
}

.actions-cell button {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.edit-btn {
  background: #ffa800;
  color: #1e1e2d;
}

.edit-btn:hover {
  background: #e0a800;
}

.delete-btn {
  background: #f64e60;
  color: white;
}

.delete-btn:hover {
  background: #e63946;
}

.test-btn {
  background: #1bc5bd;
  color: white;
}

.test-btn:hover {
  background: #17a2b8;
}

.star-btn {
  background: #ffa800;
  color: #1e1e2d;
}

.star-btn:hover {
  background: #e0a800;
}

/* Status indicators */
.status-active {
  color: #1bc5bd;
  font-weight: bold;
}

.status-inactive {
  color: #f64e60;
  font-weight: bold;
}

.status-default {
  color: #ffa800;
  font-weight: bold;
}

/* Create buttons */
.create-btn {
  background: #1bc5bd;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  margin-left: 10px;
}

.create-btn:hover {
  background: #17a2b8;
  transform: translateY(-1px);
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: #1e1e2d;
  border-radius: 12px;
  padding: 0;
  max-width: 600px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  border: 1px solid #2b2b40;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #2b2b40;
  background: #323248;
  border-radius: 12px 12px 0 0;
}

.modal-header h3 {
  margin: 0;
  color: #e4e6ef;
  font-size: 18px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #9899ac;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #2b2b40;
  color: #e4e6ef;
}

/* Form styles */
.provider-form,
.model-form {
  padding: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #e4e6ef;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #2b2b40;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s ease;
  background: #323248;
  color: #e4e6ef;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #3699ff;
  box-shadow: 0 0 0 2px rgba(54, 153, 255, 0.25);
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.checkbox-group label {
  margin-bottom: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  color: #e4e6ef;
}

.checkbox-group input[type="checkbox"] {
  width: auto;
  margin: 0;
}

/* Capabilities grid */
.capabilities-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  margin-top: 10px;
}

.capability-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: #323248;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  color: #e4e6ef;
}

.capability-item:hover {
  background: #2b2b40;
}

.capability-item input[type="checkbox"] {
  width: auto;
  margin: 0;
}

/* Form actions */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding-top: 20px;
  border-top: 1px solid #2b2b40;
  margin-top: 20px;
}

.cancel-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.cancel-btn:hover {
  background: #5a6268;
}

.save-btn {
  background: #3699ff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.save-btn:hover:not(:disabled) {
  background: #2d7dd2;
}

.save-btn:disabled {
  background: #6c757d;
  cursor: not-allowed;
}
