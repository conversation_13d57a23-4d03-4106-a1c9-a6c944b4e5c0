/* Styly pro stránku AI LLM Management */

:root {
  --background-primary: #121212;
  --background-secondary: #1e1e1e;
  --background-tertiary: #252525;
  --text-primary: #e1e1e1;
  --text-secondary: #b0b0b0;
  --text-muted: #6c757d;
  --primary-color: #2563eb;
  --primary-hover: #1d4ed8;
  --secondary-color: #4b5563;
  --secondary-hover: #374151;
  --success-color: #059669;
  --error-color: #dc2626;
  --border-color: #333;
  --form-bg: #2d2d2d;
  --card-bg: #232323;
  --hover-bg: rgba(255, 255, 255, 0.05);
  --active-bg: rgba(255, 255, 255, 0.1);
}

.ai-llm-container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  background-color: var(--background-primary);
  color: var(--text-primary);
  min-height: 100vh;
}

.ai-llm-container h1 {
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 15px;
  margin-bottom: 15px;
}

/* <PERSON><PERSON><PERSON>er obsahu - rozdě<PERSON>í na dvě sekce */
.content-wrapper {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
  margin-top: 30px;
}

@media (min-width: 960px) {
  .content-wrapper {
    grid-template-columns: minmax(320px, 2fr) 3fr;
  }
}

/* Sekce poskytovatelů a modelů */
.providers-section,
.models-section {
  background-color: var(--background-secondary);
  border-radius: 8px;
  padding: 20px;
  height: fit-content;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 15px;
}

.section-header h2 {
  margin: 0;
  color: var(--text-primary);
}

/* Seznam poskytovatelů */
.providers-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
}

.provider-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: var(--card-bg);
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-left: 3px solid transparent;
}

.provider-item:hover {
  background-color: var(--hover-bg);
}

.provider-item.selected {
  background-color: var(--active-bg);
  border-left-color: var(--primary-color);
}

.provider-info {
  flex: 1;
  min-width: 0;
}

.provider-info h3 {
  margin: 0 0 8px 0;
  font-size: 1.1rem;
}

.provider-meta {
  display: flex;
  gap: 8px;
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.provider-default {
  background-color: var(--primary-color);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.75rem;
}

.provider-model {
  color: var(--text-muted);
  font-style: italic;
}

.provider-actions {
  display: flex;
  gap: 8px;
}

/* Seznam modelů */
.models-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.model-item {
  background-color: var(--card-bg);
  border-radius: 8px;
  padding: 15px;
  border: 1px solid var(--border-color);
}

.model-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 8px;
}

.model-header h3 {
  margin: 0;
  font-size: 1.1rem;
}

.model-actions {
  display: flex;
  gap: 8px;
}

.model-details {
  font-size: 0.9rem;
}

.model-property {
  margin-bottom: 8px;
}

.property-label {
  font-weight: bold;
  margin-right: 5px;
  color: var(--text-secondary);
}

.property-value {
  color: var(--text-primary);
}

.model-capabilities {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 5px;
}

.capability-tag {
  background-color: var(--secondary-color);
  color: var(--text-primary);
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
}

/* Formuláře */
.form-container {
  background-color: var(--background-tertiary);
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
  border: 1px solid var(--border-color);
}

.form-container h3 {
  margin-top: 0;
  margin-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 10px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: var(--text-secondary);
}

.form-group input[type="text"],
.form-group input[type="password"],
.form-group input[type="number"],
.form-group select {
  width: 100%;
  padding: 10px;
  background-color: var(--form-bg);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  color: var(--text-primary);
  font-size: 1rem;
}

.form-group input[type="checkbox"] {
  margin-right: 6px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.capabilities-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 8px;
  padding: 10px;
  background-color: var(--form-bg);
  border-radius: 4px;
  border: 1px solid var(--border-color);
}

.capability-checkbox {
  display: flex;
  align-items: center;
}

/* Prázdný seznam */
.empty-list {
  padding: 30px;
  text-align: center;
  color: var(--text-muted);
  background-color: var(--card-bg);
  border-radius: 8px;
  border: 1px dashed var(--border-color);
}

.empty-list p {
  margin-bottom: 15px;
}

/* Načítání */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: var(--text-muted);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  border-top: 4px solid var(--primary-color);
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Notifikace */
.success-box,
.error-box {
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 15px;
}

.success-box {
  background-color: rgba(5, 150, 105, 0.2);
  border: 1px solid var(--success-color);
  color: var(--success-color);
}

.error-box {
  background-color: rgba(220, 38, 38, 0.2);
  border: 1px solid var(--error-color);
  color: var(--error-color);
}

/* Tlačítka */
.btn {
  background-color: var(--secondary-color);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn:hover {
  background-color: var(--secondary-hover);
}

.btn:active {
  transform: translateY(1px);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--primary-hover);
}

.btn-icon {
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
  border: none;
  width: 30px;
  height: 30px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-icon:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.btn-icon:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
