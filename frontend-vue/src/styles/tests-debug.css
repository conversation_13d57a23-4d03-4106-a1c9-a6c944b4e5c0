/* Tests & Debug Specific Styles */

/* Test Categories */
.test-categories {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.test-category {
  background: #1e1e2d;
  border: 1px solid #2b2b40;
  border-radius: 8px;
  padding: 1.5rem;
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #2b2b40;
}

.category-header h3 {
  margin: 0;
  color: #e4e6ef;
}

.run-category-btn,
.run-all-btn {
  background: #3699ff;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.run-category-btn:hover:not(:disabled),
.run-all-btn:hover:not(:disabled) {
  background: #2d7dd2;
}

.run-category-btn:disabled,
.run-all-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Test List */
.test-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.test-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #151521;
  border: 1px solid #2b2b40;
  border-radius: 6px;
  transition: all 0.3s;
}

.test-item:hover {
  background: #252538;
}

.test-item.test-passed {
  border-color: #1bc5bd;
}

.test-item.test-failed {
  border-color: #f64e60;
}

.test-item.test-running {
  border-color: #ffa800;
}

.test-info {
  flex: 1;
}

.test-name {
  font-weight: 600;
  color: #e4e6ef;
  margin-bottom: 0.25rem;
}

.test-description {
  font-size: 0.9rem;
  color: #9899ac;
}

.test-status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  min-width: 120px;
}

.status-icon {
  font-size: 1.2rem;
}

.status-text {
  font-size: 0.9rem;
  color: #9899ac;
}

.test-duration {
  font-size: 0.8rem;
  color: #9899ac;
}

.run-test-btn {
  background: #323248;
  color: #e4e6ef;
  border: 1px solid #2b2b40;
  padding: 0.5rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.run-test-btn:hover:not(:disabled) {
  background: #3a3a52;
  border-color: #3699ff;
}

.run-test-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Health Overview */
.health-overview {
  margin-bottom: 2rem;
}

.health-card {
  background: #1e1e2d;
  border: 1px solid #2b2b40;
  border-radius: 8px;
  padding: 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.health-card.healthy {
  border-color: #1bc5bd;
}

.health-card.warning {
  border-color: #ffa800;
}

.health-card.error {
  border-color: #f64e60;
}

.health-icon {
  font-size: 3rem;
}

.health-content h3 {
  margin: 0 0 0.5rem 0;
  color: #e4e6ef;
}

.health-status {
  font-size: 1.2rem;
  font-weight: 600;
  text-transform: capitalize;
}

.health-card.healthy .health-status {
  color: #1bc5bd;
}

.health-card.warning .health-status {
  color: #ffa800;
}

.health-card.error .health-status {
  color: #f64e60;
}

/* Components Grid */
.components-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.component-card {
  background: #1e1e2d;
  border: 1px solid #2b2b40;
  border-radius: 8px;
  padding: 1.5rem;
}

.component-card.healthy {
  border-color: #1bc5bd;
}

.component-card.warning {
  border-color: #ffa800;
}

.component-card.error {
  border-color: #f64e60;
}

.component-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.component-header h4 {
  margin: 0;
  color: #e4e6ef;
}

.component-status {
  font-size: 1.5rem;
}

.component-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
}

.detail-item span:first-child {
  color: #9899ac;
}

.detail-item span:last-child {
  color: #e4e6ef;
}

.detail-item span.healthy {
  color: #1bc5bd;
}

.detail-item span.warning {
  color: #ffa800;
}

.detail-item span.error {
  color: #f64e60;
}

.error-message {
  background: #2d1b1b;
  border: 1px solid #f64e60;
  border-radius: 4px;
  padding: 0.5rem;
  font-size: 0.8rem;
  color: #f64e60;
  margin-top: 0.5rem;
}

/* Performance Metrics */
.performance-metrics {
  margin-bottom: 2rem;
}

.performance-metrics h3 {
  margin-bottom: 1rem;
  color: #e4e6ef;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.metric-item {
  background: #1e1e2d;
  border: 1px solid #2b2b40;
  border-radius: 8px;
  padding: 1rem;
}

.metric-label {
  font-size: 0.9rem;
  color: #9899ac;
  margin-bottom: 0.5rem;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #e4e6ef;
  margin-bottom: 0.5rem;
}

.metric-bar {
  height: 6px;
  background: #323248;
  border-radius: 3px;
  overflow: hidden;
}

.metric-fill {
  height: 100%;
  transition: width 0.3s;
}

.metric-fill.good {
  background: #1bc5bd;
}

.metric-fill.warning {
  background: #ffa800;
}

.metric-fill.critical {
  background: #f64e60;
}

/* Logs Section */
.log-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.log-level-select {
  background: #323248;
  color: #e4e6ef;
  border: 1px solid #2b2b40;
  padding: 0.5rem 1rem;
  border-radius: 6px;
}

.clear-btn {
  background: #f64e60;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.clear-btn:hover {
  background: #d63447;
}

/* Log Viewer */
.log-viewer {
  background: #151521;
  border: 1px solid #2b2b40;
  border-radius: 8px;
  max-height: 400px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
}

.log-entry {
  display: grid;
  grid-template-columns: 120px 80px 120px 1fr;
  gap: 1rem;
  padding: 0.5rem 1rem;
  border-bottom: 1px solid #2b2b40;
  font-size: 0.9rem;
  align-items: center;
}

.log-entry:last-child {
  border-bottom: none;
}

.log-entry.error {
  background: #2d1b1b;
  border-left: 3px solid #f64e60;
}

.log-entry.warning {
  background: #2d2a1b;
  border-left: 3px solid #ffa800;
}

.log-entry.info {
  background: #1b2d2d;
  border-left: 3px solid #1bc5bd;
}

.log-entry.debug {
  background: #1b1b2d;
  border-left: 3px solid #3699ff;
}

.log-timestamp {
  color: #9899ac;
  font-size: 0.8rem;
}

.log-level {
  font-weight: 600;
  text-transform: uppercase;
}

.log-entry.error .log-level {
  color: #f64e60;
}

.log-entry.warning .log-level {
  color: #ffa800;
}

.log-entry.info .log-level {
  color: #1bc5bd;
}

.log-entry.debug .log-level {
  color: #3699ff;
}

.log-source {
  color: #9899ac;
  font-weight: 500;
}

.log-message {
  color: #e4e6ef;
  word-break: break-word;
}

/* Responsive Design */
@media (max-width: 768px) {
  .test-item {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }
  
  .test-status {
    justify-content: space-between;
    min-width: auto;
  }
  
  .components-grid {
    grid-template-columns: 1fr;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .log-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .log-entry {
    grid-template-columns: 1fr;
    gap: 0.25rem;
  }
}
