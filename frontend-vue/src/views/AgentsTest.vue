<template>
  <div class="agents-test-container">
    <h1>AGENTI-TEST</h1>
    <p>Konfigurace a testování agentů v systému GENT v10.</p>

    <div class="section">
      <h2><PERSON><PERSON><PERSON><PERSON> agentů</h2>
      <p><PERSON><PERSON> je seznam <PERSON>tu<PERSON><PERSON>ch agent<PERSON>, <PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON><PERSON><PERSON> modely a skupiny.</p>

      <div class="card">
        <table class="agents-table">
          <thead>
            <tr>
              <th>Název agenta</th>
              <th><PERSON><PERSON><PERSON></th>
              <th>LLM Model</th>
              <th>Skupina</th>
              <th>Stav</th>
              <th>Akce</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="agent in agents" :key="agent.id">
              <td>{{ agent.name }}</td>
              <td>{{ agent.purpose }}</td>
              <td>{{ agent.model }}</td>
              <td>{{ agent.group }}</td>
              <td>
                <span :class="['status', agent.status.toLowerCase()]">
                  {{ agent.status }}
                </span>
              </td>
              <td>
                <button class="btn btn-small" @click="configureAgent(agent)">Konfigurovat</button>
                <button class="btn btn-small" @click="testAgent(agent)">Testovat</button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div class="section">
      <h2>Skupiny agentů</h2>
      <p>Zde můžete spravovat skupiny agentů a přiřazovat agenty do skupin.</p>

      <div class="groups-grid">
        <div class="card group-card" v-for="group in groups" :key="group.id">
          <div class="group-header">
            <h3>{{ group.name }}</h3>
            <span class="agent-count">{{ group.agents.length }} agentů</span>
          </div>
          <div class="group-description">
            {{ group.description }}
          </div>
          <div class="group-agents">
            <div class="agent-tag" v-for="agent in group.agents" :key="agent.id">
              {{ agent.name }}
            </div>
          </div>
          <div class="group-actions">
            <button class="btn btn-small" @click="editGroup(group)">Upravit</button>
            <button class="btn btn-small btn-secondary" @click="deleteGroup(group)">Smazat</button>
          </div>
        </div>

        <div class="card group-card add-group" @click="createGroup">
          <div class="add-group-content">
            <div class="add-icon">+</div>
            <p>Vytvořit novou skupinu</p>
          </div>
        </div>
      </div>
    </div>

    <div class="section">
      <h2>Konfigurace LLM modelů pro agenty</h2>
      <p>Zde můžete nastavit, které LLM modely budou používat jednotliví agenti.</p>

      <div class="card">
        <div class="model-config">
          <div class="model-selector">
            <label for="agent-select">Vyberte agenta:</label>
            <select id="agent-select" v-model="selectedAgent">
              <option v-for="agent in agents" :key="agent.id" :value="agent.id">{{ agent.name }}</option>
            </select>
          </div>

          <div class="model-list" v-if="selectedAgent">
            <h3>Dostupné LLM modely</h3>
            <div class="model-options">
              <div class="model-option" v-for="model in availableModels" :key="model.id">
                <input type="radio" :id="model.id" :value="model.id" v-model="selectedModel" />
                <label :for="model.id">
                  <strong>{{ model.name }}</strong>
                  <span class="model-provider">{{ model.provider }}</span>
                  <span class="model-description">{{ model.description }}</span>
                </label>
              </div>
            </div>

            <div class="model-actions">
              <button class="btn" @click="saveModelConfig" :disabled="!selectedModel">Uložit konfiguraci</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="section">
      <h2>Dokumentace agentů</h2>

      <div class="card">
        <h3>Typy agentů</h3>
        <p>Systém GENT v10 podporuje následující typy agentů:</p>

        <div class="agent-types">
          <div class="agent-type">
            <h4>Vývojářský agent</h4>
            <p>Specializuje se na psaní, úpravu a refaktorování kódu. Používá pokročilé LLM modely s dobrou znalostí programování.</p>
            <p><strong>Doporučené modely:</strong> GPT-4, Claude 3 Opus</p>
          </div>

          <div class="agent-type">
            <h4>Testovací agent</h4>
            <p>Specializuje se na psaní a spouštění testů. Dokáže identifikovat hraniční případy a potenciální chyby.</p>
            <p><strong>Doporučené modely:</strong> GPT-4, Claude 3 Opus</p>
          </div>

          <div class="agent-type">
            <h4>Analytický agent</h4>
            <p>Specializuje se na analýzu dat a požadavků. Dokáže zpracovat velké množství informací a identifikovat vzory a trendy.</p>
            <p><strong>Doporučené modely:</strong> GPT-4, Claude 3 Opus, Gemini Pro</p>
          </div>

          <div class="agent-type">
            <h4>Výzkumný agent</h4>
            <p>Specializuje se na vyhledávání a zpracování informací. Dokáže efektivně využívat MCP servery pro získávání dat.</p>
            <p><strong>Doporučené modely:</strong> Claude 3 Opus, GPT-4, Gemini Pro</p>
          </div>

          <div class="agent-type">
            <h4>Kreativní agent</h4>
            <p>Specializuje se na generování textů, nápadů a kreativních řešení. Dokáže přicházet s originálními nápady.</p>
            <p><strong>Doporučené modely:</strong> Claude 3 Opus, GPT-4</p>
          </div>
        </div>
      </div>

      <div class="card">
        <h3>Skupiny agentů</h3>
        <p>Agenty lze organizovat do skupin podle jejich zaměření nebo projektu:</p>

        <ul class="group-types">
          <li><strong>Výzkumná skupina</strong> - Agenti zaměření na výzkum a analýzu informací</li>
          <li><strong>Vývojová skupina</strong> - Agenti zaměření na vývoj a testování kódu</li>
          <li><strong>Kreativní skupina</strong> - Agenti zaměření na generování obsahu a nápadů</li>
          <li><strong>Projektová skupina</strong> - Agenti přiřazení ke konkrétnímu projektu</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'AgentsTest',
  data() {
    return {
      agents: [],
      groups: [],
      availableModels: [],
      selectedAgent: null,
      selectedModel: null,
      selectedGroup: null,
      loading: {
        agents: false,
        groups: false,
        models: false
      },
      error: {
        agents: null,
        groups: null,
        models: null
      },
      newAgent: {
        name: '',
        agent_type: 'developer',
        description: '',
        llm_model_id: null,
        system_message: '',
        group_id: null,
        is_active: true
      },
      newGroup: {
        name: '',
        description: '',
        is_active: true
      },
      showNewAgentForm: false,
      showNewGroupForm: false,
      agentTypes: [
        { value: 'developer', label: 'Vývojářský agent' },
        { value: 'tester', label: 'Testovací agent' },
        { value: 'analyst', label: 'Analytický agent' },
        { value: 'researcher', label: 'Výzkumný agent' },
        { value: 'creative', label: 'Kreativní agent' }
      ],
      editingAgent: null,
      editingGroup: null
    }
  },
  created() {
    this.fetchAgents()
    this.fetchGroups()
    this.fetchModels()
  },
  computed: {
    agentStatusText() {
      return (agent) => agent.is_active ? 'Online' : 'Offline'
    },
    getAgentTypeName() {
      return (type) => {
        const agentType = this.agentTypes.find(t => t.value === type)
        return agentType ? agentType.label : type
      }
    },
    getModelName() {
      return (modelId) => {
        const model = this.availableModels.find(m => m.model_id === modelId)
        return model ? model.model_name : 'Není přiřazen'
      }
    },
    getGroupName() {
      return (groupId) => {
        const group = this.groups.find(g => g.group_id === groupId)
        return group ? group.name : 'Není přiřazena'
      }
    }
  },
  methods: {
    async fetchAgents() {
      this.loading.agents = true
      this.error.agents = null
      try {
        const response = await axios.get('http://localhost:8001/api/postgres/data', {
          params: {
            db_name: 'gentdb',
            table_name: 'agents',
            page: 1,
            page_size: 100
          }
        })
        if (response.data && response.data.rows) {
          this.agents = response.data.rows.map(row => {
            const agent = {}
            response.data.columns.forEach((col, index) => {
              agent[col.name] = row[index]
            })
            return agent
          })
        }
      } catch (error) {
        console.error('Chyba při načítání agentů:', error)
        this.error.agents = 'Nepodařilo se načíst agenty. Zkuste to prosím později.'
      } finally {
        this.loading.agents = false
      }
    },
    async fetchGroups() {
      this.loading.groups = true
      this.error.groups = null
      try {
        const response = await axios.get('http://localhost:8001/api/postgres/data', {
          params: {
            db_name: 'gentdb',
            table_name: 'agent_groups',
            page: 1,
            page_size: 100
          }
        })
        if (response.data && response.data.rows) {
          this.groups = response.data.rows.map(row => {
            const group = {}
            response.data.columns.forEach((col, index) => {
              group[col.name] = row[index]
            })
            // Přidáme prázdné pole pro agenty, které později naplníme
            group.agents = []
            return group
          })

          // Pro každou skupinu najdeme její agenty
          this.groups.forEach(group => {
            group.agents = this.agents.filter(agent => agent.group_id === group.group_id)
              .map(agent => ({
                agent_id: agent.agent_id,
                name: agent.name,
                agent_type: agent.agent_type
              }))
          })
        }
      } catch (error) {
        console.error('Chyba při načítání skupin:', error)
        this.error.groups = 'Nepodařilo se načíst skupiny. Zkuste to prosím později.'
      } finally {
        this.loading.groups = false
      }
    },
    async fetchModels() {
      this.loading.models = true
      this.error.models = null
      try {
        const response = await axios.get('/api/postgres/data', {
          params: {
            db_name: 'gentdb',
            table_name: 'llm_models',
            page: 1,
            page_size: 100
          }
        })
        if (response.data && response.data.rows) {
          this.availableModels = response.data.rows.map(row => {
            const model = {}
            response.data.columns.forEach((col, index) => {
              model[col.name] = row[index]
            })
            return model
          })
        }
      } catch (error) {
        console.error('Chyba při načítání modelů:', error)
        this.error.models = 'Nepodařilo se načíst modely. Zkuste to prosím později.'
      } finally {
        this.loading.models = false
      }
    },
    configureAgent(agent) {
      this.selectedAgent = agent.agent_id
      this.selectedModel = agent.llm_model_id
      this.editingAgent = { ...agent }
    },
    async testAgent(agent) {
      alert(`Testování agenta: ${agent.name}`)
      // Zde by byla implementace testování agenta
    },
    async editGroup(group) {
      this.editingGroup = { ...group }
      this.selectedGroup = group.group_id
    },
    async deleteGroup(group) {
      if (confirm(`Opravdu chcete smazat skupinu ${group.name}?`)) {
        try {
          // Nejprve odebereme všechny agenty ze skupiny
          for (const agent of this.agents.filter(a => a.group_id === group.group_id)) {
            await axios.post('/api/postgres/execute', {
              db_name: 'gentdb',
              query: 'UPDATE agents SET group_id = NULL, updated_at = CURRENT_TIMESTAMP WHERE agent_id = $1',
              params: [agent.agent_id]
            })
          }

          // Pak smažeme skupinu
          await axios.post('/api/postgres/execute', {
            db_name: 'gentdb',
            query: 'DELETE FROM agent_groups WHERE group_id = $1',
            params: [group.group_id]
          })

          alert(`Skupina ${group.name} byla smazána`)
          this.fetchGroups()
          this.fetchAgents()
        } catch (error) {
          console.error('Chyba při mazání skupiny:', error)
          alert(`Chyba při mazání skupiny: ${error.message}`)
        }
      }
    },
    showCreateGroupForm() {
      this.newGroup = {
        name: '',
        description: '',
        is_active: true
      }
      this.showNewGroupForm = true
    },
    async createGroup() {
      try {
        await axios.post('/api/postgres/execute', {
          db_name: 'gentdb',
          query: 'INSERT INTO agent_groups (name, description, is_active) VALUES ($1, $2, $3)',
          params: [this.newGroup.name, this.newGroup.description, this.newGroup.is_active]
        })

        alert(`Skupina ${this.newGroup.name} byla vytvořena`)
        this.showNewGroupForm = false
        this.fetchGroups()
      } catch (error) {
        console.error('Chyba při vytváření skupiny:', error)
        alert(`Chyba při vytváření skupiny: ${error.message}`)
      }
    },
    async saveGroupChanges() {
      try {
        await axios.post('/api/postgres/execute', {
          db_name: 'gentdb',
          query: 'UPDATE agent_groups SET name = $1, description = $2, is_active = $3, updated_at = CURRENT_TIMESTAMP WHERE group_id = $4',
          params: [this.editingGroup.name, this.editingGroup.description, this.editingGroup.is_active, this.editingGroup.group_id]
        })

        alert(`Skupina ${this.editingGroup.name} byla aktualizována`)
        this.editingGroup = null
        this.selectedGroup = null
        this.fetchGroups()
      } catch (error) {
        console.error('Chyba při aktualizaci skupiny:', error)
        alert(`Chyba při aktualizaci skupiny: ${error.message}`)
      }
    },
    showCreateAgentForm() {
      this.newAgent = {
        name: '',
        agent_type: 'developer',
        description: '',
        llm_model_id: null,
        system_message: '',
        group_id: null,
        is_active: true
      }
      this.showNewAgentForm = true
    },
    async createAgent() {
      try {
        await axios.post('/api/postgres/execute', {
          db_name: 'gentdb',
          query: 'INSERT INTO agents (name, agent_type, description, llm_model_id, system_message, group_id, is_active) VALUES ($1, $2, $3, $4, $5, $6, $7)',
          params: [
            this.newAgent.name,
            this.newAgent.agent_type,
            this.newAgent.description,
            this.newAgent.llm_model_id,
            this.newAgent.system_message,
            this.newAgent.group_id,
            this.newAgent.is_active
          ]
        })

        alert(`Agent ${this.newAgent.name} byl vytvořen`)
        this.showNewAgentForm = false
        this.fetchAgents()
        this.fetchGroups()
      } catch (error) {
        console.error('Chyba při vytváření agenta:', error)
        alert(`Chyba při vytváření agenta: ${error.message}`)
      }
    },
    async saveAgentChanges() {
      try {
        await axios.post('/api/postgres/execute', {
          db_name: 'gentdb',
          query: 'UPDATE agents SET name = $1, agent_type = $2, description = $3, llm_model_id = $4, system_message = $5, group_id = $6, is_active = $7, updated_at = CURRENT_TIMESTAMP WHERE agent_id = $8',
          params: [
            this.editingAgent.name,
            this.editingAgent.agent_type,
            this.editingAgent.description,
            this.editingAgent.llm_model_id,
            this.editingAgent.system_message,
            this.editingAgent.group_id,
            this.editingAgent.is_active,
            this.editingAgent.agent_id
          ]
        })

        alert(`Agent ${this.editingAgent.name} byl aktualizován`)
        this.editingAgent = null
        this.selectedAgent = null
        this.fetchAgents()
        this.fetchGroups()
      } catch (error) {
        console.error('Chyba při aktualizaci agenta:', error)
        alert(`Chyba při aktualizaci agenta: ${error.message}`)
      }
    },
    async deleteAgent(agent) {
      if (confirm(`Opravdu chcete smazat agenta ${agent.name}?`)) {
        try {
          await axios.post('/api/postgres/execute', {
            db_name: 'gentdb',
            query: 'DELETE FROM agents WHERE agent_id = $1',
            params: [agent.agent_id]
          })

          alert(`Agent ${agent.name} byl smazán`)
          this.fetchAgents()
          this.fetchGroups()
        } catch (error) {
          console.error('Chyba při mazání agenta:', error)
          alert(`Chyba při mazání agenta: ${error.message}`)
        }
      }
    },
    cancelAgentEdit() {
      this.editingAgent = null
      this.selectedAgent = null
    },
    cancelGroupEdit() {
      this.editingGroup = null
      this.selectedGroup = null
    },
    cancelNewAgent() {
      this.showNewAgentForm = false
    },
    cancelNewGroup() {
      this.showNewGroupForm = false
    }
  }
}
</script>

<style scoped>
.agents-test-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1rem;
}

.section {
  margin-bottom: 2rem;
}

.card {
  background-color: var(--card-bg-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.agents-table {
  width: 100%;
  border-collapse: collapse;
}

.agents-table th,
.agents-table td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.agents-table th {
  font-weight: bold;
  background-color: rgba(0, 0, 0, 0.05);
}

.status {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.85rem;
}

.status.online {
  background-color: rgba(63, 185, 80, 0.2);
  color: var(--success-color);
}

.status.offline {
  background-color: rgba(218, 54, 51, 0.2);
  color: var(--error-color);
}

.btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.5rem 1rem;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.3s;
}

.btn:hover {
  background-color: var(--primary-color-dark);
}

.btn:disabled {
  background-color: var(--disabled-color);
  cursor: not-allowed;
}

.btn-small {
  font-size: 0.8rem;
  padding: 0.35rem 0.75rem;
  margin-right: 0.5rem;
}

.btn-secondary {
  background-color: transparent;
  border: 1px solid var(--border-color);
  color: var(--text-color);
}

.btn-secondary:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.groups-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.group-card {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.group-header h3 {
  margin: 0;
}

.agent-count {
  font-size: 0.85rem;
  opacity: 0.7;
}

.group-description {
  margin-bottom: 1rem;
  flex: 1;
}

.group-agents {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.agent-tag {
  background-color: rgba(26, 117, 255, 0.1);
  color: var(--primary-color);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.85rem;
}

.group-actions {
  display: flex;
  gap: 0.5rem;
}

.add-group {
  border: 2px dashed var(--border-color);
  background-color: transparent;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-group:hover {
  border-color: var(--primary-color);
  background-color: rgba(26, 117, 255, 0.05);
}

.add-group-content {
  text-align: center;
  padding: 2rem;
}

.add-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  color: var(--primary-color);
}

.model-config {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.model-selector {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.model-selector select {
  padding: 0.5rem;
  border-radius: 4px;
  border: 1px solid var(--border-color);
  background-color: var(--input-bg-color);
  color: var(--text-color);
}

.model-list h3 {
  margin-top: 0;
  margin-bottom: 1rem;
}

.model-options {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.model-option {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}

.model-option input {
  margin-top: 0.25rem;
}

.model-option label {
  display: flex;
  flex-direction: column;
}

.model-provider {
  font-size: 0.85rem;
  opacity: 0.7;
  margin-bottom: 0.25rem;
}

.model-description {
  font-size: 0.9rem;
}

.model-actions {
  display: flex;
  justify-content: flex-end;
}

.agent-types {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.agent-type {
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  padding: 1rem;
}

.agent-type h4 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  color: var(--primary-color);
}

.group-types {
  list-style-type: none;
  padding-left: 0;
}

.group-types li {
  margin-bottom: 0.5rem;
  padding: 0.5rem;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

@media (max-width: 768px) {
  .groups-grid,
  .agent-types {
    grid-template-columns: 1fr;
  }
}
</style>
