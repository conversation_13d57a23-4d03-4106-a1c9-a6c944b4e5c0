<template>
  <div class="chat-test-container">
    <!-- <PERSON>lav<PERSON><PERSON> chat sekce -->
    <div class="chat-main-section">
      <!-- Sekce pro výb<PERSON><PERSON> poskytovatele a modelu -->
      <div class="provider-model-section">
      <div class="selector-row">
        <div class="selector-container">
          <div class="selector-label">Poskytovatel AI:</div>
          <div v-if="isLoadingProviders" class="loading-container">
            <div class="loading-spinner"></div>
            <span>Načítání poskytovatelů...</span>
          </div>
          <select
            v-else
            class="provider-select"
            v-model="selectedProviderId"
            @change="handleProviderSelect"
          >
            <option value="" disabled>-- Vyberte poskytovatele --</option>
            <option
              v-for="provider in providers"
              :key="provider.id"
              :value="provider.id"
            >
              {{ provider.provider_name || provider.name }}
            </option>
          </select>
        </div>
        <div class="selector-container">
          <div class="selector-label">Model:</div>
          <div v-if="isLoadingModels" class="loading-container">
            <div class="loading-spinner"></div>
            <span>Načítání modelů...</span>
          </div>
          <select
            v-else
            class="model-select"
            v-model="selectedModelId"
            @change="handleModelSelect"
            :disabled="!models.length || !selectedProviderId"
          >
            <option value="" disabled>-- Vyberte model --</option>
            <option
              v-for="model in models"
              :key="model.id"
              :value="model.id"
            >
              {{ model.model_name }}
            </option>
          </select>
          <div v-if="!isLoadingModels && !models.length && selectedProviderId" class="empty-models-message">
            Žádné modely nejsou k dispozici pro vybraného poskytovatele.
          </div>
        </div>
      </div>
    </div>

    <!-- Chybová zpráva -->
    <div v-if="error" class="error-message">
      {{ error }}
    </div>

    <!-- Sekce chatu -->
    <div class="chat-section">
      <div class="chat-messages">
        <!-- Prázdný stav -->
        <div v-if="messages.length === 0" class="chat-empty">
          <div class="chat-empty-icon">
            <span>💬</span>
          </div>
          <p>Vyberte poskytovatele a model, pak začněte psát zprávu.</p>
          <p>Nebo použijte tlačítko "Rychlý test" níže.</p>
        </div>

        <!-- Zprávy -->
        <div v-else>
          <div
            v-for="(message, index) in messages"
            :key="index"
            :class="['message', message.sender]"
          >
            <div class="message-bubble">
              {{ message.text }}
              <div class="message-time">
                {{ formatTime(message.time) }}
              </div>
            </div>
          </div>
        </div>

        <!-- Indikátor psaní -->
        <div v-if="isProcessing" class="message assistant">
          <div class="typing-indicator">
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
            <div class="typing-dot"></div>
          </div>
        </div>
      </div>

      <!-- Vstupní pole pro zprávu -->
      <div class="message-input-container">
        <textarea
          class="message-input"
          v-model="newMessage"
          placeholder="Napište zprávu..."
          :disabled="isProcessing || !selectedModelId"
          @keyup.enter="sendMessage"
        ></textarea>
        <button
          class="send-button"
          :disabled="!newMessage.trim() || isProcessing || !selectedModelId"
          @click="sendMessage"
        >
          <span>📤</span>
        </button>
      </div>

      <!-- Akční tlačítka -->
      <div class="action-buttons">
        <button
          class="btn-action btn-clear"
          @click="clearChat"
          :disabled="messages.length === 0 || isProcessing"
        >
          Vyčistit chat
        </button>
        <button
          class="btn-action btn-test"
          @click="quickTest"
          :disabled="isProcessing || !selectedModelId"
        >
          Rychlý test
        </button>
      </div>
    </div>
    </div>

    <!-- Performance Monitoring Panel -->
    <div class="monitoring-panel">
      <div class="monitoring-header">
        <h3>📊 Performance Monitoring</h3>
        <button
          class="toggle-monitoring"
          @click="toggleMonitoring"
          :class="{ active: showMonitoring }"
        >
          {{ showMonitoring ? '🔽' : '🔼' }}
        </button>
      </div>

      <div v-if="showMonitoring" class="monitoring-content">
        <!-- Current Session Stats -->
        <div class="stats-section">
          <h4>📈 Aktuální relace</h4>
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-label">Zprávy:</div>
              <div class="stat-value">{{ sessionStats.messageCount }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">Doba trvání:</div>
              <div class="stat-value">{{ formatDuration(sessionStats.duration) }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">Průměrný čas odpovědi:</div>
              <div class="stat-value">{{ sessionStats.avgResponseTime }}ms</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">Celkové tokeny:</div>
              <div class="stat-value">{{ sessionStats.totalTokens }}</div>
            </div>
          </div>
        </div>

        <!-- Last Response Metrics -->
        <div v-if="lastResponseMetrics" class="metrics-section">
          <h4>⚡ Poslední odpověď</h4>
          <div class="metrics-grid">
            <div class="metric-item" :class="getResponseTimeClass(lastResponseMetrics.responseTime)">
              <div class="metric-icon">⏱️</div>
              <div class="metric-content">
                <div class="metric-label">Response Time</div>
                <div class="metric-value">{{ lastResponseMetrics.responseTime }}ms</div>
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-icon">🔤</div>
              <div class="metric-content">
                <div class="metric-label">Tokeny</div>
                <div class="metric-value">{{ lastResponseMetrics.tokens || 'N/A' }}</div>
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-icon">💰</div>
              <div class="metric-content">
                <div class="metric-label">Odhadované náklady</div>
                <div class="metric-value">${{ lastResponseMetrics.estimatedCost || '0.00' }}</div>
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-icon">🤖</div>
              <div class="metric-content">
                <div class="metric-label">Model</div>
                <div class="metric-value">{{ selectedModelName || 'N/A' }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Response Time Chart -->
        <div v-if="responseTimeHistory.length > 0" class="chart-section">
          <h4>📊 Historie časů odpovědi</h4>
          <div class="response-time-chart">
            <div
              v-for="(time, index) in responseTimeHistory.slice(-10)"
              :key="index"
              class="chart-bar"
              :style="{ height: getBarHeight(time) + '%' }"
              :title="`${time}ms`"
            ></div>
          </div>
          <div class="chart-labels">
            <span>Posledních 10 odpovědí</span>
          </div>
        </div>

        <!-- Model Performance Comparison -->
        <div v-if="modelPerformance.size > 0" class="performance-section">
          <h4>🏆 Výkon modelů</h4>
          <div class="performance-list">
            <div
              v-for="[modelName, stats] in Array.from(modelPerformance.entries())"
              :key="modelName"
              class="performance-item"
            >
              <div class="performance-model">{{ modelName }}</div>
              <div class="performance-stats">
                <span class="performance-stat">
                  Avg: {{ Math.round(stats.avgTime) }}ms
                </span>
                <span class="performance-stat">
                  Použití: {{ stats.count }}x
                </span>
                <span class="performance-stat">
                  Tokeny: {{ stats.totalTokens }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- System Health -->
        <div class="health-section">
          <h4>💚 Stav systému</h4>
          <div class="health-indicators">
            <div class="health-item" :class="{ healthy: apiHealth.status === 'healthy' }">
              <div class="health-icon">{{ apiHealth.status === 'healthy' ? '🟢' : '🔴' }}</div>
              <div class="health-label">API Status</div>
              <div class="health-value">{{ apiHealth.status }}</div>
            </div>
            <div class="health-item" :class="{ healthy: connectionHealth.status === 'healthy' }">
              <div class="health-icon">{{ connectionHealth.status === 'healthy' ? '🟢' : '🔴' }}</div>
              <div class="health-label">Připojení</div>
              <div class="health-value">{{ connectionHealth.latency }}ms</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue';
import { llmDbService } from '../services/llm_db.service';
import { chatService } from '../services/chat.service';

export default {
  name: 'ChatTest',

  setup() {
    // Stav poskytovatelů a modelů
    const providers = ref([]);
    const selectedProviderId = ref('');
    const selectedProviderName = ref('');
    const selectedProvider = ref(null);

    const models = ref([]);
    const selectedModelId = ref('');
    const selectedModelName = ref('');

    // Stav načítání
    const isLoadingProviders = ref(false);
    const isLoadingModels = ref(false);

    // Stav chatu
    const messages = ref([]);
    const newMessage = ref('');
    const isProcessing = ref(false);
    const error = ref(null);

    // Monitoring stav
    const showMonitoring = ref(true);
    const sessionStartTime = ref(new Date());
    const sessionStats = ref({
      messageCount: 0,
      duration: 0,
      avgResponseTime: 0,
      totalTokens: 0
    });
    const lastResponseMetrics = ref(null);
    const responseTimeHistory = ref([]);
    const modelPerformance = ref(new Map());
    const apiHealth = ref({ status: 'healthy' });
    const connectionHealth = ref({ status: 'healthy', latency: 0 });

    // Načtení poskytovatelů z API
    const loadProviders = async () => {
      isLoadingProviders.value = true;
      error.value = null;

      try {
        console.log('Načítání poskytovatelů z databáze...');
        const response = await llmDbService.getProviders();

        if (response.error) {
          error.value = response.error.message;
          console.error('Chyba při načítání poskytovatelů:', response.error);
          return;
        }

        providers.value = response.data || [];
        console.log('Načteno', providers.value.length, 'poskytovatelů z DB');
      } catch (err) {
        console.error('Chyba při načítání poskytovatelů:', err);
        error.value = 'Nepodařilo se načíst seznam poskytovatelů z databáze. Zkuste to prosím později.';
      } finally {
        isLoadingProviders.value = false;
      }
    };

    // Zpracování výběru poskytovatele
    const handleProviderSelect = async () => {
      if (!selectedProviderId.value) return;

      // Nalezení poskytovatele a uložení jeho jména
      const provider = providers.value.find(p => p.id == selectedProviderId.value);
      if (provider) {
        selectedProviderName.value = provider.provider_name || provider.name;
      }

      // Vyčistíme chyby při změně poskytovatele
      error.value = null;

      // Načtení detailu poskytovatele a jeho modelů
      await loadProviderDetail(selectedProviderId.value);
    };

    // Načtení detailu poskytovatele včetně modelů
    const loadProviderDetail = async (providerId) => {
      if (!providerId) return;

      isLoadingModels.value = true;
      selectedModelId.value = '';
      models.value = [];

      try {
        console.log('Načítání detailu poskytovatele z databáze:', providerId);
        const response = await llmDbService.getProviderDetail(providerId);

        if (response.error) {
          error.value = response.error.message;
          console.error('Chyba při načítání detailu poskytovatele:', response.error);
          return;
        }

        selectedProvider.value = response.data;

        // Zpracování modelů z odpovědi
        if (selectedProvider.value && selectedProvider.value.models) {
          // Převod objektu modelů na pole
          const modelsObj = selectedProvider.value.models;
          const modelsArray = [];

          // Pro každý model v objektu vytvoříme položku pole
          for (const modelName in modelsObj) {
            const model = modelsObj[modelName];
            modelsArray.push({
              id: model.model_id || `temp_${modelName}`,
              model_name: modelName,
              ...model
            });
          }

          models.value = modelsArray;

          // Pokud máme modely, automaticky vybereme první
          if (models.value.length > 0) {
            selectedModelId.value = models.value[0].id;
            handleModelSelect();
          }
        }
      } catch (err) {
        console.error('Chyba při načítání detailu poskytovatele:', err);
        error.value = 'Nepodařilo se načíst modely z databáze. Zkuste to prosím později.';
      } finally {
        isLoadingModels.value = false;
      }
    };

    // Zpracování výběru modelu
    const handleModelSelect = () => {
      if (!selectedModelId.value) return;

      // Nalezení modelu a uložení jeho jména
      const model = models.value.find(m => m.id == selectedModelId.value);
      if (model) {
        selectedModelName.value = model.model_name;
      }
    };

    // Odeslání zprávy
    const sendMessage = async () => {
      if (!newMessage.value.trim() || !selectedModelId.value || isProcessing.value) {
        return;
      }

      // Performance tracking - start time
      const startTime = performance.now();

      // Zpráva uživatele
      const userMessage = {
        sender: 'user',
        text: newMessage.value,
        time: new Date()
      };

      messages.value.push(userMessage);

      // Uložíme zprávu a vymažeme vstup
      const messageToSend = newMessage.value;
      newMessage.value = '';

      // Zpracování odpovědi
      isProcessing.value = true;
      error.value = null;

      try {
        // Připravíme data pro požadavek na API
        const model = models.value.find(m => m.id == selectedModelId.value);

        // Odstranili jsme blokaci Google/Gemini poskytovatelů

        const chatRequestData = {
          model: selectedModelId.value,
          model_identifier: model ? model.model_identifier : undefined,
          message: messageToSend,
          provider_id: selectedProviderId.value,
          provider_name: selectedProviderName.value,
          temperature: 0.7,
          max_tokens: 1000,
          system_prompt: "Jsi užitečný a přátelský asistent, který odpovídá česky. Odpovídej stručně a výstižně."
        };

        // Voláme skutečné API přes chatService
        console.log('Odesílám požadavek na LLM API:', chatRequestData);

        // Použití chatService pro reálnou komunikaci s LLM
        const response = await chatService.sendMessage(chatRequestData);

        // Performance tracking - end time
        const endTime = performance.now();
        const responseTime = Math.round(endTime - startTime);

        // Zpracování odpovědi z API
        const responseText = response.text || response.content ||
                            (response.choices && response.choices[0] && response.choices[0].message.content) ||
                            'Nepodařilo se zpracovat odpověď z LLM modelu.';

        // Extrakce token informací z odpovědi
        const tokens = response.usage?.total_tokens ||
                      response.tokens_used ||
                      Math.ceil(responseText.length / 4); // Odhad tokenů

        // Odpověď asistenta
        const assistantMessage = {
          sender: 'assistant',
          text: responseText,
          time: new Date()
        };

        messages.value.push(assistantMessage);

        // Aktualizace performance metrik
        updatePerformanceMetrics(responseTime, tokens);

      } catch (err) {
        console.error('Chyba při komunikaci s LLM API:', err);
        error.value = 'Nepodařilo se získat odpověď od LLM modelu. Zkuste to prosím později.';

        // I při chybě zaznamenáme response time
        const endTime = performance.now();
        const responseTime = Math.round(endTime - startTime);
        updatePerformanceMetrics(responseTime, 0, true);

      } finally {
        isProcessing.value = false;
      }
    };

    // Vyčištění chatu
    const clearChat = () => {
      messages.value = [];
      error.value = null;
    };

    // Rychlý test
    const quickTest = () => {
      newMessage.value = "Ahoj, proveď rychlý test a popiš své schopnosti.";
      sendMessage();
    };

    // Formátování času
    const formatTime = (time) => {
      const date = new Date(time);
      return date.toLocaleTimeString('cs-CZ', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    };

    // Monitoring funkce
    const toggleMonitoring = () => {
      showMonitoring.value = !showMonitoring.value;
    };

    const updatePerformanceMetrics = (responseTime, tokens, isError = false) => {
      // Aktualizace posledních metrik
      lastResponseMetrics.value = {
        responseTime,
        tokens,
        estimatedCost: calculateEstimatedCost(tokens),
        timestamp: new Date(),
        isError
      };

      // Přidání do historie časů odpovědi
      responseTimeHistory.value.push(responseTime);
      if (responseTimeHistory.value.length > 50) {
        responseTimeHistory.value = responseTimeHistory.value.slice(-50);
      }

      // Aktualizace session statistik
      sessionStats.value.messageCount++;
      sessionStats.value.totalTokens += tokens;
      sessionStats.value.duration = Date.now() - sessionStartTime.value.getTime();

      // Výpočet průměrného času odpovědi
      const validTimes = responseTimeHistory.value.filter(time => time > 0);
      if (validTimes.length > 0) {
        sessionStats.value.avgResponseTime = Math.round(
          validTimes.reduce((sum, time) => sum + time, 0) / validTimes.length
        );
      }

      // Aktualizace výkonu modelů
      if (selectedModelName.value) {
        const modelName = selectedModelName.value;
        const existing = modelPerformance.value.get(modelName) || {
          totalTime: 0,
          count: 0,
          totalTokens: 0,
          avgTime: 0
        };

        existing.totalTime += responseTime;
        existing.count++;
        existing.totalTokens += tokens;
        existing.avgTime = existing.totalTime / existing.count;

        modelPerformance.value.set(modelName, existing);
      }
    };

    const calculateEstimatedCost = (tokens) => {
      // Základní odhad nákladů (může být upraven podle skutečných cen)
      const costPerToken = 0.00002; // $0.00002 per token (průměr)
      return (tokens * costPerToken).toFixed(4);
    };

    const formatDuration = (milliseconds) => {
      const seconds = Math.floor(milliseconds / 1000);
      const minutes = Math.floor(seconds / 60);
      const hours = Math.floor(minutes / 60);

      if (hours > 0) {
        return `${hours}h ${minutes % 60}m`;
      } else if (minutes > 0) {
        return `${minutes}m ${seconds % 60}s`;
      } else {
        return `${seconds}s`;
      }
    };

    const getResponseTimeClass = (responseTime) => {
      if (responseTime < 1000) return 'fast';
      if (responseTime < 3000) return 'medium';
      return 'slow';
    };

    const getBarHeight = (time) => {
      const maxTime = Math.max(...responseTimeHistory.value);
      return maxTime > 0 ? (time / maxTime) * 100 : 0;
    };

    // Načtení dat při inicializaci komponenty
    onMounted(() => {
      loadProviders();

      // Simulace health check
      setInterval(() => {
        connectionHealth.value.latency = Math.round(Math.random() * 100 + 20);
      }, 5000);
    });

    return {
      // Stav
      providers,
      selectedProviderId,
      selectedProviderName,
      selectedProvider,
      models,
      selectedModelId,
      selectedModelName,
      messages,
      newMessage,
      isLoadingProviders,
      isLoadingModels,
      isProcessing,
      error,

      // Monitoring stav
      showMonitoring,
      sessionStats,
      lastResponseMetrics,
      responseTimeHistory,
      modelPerformance,
      apiHealth,
      connectionHealth,

      // Metody
      loadProviders,
      handleProviderSelect,
      loadProviderDetail,
      handleModelSelect,
      sendMessage,
      clearChat,
      quickTest,
      formatTime,

      // Monitoring metody
      toggleMonitoring,
      updatePerformanceMetrics,
      calculateEstimatedCost,
      formatDuration,
      getResponseTimeClass,
      getBarHeight
    };
  }
};
</script>

<style src="@/styles/chat-test.css"></style>
