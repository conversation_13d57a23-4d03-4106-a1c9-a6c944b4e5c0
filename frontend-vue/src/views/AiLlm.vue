<template>
  <div class="ai-llm-container">
    <h1>AI LLM Management</h1>
    <p>S<PERSON>r<PERSON><PERSON> poskytovatelů AI a jejich modelů LLM v databázi.</p>
    
    <!-- Načítání -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>Načítání dat...</p>
    </div>
    
    <!-- Notifikace -->
    <div v-if="errorMessage" class="error-box">
      {{ errorMessage }}
    </div>
    
    <div v-if="successMessage" class="success-box">
      {{ successMessage }}
    </div>
    
    <!-- <PERSON><PERSON><PERSON><PERSON> obsah rozdělen na dvě sekce -->
    <div class="content-wrapper">
      <!-- <PERSON><PERSON> sekce - Poskytovatelé -->
      <div class="providers-section">
        <div class="section-header">
          <h2>Poskytovatelé AI</h2>
          <button @click="openAddProviderForm" class="btn btn-primary">
            <i class="fas fa-plus"></i> Nový poskytovatel
          </button>
        </div>
        
        <!-- Seznam poskytovatelů -->
        <div v-if="providers.length === 0" class="empty-list">
          <p>Žádní poskytovatelé nebyli nalezeni.</p>
          <button @click="loadProviders" class="btn">Zkusit znovu</button>
        </div>
        
        <div v-else class="providers-list">
          <div v-for="provider in providers" :key="provider.id" class="provider-item"
              :class="{ 'selected': provider.id === selectedProviderId }">
            <div class="provider-info" @click="selectedProviderId = provider.id; handleProviderSelect()">
              <h3>{{ provider.name }}</h3>
              <div class="provider-meta">
                <span v-if="provider.is_default" class="provider-default">Výchozí</span>
                <span class="provider-model">{{ provider.model }}</span>
              </div>
            </div>
            <div class="provider-actions">
              <button @click="openEditProviderForm()" class="btn-icon" 
                      :disabled="provider.id !== selectedProviderId"
                      title="Upravit poskytovatele">
                <i class="fas fa-edit">&#9998;</i>
              </button>
              <button @click="deleteProvider(provider.id)" class="btn-icon" 
                      :disabled="provider.id !== selectedProviderId"
                      title="Smazat poskytovatele">
                <i class="fas fa-trash">&#128465;</i>
              </button>
            </div>
          </div>
        </div>
        
        <!-- Formulář pro přidání poskytovatele -->
        <div v-if="showAddProviderForm" class="form-container">
          <h3>Přidat poskytovatele</h3>
          <div class="form-group">
            <label for="provider-name">Název poskytovatele</label>
            <input type="text" id="provider-name" v-model="newProvider.name" />
          </div>
          
          <div class="form-group">
            <label for="provider-api-key">API klíč</label>
            <input type="password" id="provider-api-key" v-model="newProvider.api_key" />
          </div>
          
          <div class="form-group">
            <label for="provider-base-url">Base URL</label>
            <input type="text" id="provider-base-url" v-model="newProvider.base_url" />
          </div>
          
          <div class="form-group">
            <label for="provider-model">Výchozí model</label>
            <input type="text" id="provider-model" v-model="newProvider.model" />
          </div>
          
          <div class="form-group">
            <label for="provider-timeout">Timeout (sekundy)</label>
            <input type="number" id="provider-timeout" v-model.number="newProvider.timeout" min="1" />
          </div>
          
          <div class="form-group">
            <label for="provider-retry-count">Počet opakování</label>
            <input type="number" id="provider-retry-count" v-model.number="newProvider.retry_count" min="0" />
          </div>
          
          <div class="form-group">
            <label>
              <input type="checkbox" v-model="newProvider.is_default" />
              Nastavit jako výchozí
            </label>
          </div>
          
          <div class="form-actions">
            <button @click="createProvider()" class="btn btn-primary">Vytvořit</button>
            <button @click="closeAddProviderForm()" class="btn">Zrušit</button>
          </div>
        </div>
        
        <!-- Formulář pro editaci poskytovatele -->
        <div v-if="showEditProviderForm" class="form-container">
          <h3>Upravit poskytovatele</h3>
          <div class="form-group">
            <label for="provider-edit-name">Název poskytovatele</label>
            <input type="text" id="provider-edit-name" v-model="editedProvider.name" />
          </div>
          
          <div class="form-group">
            <label for="provider-edit-api-key">API klíč</label>
            <input type="password" id="provider-edit-api-key" v-model="editedProvider.api_key" />
          </div>
          
          <div class="form-group">
            <label for="provider-edit-base-url">Base URL</label>
            <input type="text" id="provider-edit-base-url" v-model="editedProvider.base_url" />
          </div>
          
          <div class="form-group">
            <label for="provider-edit-model">Výchozí model</label>
            <select id="provider-edit-model" v-model="editedProvider.model">
              <option v-for="(model, modelName) in editedProvider.models" :key="modelName" :value="modelName">
                {{ modelName }}
              </option>
            </select>
          </div>
          
          <div class="form-group">
            <label for="provider-edit-timeout">Timeout (sekundy)</label>
            <input type="number" id="provider-edit-timeout" v-model.number="editedProvider.timeout" min="1" />
          </div>
          
          <div class="form-group">
            <label for="provider-edit-retry-count">Počet opakování</label>
            <input type="number" id="provider-edit-retry-count" v-model.number="editedProvider.retry_count" min="0" />
          </div>
          
          <div class="form-group">
            <label>
              <input type="checkbox" v-model="editedProvider.is_default" />
              Nastavit jako výchozí
            </label>
          </div>
          
          <div class="form-actions">
            <button @click="updateProvider()" class="btn btn-primary">Uložit</button>
            <button @click="closeEditProviderForm()" class="btn">Zrušit</button>
          </div>
        </div>
      </div>
      
      <!-- Pravá sekce - Modely -->
      <div class="models-section">
        <div class="section-header">
          <h2>Modely {{ selectedProvider ? selectedProvider.name : '' }}</h2>
          <button @click="openAddModelForm()" class="btn btn-primary" :disabled="!selectedProvider">
            <i class="fas fa-plus"></i> Přidat model
          </button>
        </div>
        
        <!-- Seznam modelů -->
        <div v-if="!selectedProvider" class="empty-list">
          <p>Vyberte poskytovatele pro zobrazení jeho modelů.</p>
        </div>
        
        <div v-else-if="!selectedProvider.models || Object.keys(selectedProvider.models).length === 0" class="empty-list">
          <p>Žádné modely nebyly nalezeny pro poskytovatele {{ selectedProvider.name }}.</p>
        </div>
        
        <div v-else class="models-list">
          <div v-for="(model, modelName) in selectedProvider.models" :key="modelName" class="model-item">
            <div class="model-header">
              <h3>{{ modelName }}</h3>
              <div class="model-actions">
                <button @click="openEditModelForm(modelName)" class="btn-icon" title="Upravit model">
                  <i class="fas fa-edit">&#9998;</i>
                </button>
                <button @click="deleteModel(modelName)" class="btn-icon" title="Smazat model">
                  <i class="fas fa-trash">&#128465;</i>
                </button>
              </div>
            </div>
            <div class="model-details">
              <div class="model-property">
                <span class="property-label">Kontextové okno:</span>
                <span class="property-value">{{ model.context_window }}</span>
              </div>
              <div class="model-property">
                <span class="property-label">Max tokenů:</span>
                <span class="property-value">{{ model.max_tokens }}</span>
              </div>
              <div class="model-property">
                <span class="property-label">Schopnosti:</span>
                <div class="model-capabilities">
                  <span v-for="capability in model.capabilities" :key="capability" class="capability-tag">
                    {{ capability }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Formulář pro přidání modelu -->
        <div v-if="showAddModelForm" class="form-container">
          <h3>Přidat model</h3>
          <div class="form-group">
            <label for="model-name">Název modelu</label>
            <input type="text" id="model-name" v-model="newModel.name" />
          </div>
          
          <div class="form-group">
            <label for="model-context-window">Velikost kontextového okna</label>
            <input type="number" id="model-context-window" v-model.number="newModel.context_window" min="1" />
          </div>
          
          <div class="form-group">
            <label for="model-max-tokens">Maximální tokeny</label>
            <input type="number" id="model-max-tokens" v-model.number="newModel.max_tokens" min="1" />
          </div>
          
          <div class="form-group">
            <label>Schopnosti</label>
            <div class="capabilities-container">
              <div v-for="capability in availableCapabilities" :key="capability" class="capability-checkbox">
                <label>
                  <input type="checkbox" :value="capability" v-model="newModel.capabilities" />
                  {{ capability }}
                </label>
              </div>
            </div>
          </div>
          
          <div class="form-actions">
            <button @click="addModel()" class="btn btn-primary">Přidat</button>
            <button @click="closeAddModelForm()" class="btn">Zrušit</button>
          </div>
        </div>
        
        <!-- Formulář pro editaci modelu -->
        <div v-if="showEditModelForm" class="form-container">
          <h3>Upravit model</h3>
          <div class="form-group">
            <label for="model-edit-name">Název modelu</label>
            <input type="text" id="model-edit-name" v-model="editedModel.name" />
          </div>
          
          <div class="form-group">
            <label for="model-edit-context-window">Velikost kontextového okna</label>
            <input type="number" id="model-edit-context-window" v-model.number="editedModel.context_window" min="1" />
          </div>
          
          <div class="form-group">
            <label for="model-edit-max-tokens">Maximální tokeny</label>
            <input type="number" id="model-edit-max-tokens" v-model.number="editedModel.max_tokens" min="1" />
          </div>
          
          <div class="form-group">
            <label>Schopnosti</label>
            <div class="capabilities-container">
              <div v-for="capability in availableCapabilities" :key="capability" class="capability-checkbox">
                <label>
                  <input type="checkbox" :value="capability" v-model="editedModel.capabilities" />
                  {{ capability }}
                </label>
              </div>
            </div>
          </div>
          
          <div class="form-actions">
            <button @click="updateModel()" class="btn btn-primary">Uložit</button>
            <button @click="closeEditModelForm()" class="btn">Zrušit</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<!-- Import JavaScript logiky z externího souboru -->
<script src="../scripts/ai-llm.js"></script>

<!-- Import CSS ze samostatného souboru -->
<style src="../styles/ai-llm.css"></style>
