<template>
  <div class="ideas-container">
    <!-- Hero sekce s rychlým vstupem -->
    <div class="hero-section">
      <div class="hero-content">
        <h1 class="hero-title">
          <span class="hero-icon">💡</span>
          <PERSON><PERSON><PERSON><PERSON>, naše realizace
        </h1>
        <p class="hero-subtitle">
          Sdělte mi svou myšlenku a společně ji proměníme v realitu pomocí specializovaných AI týmů.
        </p>

        <!-- Rychlý vstup pro novou myšlenku -->
        <div class="quick-input-section">
          <div class="input-container">
            <textarea
              v-model="newIdeaText"
              placeholder="Popište svou myšlenku, nápad nebo problém, který chcete vyřešit..."
              class="idea-input"
              rows="4"
              @keydown.ctrl.enter="submitIdea"
            ></textarea>
            <div class="input-actions">
              <button
                @click="submitIdea"
                class="submit-btn primary"
                :disabled="!newIdeaText.trim() || isSubmitting"
              >
                <span v-if="isSubmitting">🔄 Zpracovávám...</span>
                <span v-else>🚀 Začít realizaci</span>
              </button>
              <button @click="showTemplates = !showTemplates" class="template-btn">
                📋 Šablony
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Templates sekce -->
    <div v-if="showTemplates" class="templates-section">
      <h3>🎯 Šablony projektů</h3>
      <div class="templates-grid">
        <div
          v-for="template in projectTemplates"
          :key="template.id"
          class="template-card"
          @click="useTemplate(template)"
        >
          <div class="template-icon">{{ template.icon }}</div>
          <div class="template-content">
            <h4>{{ template.title }}</h4>
            <p>{{ template.description }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Aktivní projekty -->
    <div class="active-projects-section">
      <div class="section-header">
        <h2>🚀 Aktivní projekty</h2>
        <div class="section-actions">
          <button @click="refreshProjects" class="refresh-btn">
            🔄 Obnovit
          </button>
        </div>
      </div>

      <div v-if="activeProjects.length === 0" class="empty-state">
        <div class="empty-icon">📝</div>
        <h3>Zatím žádné aktivní projekty</h3>
        <p>Začněte zadáním své první myšlenky výše.</p>
      </div>

      <div v-else class="projects-grid">
        <div
          v-for="project in activeProjects"
          :key="project.id"
          class="project-card"
          @click="openProject(project)"
        >
          <div class="project-header">
            <div class="project-status" :class="project.status">
              {{ getStatusIcon(project.status) }}
            </div>
            <div class="project-meta">
              <span class="project-date">{{ formatDate(project.created_at) }}</span>
            </div>
          </div>
          <div class="project-content">
            <h3>{{ project.title }}</h3>
            <p>{{ project.description }}</p>
          </div>
          <div class="project-footer">
            <div class="project-progress">
              <div class="progress-bar">
                <div
                  class="progress-fill"
                  :style="{ width: project.progress + '%' }"
                ></div>
              </div>
              <span class="progress-text">{{ project.progress }}%</span>
            </div>
            <div class="project-team">
              <span class="team-count">{{ project.agents_count }} agentů</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Historie myšlenek -->
    <div class="history-section">
      <div class="section-header">
        <h2>📚 Historie myšlenek</h2>
        <div class="section-actions">
          <select v-model="historyFilter" class="filter-select">
            <option value="all">Všechny</option>
            <option value="completed">Dokončené</option>
            <option value="archived">Archivované</option>
          </select>
        </div>
      </div>

      <div class="history-list">
        <div
          v-for="idea in filteredHistory"
          :key="idea.id"
          class="history-item"
          @click="viewIdea(idea)"
        >
          <div class="history-icon">{{ getStatusIcon(idea.status) }}</div>
          <div class="history-content">
            <h4>{{ idea.title }}</h4>
            <p>{{ idea.summary }}</p>
            <div class="history-meta">
              <span class="history-date">{{ formatDate(idea.created_at) }}</span>
              <span class="history-status">{{ idea.status }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Quick Stats -->
    <div class="stats-section">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">💡</div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.totalIdeas }}</div>
            <div class="stat-label">Celkem myšlenek</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">🚀</div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.activeProjects }}</div>
            <div class="stat-label">Aktivní projekty</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">✅</div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.completedProjects }}</div>
            <div class="stat-label">Dokončené</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">⚡</div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.avgCompletionTime }}d</div>
            <div class="stat-label">Průměrná doba</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Ideas',

  data() {
    return {
      newIdeaText: '',
      isSubmitting: false,
      showTemplates: false,
      historyFilter: 'all',

      projectTemplates: [
        {
          id: 1,
          icon: '🌐',
          title: 'Webová aplikace',
          description: 'Vytvoření webové aplikace nebo webu',
          template: 'Chci vytvořit webovou aplikaci, která...'
        },
        {
          id: 2,
          icon: '📱',
          title: 'Mobilní aplikace',
          description: 'Vývoj mobilní aplikace pro iOS/Android',
          template: 'Potřebuji mobilní aplikaci, která...'
        },
        {
          id: 3,
          icon: '🤖',
          title: 'AI řešení',
          description: 'Implementace AI/ML řešení',
          template: 'Chci využít AI pro...'
        },
        {
          id: 4,
          icon: '📊',
          title: 'Analýza dat',
          description: 'Analýza a vizualizace dat',
          template: 'Potřebuji analyzovat data...'
        },
        {
          id: 5,
          icon: '🔧',
          title: 'Automatizace',
          description: 'Automatizace procesů a úkolů',
          template: 'Chci automatizovat...'
        },
        {
          id: 6,
          icon: '📝',
          title: 'Obsah a dokumentace',
          description: 'Tvorba obsahu, dokumentace, textů',
          template: 'Potřebuji vytvořit...'
        }
      ],

      activeProjects: [],
      ideaHistory: [],

      stats: {
        totalIdeas: 0,
        activeProjects: 0,
        completedProjects: 0,
        avgCompletionTime: 0
      }
    };
  },

  computed: {
    filteredHistory() {
      if (this.historyFilter === 'all') {
        return this.ideaHistory;
      }
      return this.ideaHistory.filter(idea => idea.status === this.historyFilter);
    }
  },

  methods: {
    async submitIdea() {
      if (!this.newIdeaText.trim()) return;

      this.isSubmitting = true;

      try {
        // Zde by bylo volání API pro vytvoření nového projektu
        console.log('Submitting idea:', this.newIdeaText);

        // Simulace API volání
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Přesměrování na Collaboration sekci pro upřesnění
        this.$router.push({
          name: 'Collaboration',
          query: { newIdea: this.newIdeaText }
        });

      } catch (error) {
        console.error('Chyba při odesílání myšlenky:', error);
      } finally {
        this.isSubmitting = false;
      }
    },

    useTemplate(template) {
      this.newIdeaText = template.template;
      this.showTemplates = false;
    },

    async refreshProjects() {
      // Načtení aktivních projektů z API
      console.log('Refreshing projects...');
    },

    openProject(project) {
      this.$router.push({
        name: 'Execution',
        params: { projectId: project.id }
      });
    },

    viewIdea(idea) {
      // Zobrazení detailu myšlenky
      console.log('Viewing idea:', idea);
    },

    getStatusIcon(status) {
      const icons = {
        'planning': '📋',
        'active': '🚀',
        'completed': '✅',
        'paused': '⏸️',
        'archived': '📦'
      };
      return icons[status] || '📝';
    },

    formatDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleDateString('cs-CZ');
    }
  },

  async mounted() {
    // Načtení dat při inicializaci
    await this.refreshProjects();
  }
};
</script>

<style>
@import '@/styles/ideas.css';
</style>
