<template>
  <div class="dashboard-container">
    <h1>Dashboard</h1>
    <p class="dashboard-intro">Vítejte v systému GENT v10 - Generativní Extra Neuronová Technologie</p>

    <div class="dashboard-stats">
      <div class="stat-card" :class="{ 'online': apiAvailable, 'offline': !apiAvailable }">
        <h3>API Server</h3>
        <div class="stat-value">{{ apiAvailable ? 'Online' : 'Offline' }}</div>
        <div class="stat-info">Port: 8001</div>
        <div class="stat-indicator">{{ apiAvailable ? '🟢' : '🔴' }}</div>
      </div>

      <div class="stat-card" :class="{ 'online': databaseStatus.connected, 'offline': !databaseStatus.connected }">
        <h3>PostgreSQL</h3>
        <div class="stat-value">{{ databaseStatus.connected ? 'Connected' : 'Disconnected' }}</div>
        <div class="stat-info">{{ databaseStatus.databases }} databází</div>
        <div class="stat-indicator">{{ databaseStatus.connected ? '🟢' : '🔴' }}</div>
      </div>

      <div class="stat-card">
        <h3>LLM Modely</h3>
        <div class="stat-value">{{ llmStats.totalModels }}</div>
        <div class="stat-info">{{ llmStats.activeProviders }} poskytovatelů</div>
        <div class="stat-indicator">🤖</div>
      </div>

      <div class="stat-card">
        <h3>System Health</h3>
        <div class="stat-value">{{ systemHealth.status }}</div>
        <div class="stat-info">Uptime: {{ systemHealth.uptime }}</div>
        <div class="stat-indicator">{{ systemHealth.status === 'Healthy' ? '💚' : '⚠️' }}</div>
      </div>
    </div>

    <div class="dashboard-actions">
      <h2>Rychlé akce</h2>
      <div class="action-buttons">
        <router-link to="/config" class="action-btn">
          <div class="btn-icon">⚙️</div>
          <div class="btn-text">Konfigurace</div>
        </router-link>

        <router-link to="/chat" class="action-btn">
          <div class="btn-icon">💬</div>
          <div class="btn-text">Chat</div>
        </router-link>

        <router-link to="/agents" class="action-btn">
          <div class="btn-icon">🤖</div>
          <div class="btn-text">Agenti</div>
        </router-link>

        <router-link to="/tasks" class="action-btn">
          <div class="btn-icon">📝</div>
          <div class="btn-text">Úkoly</div>
        </router-link>

        <router-link to="/db-viewer" class="action-btn">
          <div class="btn-icon">🗄️</div>
          <div class="btn-text">Databáze</div>
        </router-link>

        <router-link to="/chat-test" class="action-btn">
          <div class="btn-icon">🧪</div>
          <div class="btn-text">Chat Test</div>
        </router-link>
      </div>
    </div>

    <!-- Real-time Activity Feed -->
    <div class="dashboard-activity">
      <h2>Aktivita systému</h2>
      <div class="activity-feed">
        <div v-if="recentActivity.length === 0" class="no-activity">
          <div class="no-activity-icon">📊</div>
          <p>Žádná nedávná aktivita</p>
        </div>
        <div v-else>
          <div
            v-for="(activity, index) in recentActivity"
            :key="index"
            class="activity-item"
            :class="activity.type"
          >
            <div class="activity-icon">{{ getActivityIcon(activity.type) }}</div>
            <div class="activity-content">
              <div class="activity-message">{{ activity.message }}</div>
              <div class="activity-time">{{ formatTime(activity.timestamp) }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="dashboard-info">
      <h2>Systémové informace</h2>
      <div class="info-card">
        <div class="info-item">
          <span class="info-label">Název:</span>
          <span class="info-value">{{ systemInfo.name }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">Verze:</span>
          <span class="info-value">{{ systemInfo.version }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">Popis:</span>
          <span class="info-value">{{ systemInfo.description }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">Prostředí:</span>
          <span class="info-value">{{ systemInfo.environment }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">Poslední update:</span>
          <span class="info-value">{{ lastUpdate }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { systemService } from '@/services/system.service';
import { llmDbService } from '@/services/llm_db.service';
import { postgresService } from '@/services/postgres.service';

export default {
  name: 'Dashboard',

  data() {
    return {
      apiAvailable: false,
      systemInfo: {
        name: 'GENT v10',
        version: '10.0.0',
        description: 'Generativní Extra Neuronová Technologie',
        environment: 'development'
      },
      databaseStatus: {
        connected: false,
        databases: 0
      },
      llmStats: {
        totalModels: 0,
        activeProviders: 0
      },
      systemHealth: {
        status: 'Checking...',
        uptime: '0m'
      },
      recentActivity: [],
      lastUpdate: 'Nikdy',
      updateInterval: null
    };
  },

  methods: {
    async checkApiStatus() {
      try {
        const response = await systemService.getSystemConfig();
        this.apiAvailable = true;
        this.systemInfo = {
          name: response.data.system?.name || 'GENT v10',
          version: response.data.system?.version || '10.0.0',
          description: response.data.system?.description || 'Generativní Extra Neuronová Technologie',
          environment: response.data.environment || 'development'
        };
        this.systemHealth.status = 'Healthy';
      } catch (error) {
        console.error('API není dostupné:', error);
        this.apiAvailable = false;
        this.systemHealth.status = 'Error';
      }
    },

    async checkDatabaseStatus() {
      try {
        const response = await postgresService.getDatabases();
        this.databaseStatus.connected = true;
        this.databaseStatus.databases = response.data?.length || 0;
        this.addActivity('database', 'PostgreSQL připojení úspěšné');
      } catch (error) {
        console.error('Database není dostupná:', error);
        this.databaseStatus.connected = false;
        this.databaseStatus.databases = 0;
        this.addActivity('error', 'PostgreSQL připojení selhalo');
      }
    },

    async loadLLMStats() {
      try {
        const providersResponse = await llmDbService.getProviders();
        if (!providersResponse.error) {
          this.llmStats.activeProviders = providersResponse.data?.length || 0;

          // Spočítáme celkový počet modelů
          let totalModels = 0;
          for (const provider of providersResponse.data || []) {
            try {
              const detailResponse = await llmDbService.getProviderDetail(provider.id);
              if (!detailResponse.error && detailResponse.data?.models) {
                totalModels += Object.keys(detailResponse.data.models).length;
              }
            } catch (error) {
              console.warn('Chyba při načítání modelů pro poskytovatele:', provider.id);
            }
          }
          this.llmStats.totalModels = totalModels;
          this.addActivity('llm', `Načteno ${totalModels} LLM modelů`);
        }
      } catch (error) {
        console.error('Chyba při načítání LLM statistik:', error);
        this.addActivity('error', 'Chyba při načítání LLM statistik');
      }
    },

    addActivity(type, message) {
      const activity = {
        type: type,
        message: message,
        timestamp: new Date()
      };

      this.recentActivity.unshift(activity);

      // Omezíme na posledních 10 aktivit
      if (this.recentActivity.length > 10) {
        this.recentActivity = this.recentActivity.slice(0, 10);
      }
    },

    getActivityIcon(type) {
      const icons = {
        'database': '🗄️',
        'llm': '🤖',
        'api': '🔌',
        'error': '❌',
        'success': '✅',
        'info': 'ℹ️'
      };
      return icons[type] || 'ℹ️';
    },

    formatTime(timestamp) {
      const now = new Date();
      const diff = now - timestamp;
      const minutes = Math.floor(diff / 60000);

      if (minutes < 1) return 'Právě teď';
      if (minutes < 60) return `${minutes}m`;

      const hours = Math.floor(minutes / 60);
      if (hours < 24) return `${hours}h`;

      const days = Math.floor(hours / 24);
      return `${days}d`;
    },

    updateUptime() {
      // Simulace uptime - v reálné aplikaci by se načítalo z API
      const startTime = new Date(Date.now() - Math.random() * 86400000); // Random start time
      const now = new Date();
      const diff = now - startTime;
      const hours = Math.floor(diff / 3600000);
      const minutes = Math.floor((diff % 3600000) / 60000);

      if (hours > 0) {
        this.systemHealth.uptime = `${hours}h ${minutes}m`;
      } else {
        this.systemHealth.uptime = `${minutes}m`;
      }
    },

    async refreshAllData() {
      this.lastUpdate = new Date().toLocaleTimeString('cs-CZ');
      await Promise.all([
        this.checkApiStatus(),
        this.checkDatabaseStatus(),
        this.loadLLMStats()
      ]);
      this.updateUptime();
      this.addActivity('info', 'Dashboard data aktualizována');
    }
  },

  async mounted() {
    // Počáteční načtení dat
    await this.refreshAllData();

    // Nastavíme interval pro automatické aktualizace každých 30 sekund
    this.updateInterval = setInterval(() => {
      this.refreshAllData();
    }, 30000);
  },

  beforeUnmount() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
    }
  }
};
</script>
