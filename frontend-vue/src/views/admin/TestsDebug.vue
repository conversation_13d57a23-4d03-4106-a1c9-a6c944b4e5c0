<template>
  <div class="tests-debug-container">
    <div class="admin-header">
      <h1>🧪 Tests & Debug</h1>
      <p>Systémové testy, diagnostika a ladění</p>
    </div>

    <!-- Tab Navigation -->
    <div class="tab-navigation">
      <button
        @click="activeTab = 'tests'"
        :class="{ active: activeTab === 'tests' }"
        class="tab-btn"
      >
        🧪 Testy
      </button>
      <button
        @click="activeTab = 'diagnostics'"
        :class="{ active: activeTab === 'diagnostics' }"
        class="tab-btn"
      >
        🔍 Diagnostika
      </button>
      <button
        @click="activeTab = 'logs'"
        :class="{ active: activeTab === 'logs' }"
        class="tab-btn"
      >
        📋 Logy
      </button>
      <button
        @click="activeTab = 'activity'"
        :class="{ active: activeTab === 'activity' }"
        class="tab-btn"
      >
        👤 User Activity
      </button>
    </div>

    <!-- Tests Tab -->
    <div v-if="activeTab === 'tests'" class="tab-content">
      <div class="tests-section">
        <div class="section-header">
          <h2>Systémové testy</h2>
          <button @click="runAllTests" class="run-all-btn" :disabled="isRunningTests">
            {{ isRunningTests ? '🔄 Spouštím...' : '🚀 Spustit všechny' }}
          </button>
        </div>

        <!-- Test Categories -->
        <div class="test-categories">
          <div
            v-for="category in testCategories"
            :key="category.id"
            class="test-category"
          >
            <div class="category-header">
              <h3>{{ category.icon }} {{ category.name }}</h3>
              <button
                @click="runCategoryTests(category.id)"
                class="run-category-btn"
                :disabled="isRunningTests"
              >
                🧪 Spustit
              </button>
            </div>

            <div class="test-list">
              <div
                v-for="test in category.tests"
                :key="test.id"
                class="test-item"
                :class="getTestStatusClass(test.status)"
              >
                <div class="test-info">
                  <div class="test-name">{{ test.name }}</div>
                  <div class="test-description">{{ test.description }}</div>
                </div>
                <div class="test-status">
                  <div class="status-icon">{{ getTestStatusIcon(test.status) }}</div>
                  <div class="status-text">{{ test.status }}</div>
                  <div v-if="test.duration" class="test-duration">{{ test.duration }}ms</div>
                </div>
                <button
                  @click="runSingleTest(test.id)"
                  class="run-test-btn"
                  :disabled="isRunningTests"
                >
                  ▶️
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Diagnostics Tab -->
    <div v-if="activeTab === 'diagnostics'" class="tab-content">
      <div class="diagnostics-section">
        <div class="section-header">
          <h2>Systémová diagnostika</h2>
          <button @click="refreshDiagnostics" class="refresh-btn">
            🔄 Obnovit
          </button>
        </div>

        <!-- System Health -->
        <div class="health-overview">
          <div class="health-card" :class="systemHealth.overall">
            <div class="health-icon">{{ getHealthIcon(systemHealth.overall) }}</div>
            <div class="health-content">
              <h3>Celkové zdraví systému</h3>
              <div class="health-status">{{ systemHealth.overall }}</div>
            </div>
          </div>
        </div>

        <!-- Component Status -->
        <div class="components-grid">
          <div
            v-for="component in systemComponents"
            :key="component.name"
            class="component-card"
            :class="component.status"
          >
            <div class="component-header">
              <h4>{{ component.icon }} {{ component.name }}</h4>
              <div class="component-status">{{ getHealthIcon(component.status) }}</div>
            </div>
            <div class="component-details">
              <div class="detail-item">
                <span>Status:</span>
                <span :class="component.status">{{ component.status }}</span>
              </div>
              <div class="detail-item">
                <span>Uptime:</span>
                <span>{{ component.uptime }}</span>
              </div>
              <div class="detail-item">
                <span>Last Check:</span>
                <span>{{ formatTime(component.lastCheck) }}</span>
              </div>
              <div v-if="component.error" class="error-message">
                {{ component.error }}
              </div>
            </div>
          </div>
        </div>

        <!-- Performance Metrics -->
        <div class="performance-metrics">
          <h3>Výkonové metriky</h3>
          <div class="metrics-grid">
            <div class="metric-item">
              <div class="metric-label">CPU Usage</div>
              <div class="metric-value">{{ performanceMetrics.cpu }}%</div>
              <div class="metric-bar">
                <div
                  class="metric-fill"
                  :style="{ width: performanceMetrics.cpu + '%' }"
                  :class="getMetricClass(performanceMetrics.cpu)"
                ></div>
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-label">Memory Usage</div>
              <div class="metric-value">{{ performanceMetrics.memory }}%</div>
              <div class="metric-bar">
                <div
                  class="metric-fill"
                  :style="{ width: performanceMetrics.memory + '%' }"
                  :class="getMetricClass(performanceMetrics.memory)"
                ></div>
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-label">Disk Usage</div>
              <div class="metric-value">{{ performanceMetrics.disk }}%</div>
              <div class="metric-bar">
                <div
                  class="metric-fill"
                  :style="{ width: performanceMetrics.disk + '%' }"
                  :class="getMetricClass(performanceMetrics.disk)"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Logs Tab -->
    <div v-if="activeTab === 'logs'" class="tab-content">
      <div class="logs-section">
        <div class="section-header">
          <h2>Systémové logy</h2>
          <div class="log-controls">
            <select v-model="selectedLogLevel" class="log-level-select">
              <option value="all">Všechny úrovně</option>
              <option value="error">Error</option>
              <option value="warning">Warning</option>
              <option value="info">Info</option>
              <option value="debug">Debug</option>
            </select>
            <button @click="refreshLogs" class="refresh-btn">🔄 Obnovit</button>
            <button @click="createTestLog" class="test-btn">➕ Test Log</button>
            <button @click="clearLogs" class="clear-btn">🗑️ Vymazat</button>
          </div>
        </div>

        <!-- Log Viewer -->
        <div class="log-viewer">
          <div
            v-for="log in filteredLogs"
            :key="log.id"
            class="log-entry"
            :class="log.level"
          >
            <div class="log-timestamp">{{ formatTime(log.timestamp) }}</div>
            <div class="log-level">{{ log.level.toUpperCase() }}</div>
            <div class="log-source">{{ log.source }}</div>
            <div class="log-message">{{ log.message }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- User Activity Tab -->
    <div v-if="activeTab === 'activity'" class="tab-content">
      <div class="activity-section">
        <div class="section-header">
          <h2>Uživatelská aktivita</h2>
          <div class="activity-controls">
            <select v-model="selectedActivityType" class="activity-type-select">
              <option value="all">Všechny typy</option>
              <option value="click">Klikání</option>
              <option value="navigate">Navigace</option>
              <option value="api_call">API volání</option>
              <option value="error">Chyby</option>
              <option value="success">Úspěchy</option>
            </select>
            <button @click="refreshActivity" class="refresh-btn">🔄 Obnovit</button>
            <button @click="clearActivity" class="clear-btn">🗑️ Vymazat</button>
          </div>
        </div>

        <!-- Activity Stats -->
        <div class="activity-stats">
          <div class="stat-card">
            <h4>📊 Celkem aktivit</h4>
            <div class="stat-value">{{ activityStats.total_activities || 0 }}</div>
          </div>
          <div class="stat-card">
            <h4>👆 Kliknutí</h4>
            <div class="stat-value">{{ activityStats.event_types?.click || 0 }}</div>
          </div>
          <div class="stat-card">
            <h4>🧭 Navigace</h4>
            <div class="stat-value">{{ activityStats.event_types?.navigate || 0 }}</div>
          </div>
          <div class="stat-card">
            <h4>❌ Chyby</h4>
            <div class="stat-value">{{ activityStats.event_types?.error || 0 }}</div>
          </div>
        </div>

        <!-- Activity Viewer -->
        <div class="activity-viewer">
          <div
            v-for="activity in filteredActivity"
            :key="activity.id"
            class="activity-entry"
            :class="activity.event_type"
          >
            <div class="activity-timestamp">{{ formatTime(new Date(activity.timestamp)) }}</div>
            <div class="activity-type">{{ getActivityTypeIcon(activity.event_type) }} {{ activity.event_type.toUpperCase() }}</div>
            <div class="activity-page">{{ activity.page }}</div>
            <div class="activity-action">{{ activity.action }}</div>
            <div v-if="activity.element" class="activity-element">{{ activity.element }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'TestsDebug',

  data() {
    return {
      activeTab: 'tests',
      isRunningTests: false,
      selectedLogLevel: 'all',
      selectedActivityType: 'all',

      testCategories: [
        {
          id: 'database',
          name: 'Database Tests',
          icon: '🗄️',
          tests: [
            { id: 'db_connection', name: 'Database Connection', description: 'Test připojení k PostgreSQL', status: 'pending' },
            { id: 'db_queries', name: 'Query Performance', description: 'Test výkonu databázových dotazů', status: 'pending' },
            { id: 'db_migrations', name: 'Migrations', description: 'Test databázových migrací', status: 'pending' }
          ]
        },
        {
          id: 'api',
          name: 'API Tests',
          icon: '🌐',
          tests: [
            { id: 'api_health', name: 'API Health Check', description: 'Test dostupnosti API', status: 'pending' },
            { id: 'api_auth', name: 'Authentication', description: 'Test autentizace', status: 'pending' },
            { id: 'api_endpoints', name: 'Endpoints', description: 'Test všech API endpointů', status: 'pending' }
          ]
        },
        {
          id: 'llm',
          name: 'LLM Tests',
          icon: '🤖',
          tests: [
            { id: 'llm_providers', name: 'LLM Providers', description: 'Test připojení k LLM poskytovatelům', status: 'pending' },
            { id: 'llm_models', name: 'Model Availability', description: 'Test dostupnosti modelů', status: 'pending' },
            { id: 'llm_performance', name: 'Response Performance', description: 'Test rychlosti odpovědí', status: 'pending' }
          ]
        }
      ],

      systemHealth: {
        overall: 'healthy'
      },

      systemComponents: [
        { name: 'PostgreSQL', icon: '🗄️', status: 'healthy', uptime: '5d 12h', lastCheck: new Date() },
        { name: 'API Server', icon: '🌐', status: 'healthy', uptime: '5d 12h', lastCheck: new Date() },
        { name: 'Frontend', icon: '💻', status: 'healthy', uptime: '5d 12h', lastCheck: new Date() },
        { name: 'LLM Services', icon: '🤖', status: 'warning', uptime: '2d 8h', lastCheck: new Date(), error: 'Some models unavailable' }
      ],

      performanceMetrics: {
        cpu: 0,
        memory: 0,
        disk: 0
      },

      logs: [],

      // User activity data
      userActivity: [],
      activityStats: {},

      // Interval pro aktualizaci metrik
      metricsInterval: null
    };
  },

  computed: {
    filteredLogs() {
      if (this.selectedLogLevel === 'all') {
        return this.logs;
      }
      return this.logs.filter(log => log.level === this.selectedLogLevel);
    },

    filteredActivity() {
      if (this.selectedActivityType === 'all') {
        return this.userActivity;
      }
      return this.userActivity.filter(activity => activity.event_type === this.selectedActivityType);
    }
  },

  async mounted() {
    // Načteme skutečné systémové metriky při načtení komponenty
    await this.loadSystemMetrics();

    // Načteme logy z databáze
    await this.loadLogs();

    // Načteme user activity
    await this.loadUserActivity();
    await this.loadActivityStats();

    // Nastavíme interval pro pravidelné aktualizace metrik (každých 30 sekund)
    this.metricsInterval = setInterval(() => {
      this.loadSystemMetrics();
      this.loadLogs();
      this.loadUserActivity();
      this.loadActivityStats();
    }, 30000);
  },

  beforeUnmount() {
    // Vyčistíme interval při zničení komponenty
    if (this.metricsInterval) {
      clearInterval(this.metricsInterval);
    }
  },

  methods: {
    async loadSystemMetrics() {
      try {
        const response = await fetch('/api/system/health');
        const data = await response.json();

        if (data.metrics) {
          this.performanceMetrics.cpu = data.metrics.cpu.usage_percent;
          this.performanceMetrics.memory = data.metrics.memory.usage_percent;
          this.performanceMetrics.disk = data.metrics.disk.usage_percent;

          // Aktualizujeme také systémové komponenty s reálnými daty
          this.updateSystemComponents(data);
        }
      } catch (error) {
        console.error('Chyba při načítání systémových metrik:', error);
      }
    },

    updateSystemComponents(healthData) {
      // Aktualizujeme uptime pro všechny komponenty
      const uptime = healthData.metrics.uptime.uptime_formatted;

      this.systemComponents.forEach(component => {
        if (component.name === 'PostgreSQL' || component.name === 'API Server' || component.name === 'Frontend') {
          component.uptime = uptime;
          component.lastCheck = new Date();

          // Nastavíme status podle zdravotního stavu
          if (component.name === 'PostgreSQL') {
            component.status = healthData.components.memory === 'healthy' ? 'healthy' : 'warning';
          } else if (component.name === 'API Server') {
            component.status = healthData.components.cpu === 'healthy' ? 'healthy' : 'warning';
          } else if (component.name === 'Frontend') {
            component.status = healthData.components.disk === 'healthy' ? 'healthy' : 'warning';
          }
        }
      });

      // Aktualizujeme celkový zdravotní stav
      this.systemHealth.overall = healthData.overall_status;
    },

    async runAllTests() {
      this.isRunningTests = true;
      // Simulace spuštění všech testů
      for (const category of this.testCategories) {
        for (const test of category.tests) {
          test.status = 'running';
          await new Promise(resolve => setTimeout(resolve, 500));
          test.status = Math.random() > 0.2 ? 'passed' : 'failed';
          test.duration = Math.floor(Math.random() * 1000) + 100;
        }
      }
      this.isRunningTests = false;
    },

    async runCategoryTests(categoryId) {
      this.isRunningTests = true;
      const category = this.testCategories.find(c => c.id === categoryId);
      if (category) {
        for (const test of category.tests) {
          test.status = 'running';
          await new Promise(resolve => setTimeout(resolve, 300));
          test.status = Math.random() > 0.2 ? 'passed' : 'failed';
          test.duration = Math.floor(Math.random() * 1000) + 100;
        }
      }
      this.isRunningTests = false;
    },

    async runSingleTest(testId) {
      this.isRunningTests = true;
      const test = this.findTestById(testId);
      if (test) {
        test.status = 'running';
        await new Promise(resolve => setTimeout(resolve, 1000));
        test.status = Math.random() > 0.2 ? 'passed' : 'failed';
        test.duration = Math.floor(Math.random() * 1000) + 100;
      }
      this.isRunningTests = false;
    },

    findTestById(testId) {
      for (const category of this.testCategories) {
        const test = category.tests.find(t => t.id === testId);
        if (test) return test;
      }
      return null;
    },

    getTestStatusClass(status) {
      return `test-${status}`;
    },

    getTestStatusIcon(status) {
      const icons = {
        'pending': '⏳',
        'running': '🔄',
        'passed': '✅',
        'failed': '❌'
      };
      return icons[status] || '❓';
    },

    getHealthIcon(status) {
      const icons = {
        'healthy': '🟢',
        'warning': '🟡',
        'error': '🔴',
        'unknown': '⚪'
      };
      return icons[status] || '❓';
    },

    getMetricClass(value) {
      if (value < 50) return 'good';
      if (value < 80) return 'warning';
      return 'critical';
    },

    formatTime(timestamp) {
      return timestamp.toLocaleTimeString('cs-CZ');
    },

    refreshDiagnostics() {
      // Refresh diagnostics data
      console.log('Refreshing diagnostics...');
    },

    async loadLogs() {
      try {
        const response = await fetch('/api/logs/');
        const logs = await response.json();

        // Převedeme timestamp na Date objekty pro správné zobrazení
        this.logs = logs.map(log => ({
          ...log,
          timestamp: new Date(log.timestamp)
        }));
      } catch (error) {
        console.error('Chyba při načítání logů:', error);
      }
    },

    async refreshLogs() {
      // Refresh logs from database
      await this.loadLogs();
    },

    async clearLogs() {
      try {
        const response = await fetch('/api/logs/', {
          method: 'DELETE'
        });

        if (response.ok) {
          this.logs = [];
          console.log('Logy byly úspěšně vymazány');
        } else {
          console.error('Chyba při mazání logů');
        }
      } catch (error) {
        console.error('Chyba při mazání logů:', error);
      }
    },

    async createTestLog() {
      try {
        const testLogs = [
          { level: 'info', message: 'Test log entry created', source: 'Frontend' },
          { level: 'warning', message: 'Test warning message', source: 'Test' },
          { level: 'error', message: 'Test error message', source: 'Test' }
        ];

        const randomLog = testLogs[Math.floor(Math.random() * testLogs.length)];

        const response = await fetch('/api/logs/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            ...randomLog,
            extra_data: { created_from: 'frontend_test' }
          })
        });

        if (response.ok) {
          // Znovu načteme logy
          await this.loadLogs();
          console.log('Testovací log byl vytvořen');
        } else {
          console.error('Chyba při vytváření testovacího logu');
        }
      } catch (error) {
        console.error('Chyba při vytváření testovacího logu:', error);
      }
    },

    async loadUserActivity() {
      try {
        const response = await fetch('/api/activity/');
        const activities = await response.json();
        this.userActivity = activities;
      } catch (error) {
        console.error('Chyba při načítání user activity:', error);
      }
    },

    async loadActivityStats() {
      try {
        const response = await fetch('/api/activity/stats');
        const stats = await response.json();
        this.activityStats = stats;
      } catch (error) {
        console.error('Chyba při načítání activity stats:', error);
      }
    },

    async refreshActivity() {
      await this.loadUserActivity();
      await this.loadActivityStats();
    },

    async clearActivity() {
      try {
        const response = await fetch('/api/activity/', {
          method: 'DELETE'
        });

        if (response.ok) {
          this.userActivity = [];
          this.activityStats = {};
          console.log('User activity byla úspěšně vymazána');
        } else {
          console.error('Chyba při mazání user activity');
        }
      } catch (error) {
        console.error('Chyba při mazání user activity:', error);
      }
    },

    getActivityTypeIcon(eventType) {
      const icons = {
        'click': '👆',
        'navigate': '🧭',
        'api_call': '🔌',
        'error': '❌',
        'success': '✅',
        'info': 'ℹ️'
      };
      return icons[eventType] || '❓';
    }
  }
};
</script>

<style>
@import '@/styles/admin-common.css';
@import '@/styles/tests-debug.css';
</style>
