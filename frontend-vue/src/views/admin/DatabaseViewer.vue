<template>
  <div class="db-viewer-page">
    <h1>Pro<PERSON><PERSON>žeč databází</h1>

    <div class="db-viewer-container">
      <!-- Panel s databázemi -->
      <div class="db-list-panel">
        <h2>Databáze</h2>

        <div v-if="loading.databases" class="loading">
          <div class="spinner"></div>
          <span>Načítání databází...</span>
        </div>

        <div v-else-if="error.databases" class="error-state">
          <div class="error-message">{{ error.databases }}</div>
          <button class="pagination-button" @click="loadDatabases">
            Zkusit znovu
          </button>
        </div>

        <div v-else-if="!databases.length" class="no-data">
          <div class="no-data-icon">📂</div>
          <p>Žádné databáze nenalezeny</p>
        </div>

        <ul v-else class="db-list">
          <li
            v-for="db in databases"
            :key="db.name + db.type + (db.file_path || '')"
            class="db-list-item"
            :class="{ active: selectedDb && selectedDb.name === db.name && selectedDb.type === db.type }"
            @click="selectDatabase(db)"
          >
            <div class="db-icon" v-if="db.type === 'sqlite'">📊</div>
            <div class="db-icon" v-else-if="db.type === 'mongodb'">📊</div>
            <div class="db-icon" v-else-if="db.type === 'mysql'">📊</div>
            <div class="db-info">
              <div class="db-name">{{ db.name }}</div>
              <div class="db-type">{{ db.description || db.type }}</div>
            </div>
          </li>
        </ul>
      </div>

      <!-- Panel s tabulkami -->
      <div class="tables-panel" v-if="selectedDb">
        <h2>Tabulky</h2>

        <div v-if="loading.tables" class="loading">
          <div class="spinner"></div>
          <span>Načítání tabulek...</span>
        </div>

        <div v-else-if="error.tables" class="error-state">
          <div class="error-message">{{ error.tables }}</div>
          <button class="pagination-button" @click="loadTables">
            Zkusit znovu
          </button>
        </div>

        <div v-else-if="!tables.length" class="no-data">
          <div class="no-data-icon">📄</div>
          <p>Žádné tabulky nenalezeny</p>
        </div>

        <ul v-else class="table-list">
          <li
            v-for="table in tables"
            :key="table.name"
            class="table-list-item"
            :class="{ active: selectedTable === table.name }"
            @click="selectTable(table.name)"
          >
            <div class="table-icon">📋</div>
            <div class="table-info">
              <div class="table-name">{{ table.name }}</div>
              <div class="table-rows" v-if="table.rows_count !== undefined">
                {{ table.rows_count }} {{ table.rows_count === 1 ? 'řádek' : (table.rows_count >= 2 && table.rows_count <= 4 ? 'řádky' : 'řádků') }}
              </div>
            </div>
          </li>
        </ul>
      </div>

      <!-- Panel s daty -->
      <div class="data-panel" v-if="selectedDb && selectedTable">
        <h2>Data: {{ selectedTable }}</h2>

        <div class="data-tools" v-if="!loading.data && !error.data && tableData">
          <div class="data-search">
            <input
              type="text"
              v-model="searchQuery"
              placeholder="Vyhledat..."
              @input="filterData"
            />
          </div>

          <div class="export-buttons">
            <button class="export-button csv-button" @click="exportCsv">
              <span class="export-icon">📄</span> Export CSV
            </button>
            <button class="export-button json-button" @click="exportJson">
              <span class="export-icon">📄</span> Export JSON
            </button>
          </div>
        </div>

        <div v-if="loading.data" class="loading">
          <div class="spinner"></div>
          <span>Načítání dat...</span>
        </div>

        <div v-else-if="error.data" class="error-state">
          <div class="error-message">{{ error.data }}</div>
          <button class="pagination-button" @click="loadData">
            Zkusit znovu
          </button>
        </div>

        <div v-else-if="!tableData || !tableData.rows || !tableData.rows.length" class="no-data">
          <div class="no-data-icon">📊</div>
          <p>Žádná data nenalezena</p>
        </div>

        <div v-else class="data-table-wrapper">
          <table class="data-table">
            <thead>
              <tr>
                <th v-for="(column, index) in tableData.columns" :key="index">
                  {{ column.name }}
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(row, rowIndex) in filteredRows" :key="rowIndex">
                <td v-for="(cell, cellIndex) in row" :key="cellIndex">
                  {{ cell !== null && cell !== undefined ? cell : '' }}
                </td>
              </tr>
            </tbody>
          </table>

          <div class="pagination" v-if="tableData.total_rows > tableData.page_size">
            <button
              class="pagination-button"
              @click="prevPage"
              :disabled="currentPage === 1 || loading.data"
            >
              &lt; Předchozí
            </button>

            <div class="pagination-info">
              Stránka {{ currentPage }} z {{ totalPages }}
              ({{ tableData.total_rows }} {{ tableData.total_rows === 1 ? 'řádek' : (tableData.total_rows >= 2 && tableData.total_rows <= 4 ? 'řádky' : 'řádků') }})
            </div>

            <button
              class="pagination-button"
              @click="nextPage"
              :disabled="currentPage >= totalPages || loading.data"
            >
              Další &gt;
            </button>
          </div>
        </div>
      </div>

      <!-- Info panel, když není vybrána databáze -->
      <div class="data-panel" v-if="!selectedDb">
        <div class="info-state">
          <h2>Prohlížeč databází</h2>
          <p>Vyberte databázi ze seznamu vlevo pro zobrazení tabulek a dat.</p>
        </div>
      </div>
    </div>

    <!-- Database Monitoring Panel -->
    <div class="db-monitoring-panel">
      <div class="monitoring-header">
        <h3>📊 Database Monitoring</h3>
        <button
          class="toggle-monitoring"
          @click="toggleMonitoring"
          :class="{ active: showMonitoring }"
        >
          {{ showMonitoring ? '🔽' : '🔼' }}
        </button>
      </div>

      <div v-if="showMonitoring" class="monitoring-content">
        <!-- Connection Status -->
        <div class="connection-section">
          <h4>🔌 Stav připojení</h4>
          <div class="connection-grid">
            <div class="connection-item" :class="{ connected: connectionStatus.connected }">
              <div class="connection-icon">{{ connectionStatus.connected ? '🟢' : '🔴' }}</div>
              <div class="connection-info">
                <div class="connection-label">PostgreSQL</div>
                <div class="connection-value">{{ connectionStatus.connected ? 'Připojeno' : 'Odpojeno' }}</div>
              </div>
            </div>
            <div class="connection-item">
              <div class="connection-icon">⏱️</div>
              <div class="connection-info">
                <div class="connection-label">Latence</div>
                <div class="connection-value">{{ connectionStatus.latency }}ms</div>
              </div>
            </div>
            <div class="connection-item">
              <div class="connection-icon">🔄</div>
              <div class="connection-info">
                <div class="connection-label">Aktivní spojení</div>
                <div class="connection-value">{{ connectionStatus.activeConnections }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Database Statistics -->
        <div v-if="selectedDb" class="stats-section">
          <h4>📈 Statistiky databáze</h4>
          <div class="db-stats-grid">
            <div class="stat-item">
              <div class="stat-icon">📊</div>
              <div class="stat-content">
                <div class="stat-label">Tabulky</div>
                <div class="stat-value">{{ dbStats.tableCount }}</div>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon">📄</div>
              <div class="stat-content">
                <div class="stat-label">Celkové řádky</div>
                <div class="stat-value">{{ formatNumber(dbStats.totalRows) }}</div>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon">💾</div>
              <div class="stat-content">
                <div class="stat-label">Velikost DB</div>
                <div class="stat-value">{{ dbStats.size }}</div>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon">🕒</div>
              <div class="stat-content">
                <div class="stat-label">Poslední update</div>
                <div class="stat-value">{{ dbStats.lastUpdate }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Query Performance -->
        <div v-if="queryMetrics.length > 0" class="performance-section">
          <h4>⚡ Výkon dotazů</h4>
          <div class="query-metrics">
            <div
              v-for="(metric, index) in queryMetrics.slice(-5)"
              :key="index"
              class="query-metric-item"
              :class="getQueryPerformanceClass(metric.duration)"
            >
              <div class="query-info">
                <div class="query-type">{{ metric.type }}</div>
                <div class="query-target">{{ metric.target }}</div>
              </div>
              <div class="query-timing">
                <div class="query-duration">{{ metric.duration }}ms</div>
                <div class="query-time">{{ formatTime(metric.timestamp) }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Table Performance Chart -->
        <div v-if="selectedTable && tablePerformance.length > 0" class="chart-section">
          <h4>📊 Výkon tabulky: {{ selectedTable }}</h4>
          <div class="table-performance-chart">
            <div
              v-for="(perf, index) in tablePerformance.slice(-10)"
              :key="index"
              class="perf-bar"
              :style="{ height: getPerformanceBarHeight(perf.loadTime) + '%' }"
              :title="`${perf.loadTime}ms - ${perf.rowCount} řádků`"
            ></div>
          </div>
          <div class="chart-labels">
            <span>Posledních 10 načtení</span>
          </div>
        </div>

        <!-- System Health -->
        <div class="health-section">
          <h4>💚 Zdraví systému</h4>
          <div class="health-indicators">
            <div class="health-item" :class="{ healthy: systemHealth.database === 'healthy' }">
              <div class="health-icon">{{ systemHealth.database === 'healthy' ? '🟢' : '⚠️' }}</div>
              <div class="health-label">Databáze</div>
              <div class="health-value">{{ systemHealth.database }}</div>
            </div>
            <div class="health-item" :class="{ healthy: systemHealth.queries === 'healthy' }">
              <div class="health-icon">{{ systemHealth.queries === 'healthy' ? '🟢' : '⚠️' }}</div>
              <div class="health-label">Dotazy</div>
              <div class="health-value">{{ systemHealth.queries }}</div>
            </div>
            <div class="health-item" :class="{ healthy: systemHealth.memory === 'healthy' }">
              <div class="health-icon">{{ systemHealth.memory === 'healthy' ? '🟢' : '⚠️' }}</div>
              <div class="health-label">Paměť</div>
              <div class="health-value">{{ systemHealth.memory }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { dbViewerService } from '@/services/db_viewer.service';
import { postgresService } from '@/services/postgres.service';

export default {
  name: 'DbViewer',
  data() {
    return {
      // Databáze
      databases: [],
      selectedDb: null,

      // Tabulky
      tables: [],
      selectedTable: null,

      // Data tabulky
      tableData: null,
      filteredRows: [],
      searchQuery: '',
      currentPage: 1,
      pageSize: 25, // Velikost stránky

      // Stavy načítání
      loading: {
        databases: false,
        tables: false,
        data: false
      },

      // Chybové stavy
      error: {
        databases: null,
        tables: null,
        data: null
      },

      // Monitoring stav
      showMonitoring: true,
      connectionStatus: {
        connected: false,
        latency: 0,
        activeConnections: 0
      },
      dbStats: {
        tableCount: 0,
        totalRows: 0,
        size: 'N/A',
        lastUpdate: 'Nikdy'
      },
      queryMetrics: [],
      tablePerformance: [],
      systemHealth: {
        database: 'checking',
        queries: 'checking',
        memory: 'checking'
      }
    };
  },

  computed: {
    // Celkový počet stránek
    totalPages() {
      if (!this.tableData || !this.tableData.total_rows) return 1;
      return Math.ceil(this.tableData.total_rows / this.pageSize);
    }
  },

  created() {
    // Načtení databází
    this.loadDatabases();

    // Inicializace monitoringu
    this.initializeMonitoring();
  },

  methods: {
    // Načíst seznam databází
    async loadDatabases() {
      this.loading.databases = true;
      this.error.databases = null;

      // Performance tracking
      const startTime = performance.now();

      try {
        console.time('loadDatabases');

        // Načíst standardní databáze přes dbViewerService
        let standardDatabases = [];
        try {
          const dbResponse = await dbViewerService.getDatabases();
          standardDatabases = dbResponse.data || [];
        } catch (dbError) {
          console.warn('Chyba při načítání standardních databází:', dbError);
        }

        // Načíst PostgreSQL databáze přes postgresService
        let postgresqlDatabases = [];
        try {
          const pgResponse = await postgresService.getDatabases();

          // Transformace formátu dat na stejný formát jako používá dbViewerService
          postgresqlDatabases = (pgResponse.data || []).map(db => ({
            name: db.name,
            type: 'postgresql',
            description: db.description || 'PostgreSQL databáze',
            tables_count: db.tables_count || 0
          }));
        } catch (pgError) {
          console.warn('Chyba při načítání PostgreSQL databází:', pgError);
        }

        // Sloučit standardní a PostgreSQL databáze
        this.databases = [...standardDatabases, ...postgresqlDatabases];

        // Performance tracking
        const endTime = performance.now();
        const duration = Math.round(endTime - startTime);
        this.addQueryMetric('LOAD_DATABASES', 'databases', duration);

        // Update connection status
        this.connectionStatus.connected = this.databases.length > 0;
        this.connectionStatus.latency = duration;

        console.timeEnd('loadDatabases');
      } catch (error) {
        console.error('Chyba při načítání databází:', error);
        this.error.databases = 'Nepodařilo se načíst seznam databází.';
        this.connectionStatus.connected = false;
      } finally {
        this.loading.databases = false;
      }
    },

    // Vybrat databázi
    selectDatabase(db) {
      this.selectedDb = db;
      this.selectedTable = null;
      this.tableData = null;
      this.filteredRows = [];
      this.currentPage = 1;

      // Reset database stats
      this.dbStats = {
        tableCount: 0,
        totalRows: 0,
        size: 'N/A',
        lastUpdate: 'Načítání...'
      };

      this.loadTables();
    },

    // Načíst seznam tabulek
    async loadTables() {
      if (!this.selectedDb) return;

      this.loading.tables = true;
      this.error.tables = null;

      // Performance tracking
      const startTime = performance.now();

      try {
        console.time('loadTables');
        console.log('Začínám načítání tabulek pro databázi:', this.selectedDb);

        let response;

        // Použijeme různé služby podle typu databáze
        if (this.selectedDb.type === 'postgresql') {
          // Přímé volání API pro PostgreSQL databáze
          console.log('Používám přímé volání API pro načtení tabulek z PostgreSQL:', this.selectedDb.name);

          try {
            // Použití fetch API místo axios/apiService pro obejití možných problémů
            const url = `/api/postgres/tables?db_name=${encodeURIComponent(this.selectedDb.name)}`;
            console.log('Volám URL:', url);

            const fetchResponse = await fetch(url, {
              method: 'GET',
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
              }
            });

            if (!fetchResponse.ok) {
              throw new Error(`HTTP error! status: ${fetchResponse.status}`);
            }

            const tablesData = await fetchResponse.json();
            console.log('Přímé volání API vrátilo:', tablesData);

            // Nastav odpověď pro správné zpracování
            response = { data: tablesData };
          } catch (directError) {
            console.error('Přímé volání API selhalo:', directError);

            // Zkusíme záložní metodu přes postgresService
            console.log('Zkouším záložní metodu přes postgresService...');
            response = await postgresService.getTables(this.selectedDb.name);
          }
        } else {
          // Použít standardní dbViewerService pro ostatní databáze
          console.log('Používám dbViewerService pro načtení tabulek z', this.selectedDb.type, ':', this.selectedDb.name);
          response = await dbViewerService.getTables(
            this.selectedDb.name,
            this.selectedDb.type,
            this.selectedDb.file_path
          );
        }

        console.log('Odpověď z API pro tabulky:', response);
        this.tables = response.data;
        console.log('Nastaveno this.tables:', this.tables);

        // Performance tracking
        const endTime = performance.now();
        const duration = Math.round(endTime - startTime);
        this.addQueryMetric('LOAD_TABLES', this.selectedDb.name, duration);

        // Update database stats
        this.updateDbStats();

        console.timeEnd('loadTables');
      } catch (error) {
        console.error('Chyba při načítání tabulek - detail:', error);
        if (error.response) {
          console.error('Odpověď ze serveru:', error.response.data);
          console.error('Status kód:', error.response.status);
        }
        this.error.tables = 'Nepodařilo se načíst seznam tabulek.';
      } finally {
        this.loading.tables = false;
      }
    },

    // Vybrat tabulku
    selectTable(tableName) {
      this.selectedTable = tableName;
      this.currentPage = 1;
      this.searchQuery = '';
      this.loadData();
    },

    // Načíst data tabulky - přímé napojení na DB
    async loadData() {
      if (!this.selectedDb || !this.selectedTable) return;

      this.loading.data = true;
      this.error.data = null;

      // Performance tracking
      const startTime = performance.now();

      try {
        console.time(`loadData:${this.currentPage}`);
        console.log(`Začínám načítání dat z tabulky ${this.selectedTable} v databázi ${this.selectedDb.name} (stránka ${this.currentPage})`);

        let response;

        // Použijeme různé služby podle typu databáze
        if (this.selectedDb.type === 'postgresql') {
          // Použít přímé volání API pro PostgreSQL databáze
          console.log('Používám přímé volání API pro načtení dat z PostgreSQL tabulky:', this.selectedTable);

          try {
            // Použití fetch API místo axios/apiService pro obejití možných problémů
            const url = `/api/postgres/data?db_name=${encodeURIComponent(this.selectedDb.name)}&table_name=${encodeURIComponent(this.selectedTable)}&schema=public&page=${this.currentPage}&page_size=${this.pageSize}`;
            console.log('Volám URL:', url);

            const fetchResponse = await fetch(url, {
              method: 'GET',
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json'
              }
            });

            if (!fetchResponse.ok) {
              throw new Error(`HTTP error! status: ${fetchResponse.status}`);
            }

            const tableData = await fetchResponse.json();
            console.log('Přímé volání API vrátilo data tabulky:', tableData);

            // Nastav odpověď pro správné zpracování
            response = { data: tableData };
          } catch (directError) {
            console.error('Přímé volání API pro data selhalo:', directError);

            // Zkusíme záložní metodu přes postgresService
            console.log('Zkouším záložní metodu přes postgresService pro načtení dat...');
            response = await postgresService.getTableData(
              this.selectedDb.name,
              this.selectedTable,
              'public',  // Výchozí schéma
              this.currentPage,
              this.pageSize
            );
          }
        } else {
          // Použít standardní dbViewerService pro ostatní databáze
          console.log('Používám dbViewerService pro načtení dat z', this.selectedDb.type, 'tabulky:', this.selectedTable);
          response = await dbViewerService.getTableData(
            this.selectedDb.name,
            this.selectedDb.type,
            this.selectedTable,
            this.selectedDb.file_path,
            this.currentPage,
            this.pageSize
          );
        }

        console.log('Odpověď z API pro data tabulky:', response);
        this.tableData = response.data;
        this.filteredRows = this.tableData.rows;
        console.log('Nastaveno this.tableData. Počet řádků:', this.tableData.rows.length);

        // Performance tracking
        const endTime = performance.now();
        const duration = Math.round(endTime - startTime);
        const rowCount = this.tableData.rows ? this.tableData.rows.length : 0;

        this.addQueryMetric('LOAD_DATA', this.selectedTable, duration);
        this.addTablePerformance(duration, rowCount);

        console.timeEnd(`loadData:${this.currentPage}`);
      } catch (error) {
        console.error('Chyba při načítání dat - detail:', error);
        if (error.response) {
          console.error('Odpověď ze serveru:', error.response.data);
          console.error('Status kód:', error.response.status);
        }
        this.error.data = 'Nepodařilo se načíst data z tabulky.';
      } finally {
        this.loading.data = false;
      }
    },

    // Filtrovat data podle vyhledávání
    filterData() {
      if (!this.tableData || !this.tableData.rows) return;

      if (!this.searchQuery.trim()) {
        this.filteredRows = this.tableData.rows;
        return;
      }

      const query = this.searchQuery.toLowerCase();
      console.time('filterData');

      // Vyhledávání
      this.filteredRows = this.tableData.rows.filter(row => {
        return row.some(cell => {
          if (cell === null || cell === undefined) return false;
          return String(cell).toLowerCase().includes(query);
        });
      });

      console.timeEnd('filterData');
    },

    // Předchozí stránka
    prevPage() {
      if (this.currentPage > 1 && !this.loading.data) {
        this.currentPage--;
        this.loadData();
      }
    },

    // Další stránka
    nextPage() {
      if (this.currentPage < this.totalPages && !this.loading.data) {
        this.currentPage++;
        this.loadData();
      }
    },

    // Export do CSV
    exportCsv() {
      if (!this.tableData || !this.tableData.columns || !this.tableData.rows) return;

      const csvContent = dbViewerService.exportToCsv(this.tableData.columns, this.tableData.rows);
      const filename = `${this.selectedTable}_${new Date().toISOString().split('T')[0]}.csv`;

      dbViewerService.downloadFile(csvContent, filename, 'text/csv');
    },

    // Export do JSON
    exportJson() {
      if (!this.tableData || !this.tableData.columns || !this.tableData.rows) return;

      const jsonContent = dbViewerService.exportToJson(this.tableData.columns, this.tableData.rows);
      const filename = `${this.selectedTable}_${new Date().toISOString().split('T')[0]}.json`;

      dbViewerService.downloadFile(jsonContent, filename, 'application/json');
    },

    // Monitoring funkce
    initializeMonitoring() {
      // Simulace health check
      this.systemHealth = {
        database: 'healthy',
        queries: 'healthy',
        memory: 'healthy'
      };

      // Pravidelné aktualizace connection status
      setInterval(() => {
        if (this.connectionStatus.connected) {
          this.connectionStatus.activeConnections = Math.floor(Math.random() * 5) + 1;
          this.connectionStatus.latency = Math.floor(Math.random() * 50) + 10;
        }
      }, 5000);
    },

    toggleMonitoring() {
      this.showMonitoring = !this.showMonitoring;
    },

    addQueryMetric(type, target, duration) {
      const metric = {
        type,
        target,
        duration,
        timestamp: new Date()
      };

      this.queryMetrics.push(metric);

      // Omezíme na posledních 20 metrik
      if (this.queryMetrics.length > 20) {
        this.queryMetrics = this.queryMetrics.slice(-20);
      }
    },

    addTablePerformance(loadTime, rowCount) {
      const performance = {
        loadTime,
        rowCount,
        timestamp: new Date()
      };

      this.tablePerformance.push(performance);

      // Omezíme na posledních 20 záznamů
      if (this.tablePerformance.length > 20) {
        this.tablePerformance = this.tablePerformance.slice(-20);
      }
    },

    updateDbStats() {
      if (!this.selectedDb) return;

      this.dbStats.tableCount = this.tables.length;

      // Spočítáme celkové řádky z tabulek
      let totalRows = 0;
      this.tables.forEach(table => {
        if (table.rows_count !== undefined) {
          totalRows += table.rows_count;
        }
      });
      this.dbStats.totalRows = totalRows;

      // Simulace velikosti databáze
      const sizeInMB = Math.floor(totalRows / 1000) + Math.floor(Math.random() * 100);
      this.dbStats.size = sizeInMB > 1024 ?
        `${(sizeInMB / 1024).toFixed(1)} GB` :
        `${sizeInMB} MB`;

      this.dbStats.lastUpdate = new Date().toLocaleTimeString('cs-CZ');
    },

    getQueryPerformanceClass(duration) {
      if (duration < 100) return 'fast';
      if (duration < 500) return 'medium';
      return 'slow';
    },

    getPerformanceBarHeight(loadTime) {
      const maxTime = Math.max(...this.tablePerformance.map(p => p.loadTime));
      return maxTime > 0 ? (loadTime / maxTime) * 100 : 0;
    },

    formatNumber(num) {
      if (num >= 1000000) {
        return (num / 1000000).toFixed(1) + 'M';
      } else if (num >= 1000) {
        return (num / 1000).toFixed(1) + 'K';
      }
      return num.toString();
    },

    formatTime(timestamp) {
      const now = new Date();
      const diff = now - timestamp;
      const minutes = Math.floor(diff / 60000);

      if (minutes < 1) return 'Právě teď';
      if (minutes < 60) return `${minutes}m`;

      const hours = Math.floor(minutes / 60);
      if (hours < 24) return `${hours}h`;

      const days = Math.floor(hours / 24);
      return `${days}d`;
    }
  }
};
</script>

<style>
@import '@/styles/db-viewer.css';
</style>
