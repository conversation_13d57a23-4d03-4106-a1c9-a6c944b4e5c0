<template>
  <div class="llm-management-container">
    <div class="admin-header">
      <h1>🤖 LLM Management</h1>
      <p>Správa LLM poskytovatelů, modelů a testování výkonu</p>
    </div>

    <!-- Tab Navigation -->
    <div class="tab-navigation">
      <button
        @click="activeTab = 'providers'"
        :class="{ active: activeTab === 'providers' }"
        class="tab-btn"
      >
        🏢 Poskytovatelé
      </button>
      <button
        @click="activeTab = 'management'"
        :class="{ active: activeTab === 'management' }"
        class="tab-btn"
      >
        ⚙️ Správa
      </button>
      <button
        @click="switchTab('testing')"
        :class="{ active: activeTab === 'testing' }"
        class="tab-btn"
      >
        🧪 Testování
      </button>
      <button
        @click="activeTab = 'performance'"
        :class="{ active: activeTab === 'performance' }"
        class="tab-btn"
      >
        📊 Výkon
      </button>
      <button
        @click="switchTab('communication')"
        :class="{ active: activeTab === 'communication' }"
        class="tab-btn"
      >
        🤝 Kom mezi LLM
      </button>
    </div>

    <!-- Providers Tab -->
    <div v-if="activeTab === 'providers'" class="tab-content">
      <div class="providers-section">
        <div class="section-header">
          <h2>LLM Poskytovatelé a Modely</h2>
          <div class="header-actions">
            <button @click="refreshProviders" class="refresh-btn">
              🔄 Obnovit
            </button>
            <button @click="testAllProviders" class="test-btn" :disabled="isTestingProviders">
              {{ isTestingProviders ? '🔄 Testování...' : '🧪 Test všech' }}
            </button>
          </div>
        </div>

        <!-- Providers Grid -->
        <div class="providers-grid">
          <div
            v-for="provider in providers"
            :key="provider.id"
            class="provider-card"
            @click="selectProvider(provider)"
            :class="{ selected: selectedProvider?.id === provider.id }"
          >
            <div class="provider-header">
              <h3>{{ provider.name }}</h3>
              <div class="provider-status" :class="provider.status">
                {{ provider.status === 'active' ? '🟢' : '🔴' }}
              </div>
            </div>
            <div class="provider-info">
              <p>{{ provider.description }}</p>
              <div class="provider-stats">
                <span>Modely: {{ provider.models_count || 0 }}</span>
                <span>API: {{ provider.api_version || 'N/A' }}</span>
                <span>Klíč: {{ provider.api_key }}</span>
              </div>
              <div class="provider-actions">
                <button @click.stop="testProvider(provider)" class="test-provider-btn" :disabled="provider.testing">
                  {{ provider.testing ? '🔄' : '🧪' }} Test
                </button>
                <button @click.stop="editProvider(provider)" class="edit-provider-btn">
                  ✏️ Upravit
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Selected Provider Details -->
        <div v-if="selectedProvider" class="provider-details">
          <div class="provider-detail-header">
            <h3>Modely poskytovatele: {{ selectedProvider.name }}</h3>
            <div class="provider-detail-stats">
              <div class="stat-item">
                <span class="stat-label">Celkem modelů:</span>
                <span class="stat-value">{{ selectedProviderModels.length }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Aktivní modely:</span>
                <span class="stat-value">{{ selectedProviderModels.filter(m => m.available).length }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Průměrný context:</span>
                <span class="stat-value">{{ getAverageContext() }}</span>
              </div>
            </div>
          </div>

          <div class="models-grid">
            <div
              v-for="model in selectedProviderModels"
              :key="model.id"
              class="model-card"
              :class="{ 'model-default': model.is_default }"
            >
              <div class="model-header">
                <h4>{{ model.name }}</h4>
                <div class="model-badges">
                  <span v-if="model.is_default" class="badge default">Default</span>
                  <div class="model-status">
                    {{ model.available ? '✅' : '❌' }}
                  </div>
                </div>
              </div>
              <div class="model-info">
                <div class="model-specs">
                  <span>Context: {{ formatNumber(model.context_length) || 'N/A' }}</span>
                  <span>Max Output: {{ formatNumber(model.max_tokens_output) || 'N/A' }}</span>
                  <span>Cost: {{ model.cost_per_token || 'N/A' }}</span>
                </div>
                <div class="model-capabilities">
                  <span v-for="capability in getModelCapabilities(model)" :key="capability" class="capability-tag">
                    {{ capability }}
                  </span>
                </div>
                <p class="model-description">{{ model.description }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Management Tab -->
    <div v-if="activeTab === 'management'" class="tab-content">
      <div class="management-section">
        <div class="section-header">
          <h2>⚙️ Správa poskytovatelů a modelů</h2>
          <div class="header-actions">
            <button @click="showCreateProviderForm" class="create-btn">
              ➕ Nový poskytovatel
            </button>
            <button @click="showCreateModelForm" class="create-btn">
              🤖 Nový model
            </button>
          </div>
        </div>

        <!-- Sub-tabs for Management -->
        <div class="sub-tab-navigation">
          <button
            @click="managementSubTab = 'providers'"
            :class="{ active: managementSubTab === 'providers' }"
            class="sub-tab-btn"
          >
            🏢 Poskytovatelé
          </button>
          <button
            @click="managementSubTab = 'models'"
            :class="{ active: managementSubTab === 'models' }"
            class="sub-tab-btn"
          >
            🤖 Modely
          </button>
        </div>

        <!-- Providers Management -->
        <div v-if="managementSubTab === 'providers'" class="providers-management">
          <div class="management-header">
            <h3>Správa poskytovatelů</h3>
            <div class="header-actions">
              <button @click="addOllamaProvider" class="ollama-btn" title="Automaticky přidat Ollama provider s modely">
                🦙 Přidat Ollama
              </button>
              <button @click="addOllama2Provider" class="ollama2-btn" title="Automaticky přidat Ollama2 provider s modely">
                🦙 Přidat Ollama2
              </button>
              <button @click="addLMStudioProvider" class="lmstudio-btn" title="Automaticky přidat LM Studio provider s modely">
                🏭 Přidat LM Studio
              </button>
              <button @click="addProvider" class="add-btn">
                ➕ Přidat poskytovatele
              </button>
            </div>
          </div>
          <div class="management-table">
            <div class="table-header">
              <div>ID</div>
              <div>Název</div>
              <div>API URL</div>
              <div>API Klíč</div>
              <div>Aktivní</div>
              <div>Modely</div>
              <div>Akce</div>
            </div>
            <div
              v-for="provider in providers"
              :key="provider.id"
              class="table-row"
            >
              <div>{{ provider.id }}</div>
              <div>{{ provider.name }}</div>
              <div class="url-cell">{{ provider.base_url || 'N/A' }}</div>
              <div>{{ provider.api_key }}</div>
              <div>
                <span :class="provider.status === 'active' ? 'status-active' : 'status-inactive'">
                  {{ provider.status === 'active' ? '✅' : '❌' }}
                </span>
              </div>
              <div>{{ provider.models_count }}</div>
              <div class="actions-cell">
                <button @click="editProvider(provider)" class="edit-btn" title="Upravit">
                  ✏️
                </button>
                <button @click="deleteProvider(provider)" class="delete-btn" title="Smazat">
                  🗑️
                </button>
                <button @click="testProvider(provider)" class="test-btn" title="Test">
                  🧪
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Models Management -->
        <div v-if="managementSubTab === 'models'" class="models-management">
          <div class="management-table">
            <div class="table-header">
              <div>ID</div>
              <div>Název</div>
              <div>Poskytovatel</div>
              <div>Context</div>
              <div>Max Tokens</div>
              <div>Výchozí</div>
              <div>Aktivní</div>
              <div>Akce</div>
            </div>
            <div
              v-for="model in models"
              :key="model.id"
              class="table-row"
            >
              <div>{{ model.model_id }}</div>
              <div>{{ model.model_name }}</div>
              <div>{{ model.provider_name }}</div>
              <div>{{ formatNumber(model.context_length) }}</div>
              <div>{{ formatNumber(model.max_tokens_output) }}</div>
              <div>
                <span :class="model.is_default ? 'status-default' : ''">
                  {{ model.is_default ? '⭐' : '' }}
                </span>
              </div>
              <div>
                <span :class="model.is_active ? 'status-active' : 'status-inactive'">
                  {{ model.is_active ? '✅' : '❌' }}
                </span>
              </div>
              <div class="actions-cell">
                <button @click="editModel(model)" class="edit-btn" title="Upravit">
                  ✏️
                </button>
                <button @click="deleteModel(model)" class="delete-btn" title="Smazat">
                  🗑️
                </button>
                <button @click="toggleModelDefault(model)" class="star-btn" title="Nastavit jako výchozí">
                  {{ model.is_default ? '⭐' : '☆' }}
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Provider Form Modal -->
        <div v-if="showProviderForm" class="modal-overlay" @click="closeProviderForm">
          <div class="modal-content" @click.stop>
            <div class="modal-header">
              <h3>{{ editingProvider ? 'Upravit poskytovatele' : 'Nový poskytovatel' }}</h3>
              <button @click="closeProviderForm" class="close-btn">✕</button>
            </div>
            <form @submit.prevent="saveProvider" class="provider-form">
              <div class="form-group">
                <label for="provider-name">Název poskytovatele *</label>
                <input
                  id="provider-name"
                  v-model="providerForm.name"
                  type="text"
                  required
                  placeholder="např. OpenAI, Anthropic"
                />
              </div>
              <div class="form-group">
                <label for="provider-url">API Base URL</label>
                <input
                  id="provider-url"
                  v-model="providerForm.base_url"
                  type="url"
                  placeholder="https://api.example.com/v1"
                />
              </div>
              <div class="form-group">
                <label for="provider-key">API Klíč</label>
                <input
                  id="provider-key"
                  v-model="providerForm.api_key"
                  type="password"
                  placeholder="Zadejte API klíč"
                />
              </div>
              <div class="form-group">
                <label for="provider-version">API Verze</label>
                <input
                  id="provider-version"
                  v-model="providerForm.api_version"
                  type="text"
                  placeholder="v1"
                />
              </div>
              <div class="form-group">
                <label for="provider-auth">Typ autentizace</label>
                <select id="provider-auth" v-model="providerForm.auth_type">
                  <option value="api_key">API Key</option>
                  <option value="bearer">Bearer Token</option>
                  <option value="basic">Basic Auth</option>
                </select>
              </div>
              <div class="form-group">
                <label for="provider-rate-limit">Rate Limit (req/min)</label>
                <input
                  id="provider-rate-limit"
                  v-model.number="providerForm.rate_limit"
                  type="number"
                  placeholder="1000"
                />
              </div>
              <div class="form-group checkbox-group">
                <label>
                  <input
                    v-model="providerForm.api_key_required"
                    type="checkbox"
                  />
                  API klíč je povinný
                </label>
              </div>
              <div class="form-group checkbox-group">
                <label>
                  <input
                    v-model="providerForm.is_active"
                    type="checkbox"
                  />
                  Aktivní
                </label>
              </div>
              <div class="form-actions">
                <button type="button" @click="closeProviderForm" class="cancel-btn">
                  Zrušit
                </button>
                <button type="submit" class="save-btn" :disabled="isSaving">
                  {{ isSaving ? 'Ukládám...' : 'Uložit' }}
                </button>
              </div>
            </form>
          </div>
        </div>

        <!-- Model Form Modal -->
        <div v-if="showModelForm" class="modal-overlay" @click="closeModelForm">
          <div class="modal-content" @click.stop>
            <div class="modal-header">
              <h3>{{ editingModel ? 'Upravit model' : 'Nový model' }}</h3>
              <button @click="closeModelForm" class="close-btn">✕</button>
            </div>
            <form @submit.prevent="saveModel" class="model-form">
              <div class="form-group">
                <label for="model-provider">Poskytovatel *</label>
                <select id="model-provider" v-model="modelForm.provider_id" required>
                  <option value="">-- Vyberte poskytovatele --</option>
                  <option
                    v-for="provider in providers"
                    :key="provider.id"
                    :value="provider.id"
                  >
                    {{ provider.name }}
                  </option>
                </select>
              </div>
              <div class="form-group">
                <label for="model-name">Název modelu *</label>
                <input
                  id="model-name"
                  v-model="modelForm.model_name"
                  type="text"
                  required
                  placeholder="např. gpt-4o, claude-3-sonnet"
                />
              </div>
              <div class="form-group">
                <label for="model-identifier">Model Identifier *</label>
                <input
                  id="model-identifier"
                  v-model="modelForm.model_identifier"
                  type="text"
                  required
                  placeholder="Identifikátor pro API volání"
                />
              </div>
              <div class="form-group">
                <label for="model-context">Context Length</label>
                <input
                  id="model-context"
                  v-model.number="modelForm.context_length"
                  type="number"
                  placeholder="128000"
                />
              </div>
              <div class="form-group">
                <label for="model-max-tokens">Max Output Tokens</label>
                <input
                  id="model-max-tokens"
                  v-model.number="modelForm.max_tokens_output"
                  type="number"
                  placeholder="4096"
                />
              </div>
              <div class="form-group">
                <label for="model-temperature">Temperature</label>
                <input
                  id="model-temperature"
                  v-model.number="modelForm.temperature"
                  type="number"
                  step="0.1"
                  min="0"
                  max="2"
                  placeholder="0.7"
                />
              </div>
              <div class="form-group">
                <label>Capabilities</label>
                <div class="capabilities-grid">
                  <label class="capability-item">
                    <input v-model="modelForm.capabilities.text" type="checkbox" />
                    Text
                  </label>
                  <label class="capability-item">
                    <input v-model="modelForm.capabilities.code" type="checkbox" />
                    Code
                  </label>
                  <label class="capability-item">
                    <input v-model="modelForm.capabilities.vision" type="checkbox" />
                    Vision
                  </label>
                  <label class="capability-item">
                    <input v-model="modelForm.capabilities.function_calling" type="checkbox" />
                    Function Calling
                  </label>
                  <label class="capability-item">
                    <input v-model="modelForm.capabilities.reasoning" type="checkbox" />
                    Reasoning
                  </label>
                  <label class="capability-item">
                    <input v-model="modelForm.capabilities.multimodal" type="checkbox" />
                    Multimodal
                  </label>
                </div>
              </div>
              <div class="form-group checkbox-group">
                <label>
                  <input
                    v-model="modelForm.is_default"
                    type="checkbox"
                  />
                  Výchozí model pro poskytovatele
                </label>
              </div>
              <div class="form-group checkbox-group">
                <label>
                  <input
                    v-model="modelForm.is_active"
                    type="checkbox"
                  />
                  Aktivní
                </label>
              </div>
              <div class="form-actions">
                <button type="button" @click="closeModelForm" class="cancel-btn">
                  Zrušit
                </button>
                <button type="submit" class="save-btn" :disabled="isSaving">
                  {{ isSaving ? 'Ukládám...' : 'Uložit' }}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>

    <!-- Testing Tab -->
    <div v-if="activeTab === 'testing'" class="tab-content">
      <div class="testing-section">
        <div class="test-controls">
          <h2>🧪 LLM Testování</h2>
          <div class="model-selector">
            <label>Vyberte model:</label>
            <select v-model="selectedTestModel" class="model-select">
              <option value="">-- Vyberte model ({{ allModels.length }} dostupných) --</option>
              <option
                v-for="model in allModels"
                :key="model.id"
                :value="model.id"
              >
                {{ model.display_name }}
              </option>
            </select>
            <div v-if="allModels.length === 0 && models.length > 0" class="debug-info">
              🔍 Debug: Problém s mapováním modelů. Raw modelů: {{ models.length }}
            </div>
            <div v-if="models.length === 0" class="debug-info">
              🔍 Debug: Žádné modely nenačteny z API.
            </div>
          </div>
        </div>

        <!-- Chat Interface -->
        <div class="chat-interface">
          <div class="chat-messages">
            <div
              v-for="message in testMessages"
              :key="message.id"
              class="message"
              :class="message.sender"
            >
              <div class="message-content">
                <div class="message-text">{{ message.text }}</div>
                <div class="message-meta">
                  <span class="message-time">{{ formatTime(message.time) }}</span>
                  <span v-if="message.metrics" class="message-metrics">
                    {{ message.metrics.responseTime }}ms | {{ message.metrics.tokens }} tokens
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div class="chat-input">
            <textarea
              v-model="testMessage"
              placeholder="Zadejte testovací zprávu..."
              @keydown.enter.prevent="sendTestMessage"
              rows="3"
            ></textarea>
            <button
              @click="sendTestMessage"
              :disabled="!selectedTestModel || !testMessage.trim() || isTesting"
              class="send-btn"
            >
              {{ isTesting ? '🔄 Testování...' : '🚀 Odeslat' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Performance Tab -->
    <div v-if="activeTab === 'performance'" class="tab-content">
      <div class="performance-section">
        <div class="section-header">
          <h2>📊 Výkonové metriky</h2>
          <div class="header-actions">
            <button @click="refreshPerformanceData" class="refresh-btn">
              🔄 Obnovit data
            </button>
            <button @click="exportPerformanceData" class="export-btn">
              📊 Export CSV
            </button>
          </div>
        </div>

        <!-- Performance Overview -->
        <div class="performance-overview">
          <div class="metric-card">
            <div class="metric-icon">⚡</div>
            <div class="metric-content">
              <div class="metric-value">{{ performanceStats.avgResponseTime }}ms</div>
              <div class="metric-label">Průměrný čas odpovědi</div>
              <div class="metric-trend" :class="performanceStats.responseTimeTrend">
                {{ performanceStats.responseTimeTrend === 'up' ? '📈' : performanceStats.responseTimeTrend === 'down' ? '📉' : '➡️' }}
              </div>
            </div>
          </div>
          <div class="metric-card">
            <div class="metric-icon">🔤</div>
            <div class="metric-content">
              <div class="metric-value">{{ formatNumber(performanceStats.totalTokens) }}</div>
              <div class="metric-label">Celkové tokeny</div>
              <div class="metric-trend" :class="performanceStats.tokensTrend">
                {{ performanceStats.tokensTrend === 'up' ? '📈' : performanceStats.tokensTrend === 'down' ? '📉' : '➡️' }}
              </div>
            </div>
          </div>
          <div class="metric-card">
            <div class="metric-icon">💰</div>
            <div class="metric-content">
              <div class="metric-value">${{ performanceStats.totalCost.toFixed(2) }}</div>
              <div class="metric-label">Celkové náklady</div>
              <div class="metric-trend" :class="performanceStats.costTrend">
                {{ performanceStats.costTrend === 'up' ? '📈' : performanceStats.costTrend === 'down' ? '📉' : '➡️' }}
              </div>
            </div>
          </div>
          <div class="metric-card">
            <div class="metric-icon">📈</div>
            <div class="metric-content">
              <div class="metric-value">{{ performanceStats.successRate }}%</div>
              <div class="metric-label">Úspěšnost</div>
              <div class="metric-trend" :class="performanceStats.successRateTrend">
                {{ performanceStats.successRateTrend === 'up' ? '📈' : performanceStats.successRateTrend === 'down' ? '📉' : '➡️' }}
              </div>
            </div>
          </div>
        </div>

        <!-- Time Range Selector -->
        <div class="time-range-selector">
          <label>Časové období:</label>
          <select v-model="selectedTimeRange" @change="refreshPerformanceData">
            <option value="1h">Poslední hodina</option>
            <option value="24h">Posledních 24 hodin</option>
            <option value="7d">Posledních 7 dní</option>
            <option value="30d">Posledních 30 dní</option>
          </select>
        </div>

        <!-- Model Comparison -->
        <div class="model-comparison">
          <h3>Porovnání modelů</h3>
          <div class="comparison-table">
            <div class="table-header">
              <div>Model</div>
              <div>Avg Response</div>
              <div>Success Rate</div>
              <div>Cost/Token</div>
              <div>Usage</div>
              <div>Last Used</div>
            </div>
            <div
              v-for="model in modelPerformanceData"
              :key="model.id"
              class="table-row"
              :class="{ 'top-performer': model.isTopPerformer }"
            >
              <div class="model-name">
                <span class="model-provider">{{ model.provider }}</span>
                <span class="model-title">{{ model.name }}</span>
              </div>
              <div class="response-time" :class="getPerformanceClass(model.avgResponseTime)">
                {{ model.avgResponseTime }}ms
              </div>
              <div class="success-rate" :class="getSuccessRateClass(model.successRate)">
                {{ model.successRate }}%
              </div>
              <div class="cost">{{ model.costPerToken }}</div>
              <div class="usage">{{ model.usageCount }}x</div>
              <div class="last-used">{{ formatRelativeTime(model.lastUsed) }}</div>
            </div>
          </div>
        </div>

        <!-- Performance Charts -->
        <div class="performance-charts">
          <div class="chart-container">
            <h4>Čas odpovědi v čase</h4>
            <div class="chart-placeholder">
              📊 Graf času odpovědi (implementace v budoucnu)
            </div>
          </div>
          <div class="chart-container">
            <h4>Využití modelů</h4>
            <div class="chart-placeholder">
              📊 Graf využití modelů (implementace v budoucnu)
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Communication Tab -->
    <div v-if="activeTab === 'communication'" class="tab-content">
      <div class="communication-section">
        <div class="section-header">
          <h2>🤝 Komunikace mezi LLM</h2>
          <p class="section-description">
            Orchestrovaná komunikace mezi třemi AI modely s iterativním zpracováním a Mem0 memory systémem
          </p>
        </div>

        <!-- Input Controls -->
        <div class="communication-controls">
          <div class="control-row">
            <div class="control-group">
              <label for="ai0-model">AI-0 Model (Prompt Optimizer):</label>
              <select id="ai0-model" v-model="communicationForm.ai0Model" class="model-select">
                <option value="">-- Vyberte model pro AI-0 --</option>
                <option
                  v-for="model in allModels"
                  :key="'ai0-' + model.id"
                  :value="model.id"
                >
                  {{ model.display_name }}
                </option>
              </select>
            </div>
            <div class="control-group">
              <label for="ai1-model">AI-1 Model (Primary Processor):</label>
              <select id="ai1-model" v-model="communicationForm.ai1Model" class="model-select">
                <option value="">-- Vyberte model pro AI-1 --</option>
                <option
                  v-for="model in allModels"
                  :key="'ai1-' + model.id"
                  :value="model.id"
                >
                  {{ model.display_name }}
                </option>
              </select>
            </div>
            <div class="control-group">
              <label for="ai2-model">AI-2 Model (Secondary Processor):</label>
              <select id="ai2-model" v-model="communicationForm.ai2Model" class="model-select">
                <option value="">-- Vyberte model pro AI-2 --</option>
                <option
                  v-for="model in allModels"
                  :key="'ai2-' + model.id"
                  :value="model.id"
                >
                  {{ model.display_name }}
                </option>
              </select>
            </div>
          </div>

          <div class="control-row">
            <div class="control-group">
              <label for="iteration-count">Počet iterací:</label>
              <input
                id="iteration-count"
                v-model.number="communicationForm.iterationCount"
                type="number"
                min="1"
                max="50"
                class="iteration-input"
              />
            </div>
          </div>

          <div class="control-row">
            <div class="control-group full-width">
              <label for="user-prompt">Původní prompt:</label>
              <textarea
                id="user-prompt"
                v-model="communicationForm.userPrompt"
                placeholder="Zadejte váš prompt, který bude optimalizován AI-0 a zpracován AI-1 a AI-2..."
                rows="4"
                class="prompt-textarea"
              ></textarea>
            </div>
          </div>

          <div class="control-row">
            <button
              @click="startCommunication"
              :disabled="!canStartCommunication || isProcessing"
              class="start-communication-btn"
            >
              {{ isProcessing ? '🔄 Zpracovávám...' : '🚀 Spustit komunikaci' }}
            </button>
            <button
              v-if="currentSession"
              @click="stopCommunication"
              :disabled="!isProcessing"
              class="stop-communication-btn"
            >
              🛑 Zastavit
            </button>
            <button
              v-if="currentSession"
              @click="clearSession"
              :disabled="isProcessing"
              class="clear-session-btn"
            >
              🗑️ Vymazat session
            </button>
          </div>
        </div>

        <!-- Session Status -->
        <div v-if="currentSession" class="session-status">
          <div class="status-header">
            <h3>📊 Status Session: {{ currentSession.id }}</h3>
            <div class="session-meta">
              <span class="session-time">Spuštěno: {{ formatTime(currentSession.startTime) }}</span>
              <span class="session-iterations">Iterace: {{ currentSession.currentIteration }}/{{ currentSession.maxIterations }}</span>
              <span class="session-status" :class="currentSession.status">{{ getSessionStatusText(currentSession.status) }}</span>
            </div>
          </div>

          <div class="progress-bar">
            <div
              class="progress-fill"
              :style="{ width: sessionProgress + '%' }"
            ></div>
          </div>
        </div>

        <!-- Final Answer -->
        <div v-if="finalAnswer" class="final-answer">
          <div class="final-answer-header">
            <h3>✨ Finální odpověď</h3>
            <div class="answer-meta">
              <span>Validováno AI-0</span>
              <span v-if="finalAnswer.topicAdherence" :class="getAdherenceClass(finalAnswer.topicAdherence)">
                {{ getAdherenceText(finalAnswer.topicAdherence) }}
              </span>
            </div>
          </div>
          <div class="final-answer-content">
            {{ finalAnswer.content }}
          </div>
        </div>

        <!-- AI-0 Activity Panel -->
        <div v-if="ai0Messages.length > 0" class="ai0-activity">
          <div class="ai-panel ai0-panel">
            <div class="panel-header">
              <h3>⚡ AI-0 (Prompt Optimizer) - Aktivita</h3>
            </div>
            <div class="conversation-display">
              <div
                v-for="message in ai0Messages"
                :key="message.id"
                class="conversation-message"
                :class="message.type"
              >
                <div class="message-header">
                  <span class="message-phase">{{ message.phase }}</span>
                  <span class="message-time">{{ formatTime(message.timestamp) }}</span>
                </div>
                <div class="message-content">
                  <div class="prompt-section" v-if="message.inputPrompt">
                    <strong>📥 Vstupní prompt:</strong>
                    <div class="prompt-text">{{ message.inputPrompt }}</div>
                  </div>
                  <div class="prompt-section" v-if="message.outputPrompt">
                    <strong>📤 Výstupní prompt:</strong>
                    <div class="prompt-text">{{ message.outputPrompt }}</div>
                  </div>
                  <div v-if="message.analysis" class="analysis-section">
                    <strong>🔍 Analýza:</strong>
                    <div class="analysis-text">{{ message.analysis }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Triple Panel Layout -->
        <div class="triple-panel-layout">
          <!-- AI-1 Panel -->
          <div class="ai-panel ai1-panel">
            <div class="panel-header">
              <h3>🤖 AI-1 (Primary Processor)</h3>
              <div class="panel-controls">
                <button
                  @click="showManualInput('ai1')"
                  :disabled="!currentSession || isProcessing"
                  class="manual-input-btn"
                >
                  ✏️ Manuální input
                </button>
              </div>
            </div>
            <div class="conversation-display">
              <div
                v-for="message in ai1Messages"
                :key="message.id"
                class="conversation-message"
                :class="message.type"
              >
                <div class="message-header">
                  <span class="message-iteration">Iterace {{ message.iteration }}</span>
                  <span class="message-time">{{ formatTime(message.timestamp) }}</span>
                </div>
                <div class="message-content">
                  <div class="prompt-section" v-if="message.inputPrompt">
                    <strong>📥 Dostal prompt:</strong>
                    <div class="prompt-text">{{ message.inputPrompt }}</div>
                  </div>
                  <div class="prompt-section" v-if="message.content">
                    <strong>📤 Odpověď:</strong>
                    <div class="response-text">{{ message.content }}</div>
                  </div>
                  <div class="prompt-section" v-if="message.outputPrompt">
                    <strong>➡️ Předal AI-2:</strong>
                    <div class="prompt-text">{{ message.outputPrompt }}</div>
                  </div>
                </div>
              </div>
              <div v-if="ai1Messages.length === 0" class="empty-conversation">
                Žádné zprávy v AI-1 konverzaci
              </div>
            </div>
          </div>

          <!-- AI-2 Panel -->
          <div class="ai-panel ai2-panel">
            <div class="panel-header">
              <h3>🧠 AI-2 (Secondary Processor)</h3>
              <div class="panel-controls">
                <button
                  @click="showManualInput('ai2')"
                  :disabled="!currentSession || isProcessing"
                  class="manual-input-btn"
                >
                  ✏️ Manuální input
                </button>
              </div>
            </div>
            <div class="conversation-display">
              <div
                v-for="message in ai2Messages"
                :key="message.id"
                class="conversation-message"
                :class="message.type"
              >
                <div class="message-header">
                  <span class="message-iteration">Iterace {{ message.iteration }}</span>
                  <span class="message-time">{{ formatTime(message.timestamp) }}</span>
                </div>
                <div class="message-content">
                  <div class="prompt-section" v-if="message.inputPrompt">
                    <strong>📥 Dostal od AI-1:</strong>
                    <div class="prompt-text">{{ message.inputPrompt }}</div>
                  </div>
                  <div class="prompt-section" v-if="message.content">
                    <strong>📤 Analýza:</strong>
                    <div class="response-text">{{ message.content }}</div>
                  </div>
                  <div class="prompt-section" v-if="message.outputPrompt">
                    <strong>➡️ Předal AI-1:</strong>
                    <div class="prompt-text">{{ message.outputPrompt }}</div>
                  </div>
                </div>
              </div>
              <div v-if="ai2Messages.length === 0" class="empty-conversation">
                Žádné zprávy v AI-2 konverzaci
              </div>
            </div>
          </div>
        </div>

        <!-- Manual Input Modal -->
        <div v-if="showManualInputModal" class="modal-overlay" @click="closeManualInput">
          <div class="modal-content manual-input-modal" @click.stop>
            <div class="modal-header">
              <h3>✏️ Manuální input pro {{ manualInputTarget === 'ai1' ? 'AI-1' : 'AI-2' }}</h3>
              <button @click="closeManualInput" class="close-btn">✕</button>
            </div>
            <div class="modal-body">
              <textarea
                v-model="manualInputText"
                placeholder="Zadejte zprávu pro AI model..."
                rows="6"
                class="manual-input-textarea"
              ></textarea>
            </div>
            <div class="modal-actions">
              <button @click="closeManualInput" class="cancel-btn">Zrušit</button>
              <button
                @click="sendManualInput"
                :disabled="!manualInputText.trim() || isProcessing"
                class="send-btn"
              >
                🚀 Odeslat
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { llmDbService } from '@/services/llm_db.service';
import { chatService } from '@/services/chat.service';

export default {
  name: 'LlmManagement',

  data() {
    return {
      activeTab: 'providers',

      // Providers data
      providers: [],
      models: [],
      selectedProvider: null,
      selectedProviderModels: [],
      isTestingProviders: false,

      // Management data
      managementSubTab: 'providers',
      showProviderForm: false,
      showModelForm: false,
      editingProvider: null,
      editingModel: null,
      isSaving: false,

      // Provider form data
      providerForm: {
        name: '',
        base_url: '',
        api_key: '',
        api_version: 'v1',
        auth_type: 'api_key',
        rate_limit: null,
        api_key_required: true,
        is_active: true
      },

      // Model form data
      modelForm: {
        provider_id: '',
        model_name: '',
        model_identifier: '',
        context_length: null,
        max_tokens_output: null,
        temperature: 0.7,
        capabilities: {
          text: true,
          code: false,
          vision: false,
          function_calling: false,
          reasoning: false,
          multimodal: false
        },
        is_default: false,
        is_active: true
      },

      // Testing data
      selectedTestModel: '',
      testMessage: '',
      testMessages: [],
      isTesting: false,

      // Performance data
      performanceStats: {
        avgResponseTime: 1250,
        totalTokens: 45678,
        totalCost: 12.34,
        successRate: 94.2,
        responseTimeTrend: 'down',
        tokensTrend: 'up',
        costTrend: 'up',
        successRateTrend: 'stable'
      },
      modelPerformanceData: [],
      selectedTimeRange: '24h',

      // Communication between LLM data
      communicationForm: {
        ai0Model: '',
        ai1Model: '',
        ai2Model: '',
        iterationCount: 10,
        userPrompt: ''
      },
      currentSession: null,
      isProcessing: false,
      finalAnswer: null,
      ai0Messages: [],
      ai1Messages: [],
      ai2Messages: [],
      showManualInputModal: false,
      manualInputTarget: null,
      manualInputText: ''
    };
  },

  computed: {
    allModels() {
      console.log('allModels computed called, models.length:', this.models.length);
      if (this.models.length === 0) {
        console.log('No models available for mapping');
        return [];
      }

      // Vrátíme modely načtené z API endpoint /api/db/llm/models
      const mappedModels = this.models.map(model => {
        console.log('Mapping model:', model.provider_name, '-', model.model_name);
        return {
          ...model,
          // Formát zobrazení: "Poskytovatel - Model"
          display_name: `${model.provider_name} - ${model.model_name}`
        };
      });
      console.log('allModels computed result:', mappedModels.length, 'modelů');
      console.log('První 3 allModels:', mappedModels.slice(0, 3).map(m => m.display_name));
      return mappedModels;
    },

    // Communication computed properties
    canStartCommunication() {
      return this.communicationForm.ai0Model &&
             this.communicationForm.ai1Model &&
             this.communicationForm.ai2Model &&
             this.communicationForm.userPrompt.trim() &&
             this.communicationForm.iterationCount > 0 &&
             !this.isProcessing;
    },

    sessionProgress() {
      if (!this.currentSession) return 0;
      return Math.round((this.currentSession.currentIteration / this.currentSession.maxIterations) * 100);
    }
  },

  watch: {
    models: {
      handler(newModels) {
        console.log('Models watch triggered, new length:', newModels.length);
        console.log('allModels computed will now have:', this.allModels.length, 'items');
      },
      deep: true
    }
  },

  methods: {
    async switchTab(tabName) {
      this.activeTab = tabName;
      console.log(`Přepínám na záložku: ${tabName}`);

      // Při přepnutí na testování se ujistíme, že máme načtené modely
      if (tabName === 'testing') {
        console.log('Testování záložka - kontroluji modely');
        if (this.models.length === 0) {
          console.log('Žádné modely - načítám...');
          await this.refreshModels();
        } else {
          console.log(`Máme ${this.models.length} modelů`);
        }
      }

      // Při přepnutí na komunikaci mezi LLM se ujistíme, že máme načtené modely
      if (tabName === 'communication') {
        console.log('Komunikace mezi LLM záložka - kontroluji modely');
        if (this.models.length === 0) {
          console.log('Žádné modely - načítám...');
          await this.refreshModels();
        } else {
          console.log(`Máme ${this.models.length} modelů pro komunikaci`);
        }
      }
    },

    async refreshProviders() {
      try {
        console.log('Načítání poskytovatelů z API...');
        const response = await fetch('http://localhost:8001/api/db/llm/providers');
        console.log('API Response status:', response.status);

        if (response.ok) {
          const data = await response.json();
          console.log('Raw provider data:', data);

          this.providers = data.map(provider => ({
            // Mapování podle skutečné struktury dat z API
            id: provider.id,  // API vrací "id", ne "provider_id"
            name: provider.name,  // API vrací "name", ne "provider_name"
            status: provider.is_active ? 'active' : 'inactive',
            description: `${provider.name} LLM provider - ${provider.base_url || 'No URL'}`,
            models_count: 0, // Bude doplněno při načítání modelů
            api_version: provider.api_version || 'v1',
            api_key: provider.api_key && provider.api_key !== '********' ? '✅ Nastaven' : '❌ Chybí',
            base_url: provider.base_url,
            auth_type: provider.auth_type || 'api_key',
            rate_limit: provider.rate_limit
          }));
          console.log('Načteno poskytovatelů:', this.providers.length);
          console.log('Mapped providers:', this.providers);

          // Načteme také modely
          await this.refreshModels();

          // Spočítáme počet modelů pro každého poskytovatele
          this.providers.forEach(provider => {
            const providerModels = this.models.filter(model => model.provider_id === provider.id);
            provider.models_count = providerModels.length;
          });
        } else {
          console.error('Chyba při načítání poskytovatelů:', response.status);
          const errorText = await response.text();
          console.error('Error response:', errorText);
        }
      } catch (error) {
        console.error('Chyba při načítání poskytovatelů:', error);
      }
    },

    async refreshModels() {
      try {
        console.log('Načítání modelů z API...');
        const response = await fetch('/api/db/llm/models');
        console.log('API Response status:', response.status);

        if (response.ok) {
          const data = await response.json();
          console.log('Raw models data:', data);

          // Mapování modelů podle skutečné struktury dat z API
          this.models = data.map(model => ({
            id: model.id,
            model_id: model.model_id,
            provider_id: model.provider_id,
            model_name: model.name,  // API vrací "name"
            model_identifier: model.model_identifier,
            provider_name: model.provider_name,
            context_length: model.context_length,
            max_tokens_output: model.max_tokens,
            temperature: model.temperature,
            capabilities: model.capabilities,
            is_default: model.is_default,
            is_active: model.is_active,
            api_key_required: model.api_key_required,
            pricing_input: null, // Není v API datech
            pricing_output: null // Není v API datech
          }));

          console.log('Načteno modelů:', this.models.length);
          console.log('Mapped models:', this.models);
          console.log('Dostupné modely:', this.allModels.length);
          console.log('První 3 modely:', this.models.slice(0, 3));

          // Debug výchozích modelů
          const defaultModels = this.models.filter(m => m.is_default);
          console.log('Výchozí modely po načtení:', defaultModels.map(m => ({
            name: m.model_name,
            provider_id: m.provider_id,
            is_default: m.is_default
          })));

          // Vynucení aktualizace Vue komponenty
          this.$forceUpdate();
        } else {
          console.error('Chyba při načítání modelů:', response.status);
          const errorText = await response.text();
          console.error('Error response:', errorText);


        }
      } catch (error) {
        console.error('Chyba při načítání modelů:', error);


      }
    },

    async selectProvider(provider) {
      this.selectedProvider = provider;
      // Načteme modely pro vybraného poskytovatele
      this.selectedProviderModels = this.models.filter(
        model => model.provider_id === provider.id
      ).map(model => ({
        id: model.model_id,
        name: model.model_name,  // Už je správně mapováno
        identifier: model.model_identifier,
        available: model.is_active,
        is_default: model.is_default,
        context_length: model.context_length,
        max_tokens_output: model.max_tokens_output,
        cost_per_token: model.pricing_input ? `${model.pricing_input}/1k` : 'N/A',
        capabilities: model.capabilities,
        description: model.capabilities && Object.keys(model.capabilities).length > 0 ?
          `Model s možnostmi: ${Object.keys(model.capabilities || {}).join(', ')}` :
          'Standardní LLM model'
      }));
      console.log(`Načteno ${this.selectedProviderModels.length} modelů pro ${provider.name}`);
      console.log('Selected provider models:', this.selectedProviderModels);
    },

    async testProvider(provider) {
      provider.testing = true;
      try {
        console.log(`Testování poskytovatele ${provider.name} (ID: ${provider.id})`);

        const response = await fetch(`/api/db/llm/performance/test-provider/${provider.id}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const result = await response.json();
          console.log('Test result:', result);

          if (result.success) {
            alert(`✅ Test poskytovatele ${provider.name} byl úspěšný!\n\n` +
                  `Model: ${result.model_tested}\n` +
                  `Čas odpovědi: ${result.responseTime}ms\n` +
                  `Odpověď: ${result.response_text}`);
          } else {
            alert(`❌ Test poskytovatele ${provider.name} selhal!\n\n` +
                  `Model: ${result.model_tested}\n` +
                  `Čas: ${result.responseTime}ms\n` +
                  `Chyba: ${result.error || result.message}`);
          }
        } else {
          const errorData = await response.json();
          alert(`❌ Test poskytovatele ${provider.name} selhal: ${errorData.detail}`);
        }
      } catch (error) {
        console.error('Chyba při testování poskytovatele:', error);
        alert(`❌ Test poskytovatele ${provider.name} selhal: ${error.message}`);
      } finally {
        provider.testing = false;
      }
    },

    async testAllProviders() {
      this.isTestingProviders = true;
      try {
        for (const provider of this.providers) {
          if (provider.status === 'active') {
            await this.testProvider(provider);
          }
        }
        alert('✅ Testování všech poskytovatelů dokončeno!');
      } catch (error) {
        alert(`❌ Chyba při testování poskytovatelů: ${error.message}`);
      } finally {
        this.isTestingProviders = false;
      }
    },

    editProvider(provider) {
      // TODO: Implementovat editaci poskytovatele
      alert(`Editace poskytovatele ${provider.name} - implementace v budoucnu`);
    },

    getAverageContext() {
      if (this.selectedProviderModels.length === 0) return 'N/A';
      const total = this.selectedProviderModels.reduce((sum, model) => {
        return sum + (model.context_length || 0);
      }, 0);
      const avg = Math.round(total / this.selectedProviderModels.length);
      return this.formatNumber(avg);
    },

    getModelCapabilities(model) {
      if (!model.capabilities) return [];
      const caps = typeof model.capabilities === 'string' ?
        JSON.parse(model.capabilities) : model.capabilities;
      return Object.keys(caps).filter(key => caps[key]);
    },

    formatNumber(num) {
      if (!num) return '0';
      return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    },

    async sendTestMessage() {
      if (!this.selectedTestModel || !this.testMessage.trim()) return;

      this.isTesting = true;
      const startTime = performance.now();

      // Add user message
      const userMessage = {
        id: Date.now(),
        sender: 'user',
        text: this.testMessage,
        time: new Date()
      };
      this.testMessages.push(userMessage);

      const messageToSend = this.testMessage;
      this.testMessage = '';

      try {
        const response = await chatService.sendMessage({
          message: messageToSend,
          model_id: this.selectedTestModel
        });

        const endTime = performance.now();
        const responseTime = Math.round(endTime - startTime);

        // Add assistant message
        const assistantMessage = {
          id: Date.now() + 1,
          sender: 'assistant',
          text: response.response?.text || response.text || response.content || 'Odpověď nebyla získána',
          time: new Date(),
          metrics: {
            responseTime,
            tokens: response.response?.usage?.total_tokens || response.usage?.total_tokens || 0
          }
        };
        this.testMessages.push(assistantMessage);

      } catch (error) {
        console.error('Chyba při testování:', error);
        const errorMessage = {
          id: Date.now() + 1,
          sender: 'assistant',
          text: 'Chyba při komunikaci s modelem: ' + error.message,
          time: new Date(),
          metrics: {
            responseTime: performance.now() - startTime,
            tokens: 0
          }
        };
        this.testMessages.push(errorMessage);
      } finally {
        this.isTesting = false;
      }
    },

    formatTime(timestamp) {
      return timestamp.toLocaleTimeString('cs-CZ');
    },

    getPerformanceClass(responseTime) {
      if (responseTime < 1000) return 'fast';
      if (responseTime < 3000) return 'medium';
      return 'slow';
    },

    getSuccessRateClass(successRate) {
      if (successRate >= 95) return 'excellent';
      if (successRate >= 85) return 'good';
      if (successRate >= 70) return 'fair';
      return 'poor';
    },

    formatRelativeTime(timestamp) {
      if (!timestamp) return 'Nikdy';
      const now = new Date();
      const time = new Date(timestamp);
      const diffMs = now - time;
      const diffMins = Math.floor(diffMs / 60000);
      const diffHours = Math.floor(diffMs / 3600000);
      const diffDays = Math.floor(diffMs / 86400000);

      if (diffMins < 1) return 'Právě teď';
      if (diffMins < 60) return `${diffMins}m`;
      if (diffHours < 24) return `${diffHours}h`;
      return `${diffDays}d`;
    },

    async refreshPerformanceData() {
      try {
        console.log(`Načítání výkonových dat pro období: ${this.selectedTimeRange}`);

        // Načtení přehledu výkonu
        const overviewResponse = await fetch(`/api/db/llm/performance/overview?time_range=${this.selectedTimeRange}`);
        if (overviewResponse.ok) {
          const overviewData = await overviewResponse.json();
          this.performanceStats = {
            avgResponseTime: overviewData.avgResponseTime,
            totalTokens: overviewData.totalTokens,
            totalCost: overviewData.totalCost,
            successRate: overviewData.successRate,
            responseTimeTrend: overviewData.responseTimeTrend,
            tokensTrend: overviewData.tokensTrend,
            costTrend: overviewData.costTrend,
            successRateTrend: overviewData.successRateTrend
          };
        }

        // Načtení výkonu modelů
        const modelsResponse = await fetch(`/api/db/llm/performance/models?time_range=${this.selectedTimeRange}`);
        if (modelsResponse.ok) {
          const modelsData = await modelsResponse.json();
          this.modelPerformanceData = modelsData.map(model => ({
            ...model,
            lastUsed: new Date(model.lastUsed)
          }));
        }

        console.log('Výkonová data načtena:', this.modelPerformanceData.length, 'modelů');
      } catch (error) {
        console.error('Chyba při načítání výkonových dat:', error);
        // Fallback na simulovaná data při chybě
        this.modelPerformanceData = [
          {
            id: 1,
            name: 'GPT-4o',
            provider: 'OpenAI',
            avgResponseTime: 850,
            successRate: 98.5,
            costPerToken: '$0.03/1k',
            usageCount: 1247,
            lastUsed: new Date(Date.now() - 300000),
            isTopPerformer: true
          }
        ];
      }
    },

    async exportPerformanceData() {
      try {
        // Vytvoření CSV dat
        const headers = ['Model', 'Provider', 'Avg Response (ms)', 'Success Rate (%)', 'Cost/Token', 'Usage Count', 'Last Used'];
        const csvData = [
          headers.join(','),
          ...this.modelPerformanceData.map(model => [
            model.name,
            model.provider,
            model.avgResponseTime,
            model.successRate,
            model.costPerToken,
            model.usageCount,
            model.lastUsed.toISOString()
          ].join(','))
        ].join('\n');

        // Stažení souboru
        const blob = new Blob([csvData], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `llm-performance-${this.selectedTimeRange}-${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        window.URL.revokeObjectURL(url);

        alert('✅ Výkonová data byla exportována do CSV souboru!');
      } catch (error) {
        console.error('Chyba při exportu dat:', error);
        alert('❌ Chyba při exportu dat: ' + error.message);
      }
    },

    // === CRUD Methods for Management ===

    // Provider CRUD methods
    showCreateProviderForm() {
      this.editingProvider = null;
      this.resetProviderForm();
      this.showProviderForm = true;
    },

    editProvider(provider) {
      this.editingProvider = provider;
      this.providerForm = {
        name: provider.name,
        base_url: provider.base_url || '',
        api_key: '', // Nezobrazujeme existující klíč z bezpečnostních důvodů
        api_version: provider.api_version || 'v1',
        auth_type: provider.auth_type || 'api_key',
        rate_limit: provider.rate_limit,
        api_key_required: provider.api_key_required !== false,
        is_active: provider.status === 'active'
      };
      this.showProviderForm = true;
    },

    async deleteProvider(provider) {
      if (!confirm(`Opravdu chcete smazat poskytovatele "${provider.name}"? Tato akce smaže také všechny jeho modely a nelze ji vrátit zpět.`)) {
        return;
      }

      try {
        const response = await fetch(`/api/db/llm/providers/${provider.id}`, {
          method: 'DELETE'
        });

        if (response.ok) {
          alert(`✅ Poskytovatel "${provider.name}" byl úspěšně smazán!`);
          await this.refreshProviders();
          await this.refreshModels();
        } else {
          const errorData = await response.json();
          alert(`❌ Chyba při mazání poskytovatele: ${errorData.detail || 'Neznámá chyba'}`);
        }
      } catch (error) {
        console.error('Chyba při mazání poskytovatele:', error);
        alert('❌ Chyba při mazání poskytovatele: ' + error.message);
      }
    },

    async saveProvider() {
      this.isSaving = true;
      try {
        const url = this.editingProvider
          ? `/api/db/llm/providers/${this.editingProvider.id}`
          : '/api/db/llm/providers';

        const method = this.editingProvider ? 'PUT' : 'POST';

        const providerData = {
          name: this.providerForm.name,
          base_url: this.providerForm.base_url || null,
          api_key: this.providerForm.api_key || null,
          api_version: this.providerForm.api_version || 'v1',
          auth_type: this.providerForm.auth_type,
          rate_limit: this.providerForm.rate_limit || null,
          api_key_required: this.providerForm.api_key_required,
          is_active: this.providerForm.is_active
        };

        const response = await fetch(url, {
          method: method,
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(providerData)
        });

        if (response.ok) {
          const action = this.editingProvider ? 'aktualizován' : 'vytvořen';
          alert(`✅ Poskytovatel "${this.providerForm.name}" byl úspěšně ${action}!`);
          this.closeProviderForm();
          await this.refreshProviders();
        } else {
          const errorData = await response.json();
          alert(`❌ Chyba při ukládání poskytovatele: ${errorData.detail || 'Neznámá chyba'}`);
        }
      } catch (error) {
        console.error('Chyba při ukládání poskytovatele:', error);
        alert('❌ Chyba při ukládání poskytovatele: ' + error.message);
      } finally {
        this.isSaving = false;
      }
    },

    closeProviderForm() {
      this.showProviderForm = false;
      this.editingProvider = null;
      this.resetProviderForm();
    },

    resetProviderForm() {
      this.providerForm = {
        name: '',
        base_url: '',
        api_key: '',
        api_version: 'v1',
        auth_type: 'api_key',
        rate_limit: null,
        api_key_required: true,
        is_active: true
      };
    },

    // Model CRUD methods
    showCreateModelForm() {
      this.editingModel = null;
      this.resetModelForm();
      this.showModelForm = true;
    },

    editModel(model) {
      console.log('🔧 Editace modelu - debug info:', {
        model_id: model.model_id,
        id: model.id,
        model_name: model.model_name,
        provider_id: model.provider_id,
        provider_name: model.provider_name,
        full_model_object: model
      });

      this.editingModel = model;
      this.modelForm = {
        provider_id: model.provider_id,
        model_name: model.model_name,
        model_identifier: model.model_identifier,
        context_length: model.context_length,
        max_tokens_output: model.max_tokens_output,
        temperature: model.temperature || 0.7,
        capabilities: {
          text: model.capabilities?.text !== false,
          code: model.capabilities?.code === true,
          vision: model.capabilities?.vision === true,
          function_calling: model.capabilities?.function_calling === true,
          reasoning: model.capabilities?.reasoning === true,
          multimodal: model.capabilities?.multimodal === true
        },
        is_default: model.is_default,
        is_active: model.is_active
      };
      this.showModelForm = true;
    },

    async deleteModel(model) {
      if (!confirm(`Opravdu chcete smazat model "${model.model_name}"? Tato akce nelze vrátit zpět.`)) {
        return;
      }

      try {
        const response = await fetch(`/api/db/llm/models/${model.model_id}`, {
          method: 'DELETE'
        });

        if (response.ok) {
          alert(`✅ Model "${model.model_name}" byl úspěšně smazán!`);
          await this.refreshModels();
          await this.refreshProviders(); // Aktualizace počtu modelů u poskytovatelů
        } else {
          const errorData = await response.json();
          alert(`❌ Chyba při mazání modelu: ${errorData.detail || 'Neznámá chyba'}`);
        }
      } catch (error) {
        console.error('Chyba při mazání modelu:', error);
        alert('❌ Chyba při mazání modelu: ' + error.message);
      }
    },

    async toggleModelDefault(model) {
      try {
        console.log('🔄 Nastavování výchozího modelu:', {
          model_name: model.model_name,
          provider_id: model.provider_id,
          provider_name: model.provider_name,
          current_is_default: model.is_default,
          model_id: model.model_id
        });

        const url = `/api/db/llm/providers/${model.provider_id}/models/${model.model_name}/set-default`;
        console.log('🌐 API URL:', url);

        const response = await fetch(url, {
          method: 'PUT'
        });

        console.log('📡 API Response status:', response.status);

        if (response.ok) {
          const responseData = await response.json();
          console.log('✅ API Response data:', responseData);
          console.log(`Nastavuji model "${model.model_name}" jako výchozí pro poskytovatele ${model.provider_id}`);

          // Aktualizujeme data lokálně pro okamžitou odezvu
          this.models.forEach(m => {
            if (m.provider_id === model.provider_id) {
              m.is_default = (m.model_id === model.model_id);
              console.log(`🔄 Lokální aktualizace: ${m.model_name} → is_default: ${m.is_default}`);
            }
          });

          // Vynucení re-render Vue komponenty
          this.$forceUpdate();

          alert(`✅ Model "${model.model_name}" byl nastaven jako výchozí!`);

          // Načteme čerstvá data z API
          console.log('🔄 Načítám čerstvá data z API...');
          await this.refreshModels();
          await this.refreshProviders(); // Aktualizace poskytovatelů kvůli výchozímu modelu
        } else {
          const errorData = await response.json();
          console.error('❌ API Error:', errorData);
          alert(`❌ Chyba při nastavování výchozího modelu: ${errorData.detail || 'Neznámá chyba'}`);
        }
      } catch (error) {
        console.error('❌ Chyba při nastavování výchozího modelu:', error);
        alert('❌ Chyba při nastavování výchozího modelu: ' + error.message);
      }
    },

    async saveModel() {
      this.isSaving = true;
      try {
        console.log('💾 Ukládání modelu - debug info:', {
          editingModel: this.editingModel,
          editingModel_model_id: this.editingModel?.model_id,
          editingModel_id: this.editingModel?.id,
          modelForm: this.modelForm
        });

        const url = this.editingModel
          ? `/api/db/llm/models/${this.editingModel.model_id}`
          : '/api/db/llm/models';

        console.log('🌐 API URL pro ukládání:', url);

        const method = this.editingModel ? 'PUT' : 'POST';

        const modelData = {
          provider_id: this.modelForm.provider_id,
          model_name: this.modelForm.model_name,
          model_identifier: this.modelForm.model_identifier,
          context_length: this.modelForm.context_length || null,
          max_tokens_output: this.modelForm.max_tokens_output || null,
          temperature: this.modelForm.temperature || 0.7,
          capabilities: this.modelForm.capabilities,
          is_default: this.modelForm.is_default,
          is_active: this.modelForm.is_active
        };

        console.log('📤 Odesílaná data:', modelData);

        const response = await fetch(url, {
          method: method,
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(modelData)
        });

        console.log('📡 Response status:', response.status);

        if (response.ok) {
          const responseData = await response.json();
          console.log('✅ Response data:', responseData);
          const action = this.editingModel ? 'aktualizován' : 'vytvořen';
          alert(`✅ Model "${this.modelForm.model_name}" byl úspěšně ${action}!`);
          this.closeModelForm();
          await this.refreshModels();
          await this.refreshProviders(); // Aktualizace počtu modelů u poskytovatelů
        } else {
          const errorData = await response.json();
          console.error('❌ API Error:', errorData);
          alert(`❌ Chyba při ukládání modelu: ${errorData.detail || 'Neznámá chyba'}`);
        }
      } catch (error) {
        console.error('❌ Chyba při ukládání modelu:', error);
        alert('❌ Chyba při ukládání modelu: ' + error.message);
      } finally {
        this.isSaving = false;
      }
    },

    async addOllamaProvider() {
      try {
        console.log('🦙 Přidávání Ollama provideru...');

        const response = await fetch('/api/db/llm/providers/ollama', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            ollama_url: 'http://192.168.111.152:11434',
            provider_name: 'Ollama'
          })
        });

        if (response.ok) {
          const data = await response.json();
          console.log('✅ Ollama provider přidán:', data);

          // Zobrazíme úspěšnou zprávu
          this.$nextTick(() => {
            const message = `✅ ${data.message}\n\nAutomaticky načteny modely z Ollama serveru.`;
            alert(message);
          });

          // Obnovíme data
          await this.refreshProviders();
          await this.refreshModels();

        } else {
          const errorData = await response.json();
          console.error('❌ Chyba při přidávání Ollama:', errorData);
          alert(`❌ Chyba při přidávání Ollama provideru: ${errorData.detail || 'Neznámá chyba'}`);
        }
      } catch (error) {
        console.error('❌ Chyba při přidávání Ollama provideru:', error);
        alert('❌ Chyba při přidávání Ollama provideru: ' + error.message);
      }
    },

    async addOllama2Provider() {
      try {
        console.log('🦙 Přidávání Ollama2 provideru...');

        const response = await fetch('/api/db/llm/providers/ollama', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            ollama_url: 'http://192.168.111.106:11434',
            provider_name: 'Ollama2'
          })
        });

        if (response.ok) {
          const data = await response.json();
          console.log('✅ Ollama2 provider přidán:', data);

          // Zobrazíme úspěšnou zprávu
          this.$nextTick(() => {
            const message = `✅ ${data.message}\n\nAutomaticky načteny modely z Ollama2 serveru (192.168.111.106).`;
            alert(message);
          });

          // Obnovíme data
          await this.refreshProviders();
          await this.refreshModels();

        } else {
          const errorData = await response.json();
          console.error('❌ Chyba při přidávání Ollama2:', errorData);
          alert(`❌ Chyba při přidávání Ollama2 provideru: ${errorData.detail || 'Neznámá chyba'}`);
        }
      } catch (error) {
        console.error('❌ Chyba při přidávání Ollama2 provideru:', error);
        alert('❌ Chyba při přidávání Ollama2 provideru: ' + error.message);
      }
    },

    async addLMStudioProvider() {
      try {
        console.log('🏭 Přidávání LM Studio provideru...');

        const response = await fetch('/api/db/llm/providers/lmstudio', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            lmstudio_url: 'http://192.168.111.126:1234',
            provider_name: 'LM Studio'
          })
        });

        if (response.ok) {
          const data = await response.json();
          console.log('✅ LM Studio provider přidán:', data);

          // Zobrazíme úspěšnou zprávu
          this.$nextTick(() => {
            const message = `✅ ${data.message}\n\nAutomaticky načteny modely z LM Studio serveru (192.168.111.126).`;
            alert(message);
          });

          // Obnovíme data
          await this.refreshProviders();
          await this.refreshModels();

        } else {
          const errorData = await response.json();
          console.error('❌ Chyba při přidávání LM Studio:', errorData);
          alert(`❌ Chyba při přidávání LM Studio provideru: ${errorData.detail || 'Neznámá chyba'}`);
        }
      } catch (error) {
        console.error('❌ Chyba při přidávání LM Studio provideru:', error);
        alert('❌ Chyba při přidávání LM Studio provideru: ' + error.message);
      }
    },

    addProvider() {
      this.editingProvider = null;
      this.resetProviderForm();
      this.showProviderForm = true;
    },

    closeModelForm() {
      this.showModelForm = false;
      this.editingModel = null;
      this.resetModelForm();
    },

    resetModelForm() {
      this.modelForm = {
        provider_id: '',
        model_name: '',
        model_identifier: '',
        context_length: null,
        max_tokens_output: null,
        temperature: 0.7,
        capabilities: {
          text: true,
          code: false,
          vision: false,
          function_calling: false,
          reasoning: false,
          multimodal: false
        },
        is_default: false,
        is_active: true
      };
    },

    // Communication between LLM methods
    async startCommunication() {
      if (!this.canStartCommunication) return;

      this.isProcessing = true;
      this.finalAnswer = null;
      this.ai0Messages = [];
      this.ai1Messages = [];
      this.ai2Messages = [];

      try {
        // Vytvoření nové session
        this.currentSession = {
          id: `session_${Date.now()}`,
          startTime: new Date(),
          maxIterations: this.communicationForm.iterationCount,
          currentIteration: 0,
          status: 'initializing',
          ai0Model: this.communicationForm.ai0Model,
          originalPrompt: this.communicationForm.userPrompt
        };

        console.log('🚀 Spouštím komunikaci mezi LLM:', this.currentSession);

        // Krok 1: AI-0 optimalizuje prompt
        await this.optimizePromptWithAI0();

        // Krok 2: Spustit iterativní komunikaci mezi AI-1 a AI-2
        await this.runIterativeCommunication();

        // Krok 3: AI-0 validuje finální odpověď
        await this.validateFinalAnswer();

        this.currentSession.status = 'completed';
        console.log('✅ Komunikace mezi LLM dokončena');

      } catch (error) {
        console.error('❌ Chyba při komunikaci mezi LLM:', error);
        this.currentSession.status = 'error';
        alert(`Chyba při komunikaci: ${error.message}`);
      } finally {
        this.isProcessing = false;
      }
    },

    async optimizePromptWithAI0() {
      this.currentSession.status = 'optimizing';
      console.log('🔧 AI-0 optimalizuje prompt...');

      try {
        // Simulace optimalizace promptu (později nahradit skutečným API voláním)
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Detekce typu úkolu pro lepší optimalizaci
        const isCalculatorTask = this.currentSession.originalPrompt.toLowerCase().includes('kalkulačka') ||
                                this.currentSession.originalPrompt.toLowerCase().includes('calculator');

        let optimizedPrompt;
        let analysis;

        if (isCalculatorTask) {
          optimizedPrompt = `Vytvoř funkční webovou kalkulačku s následujícími požadavky:
1. HTML struktura s tlačítky pro číslice 0-9 a operace (+, -, *, /, =)
2. CSS styling pro moderní vzhled s tmavým designem
3. JavaScript funkcionalita pro všechny matematické operace
4. Zobrazení výsledků v reálném čase
5. Možnost mazání (Clear) a zpětného kroku (Backspace)
6. Responzivní design pro různé velikosti obrazovek

Původní požadavek: ${this.currentSession.originalPrompt}`;

          analysis = 'Detekován požadavek na kalkulačku. Optimalizován prompt pro vytvoření kompletní webové aplikace s HTML, CSS a JavaScript.';
        } else {
          optimizedPrompt = `[OPTIMALIZOVÁNO AI-0] ${this.currentSession.originalPrompt}

Rozšířené instrukce:
- Poskytni detailní a strukturovanou odpověď
- Zahrň praktické příklady kde je to možné
- Ověř faktickou správnost informací
- Použij jasný a srozumitelný jazyk`;

          analysis = 'Obecný prompt optimalizován pro lepší strukturu a detailnost odpovědi.';
        }

        // Uložení AI-0 aktivity
        const ai0Message = {
          id: `ai0_optimize_${Date.now()}`,
          type: 'optimization',
          phase: 'Optimalizace promptu',
          inputPrompt: this.currentSession.originalPrompt,
          outputPrompt: optimizedPrompt,
          analysis: analysis,
          timestamp: new Date()
        };

        this.ai0Messages.push(ai0Message);

        // Uložení do Mem0 (později implementovat)
        console.log('💾 Ukládám optimalizovaný prompt do Mem0:', optimizedPrompt);

        this.currentSession.optimizedPrompt = optimizedPrompt;

      } catch (error) {
        throw new Error(`Chyba při optimalizaci promptu: ${error.message}`);
      }
    },

    async runIterativeCommunication() {
      this.currentSession.status = 'processing';
      console.log('🔄 Spouštím iterativní komunikaci...');

      for (let i = 1; i <= this.currentSession.maxIterations; i++) {
        this.currentSession.currentIteration = i;
        console.log(`📝 Iterace ${i}/${this.currentSession.maxIterations}`);

        // AI-1 zpracuje prompt/předchozí odpověď AI-2
        await this.processWithAI1(i);

        // AI-2 analyzuje odpověď AI-1
        await this.processWithAI2(i);

        // Krátká pauza mezi iteracemi
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    },

    async processWithAI1(iteration) {
      console.log(`🤖 AI-1 zpracovává iteraci ${iteration}...`);

      try {
        // Simulace zpracování AI-1 (později nahradit skutečným API voláním)
        await new Promise(resolve => setTimeout(resolve, 1500));

        // Určení vstupního promptu
        let inputPrompt;
        if (iteration === 1) {
          inputPrompt = this.currentSession.optimizedPrompt;
        } else {
          // Najít poslední výstup od AI-2
          const lastAI2Message = this.ai2Messages[this.ai2Messages.length - 1];
          inputPrompt = lastAI2Message?.outputPrompt || 'Pokračuj v předchozí práci...';
        }

        // Simulace různých typů odpovědí podle iterace
        let ai1Response;
        let outputPrompt;

        const isCalculatorTask = this.currentSession.originalPrompt.toLowerCase().includes('kalkulačka');

        if (isCalculatorTask) {
          if (iteration === 1) {
            ai1Response = `Vytvářím HTML strukturu kalkulačky:

<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kalkulačka</title>
    <style>
        body { background: #1a1a1a; color: #fff; font-family: Arial; }
        .calculator { max-width: 300px; margin: 50px auto; }
        .display { width: 100%; height: 60px; font-size: 24px; text-align: right; }
        .buttons { display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px; }
        button { height: 60px; font-size: 18px; border: none; border-radius: 5px; cursor: pointer; }
        .number { background: #333; color: #fff; }
        .operator { background: #ff9500; color: #fff; }
        .equals { background: #ff9500; color: #fff; }
        .clear { background: #a6a6a6; color: #000; }
    </style>
</head>
<body>
    <div class="calculator">
        <input type="text" class="display" id="display" readonly>
        <div class="buttons">
            <button class="clear" onclick="clearDisplay()">C</button>
            <button class="clear" onclick="backspace()">⌫</button>
            <button class="operator" onclick="appendToDisplay('/')">/</button>
            <button class="operator" onclick="appendToDisplay('*')">×</button>
            <!-- Další tlačítka... -->
        </div>
    </div>
</body>
</html>`;
            outputPrompt = 'Zkontroluj HTML strukturu kalkulačky a navrhni vylepšení CSS stylů pro lepší UX.';
          } else if (iteration === 2) {
            ai1Response = `Přidávám JavaScript funkcionalita:

<script>
let display = document.getElementById('display');
let currentInput = '';
let operator = '';
let previousInput = '';

function appendToDisplay(value) {
    if (['+', '-', '*', '/'].includes(value)) {
        if (currentInput !== '') {
            operator = value;
            previousInput = currentInput;
            currentInput = '';
        }
    } else {
        currentInput += value;
    }
    updateDisplay();
}

function calculate() {
    if (previousInput !== '' && currentInput !== '' && operator !== '') {
        let result;
        const prev = parseFloat(previousInput);
        const curr = parseFloat(currentInput);

        switch(operator) {
            case '+': result = prev + curr; break;
            case '-': result = prev - curr; break;
            case '*': result = prev * curr; break;
            case '/': result = curr !== 0 ? prev / curr : 'Error'; break;
        }

        currentInput = result.toString();
        operator = '';
        previousInput = '';
        updateDisplay();
    }
}

function clearDisplay() {
    currentInput = '';
    operator = '';
    previousInput = '';
    updateDisplay();
}

function updateDisplay() {
    display.value = currentInput || '0';
}
</script>`;
            outputPrompt = 'Otestuj JavaScript funkcionalitu a navrhni dodatečné funkce jako historie výpočtů.';
          } else {
            ai1Response = `Finalizuji kalkulačku s pokročilými funkcemi a optimalizacemi pro iteraci ${iteration}.`;
            outputPrompt = 'Proveď finální kontrolu a optimalizaci celé kalkulačky.';
          }
        } else {
          ai1Response = `AI-1 odpověď pro iteraci ${iteration}: Zpracovávám optimalizovaný prompt a předchozí kontext. Analyzuji požadavky a vytvářím strukturovanou odpověď.`;
          outputPrompt = `Zkontroluj a rozšiř tuto odpověď o další perspektivy pro iteraci ${iteration}.`;
        }

        const message = {
          id: `ai1_${iteration}_${Date.now()}`,
          type: 'response',
          content: ai1Response,
          inputPrompt: inputPrompt,
          outputPrompt: outputPrompt,
          iteration: iteration,
          timestamp: new Date()
        };

        this.ai1Messages.push(message);
        console.log('💾 Ukládám AI-1 odpověď do Mem0:', ai1Response);

      } catch (error) {
        throw new Error(`Chyba při zpracování AI-1 v iteraci ${iteration}: ${error.message}`);
      }
    },

    async processWithAI2(iteration) {
      console.log(`🧠 AI-2 analyzuje iteraci ${iteration}...`);

      try {
        // Simulace analýzy AI-2 (později nahradit skutečným API voláním)
        await new Promise(resolve => setTimeout(resolve, 1500));

        // Získání posledního výstupu od AI-1
        const lastAI1Message = this.ai1Messages[this.ai1Messages.length - 1];
        const inputFromAI1 = lastAI1Message?.content || 'Žádný vstup od AI-1';

        let ai2Response;
        let outputPrompt;

        const isCalculatorTask = this.currentSession.originalPrompt.toLowerCase().includes('kalkulačka');

        if (isCalculatorTask) {
          if (iteration === 1) {
            ai2Response = `Analýza HTML struktury kalkulačky:

✅ Pozitivní aspekty:
- Správná HTML5 struktura
- Tmavý design konzistentní s požadavky
- Responzivní grid layout pro tlačítka
- Základní CSS styling

🔧 Doporučení pro vylepšení:
- Přidat všechna číselná tlačítka (0-9)
- Implementovat hover efekty pro lepší UX
- Přidat box-shadow pro 3D efekt tlačítek
- Zlepšit typografii a spacing
- Přidat animace pro stisknutí tlačítek

📝 Navrhované CSS vylepšení:
button:hover { transform: scale(1.05); transition: 0.2s; }
button:active { transform: scale(0.95); }
.display { border: 2px solid #333; padding: 10px; }`;

            outputPrompt = 'Implementuj navrhovaná CSS vylepšení a přidej kompletní sadu tlačítek.';
          } else if (iteration === 2) {
            ai2Response = `Analýza JavaScript funkcionality:

✅ Správně implementováno:
- Základní matematické operace
- Správné parsování čísel
- Error handling pro dělení nulou
- Čistá struktura kódu

🔧 Doporučení pro rozšíření:
- Přidat podporu pro desetinná čísla
- Implementovat backspace funkcionalitu
- Přidat klávesovou podporu
- Vytvořit historii výpočtů
- Přidat pokročilé operace (%, √, x²)

📝 Navrhovaný kód pro vylepšení:
function backspace() {
    if (currentInput.length > 0) {
        currentInput = currentInput.slice(0, -1);
        updateDisplay();
    }
}

document.addEventListener('keydown', function(event) {
    if (event.key >= '0' && event.key <= '9') {
        appendToDisplay(event.key);
    }
    // Další klávesové zkratky...
});`;

            outputPrompt = 'Implementuj navrhovaná vylepšení a přidej pokročilé matematické funkce.';
          } else {
            ai2Response = `Finální analýza kalkulačky pro iteraci ${iteration}:

Kalkulačka je téměř kompletní. Doporučuji finální optimalizace:
- Testování na různých zařízeních
- Optimalizace výkonu
- Přidání dokumentace
- Implementace unit testů`;

            outputPrompt = 'Proveď finální optimalizace a připrav kalkulačku k nasazení.';
          }
        } else {
          ai2Response = `AI-2 analýza pro iteraci ${iteration}:

Analyzuji odpověď AI-1 a poskytuju alternativní perspektivu:

🔍 Klíčové pozorování:
- Odpověď AI-1 je strukturovaná a relevantní
- Obsahuje praktické informace
- Může být rozšířena o další detaily

💡 Doporučení pro vylepšení:
- Přidat konkrétní příklady
- Rozšířit o související témata
- Ověřit faktickou správnost
- Zlepšit čitelnost a strukturu`;

          outputPrompt = `Rozšiř odpověď o doporučené vylepšení pro iteraci ${iteration + 1}.`;
        }

        const message = {
          id: `ai2_${iteration}_${Date.now()}`,
          type: 'analysis',
          content: ai2Response,
          inputPrompt: inputFromAI1,
          outputPrompt: outputPrompt,
          iteration: iteration,
          timestamp: new Date()
        };

        this.ai2Messages.push(message);
        console.log('💾 Ukládám AI-2 analýzu do Mem0:', ai2Response);

      } catch (error) {
        throw new Error(`Chyba při zpracování AI-2 v iteraci ${iteration}: ${error.message}`);
      }
    },

    async validateFinalAnswer() {
      this.currentSession.status = 'validating';
      console.log('✅ AI-0 validuje finální odpověď...');

      try {
        // Simulace validace (později nahradit skutečným API voláním)
        await new Promise(resolve => setTimeout(resolve, 2000));

        const isCalculatorTask = this.currentSession.originalPrompt.toLowerCase().includes('kalkulačka');
        let finalContent;
        let topicAdherence;

        if (isCalculatorTask) {
          finalContent = `🧮 **Kompletní webová kalkulačka**

Na základě ${this.currentSession.maxIterations} iterací komunikace mezi AI-1 a AI-2 byla vytvořena plně funkční webová kalkulačka:

**📋 Implementované funkce:**
✅ HTML5 struktura s moderním designem
✅ CSS styling s tmavým tématem
✅ JavaScript funkcionalita pro všechny základní operace (+, -, *, /)
✅ Responzivní design pro různé obrazovky
✅ Error handling (dělení nulou)
✅ Clear a Backspace funkce
✅ Klávesová podpora
✅ Hover efekty a animace

**🎯 Výsledek:** Plně funkční, moderní webová kalkulačka s kompletním HTML, CSS a JavaScript kódem připravená k použití!`;

          topicAdherence = 'excellent';
        } else {
          finalContent = `📋 **Finální odpověď**

Na základě ${this.currentSession.maxIterations} iterací komunikace mezi AI-1 a AI-2 byla vytvořena komplexní odpověď na zadaný prompt.

**🔍 Proces zpracování:**
1. **AI-0** optimalizoval původní prompt pro lepší strukturu
2. **AI-1** vytvořil primární odpověď s detailními informacemi
3. **AI-2** analyzoval a rozšířil odpověď o další perspektivy
4. Iterativní proces zajistil vysokou kvalitu výsledku

**✅ Kvalita odpovědi:**
- Téma bylo dodrženo během celého procesu
- Odpověď je strukturovaná a komplexní
- Obsahuje praktické informace a příklady
- Prošla validací AI-0 systémem

**🎯 Výsledek:** Kvalitní, ověřená odpověď připravená k použití!`;

          topicAdherence = 'good';
        }

        // Uložení AI-0 validační aktivity
        const ai0ValidationMessage = {
          id: `ai0_validation_${Date.now()}`,
          type: 'validation',
          phase: 'Finální validace',
          inputPrompt: 'Validace výsledků všech iterací',
          analysis: `Provedena validace ${this.currentSession.maxIterations} iterací. ${isCalculatorTask ? 'Kalkulačka splňuje všechny požadavky.' : 'Odpověď je kvalitní a relevantní.'}`,
          timestamp: new Date()
        };

        this.ai0Messages.push(ai0ValidationMessage);

        this.finalAnswer = {
          content: finalContent,
          topicAdherence: topicAdherence,
          validatedBy: 'AI-0',
          timestamp: new Date()
        };

        console.log('✨ Finální odpověď validována:', this.finalAnswer);

      } catch (error) {
        throw new Error(`Chyba při validaci finální odpovědi: ${error.message}`);
      }
    },

    stopCommunication() {
      if (this.currentSession) {
        this.currentSession.status = 'stopped';
        this.isProcessing = false;
        console.log('🛑 Komunikace zastavena uživatelem');
      }
    },

    clearSession() {
      this.currentSession = null;
      this.finalAnswer = null;
      this.ai0Messages = [];
      this.ai1Messages = [];
      this.ai2Messages = [];
      this.communicationForm.userPrompt = '';
      console.log('🗑️ Session vymazána');
    },

    showManualInput(target) {
      this.manualInputTarget = target;
      this.manualInputText = '';
      this.showManualInputModal = true;
    },

    closeManualInput() {
      this.showManualInputModal = false;
      this.manualInputTarget = null;
      this.manualInputText = '';
    },

    async sendManualInput() {
      if (!this.manualInputText.trim() || !this.manualInputTarget) return;

      try {
        const message = {
          id: `manual_${this.manualInputTarget}_${Date.now()}`,
          type: 'manual',
          content: this.manualInputText,
          iteration: this.currentSession?.currentIteration || 0,
          timestamp: new Date()
        };

        if (this.manualInputTarget === 'ai1') {
          this.ai1Messages.push(message);
        } else {
          this.ai2Messages.push(message);
        }

        console.log(`✏️ Manuální input pro ${this.manualInputTarget}:`, this.manualInputText);

        this.closeManualInput();

      } catch (error) {
        console.error('Chyba při odesílání manuálního inputu:', error);
        alert(`Chyba při odesílání: ${error.message}`);
      }
    },

    getSessionStatusText(status) {
      const statusMap = {
        'initializing': '🔧 Inicializace',
        'optimizing': '⚡ Optimalizace promptu',
        'processing': '🔄 Zpracovávání',
        'validating': '✅ Validace',
        'completed': '✨ Dokončeno',
        'stopped': '🛑 Zastaveno',
        'error': '❌ Chyba'
      };
      return statusMap[status] || status;
    },

    getAdherenceClass(adherence) {
      const classMap = {
        'excellent': 'adherence-excellent',
        'good': 'adherence-good',
        'fair': 'adherence-fair',
        'poor': 'adherence-poor'
      };
      return classMap[adherence] || '';
    },

    getAdherenceText(adherence) {
      const textMap = {
        'excellent': '🟢 Výborné dodržení tématu',
        'good': '🟡 Dobré dodržení tématu',
        'fair': '🟠 Částečné dodržení tématu',
        'poor': '🔴 Slabé dodržení tématu'
      };
      return textMap[adherence] || adherence;
    },

    formatTime(timestamp) {
      if (!timestamp) return '';
      const date = new Date(timestamp);
      return date.toLocaleTimeString('cs-CZ', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    }
  },

  async mounted() {
    console.log('LlmManagement mounted - začínám načítání dat');
    await this.refreshProviders();
    await this.refreshModels();
    await this.refreshPerformanceData();
    console.log('LlmManagement mounted - dokončeno, modelů:', this.models.length);
  }
};
</script>

<style>
@import '@/styles/admin-common.css';
@import '@/styles/chat-test.css';

/* Ollama specific styles */
.management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: #2a2a2a;
  border-radius: 8px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.ollama-btn {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.ollama-btn:hover {
  background: linear-gradient(135deg, #e55a2b, #e8841a);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
}

.ollama2-btn {
  background: linear-gradient(135deg, #6b46c1, #9333ea);
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.ollama2-btn:hover {
  background: linear-gradient(135deg, #5b21b6, #7c3aed);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(107, 70, 193, 0.3);
}

.lmstudio-btn {
  background: linear-gradient(135deg, #059669, #10b981);
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.lmstudio-btn:hover {
  background: linear-gradient(135deg, #047857, #059669);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
}

.add-btn {
  background: #4CAF50;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.add-btn:hover {
  background: #45a049;
  transform: translateY(-2px);
}

/* Communication between LLM styles */
.communication-section {
  padding: 20px;
}

.section-description {
  color: #b0b0b0;
  margin-bottom: 30px;
  font-style: italic;
}

.communication-controls {
  background: #2a2a2a;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.control-row {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
  align-items: end;
}

.control-row:last-child {
  margin-bottom: 0;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.control-group.full-width {
  flex: 1;
}

.control-group label {
  color: #e0e0e0;
  font-weight: 500;
  font-size: 14px;
}

.model-select, .iteration-input {
  background: #1a1a1a;
  border: 1px solid #444;
  color: #e0e0e0;
  padding: 8px 12px;
  border-radius: 4px;
  min-width: 200px;
}

.iteration-input {
  min-width: 100px;
  max-width: 120px;
}

.prompt-textarea {
  background: #1a1a1a;
  border: 1px solid #444;
  color: #e0e0e0;
  padding: 12px;
  border-radius: 4px;
  resize: vertical;
  font-family: inherit;
  width: 100%;
}

.start-communication-btn {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.start-communication-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #45a049, #3d8b40);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.start-communication-btn:disabled {
  background: #666;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.stop-communication-btn {
  background: linear-gradient(135deg, #f44336, #d32f2f);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.stop-communication-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #d32f2f, #b71c1c);
  transform: translateY(-2px);
}

.clear-session-btn {
  background: linear-gradient(135deg, #ff9800, #f57c00);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.clear-session-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #f57c00, #ef6c00);
  transform: translateY(-2px);
}

.session-status {
  background: #2a2a2a;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  border-left: 4px solid #4CAF50;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.session-meta {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #b0b0b0;
}

.session-status.error {
  border-left-color: #f44336;
}

.session-status.stopped {
  border-left-color: #ff9800;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #1a1a1a;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #45a049);
  transition: width 0.3s ease;
}

.final-answer {
  background: #2a2a2a;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  border-left: 4px solid #FFD700;
}

.final-answer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.answer-meta {
  display: flex;
  gap: 15px;
  font-size: 14px;
  color: #b0b0b0;
}

.final-answer-content {
  color: #e0e0e0;
  line-height: 1.6;
  font-size: 16px;
}

.adherence-excellent { color: #4CAF50; }
.adherence-good { color: #FFC107; }
.adherence-fair { color: #FF9800; }
.adherence-poor { color: #f44336; }

.ai0-activity {
  margin-bottom: 20px;
}

.ai0-panel {
  border-left: 4px solid #FFD700;
}

.triple-panel-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.dual-panel-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.ai-panel {
  background: #2a2a2a;
  border-radius: 8px;
  overflow: hidden;
}

.ai1-panel {
  border-left: 4px solid #2196F3;
}

.ai2-panel {
  border-left: 4px solid #9C27B0;
}

.panel-header {
  background: #1a1a1a;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  margin: 0;
  color: #e0e0e0;
}

.manual-input-btn {
  background: #666;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.manual-input-btn:hover:not(:disabled) {
  background: #777;
}

.manual-input-btn:disabled {
  background: #444;
  cursor: not-allowed;
}

.conversation-display {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}

.conversation-message {
  background: #1a1a1a;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 10px;
  border-left: 3px solid #666;
}

.conversation-message.response {
  border-left-color: #2196F3;
}

.conversation-message.analysis {
  border-left-color: #9C27B0;
}

.conversation-message.manual {
  border-left-color: #FF9800;
  background: #2a2a1a;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 12px;
  color: #b0b0b0;
}

.message-content {
  color: #e0e0e0;
  line-height: 1.5;
}

.prompt-section {
  margin-bottom: 12px;
}

.prompt-section:last-child {
  margin-bottom: 0;
}

.prompt-text, .response-text, .analysis-text {
  background: #1a1a1a;
  padding: 8px 12px;
  border-radius: 4px;
  margin-top: 4px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
  border-left: 3px solid #444;
}

.prompt-text {
  border-left-color: #2196F3;
}

.response-text {
  border-left-color: #4CAF50;
}

.analysis-text {
  border-left-color: #FF9800;
}

.empty-conversation {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 40px 20px;
}

.manual-input-modal {
  max-width: 600px;
  width: 90%;
}

.manual-input-textarea {
  width: 100%;
  background: #1a1a1a;
  border: 1px solid #444;
  color: #e0e0e0;
  padding: 12px;
  border-radius: 4px;
  resize: vertical;
  font-family: inherit;
}

@media (max-width: 768px) {
  .triple-panel-layout,
  .dual-panel-layout {
    grid-template-columns: 1fr;
  }

  .control-row {
    flex-direction: column;
    align-items: stretch;
  }

  .session-meta {
    flex-direction: column;
    gap: 5px;
  }

  .model-select {
    min-width: auto;
    width: 100%;
  }
}
</style>
