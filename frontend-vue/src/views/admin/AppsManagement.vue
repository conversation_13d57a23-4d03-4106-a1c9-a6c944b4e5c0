<template>
  <div class="apps-management">
    <div class="page-header">
      <h1>📱 Správa aplikací</h1>
      <p>Správa a spouštění aplikací v GENT systému</p>
    </div>

    <!-- Statistiky -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">📊</div>
        <div class="stat-content">
          <div class="stat-number">{{ apps.length }}</div>
          <div class="stat-label">Celkem aplikací</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">✅</div>
        <div class="stat-content">
          <div class="stat-number">{{ activeAppsCount }}</div>
          <div class="stat-label">Aktivních aplikací</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">🚀</div>
        <div class="stat-content">
          <div class="stat-number">{{ totalExecutions }}</div>
          <div class="stat-label">Celkem spuštění</div>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">🏷️</div>
        <div class="stat-content">
          <div class="stat-number">{{ categoriesCount }}</div>
          <div class="stat-label">Kategorií</div>
        </div>
      </div>
    </div>

    <!-- Filtry a vyhledávání -->
    <div class="filters-section">
      <div class="search-box">
        <input 
          v-model="searchQuery" 
          type="text" 
          placeholder="🔍 Vyhledat aplikace..."
          class="search-input"
        >
      </div>
      <div class="filter-buttons">
        <button 
          v-for="category in categories" 
          :key="category"
          @click="selectedCategory = selectedCategory === category ? '' : category"
          :class="['filter-btn', { active: selectedCategory === category }]"
        >
          {{ category }}
        </button>
      </div>
    </div>

    <!-- Seznam aplikací -->
    <div class="apps-grid">
      <div 
        v-for="app in filteredApps" 
        :key="app.id"
        class="app-card"
        :class="{ inactive: !app.is_active }"
      >
        <div class="app-header">
          <div class="app-icon">
            {{ getAppIcon(app.category) }}
          </div>
          <div class="app-title">
            <h3>{{ app.name }}</h3>
            <span class="app-version">v{{ app.version }}</span>
          </div>
          <div class="app-status">
            <span :class="['status-badge', app.is_active ? 'active' : 'inactive']">
              {{ app.is_active ? 'Aktivní' : 'Neaktivní' }}
            </span>
          </div>
        </div>

        <div class="app-description">
          {{ app.description || 'Bez popisu' }}
        </div>

        <div class="app-meta">
          <div class="meta-item">
            <span class="meta-label">Jazyk:</span>
            <span class="meta-value">{{ app.language }}</span>
          </div>
          <div class="meta-item">
            <span class="meta-label">Kategorie:</span>
            <span class="meta-value">{{ app.category || 'Nezařazeno' }}</span>
          </div>
          <div class="meta-item">
            <span class="meta-label">Spuštění:</span>
            <span class="meta-value">{{ app.execution_count }}×</span>
          </div>
          <div class="meta-item">
            <span class="meta-label">Autor:</span>
            <span class="meta-value">{{ app.author || 'Neznámý' }}</span>
          </div>
        </div>

        <div class="app-tags" v-if="app.tags && app.tags.length">
          <span v-for="tag in app.tags" :key="tag" class="tag">{{ tag }}</span>
        </div>

        <div class="app-actions">
          <button 
            @click="executeApp(app)"
            :disabled="!app.is_active"
            class="btn-execute"
          >
            🚀 Spustit
          </button>
          <button 
            @click="viewAppDetails(app)"
            class="btn-details"
          >
            📋 Detail
          </button>
          <button 
            @click="editApp(app)"
            class="btn-edit"
          >
            ✏️ Upravit
          </button>
        </div>

        <div class="app-footer">
          <span class="created-date">
            Vytvořeno: {{ formatDate(app.created_at) }}
          </span>
          <span v-if="app.last_executed_at" class="last-execution">
            Naposledy spuštěno: {{ formatDate(app.last_executed_at) }}
          </span>
        </div>
      </div>
    </div>

    <!-- Prázdný stav -->
    <div v-if="filteredApps.length === 0" class="empty-state">
      <div class="empty-icon">📱</div>
      <h3>Žádné aplikace nenalezeny</h3>
      <p v-if="searchQuery || selectedCategory">
        Zkuste změnit vyhledávací kritéria nebo filtry.
      </p>
      <p v-else>
        V systému zatím nejsou žádné aplikace.
      </p>
    </div>

    <!-- Loading stav -->
    <div v-if="isLoading" class="loading-state">
      <div class="loading-spinner"></div>
      <p>Načítám aplikace...</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AppsManagement',
  data() {
    return {
      apps: [], // Načítá se z databáze přes API
      searchQuery: '',
      selectedCategory: '',
      isLoading: false
    }
  },
  computed: {
    filteredApps() {
      let filtered = this.apps;

      // Filtrování podle vyhledávání
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase();
        filtered = filtered.filter(app => 
          app.name.toLowerCase().includes(query) ||
          app.description?.toLowerCase().includes(query) ||
          app.tags?.some(tag => tag.toLowerCase().includes(query))
        );
      }

      // Filtrování podle kategorie
      if (this.selectedCategory) {
        filtered = filtered.filter(app => app.category === this.selectedCategory);
      }

      return filtered;
    },
    activeAppsCount() {
      return this.apps.filter(app => app.is_active).length;
    },
    totalExecutions() {
      return this.apps.reduce((sum, app) => sum + app.execution_count, 0);
    },
    categories() {
      const cats = [...new Set(this.apps.map(app => app.category).filter(Boolean))];
      return cats.sort();
    },
    categoriesCount() {
      return this.categories.length;
    }
  },
  methods: {
    getAppIcon(category) {
      const icons = {
        'benchmark': '📊',
        'utility': '🔧',
        'game': '🎮',
        'tool': '⚒️',
        'analysis': '📈',
        'test': '🧪'
      };
      return icons[category] || '📱';
    },
    formatDate(dateString) {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString('cs-CZ', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    },
    async executeApp(app) {
      console.log('🚀 Spouštím aplikaci:', app.name);
      // TODO: Implementovat skutečné spuštění přes API
    },
    viewAppDetails(app) {
      console.log('📋 Zobrazuji detail aplikace:', app.name);
      // TODO: Implementovat detail view
    },
    editApp(app) {
      console.log('✏️ Upravuji aplikaci:', app.name);
      // TODO: Implementovat editaci
    },
    async loadApps() {
      this.isLoading = true;
      try {
        console.log('📱 Načítám aplikace z API...');
        const response = await fetch('/api/apps');
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        this.apps = await response.json();
        console.log('✅ Načteno aplikací:', this.apps.length);
      } catch (error) {
        console.error('❌ Chyba při načítání aplikací:', error);
        this.apps = []; // Prázdný seznam při chybě
      } finally {
        this.isLoading = false;
      }
    }
  },
  mounted() {
    this.loadApps();
  }
}
</script>

<style scoped>
.apps-management {
  padding: 20px;
  background: #1a1a1a;
  color: #e0e0e0;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h1 {
  color: #fff;
  margin: 0 0 10px 0;
  font-size: 28px;
}

.page-header p {
  color: #b0b0b0;
  margin: 0;
  font-size: 16px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: #2a2a2a;
  padding: 20px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 15px;
  border-left: 4px solid #4CAF50;
}

.stat-icon {
  font-size: 24px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #4CAF50;
}

.stat-label {
  color: #b0b0b0;
  font-size: 14px;
}

.filters-section {
  background: #2a2a2a;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;
  display: flex;
  gap: 20px;
  align-items: center;
  flex-wrap: wrap;
}

.search-input {
  background: #1a1a1a;
  border: 1px solid #444;
  color: #e0e0e0;
  padding: 10px 15px;
  border-radius: 6px;
  font-size: 14px;
  min-width: 300px;
}

.search-input:focus {
  outline: none;
  border-color: #4CAF50;
}

.filter-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.filter-btn {
  background: #333;
  color: #e0e0e0;
  border: none;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.filter-btn:hover {
  background: #444;
}

.filter-btn.active {
  background: #4CAF50;
  color: white;
}

.apps-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.app-card {
  background: #2a2a2a;
  border-radius: 8px;
  padding: 20px;
  border-left: 4px solid #4CAF50;
  transition: all 0.3s ease;
}

.app-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

.app-card.inactive {
  border-left-color: #666;
  opacity: 0.7;
}

.app-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.app-icon {
  font-size: 24px;
}

.app-title h3 {
  margin: 0;
  color: #fff;
  font-size: 18px;
}

.app-version {
  color: #b0b0b0;
  font-size: 12px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.status-badge.active {
  background: #4CAF50;
  color: white;
}

.status-badge.inactive {
  background: #666;
  color: #ccc;
}

.app-description {
  color: #b0b0b0;
  margin-bottom: 15px;
  line-height: 1.4;
}

.app-meta {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-bottom: 15px;
}

.meta-item {
  display: flex;
  justify-content: space-between;
  font-size: 14px;
}

.meta-label {
  color: #888;
}

.meta-value {
  color: #e0e0e0;
  font-weight: 500;
}

.app-tags {
  display: flex;
  gap: 8px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.tag {
  background: #333;
  color: #4CAF50;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.app-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.app-actions button {
  padding: 8px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.btn-execute {
  background: #4CAF50;
  color: white;
}

.btn-execute:hover:not(:disabled) {
  background: #45a049;
}

.btn-execute:disabled {
  background: #666;
  cursor: not-allowed;
}

.btn-details {
  background: #2196F3;
  color: white;
}

.btn-details:hover {
  background: #1976D2;
}

.btn-edit {
  background: #FF9800;
  color: white;
}

.btn-edit:hover {
  background: #F57C00;
}

.app-footer {
  border-top: 1px solid #333;
  padding-top: 10px;
  font-size: 12px;
  color: #888;
}

.created-date, .last-execution {
  display: block;
  margin-bottom: 4px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #888;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.empty-state h3 {
  color: #b0b0b0;
  margin-bottom: 10px;
}

.loading-state {
  text-align: center;
  padding: 60px 20px;
  color: #888;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #333;
  border-top: 4px solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .apps-grid {
    grid-template-columns: 1fr;
  }

  .filters-section {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input {
    min-width: auto;
    width: 100%;
  }
}
</style>
