<template>
  <div class="admin-container">
    <div class="admin-header">
      <h1>🔧 MCP Management</h1>
      <p>Správa MCP poskytovatelů, nástrojů a serverů</p>
    </div>

    <!-- Tab Navigation -->
    <div class="tab-navigation">
      <button
        @click="activeTab = 'providers'"
        :class="{ active: activeTab === 'providers' }"
        class="tab-btn"
      >
        🏢 Poskytovatelé
      </button>
      <button
        @click="activeTab = 'management'"
        :class="{ active: activeTab === 'management' }"
        class="tab-btn"
      >
        ⚙️ Správa
      </button>
      <button
        @click="activeTab = 'testing'"
        :class="{ active: activeTab === 'testing' }"
        class="tab-btn"
      >
        🧪 Testování
      </button>
      <button
        @click="activeTab = 'performance'"
        :class="{ active: activeTab === 'performance' }"
        class="tab-btn"
      >
        📊 Statistiky
      </button>
    </div>

    <!-- Providers Tab -->
    <div v-if="activeTab === 'providers'" class="tab-content">
      <div class="providers-section">
        <div class="section-header">
          <h2>MCP Poskytovatelé</h2>
          <div class="header-actions">
            <button @click="refreshProviders" class="refresh-btn">
              🔄 Obnovit
            </button>
          </div>
        </div>

        <!-- Providers Grid -->
        <div class="providers-grid">
          <div
            v-for="provider in providers"
            :key="provider.provider_id"
            class="provider-card"
            @click="selectProvider(provider)"
            :class="{ selected: selectedProvider?.provider_id === provider.provider_id }"
          >
            <div class="provider-header">
              <h3>{{ provider.display_name || provider.provider_name }}</h3>
              <div class="provider-status" :class="provider.is_active ? 'active' : 'inactive'">
                {{ provider.is_active ? '🟢' : '🔴' }}
              </div>
            </div>
            <div class="provider-info">
              <p>{{ provider.description || 'Bez popisu' }}</p>
              <div class="provider-stats">
                <span>Typ: {{ provider.provider_type }}</span>
                <span>Nástroje: {{ getProviderToolsCount(provider.provider_id) }}</span>
                <span>API: {{ getApiKeyStatus(provider) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Selected Provider Details -->
        <div v-if="selectedProvider" class="provider-details">
          <div class="provider-detail-header">
            <h3>Nástroje poskytovatele: {{ selectedProvider.display_name || selectedProvider.provider_name }}</h3>
            <div class="provider-detail-stats">
              <div class="stat-item">
                <span class="stat-label">Celkem nástrojů:</span>
                <span class="stat-value">{{ selectedProviderTools.length }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Aktivní nástroje:</span>
                <span class="stat-value">{{ selectedProviderTools.filter(t => t.is_active).length }}</span>
              </div>
            </div>
          </div>

          <div class="models-grid">
            <div
              v-for="tool in selectedProviderTools"
              :key="tool.tool_id"
              class="model-card"
            >
              <div class="model-header">
                <h4>{{ tool.display_name || tool.tool_name }}</h4>
                <div class="model-status">
                  {{ tool.is_active ? '✅' : '❌' }}
                </div>
              </div>
              <div class="model-info">
                <div class="model-specs">
                  <span>ID: {{ tool.tool_identifier }}</span>
                  <span>Timeout: {{ tool.timeout_seconds }}s</span>
                </div>
                <p class="model-description">{{ tool.description || 'Bez popisu' }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Testing Tab -->
    <div v-if="activeTab === 'testing'" class="tab-content">
      <div class="testing-section">
        <div class="section-header">
          <h2>🧪 MCP Testování</h2>
          <p>Vyberte MCP server a zadejte parametry pro test</p>
        </div>

        <!-- MCP Server Selection -->
        <div class="mcp-server-selection">
          <div class="server-cards">
            <div
              v-for="provider in providers"
              :key="provider.provider_id"
              class="server-card"
              :class="{ selected: selectedMcpServer === provider.provider_name }"
              @click="selectMcpServer(provider.provider_name)"
            >
              <div class="server-icon">
                {{ getServerIcon(provider.provider_name) }}
              </div>
              <div class="server-name">{{ provider.display_name || provider.provider_name }}</div>
              <div class="server-status">
                {{ provider.is_active ? '🟢 Aktivní' : '🔴 Neaktivní' }}
              </div>
            </div>
          </div>
        </div>

        <!-- Test Input -->
        <div v-if="selectedMcpServer" class="test-input-section">
          <h3>{{ getServerTitle(selectedMcpServer) }}</h3>

          <div class="test-form">
            <div class="form-group">
              <label>{{ getInputLabel(selectedMcpServer) }}</label>
              <input
                v-model="testInput"
                type="text"
                :placeholder="getInputPlaceholder(selectedMcpServer)"
                class="form-input"
              />
            </div>

            <!-- Format Selection for Fetch -->
            <div v-if="selectedMcpServer === 'fetch'" class="form-group">
              <label>Formát výstupu:</label>
              <select v-model="testFormat" class="form-input">
                <option value="html">HTML</option>
                <option value="text">TEXT</option>
                <option value="markdown">MARKDOWN</option>
                <option value="json">JSON</option>
              </select>
            </div>

            <!-- Search Type for Search Engines -->
            <div v-if="['brave-search', 'tavily'].includes(selectedMcpServer)" class="form-group">
              <label>Typ vyhledávání:</label>
              <select v-model="testSearchType" class="form-input">
                <option value="web">Web</option>
                <option value="news">Zprávy</option>
                <option value="images">Obrázky</option>
                <option value="videos">Videa</option>
              </select>
            </div>

            <!-- Response Type for Perplexity -->
            <div v-if="selectedMcpServer === 'perplexity'" class="form-group">
              <label>Typ odpovědi:</label>
              <select v-model="testResponseType" class="form-input">
                <option value="concise">Stručná</option>
                <option value="detailed">Detailní</option>
                <option value="creative">Kreativní</option>
                <option value="factual">Faktická</option>
              </select>
            </div>

            <button @click="runMcpTest" class="test-execute-btn" :disabled="isTestingTool || !testInput">
              {{ isTestingTool ? '🔄 Testování...' : '🧪 Spustit test' }}
            </button>
          </div>
        </div>

        <!-- Test Results -->
        <div v-if="testResult" class="test-results">
          <div class="result-header">
            <h3>📋 Výsledek testu: {{ selectedMcpServer }}</h3>
            <div class="result-meta">
              <span>Input: {{ testResult.input }}</span>
              <span>Čas: {{ testResult.response_time }}ms</span>
              <span :class="testResult.success ? 'status-success' : 'status-error'">
                {{ testResult.success ? '✅ Úspěch' : '❌ Chyba' }}
              </span>
            </div>
          </div>
          
          <div class="result-content">
            <div v-if="testResult.success" class="result-success">
              <h4>Odpověď MCP serveru:</h4>
              <pre class="result-data">{{ formatTestResult(testResult.data) }}</pre>
            </div>
            <div v-else class="result-error">
              <h4>Chyba:</h4>
              <pre class="error-data">{{ testResult.error }}</pre>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Management Tab -->
    <div v-if="activeTab === 'management'" class="tab-content">
      <div class="management-section">
        <div class="section-header">
          <h2>⚙️ Správa MCP systému</h2>
          <div class="header-actions">
            <button @click="refreshProviders" class="refresh-btn">
              🔄 Obnovit data
            </button>
          </div>
        </div>

        <!-- Add Provider Button -->
        <div class="management-actions">
          <button @click="showAddProviderForm = true" class="add-btn">
            ➕ Přidat MCP server
          </button>
        </div>

        <!-- Management Table -->
        <div class="management-table">
          <div class="table-header">
            <div>ID</div>
            <div>Název</div>
            <div>Typ</div>
            <div>API klíč</div>
            <div>Aktivní</div>
            <div>Nástroje</div>
            <div>Akce</div>
          </div>
          <div
            v-for="provider in providers"
            :key="provider.provider_id"
            class="table-row"
          >
            <div>{{ provider.provider_id }}</div>
            <div>{{ provider.display_name || provider.provider_name }}</div>
            <div>{{ provider.provider_type }}</div>
            <div>{{ getApiKeyStatus(provider) }}</div>
            <div>
              <span :class="provider.is_active ? 'status-active' : 'status-inactive'">
                {{ provider.is_active ? '✅ Ano' : '❌ Ne' }}
              </span>
            </div>
            <div>{{ getProviderToolsCount(provider.provider_id) }}</div>
            <div class="actions-cell">
              <button @click="editProvider(provider)" class="edit-btn" title="Upravit">
                ✏️
              </button>
              <button @click="deleteProvider(provider)" class="delete-btn" title="Smazat">
                🗑️
              </button>
            </div>
          </div>
        </div>

        <!-- Add/Edit Provider Form -->
        <div v-if="showAddProviderForm || editingProvider" class="provider-form-overlay">
          <div class="provider-form">
            <div class="form-header">
              <h3>{{ editingProvider ? 'Upravit MCP server' : 'Přidat MCP server' }}</h3>
              <button @click="closeProviderForm" class="close-btn">✕</button>
            </div>

            <div class="form-body">
              <div class="form-group">
                <label>Název:</label>
                <input v-model="providerForm.provider_name" type="text" placeholder="např. my-custom-server" />
              </div>

              <div class="form-group">
                <label>Zobrazovaný název:</label>
                <input v-model="providerForm.display_name" type="text" placeholder="např. Můj vlastní server" />
              </div>

              <div class="form-group">
                <label>Typ:</label>
                <select v-model="providerForm.provider_type">
                  <option value="web_search">Web Search</option>
                  <option value="web_fetch">Web Fetch</option>
                  <option value="ai_search">AI Search</option>
                  <option value="custom">Custom</option>
                </select>
              </div>

              <div class="form-group">
                <label>API klíč:</label>
                <input v-model="providerForm.api_key" type="text" placeholder="Zadejte API klíč (volitelné)" />
              </div>

              <div class="form-group">
                <label>Popis:</label>
                <textarea v-model="providerForm.description" placeholder="Popis MCP serveru"></textarea>
              </div>

              <div class="form-group">
                <label>
                  <input v-model="providerForm.is_active" type="checkbox" />
                  Aktivní
                </label>
              </div>
            </div>

            <div class="form-footer">
              <button @click="closeProviderForm" class="cancel-btn">Zrušit</button>
              <button @click="saveProvider" class="save-btn">
                {{ editingProvider ? 'Uložit změny' : 'Přidat server' }}
              </button>
            </div>
          </div>
        </div>

        <!-- Tools Overview -->
        <div class="tools-overview">
          <h3>📋 Přehled všech nástrojů</h3>
          <div class="tools-table">
            <div class="table-header">
              <div>ID</div>
              <div>Název</div>
              <div>Poskytovatel</div>
              <div>Identifier</div>
              <div>Aktivní</div>
            </div>
            <div
              v-for="tool in allTools"
              :key="tool.tool_id"
              class="table-row"
            >
              <div>{{ tool.tool_id }}</div>
              <div>{{ tool.display_name || tool.tool_name }}</div>
              <div>{{ tool.provider_display_name || tool.provider_name }}</div>
              <div>{{ tool.tool_identifier }}</div>
              <div>
                <span :class="tool.is_active ? 'status-active' : 'status-inactive'">
                  {{ tool.is_active ? '✅ Ano' : '❌ Ne' }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Performance/Statistics Tab -->
    <div v-if="activeTab === 'performance'" class="tab-content">
      <div class="stats-section">
        <div class="section-header">
          <h2>📊 MCP Statistiky</h2>
          <div class="header-actions">
            <button @click="refreshStats" class="refresh-btn">
              🔄 Obnovit
            </button>
          </div>
        </div>

        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-header">
              <h3>🏢 Poskytovatelé</h3>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.providers?.total || 0 }}</div>
              <div class="stat-details">
                <div>Aktivní: {{ stats.providers?.active || 0 }}</div>
                <div>Vlastní: {{ stats.providers?.custom || 0 }}</div>
              </div>
            </div>
          </div>

          <div class="stat-card">
            <div class="stat-header">
              <h3>🔧 Nástroje</h3>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stats.tools?.total || 0 }}</div>
              <div class="stat-details">
                <div>Aktivní: {{ stats.tools?.active || 0 }}</div>
                <div>Auto-approve: {{ stats.tools?.auto_approve || 0 }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Detailed Provider Stats -->
        <div class="detailed-stats">
          <h3>📋 Detailní statistiky poskytovatelů</h3>
          <div class="provider-stats-table">
            <div class="table-header">
              <div>Poskytovatel</div>
              <div>Typ</div>
              <div>Nástroje</div>
              <div>API klíč</div>
              <div>Status</div>
            </div>
            <div
              v-for="provider in providers"
              :key="provider.provider_id"
              class="table-row"
            >
              <div>{{ provider.display_name || provider.provider_name }}</div>
              <div>{{ provider.provider_type }}</div>
              <div>{{ getProviderToolsCount(provider.provider_id) }}</div>
              <div>{{ getApiKeyStatus(provider) }}</div>
              <div>
                <span :class="provider.is_active ? 'status-active' : 'status-inactive'">
                  {{ provider.is_active ? '✅ Aktivní' : '❌ Neaktivní' }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'McpManagement',
  data() {
    return {
      // Tab management
      activeTab: 'providers',

      // Data
      providers: [],
      allTools: [],
      stats: {},

      // Selected items
      selectedProvider: null,
      selectedProviderTools: [],

      // Testing
      selectedMcpServer: null,
      testInput: '',
      testFormat: 'html',
      testSearchType: 'web',
      testResponseType: 'concise',
      isTestingTool: false,
      testResult: null,

      // CRUD
      showAddProviderForm: false,
      editingProvider: null,
      providerForm: {
        provider_name: '',
        display_name: '',
        provider_type: 'custom',
        api_key: '',
        description: '',
        is_active: true
      }
    }
  },

  async mounted() {
    await this.loadInitialData()
  },

  methods: {
    async loadInitialData() {
      // ŽÁDNÉ loading kolečko!
      try {
        await Promise.all([
          this.refreshProviders(),
          this.refreshTools(),
          this.refreshStats()
        ])
      } catch (error) {
        console.error('Chyba při načítání MCP dat:', error)
        console.error('Nepodařilo se načíst MCP data')
      }
    },

    async refreshProviders() {
      try {
        const response = await axios.get('/api/mcp/providers')
        this.providers = response.data
        console.log('Načteno poskytovatelů:', this.providers.length)
        console.log('Providers data:', this.providers)

        // Debug API klíčů
        this.providers.forEach(provider => {
          console.log(`Provider ${provider.provider_name}: api_key =`, provider.api_key, typeof provider.api_key)
        })
      } catch (error) {
        console.error('Chyba při načítání poskytovatelů:', error)
        console.error('Nepodařilo se načíst MCP poskytovatele')
      }
    },

    async refreshTools() {
      try {
        const response = await axios.get('/api/mcp/tools')
        this.allTools = response.data
        console.log('Načteno nástrojů:', this.allTools.length)
      } catch (error) {
        console.error('Chyba při načítání nástrojů:', error)
        console.error('Nepodařilo se načíst MCP nástroje')
      }
    },

    async refreshStats() {
      try {
        const response = await axios.get('/api/mcp/stats')
        this.stats = response.data
        console.log('Načteny statistiky:', this.stats)
      } catch (error) {
        console.error('Chyba při načítání statistik:', error)
        console.error('Nepodařilo se načíst MCP statistiky')
      }
    },

    async selectProvider(provider) {
      this.selectedProvider = provider
      try {
        const response = await axios.get(`/api/mcp/providers/${provider.provider_id}`)
        this.selectedProviderTools = response.data.tools || []
        console.log(`Načteny nástroje pro ${provider.provider_name}:`, this.selectedProviderTools.length)
      } catch (error) {
        console.error('Chyba při načítání detailu poskytovatele:', error)
        console.error('Nepodařilo se načíst detail poskytovatele')
      }
    },

    getProviderToolsCount(providerId) {
      return this.allTools.filter(tool => tool.provider_id === providerId).length
    },

    getApiKeyStatus(provider) {
      // PROSTĚ ANO/NE - jednoduše!
      const hasApiKey = {
        'brave-search': true,
        'tavily': true,
        'perplexity': true,
        'fetch': false
      }

      return hasApiKey[provider.provider_name] ? 'ANO' : 'NE'
    },

    // === TESTOVÁNÍ ===

    selectMcpServer(serverName) {
      this.selectedMcpServer = serverName
      this.testInput = this.getDefaultInput(serverName)
      this.testResult = null
    },

    getServerIcon(serverName) {
      const icons = {
        'fetch': '🌐',
        'brave-search': '🔍',
        'tavily': '🔍',
        'perplexity': '🤖'
      }
      return icons[serverName] || '🔧'
    },

    getServerTitle(serverName) {
      const titles = {
        'fetch': '🌐 Fetch - Stahování webových stránek',
        'brave-search': '🔍 Brave Search - Vyhledávání na internetu',
        'tavily': '🔍 Tavily - Pokročilé AI vyhledávání',
        'perplexity': '🤖 Perplexity AI - AI asistent s citacemi'
      }
      return titles[serverName] || serverName
    },

    getInputLabel(serverName) {
      const labels = {
        'fetch': 'URL pro stažení:',
        'brave-search': 'Vyhledávací dotaz:',
        'tavily': 'Vyhledávací dotaz:',
        'perplexity': 'Dotaz pro AI:'
      }
      return labels[serverName] || 'Input:'
    },

    getInputPlaceholder(serverName) {
      const placeholders = {
        'fetch': 'https://svitsol.cz',
        'brave-search': 'svitsol.cz',
        'tavily': 'svitsol.cz company information',
        'perplexity': 'Co je svitsol.cz?'
      }
      return placeholders[serverName] || 'Zadejte input'
    },

    getDefaultInput(serverName) {
      return this.getInputPlaceholder(serverName)
    },

    async runMcpTest() {
      if (!this.testInput) {
        console.warn('Zadejte input pro test')
        return
      }

      this.isTestingTool = true
      const startTime = Date.now()

      try {
        let response

        switch (this.selectedMcpServer) {
          case 'fetch':
            response = await axios.post('/api/mcp/test/fetch', {
              url: this.testInput
            })
            break

          case 'brave-search':
            response = await axios.post('/api/mcp/test/brave-search', {
              query: this.testInput,
              count: 5
            })
            break

          case 'tavily':
            response = await axios.post('/api/mcp/test/tavily', {
              query: this.testInput,
              max_results: 5
            })
            break

          case 'perplexity':
            response = await axios.post('/api/mcp/test/perplexity', {
              query: this.testInput
            })
            break

          default:
            throw new Error(`Neznámý MCP server: ${this.selectedMcpServer}`)
        }

        const responseTime = Date.now() - startTime

        this.testResult = {
          input: this.testInput,
          success: true,
          response_time: responseTime,
          data: response.data
        }

        console.log(`✅ Test úspěšný! Čas: ${responseTime}ms`)

      } catch (error) {
        const responseTime = Date.now() - startTime
        this.testResult = {
          input: this.testInput,
          success: false,
          response_time: responseTime,
          error: error.response?.data?.detail || error.message
        }
        console.error(`❌ Test selhal: ${error.response?.data?.detail || error.message}`)
      } finally {
        this.isTestingTool = false
      }
    },

    formatTestResult(data) {
      return JSON.stringify(data, null, 2)
    },

    // === CRUD OPERACE ===

    editProvider(provider) {
      this.editingProvider = provider
      this.providerForm = {
        provider_name: provider.provider_name,
        display_name: provider.display_name || '',
        provider_type: provider.provider_type,
        api_key: provider.api_key || '',
        description: provider.description || '',
        is_active: provider.is_active
      }
    },

    async deleteProvider(provider) {
      if (confirm(`Opravdu smazat MCP server "${provider.display_name || provider.provider_name}"?`)) {
        try {
          await axios.delete(`/api/mcp/providers/${provider.provider_id}`)
          console.log(`✅ MCP server ${provider.provider_name} smazán`)
          await this.refreshProviders()
        } catch (error) {
          console.error('Chyba při mazání MCP serveru:', error)
        }
      }
    },

    closeProviderForm() {
      this.showAddProviderForm = false
      this.editingProvider = null
      this.providerForm = {
        provider_name: '',
        display_name: '',
        provider_type: 'custom',
        api_key: '',
        description: '',
        is_active: true
      }
    },

    async saveProvider() {
      try {
        if (this.editingProvider) {
          // Upravit existující
          await axios.put(`/api/mcp/providers/${this.editingProvider.provider_id}`, this.providerForm)
          console.log(`✅ MCP server ${this.providerForm.provider_name} upraven`)
        } else {
          // Přidat nový
          await axios.post('/api/mcp/providers', this.providerForm)
          console.log(`✅ MCP server ${this.providerForm.provider_name} přidán`)
        }

        this.closeProviderForm()
        await this.refreshProviders()
      } catch (error) {
        console.error('Chyba při ukládání MCP serveru:', error)
      }
    }
  }
}
</script>

<style>
@import '@/styles/admin-common.css';
@import '@/styles/chat-test.css';

/* TMAVÝ STYL PRO MCP MANAGEMENT */

/* MCP Server Selection - TMAVÝ */
.mcp-server-selection {
  margin-bottom: 30px;
}

.server-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.server-card {
  border: 2px solid #4a5568;
  border-radius: 8px;
  padding: 15px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #2d3748;
  color: #e2e8f0;
}

.server-card:hover {
  border-color: #63b3ed;
  box-shadow: 0 2px 4px rgba(99, 179, 237, 0.3);
  background: #374151;
}

.server-card.selected {
  border-color: #63b3ed;
  background: #1a365d;
  color: #90cdf4;
}

.server-icon {
  font-size: 24px;
  margin-bottom: 10px;
}

.server-name {
  font-weight: bold;
  margin-bottom: 5px;
  color: #e2e8f0;
}

.server-status {
  font-size: 12px;
  color: #a0aec0;
}

/* Test Input Section - TMAVÝ */
.test-input-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #4a5568;
  border-radius: 8px;
  background: #2d3748;
  color: #e2e8f0;
}

.test-input-section h3 {
  color: #90cdf4;
  margin-bottom: 15px;
}

.test-input-section label {
  color: #e2e8f0;
  font-weight: 500;
  margin-bottom: 8px;
  display: block;
}

.test-input-section .form-input {
  background: #1a202c;
  border: 1px solid #4a5568;
  color: #e2e8f0;
  padding: 10px 12px;
  border-radius: 6px;
  width: 100%;
  font-size: 14px;
}

.test-input-section .form-input:focus {
  border-color: #63b3ed;
  outline: none;
  box-shadow: 0 0 0 3px rgba(99, 179, 237, 0.1);
}

.test-input-section .form-input::placeholder {
  color: #718096;
}

/* Test Results - TMAVÝ */
.test-results {
  border: 1px solid #4a5568;
  border-radius: 8px;
  overflow: hidden;
  background: #2d3748;
}

.result-header {
  background: #1a202c;
  padding: 15px 20px;
  border-bottom: 1px solid #4a5568;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.result-header h3 {
  color: #90cdf4;
  margin: 0;
}

.result-meta {
  display: flex;
  gap: 15px;
  font-size: 12px;
}

.result-meta span {
  padding: 4px 8px;
  background: #2d3748;
  border-radius: 4px;
  border: 1px solid #4a5568;
  color: #e2e8f0;
}

.status-success {
  color: #68d391 !important;
  border-color: #68d391 !important;
  background: #1a365d !important;
}

.status-error {
  color: #fc8181 !important;
  border-color: #fc8181 !important;
  background: #742a2a !important;
}

.result-content {
  padding: 20px;
  background: #2d3748;
}

.result-content h4 {
  color: #90cdf4;
  margin-bottom: 10px;
}

.result-data, .error-data {
  background: #1a202c;
  border: 1px solid #4a5568;
  border-radius: 4px;
  padding: 15px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.5;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
  max-height: 400px;
  overflow-y: auto;
  color: #e2e8f0;
}

.error-data {
  background: #2d1b1b;
  border-color: #742a2a;
  color: #fc8181;
}

/* Test Execute Button - TMAVÝ */
.test-execute-btn {
  background: #3182ce;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 15px;
}

.test-execute-btn:hover:not(:disabled) {
  background: #2c5aa0;
  transform: translateY(-1px);
}

.test-execute-btn:disabled {
  background: #4a5568;
  cursor: not-allowed;
  opacity: 0.6;
}

/* CRUD Form Styles - TMAVÝ */
.management-actions {
  margin-bottom: 20px;
}

.add-btn {
  background: #38a169;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
}

.add-btn:hover {
  background: #2f855a;
}

.actions-cell {
  display: flex;
  gap: 8px;
}

.edit-btn, .delete-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
}

.edit-btn:hover {
  background: #4a5568;
}

.delete-btn:hover {
  background: #742a2a;
}

.provider-form-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.provider-form {
  background: #2d3748;
  border-radius: 8px;
  width: 500px;
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  border: 1px solid #4a5568;
}

.form-header {
  padding: 20px;
  border-bottom: 1px solid #4a5568;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-header h3 {
  color: #90cdf4;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  color: #a0aec0;
  cursor: pointer;
  font-size: 18px;
  padding: 4px;
}

.close-btn:hover {
  color: #e2e8f0;
}

.form-body {
  padding: 20px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  color: #e2e8f0;
  margin-bottom: 5px;
  font-weight: 500;
}

.form-group input, .form-group select, .form-group textarea {
  width: 100%;
  background: #1a202c;
  border: 1px solid #4a5568;
  color: #e2e8f0;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
}

.form-group input:focus, .form-group select:focus, .form-group textarea:focus {
  border-color: #63b3ed;
  outline: none;
  box-shadow: 0 0 0 3px rgba(99, 179, 237, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.form-group input[type="checkbox"] {
  width: auto;
  margin-right: 8px;
}

.form-footer {
  padding: 20px;
  border-top: 1px solid #4a5568;
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.cancel-btn {
  background: #4a5568;
  color: #e2e8f0;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
}

.cancel-btn:hover {
  background: #2d3748;
}

.save-btn {
  background: #3182ce;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.save-btn:hover {
  background: #2c5aa0;
}
</style>
