<template>
  <div class="llm-management-container">
    <div class="admin-header">
      <h1>🤖 LLM Management</h1>
      <p>Správa LLM poskytovatelů, modelů a testování výkonu</p>
    </div>

    <!-- Tab Navigation -->
    <div class="tab-navigation">
      <button
        @click="activeTab = 'providers'"
        :class="{ active: activeTab === 'providers' }"
        class="tab-btn"
      >
        🏢 Poskytovatelé
      </button>
      <button
        @click="activeTab = 'management'"
        :class="{ active: activeTab === 'management' }"
        class="tab-btn"
      >
        ⚙️ Správa
      </button>
      <button
        @click="switchTab('testing')"
        :class="{ active: activeTab === 'testing' }"
        class="tab-btn"
      >
        🧪 Testování
      </button>
      <button
        @click="activeTab = 'performance'"
        :class="{ active: activeTab === 'performance' }"
        class="tab-btn"
      >
        📊 Výkon
      </button>
      <button
        @click="switchTab('communication')"
        :class="{ active: activeTab === 'communication' }"
        class="tab-btn"
      >
        🤝 Kom mezi LLM
      </button>
    </div>

    <!-- Providers Tab -->
    <div v-if="activeTab === 'providers'" class="tab-content">
      <div class="providers-section">
        <div class="section-header">
          <h2>LLM Poskytovatelé a Modely</h2>
          <div class="header-actions">
            <button @click="refreshProviders" class="refresh-btn">
              🔄 Obnovit
            </button>
            <button @click="testAllProviders" class="test-btn" :disabled="isTestingProviders">
              {{ isTestingProviders ? '🔄 Testování...' : '🧪 Test všech' }}
            </button>
          </div>
        </div>

        <!-- Providers Grid -->
        <div class="providers-grid">
          <div
            v-for="provider in providers"
            :key="provider.id"
            class="provider-card"
            @click="selectProvider(provider)"
            :class="{ selected: selectedProvider?.id === provider.id }"
          >
            <div class="provider-header">
              <h3>{{ provider.name }}</h3>
              <div class="provider-status" :class="provider.status">
                {{ provider.status === 'active' ? '🟢' : '🔴' }}
              </div>
            </div>
            <div class="provider-info">
              <p>{{ provider.description }}</p>
              <div class="provider-stats">
                <span>Modely: {{ provider.models_count || 0 }}</span>
                <span>API: {{ provider.api_version || 'N/A' }}</span>
                <span>Klíč: {{ provider.api_key }}</span>
              </div>
              <div class="provider-actions">
                <button @click.stop="testProvider(provider)" class="test-provider-btn" :disabled="provider.testing">
                  {{ provider.testing ? '🔄' : '🧪' }} Test
                </button>
                <button @click.stop="editProvider(provider)" class="edit-provider-btn">
                  ✏️ Upravit
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Selected Provider Details -->
        <div v-if="selectedProvider" class="provider-details">
          <div class="provider-detail-header">
            <h3>Modely poskytovatele: {{ selectedProvider.name }}</h3>
            <div class="provider-detail-stats">
              <div class="stat-item">
                <span class="stat-label">Celkem modelů:</span>
                <span class="stat-value">{{ selectedProviderModels.length }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Aktivní modely:</span>
                <span class="stat-value">{{ selectedProviderModels.filter(m => m.available).length }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">Průměrný context:</span>
                <span class="stat-value">{{ getAverageContext() }}</span>
              </div>
            </div>
          </div>

          <div class="models-grid">
            <div
              v-for="model in selectedProviderModels"
              :key="model.id"
              class="model-card"
              :class="{ 'model-default': model.is_default }"
            >
              <div class="model-header">
                <h4>{{ model.name }}</h4>
                <div class="model-badges">
                  <span v-if="model.is_default" class="badge default">Default</span>
                  <div class="model-status">
                    {{ model.available ? '✅' : '❌' }}
                  </div>
                </div>
              </div>
              <div class="model-info">
                <div class="model-specs">
                  <span>Context: {{ formatNumber(model.context_length) || 'N/A' }}</span>
                  <span>Max Output: {{ formatNumber(model.max_tokens_output) || 'N/A' }}</span>
                  <span>Cost: {{ model.cost_per_token || 'N/A' }}</span>
                </div>
                <div class="model-capabilities">
                  <span v-for="capability in getModelCapabilities(model)" :key="capability" class="capability-tag">
                    {{ capability }}
                  </span>
                </div>
                <p class="model-description">{{ model.description }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Management Tab -->
    <div v-if="activeTab === 'management'" class="tab-content">
      <div class="management-section">
        <div class="section-header">
          <h2>⚙️ Správa poskytovatelů a modelů</h2>
          <div class="header-actions">
            <button @click="showCreateProviderForm" class="create-btn">
              ➕ Nový poskytovatel
            </button>
            <button @click="showCreateModelForm" class="create-btn">
              🤖 Nový model
            </button>
          </div>
        </div>

        <!-- Sub-tabs for Management -->
        <div class="sub-tab-navigation">
          <button
            @click="managementSubTab = 'providers'"
            :class="{ active: managementSubTab === 'providers' }"
            class="sub-tab-btn"
          >
            🏢 Poskytovatelé
          </button>
          <button
            @click="managementSubTab = 'models'"
            :class="{ active: managementSubTab === 'models' }"
            class="sub-tab-btn"
          >
            🤖 Modely
          </button>
        </div>

        <!-- Providers Management -->
        <div v-if="managementSubTab === 'providers'" class="providers-management">
          <div class="management-header">
            <h3>Správa poskytovatelů</h3>
            <div class="header-actions">
              <button @click="addOllamaProvider" class="ollama-btn" title="Automaticky přidat Ollama provider s modely">
                🦙 Přidat Ollama
              </button>
              <button @click="addOllama2Provider" class="ollama2-btn" title="Automaticky přidat Ollama2 provider s modely">
                🦙 Přidat Ollama2
              </button>
              <button @click="addLMStudioProvider" class="lmstudio-btn" title="Automaticky přidat LM Studio provider s modely">
                🏭 Přidat LM Studio
              </button>
              <button @click="addProvider" class="add-btn">
                ➕ Přidat poskytovatele
              </button>
            </div>
          </div>
          <div class="management-table">
            <div class="table-header">
              <div>ID</div>
              <div>Název</div>
              <div>API URL</div>
              <div>API Klíč</div>
              <div>Aktivní</div>
              <div>Modely</div>
              <div>Akce</div>
            </div>
            <div
              v-for="provider in providers"
              :key="provider.id"
              class="table-row"
            >
              <div>{{ provider.id }}</div>
              <div>{{ provider.name }}</div>
              <div class="url-cell">{{ provider.base_url || 'N/A' }}</div>
              <div>{{ provider.api_key }}</div>
              <div>
                <span :class="provider.status === 'active' ? 'status-active' : 'status-inactive'">
                  {{ provider.status === 'active' ? '✅' : '❌' }}
                </span>
              </div>
              <div>{{ provider.models_count }}</div>
              <div class="actions-cell">
                <button @click="editProvider(provider)" class="edit-btn" title="Upravit">
                  ✏️
                </button>
                <button @click="deleteProvider(provider)" class="delete-btn" title="Smazat">
                  🗑️
                </button>
                <button @click="testProvider(provider)" class="test-btn" title="Test">
                  🧪
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Models Management -->
        <div v-if="managementSubTab === 'models'" class="models-management">
          <div class="management-table">
            <div class="table-header">
              <div>ID</div>
              <div>Název</div>
              <div>Poskytovatel</div>
              <div>Context</div>
              <div>Max Tokens</div>
              <div>Výchozí</div>
              <div>Aktivní</div>
              <div>Akce</div>
            </div>
            <div
              v-for="model in models"
              :key="model.id"
              class="table-row"
            >
              <div>{{ model.model_id }}</div>
              <div>{{ model.model_name }}</div>
              <div>{{ model.provider_name }}</div>
              <div>{{ formatNumber(model.context_length) }}</div>
              <div>{{ formatNumber(model.max_tokens_output) }}</div>
              <div>
                <span :class="model.is_default ? 'status-default' : ''">
                  {{ model.is_default ? '⭐' : '' }}
                </span>
              </div>
              <div>
                <span :class="model.is_active ? 'status-active' : 'status-inactive'">
                  {{ model.is_active ? '✅' : '❌' }}
                </span>
              </div>
              <div class="actions-cell">
                <button @click="editModel(model)" class="edit-btn" title="Upravit">
                  ✏️
                </button>
                <button @click="deleteModel(model)" class="delete-btn" title="Smazat">
                  🗑️
                </button>
                <button @click="toggleModelDefault(model)" class="star-btn" title="Nastavit jako výchozí">
                  {{ model.is_default ? '⭐' : '☆' }}
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- Provider Form Modal -->
        <div v-if="showProviderForm" class="modal-overlay" @click="closeProviderForm">
          <div class="modal-content" @click.stop>
            <div class="modal-header">
              <h3>{{ editingProvider ? 'Upravit poskytovatele' : 'Nový poskytovatel' }}</h3>
              <button @click="closeProviderForm" class="close-btn">✕</button>
            </div>
            <form @submit.prevent="saveProvider" class="provider-form">
              <div class="form-group">
                <label for="provider-name">Název poskytovatele *</label>
                <input
                  id="provider-name"
                  v-model="providerForm.name"
                  type="text"
                  required
                  placeholder="např. OpenAI, Anthropic"
                />
              </div>
              <div class="form-group">
                <label for="provider-url">API Base URL</label>
                <input
                  id="provider-url"
                  v-model="providerForm.base_url"
                  type="url"
                  placeholder="https://api.example.com/v1"
                />
              </div>
              <div class="form-group">
                <label for="provider-key">API Klíč</label>
                <input
                  id="provider-key"
                  v-model="providerForm.api_key"
                  type="password"
                  placeholder="Zadejte API klíč"
                />
              </div>
              <div class="form-group">
                <label for="provider-version">API Verze</label>
                <input
                  id="provider-version"
                  v-model="providerForm.api_version"
                  type="text"
                  placeholder="v1"
                />
              </div>
              <div class="form-group">
                <label for="provider-auth">Typ autentizace</label>
                <select id="provider-auth" v-model="providerForm.auth_type">
                  <option value="api_key">API Key</option>
                  <option value="bearer">Bearer Token</option>
                  <option value="basic">Basic Auth</option>
                </select>
              </div>
              <div class="form-group">
                <label for="provider-rate-limit">Rate Limit (req/min)</label>
                <input
                  id="provider-rate-limit"
                  v-model.number="providerForm.rate_limit"
                  type="number"
                  placeholder="1000"
                />
              </div>
              <div class="form-group checkbox-group">
                <label>
                  <input
                    v-model="providerForm.api_key_required"
                    type="checkbox"
                  />
                  API klíč je povinný
                </label>
              </div>
              <div class="form-group checkbox-group">
                <label>
                  <input
                    v-model="providerForm.is_active"
                    type="checkbox"
                  />
                  Aktivní
                </label>
              </div>
              <div class="form-actions">
                <button type="button" @click="closeProviderForm" class="cancel-btn">
                  Zrušit
                </button>
                <button type="submit" class="save-btn" :disabled="isSaving">
                  {{ isSaving ? 'Ukládám...' : 'Uložit' }}
                </button>
              </div>
            </form>
          </div>
        </div>

        <!-- Model Form Modal -->
        <div v-if="showModelForm" class="modal-overlay" @click="closeModelForm">
          <div class="modal-content" @click.stop>
            <div class="modal-header">
              <h3>{{ editingModel ? 'Upravit model' : 'Nový model' }}</h3>
              <button @click="closeModelForm" class="close-btn">✕</button>
            </div>
            <form @submit.prevent="saveModel" class="model-form">
              <div class="form-group">
                <label for="model-provider">Poskytovatel *</label>
                <select id="model-provider" v-model="modelForm.provider_id" required>
                  <option value="">-- Vyberte poskytovatele --</option>
                  <option
                    v-for="provider in providers"
                    :key="provider.id"
                    :value="provider.id"
                  >
                    {{ provider.name }}
                  </option>
                </select>
              </div>
              <div class="form-group">
                <label for="model-name">Název modelu *</label>
                <input
                  id="model-name"
                  v-model="modelForm.model_name"
                  type="text"
                  required
                  placeholder="např. gpt-4o, claude-3-sonnet"
                />
              </div>
              <div class="form-group">
                <label for="model-identifier">Model Identifier *</label>
                <input
                  id="model-identifier"
                  v-model="modelForm.model_identifier"
                  type="text"
                  required
                  placeholder="Identifikátor pro API volání"
                />
              </div>
              <div class="form-group">
                <label for="model-context">Context Length</label>
                <input
                  id="model-context"
                  v-model.number="modelForm.context_length"
                  type="number"
                  placeholder="128000"
                />
              </div>
              <div class="form-group">
                <label for="model-max-tokens">Max Output Tokens</label>
                <input
                  id="model-max-tokens"
                  v-model.number="modelForm.max_tokens_output"
                  type="number"
                  placeholder="4096"
                />
              </div>
              <div class="form-group">
                <label for="model-temperature">Temperature</label>
                <input
                  id="model-temperature"
                  v-model.number="modelForm.temperature"
                  type="number"
                  step="0.1"
                  min="0"
                  max="2"
                  placeholder="0.7"
                />
              </div>
              <div class="form-group">
                <label>Capabilities</label>
                <div class="capabilities-grid">
                  <label class="capability-item">
                    <input v-model="modelForm.capabilities.text" type="checkbox" />
                    Text
                  </label>
                  <label class="capability-item">
                    <input v-model="modelForm.capabilities.code" type="checkbox" />
                    Code
                  </label>
                  <label class="capability-item">
                    <input v-model="modelForm.capabilities.vision" type="checkbox" />
                    Vision
                  </label>
                  <label class="capability-item">
                    <input v-model="modelForm.capabilities.function_calling" type="checkbox" />
                    Function Calling
                  </label>
                  <label class="capability-item">
                    <input v-model="modelForm.capabilities.reasoning" type="checkbox" />
                    Reasoning
                  </label>
                  <label class="capability-item">
                    <input v-model="modelForm.capabilities.multimodal" type="checkbox" />
                    Multimodal
                  </label>
                </div>
              </div>
              <div class="form-group checkbox-group">
                <label>
                  <input
                    v-model="modelForm.is_default"
                    type="checkbox"
                  />
                  Výchozí model pro poskytovatele
                </label>
              </div>
              <div class="form-group checkbox-group">
                <label>
                  <input
                    v-model="modelForm.is_active"
                    type="checkbox"
                  />
                  Aktivní
                </label>
              </div>
              <div class="form-actions">
                <button type="button" @click="closeModelForm" class="cancel-btn">
                  Zrušit
                </button>
                <button type="submit" class="save-btn" :disabled="isSaving">
                  {{ isSaving ? 'Ukládám...' : 'Uložit' }}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>

    <!-- Testing Tab -->
    <div v-if="activeTab === 'testing'" class="tab-content">
      <div class="testing-section">
        <div class="test-controls">
          <h2>🧪 LLM Testování</h2>
          <div class="model-selector">
            <label>Vyberte model:</label>
            <select v-model="selectedTestModel" class="model-select">
              <option value="">-- Vyberte model ({{ allModels.length }} dostupných) --</option>
              <option
                v-for="model in allModels"
                :key="model.id"
                :value="model.id"
              >
                {{ model.display_name }}
              </option>
            </select>
            <div v-if="allModels.length === 0 && models.length > 0" class="debug-info">
              🔍 Debug: Problém s mapováním modelů. Raw modelů: {{ models.length }}
            </div>
            <div v-if="models.length === 0" class="debug-info">
              🔍 Debug: Žádné modely nenačteny z API.
            </div>
          </div>
        </div>

        <!-- Chat Interface -->
        <div class="chat-interface">
          <div class="chat-messages">
            <div
              v-for="message in testMessages"
              :key="message.id"
              class="message"
              :class="message.sender"
            >
              <div class="message-content">
                <div class="message-text">{{ message.text }}</div>
                <div class="message-meta">
                  <span class="message-time">{{ formatTime(message.time) }}</span>
                  <span v-if="message.metrics" class="message-metrics">
                    {{ message.metrics.responseTime }}ms | {{ message.metrics.tokens }} tokens
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div class="chat-input">
            <textarea
              v-model="testMessage"
              placeholder="Zadejte testovací zprávu..."
              @keydown.enter.prevent="sendTestMessage"
              rows="3"
            ></textarea>
            <button
              @click="sendTestMessage"
              :disabled="!selectedTestModel || !testMessage.trim() || isTesting"
              class="send-btn"
            >
              {{ isTesting ? '🔄 Testování...' : '🚀 Odeslat' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Performance Tab -->
    <div v-if="activeTab === 'performance'" class="tab-content">
      <div class="performance-section">
        <div class="section-header">
          <h2>📊 Výkonové metriky</h2>
          <div class="header-actions">
            <button @click="refreshPerformanceData" class="refresh-btn">
              🔄 Obnovit data
            </button>
            <button @click="exportPerformanceData" class="export-btn">
              📊 Export CSV
            </button>
          </div>
        </div>

        <!-- Performance Overview -->
        <div class="performance-overview">
          <div class="metric-card">
            <div class="metric-icon">⚡</div>
            <div class="metric-content">
              <div class="metric-value">{{ performanceStats.avgResponseTime }}ms</div>
              <div class="metric-label">Průměrný čas odpovědi</div>
              <div class="metric-trend" :class="performanceStats.responseTimeTrend">
                {{ performanceStats.responseTimeTrend === 'up' ? '📈' : performanceStats.responseTimeTrend === 'down' ? '📉' : '➡️' }}
              </div>
            </div>
          </div>
          <div class="metric-card">
            <div class="metric-icon">🔤</div>
            <div class="metric-content">
              <div class="metric-value">{{ formatNumber(performanceStats.totalTokens) }}</div>
              <div class="metric-label">Celkové tokeny</div>
              <div class="metric-trend" :class="performanceStats.tokensTrend">
                {{ performanceStats.tokensTrend === 'up' ? '📈' : performanceStats.tokensTrend === 'down' ? '📉' : '➡️' }}
              </div>
            </div>
          </div>
          <div class="metric-card">
            <div class="metric-icon">💰</div>
            <div class="metric-content">
              <div class="metric-value">${{ performanceStats.totalCost.toFixed(2) }}</div>
              <div class="metric-label">Celkové náklady</div>
              <div class="metric-trend" :class="performanceStats.costTrend">
                {{ performanceStats.costTrend === 'up' ? '📈' : performanceStats.costTrend === 'down' ? '📉' : '➡️' }}
              </div>
            </div>
          </div>
          <div class="metric-card">
            <div class="metric-icon">📈</div>
            <div class="metric-content">
              <div class="metric-value">{{ performanceStats.successRate }}%</div>
              <div class="metric-label">Úspěšnost</div>
              <div class="metric-trend" :class="performanceStats.successRateTrend">
                {{ performanceStats.successRateTrend === 'up' ? '📈' : performanceStats.successRateTrend === 'down' ? '📉' : '➡️' }}
              </div>
            </div>
          </div>
        </div>

        <!-- Time Range Selector -->
        <div class="time-range-selector">
          <label>Časové období:</label>
          <select v-model="selectedTimeRange" @change="refreshPerformanceData">
            <option value="1h">Poslední hodina</option>
            <option value="24h">Posledních 24 hodin</option>
            <option value="7d">Posledních 7 dní</option>
            <option value="30d">Posledních 30 dní</option>
          </select>
        </div>

        <!-- Model Comparison -->
        <div class="model-comparison">
          <h3>Porovnání modelů</h3>
          <div class="comparison-table">
            <div class="table-header">
              <div>Model</div>
              <div>Avg Response</div>
              <div>Success Rate</div>
              <div>Cost/Token</div>
              <div>Usage</div>
              <div>Last Used</div>
            </div>
            <div
              v-for="model in modelPerformanceData"
              :key="model.id"
              class="table-row"
              :class="{ 'top-performer': model.isTopPerformer }"
            >
              <div class="model-name">
                <span class="model-provider">{{ model.provider }}</span>
                <span class="model-title">{{ model.name }}</span>
              </div>
              <div class="response-time" :class="getPerformanceClass(model.avgResponseTime)">
                {{ model.avgResponseTime }}ms
              </div>
              <div class="success-rate" :class="getSuccessRateClass(model.successRate)">
                {{ model.successRate }}%
              </div>
              <div class="cost">{{ model.costPerToken }}</div>
              <div class="usage">{{ model.usageCount }}x</div>
              <div class="last-used">{{ formatRelativeTime(model.lastUsed) }}</div>
            </div>
          </div>
        </div>

        <!-- Performance Charts -->
        <div class="performance-charts">
          <div class="chart-container">
            <h4>Čas odpovědi v čase</h4>
            <div class="chart-placeholder">
              📊 Graf času odpovědi (implementace v budoucnu)
            </div>
          </div>
          <div class="chart-container">
            <h4>Využití modelů</h4>
            <div class="chart-placeholder">
              📊 Graf využití modelů (implementace v budoucnu)
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Communication Tab -->
    <div v-if="activeTab === 'communication'" class="tab-content">
      <div class="communication-section">
        <div class="section-header">
          <h2>🤝 Komunikace mezi LLM</h2>
          <p class="section-description">
            Orchestrovaná komunikace mezi třemi AI modely s iterativním zpracováním a Mem0 memory systémem
          </p>
        </div>

        <!-- Input Controls -->
        <div class="communication-controls">
          <div class="control-row">
            <div class="control-group">
              <label for="ai0-model">AI-0 Model (Prompt Optimizer):</label>
              <select id="ai0-model" v-model="communicationForm.ai0Model" class="model-select">
                <option value="">-- Vyberte model pro AI-0 --</option>
                <option
                  v-for="model in allModels"
                  :key="'ai0-' + model.id"
                  :value="model.id"
                >
                  {{ model.display_name }}
                </option>
              </select>
            </div>
            <div class="control-group">
              <label for="ai1-model">AI-1 Model (Primary Processor):</label>
              <select id="ai1-model" v-model="communicationForm.ai1Model" class="model-select">
                <option value="">-- Vyberte model pro AI-1 --</option>
                <option
                  v-for="model in allModels"
                  :key="'ai1-' + model.id"
                  :value="model.id"
                >
                  {{ model.display_name }}
                </option>
              </select>
            </div>
            <div class="control-group">
              <label for="ai2-model">AI-2 Model (Secondary Processor):</label>
              <select id="ai2-model" v-model="communicationForm.ai2Model" class="model-select">
                <option value="">-- Vyberte model pro AI-2 --</option>
                <option
                  v-for="model in allModels"
                  :key="'ai2-' + model.id"
                  :value="model.id"
                >
                  {{ model.display_name }}
                </option>
              </select>
            </div>
            <div class="control-group">
              <label for="middleware-model">Middleware LLM (Řízení komunikace):</label>
              <select id="middleware-model" v-model="communicationForm.middlewareModel" class="model-select">
                <option value="">-- Vyberte model pro Middleware --</option>
                <option
                  v-for="model in allModels"
                  :key="'middleware-' + model.id"
                  :value="model.id"
                >
                  {{ model.display_name }}
                </option>
              </select>
            </div>
          </div>

          <div class="control-row">
            <div class="control-group">
              <label for="iteration-count">Počet iterací:</label>
              <input
                id="iteration-count"
                v-model.number="communicationForm.iterationCount"
                type="number"
                min="1"
                max="50"
                class="iteration-input"
              />
            </div>
          </div>

          <div class="control-row">
            <div class="control-group full-width">
              <label for="user-prompt">Původní prompt:</label>
              <textarea
                id="user-prompt"
                v-model="communicationForm.userPrompt"
                placeholder="Zadejte váš prompt, který bude optimalizován AI-0 a zpracován AI-1 a AI-2..."
                rows="4"
                class="prompt-textarea"
              ></textarea>
            </div>
          </div>

          <div class="control-row">
            <button
              @click="startCommunication"
              :disabled="!canStartCommunication || isProcessing"
              class="start-communication-btn"
            >
              {{ isProcessing ? '🔄 Zpracovávám...' : '🚀 Spustit komunikaci' }}
            </button>
            <button
              v-if="currentSession"
              @click="stopCommunication"
              :disabled="!isProcessing"
              class="stop-communication-btn"
            >
              🛑 Zastavit
            </button>
            <button
              v-if="currentSession"
              @click="clearSession"
              :disabled="isProcessing"
              class="clear-session-btn"
            >
              🗑️ Vymazat session
            </button>
          </div>
        </div>

        <!-- Session Status -->
        <div v-if="currentSession" class="session-status" :class="currentSession.status">
          <div class="status-header">
            <h3>📊 Status Session: {{ currentSession.id }}</h3>
            <div class="session-meta">
              <span class="session-time">Spuštěno: {{ formatTime(currentSession.startTime) }}</span>
              <span class="session-iterations">Iterace: {{ currentSession.currentIteration }}/{{ currentSession.maxIterations }}</span>
              <span class="status-text">{{ getSessionStatusText(currentSession.status) }}</span>
            </div>
          </div>

          <!-- Error Message -->
          <div v-if="currentSession.status === 'error' && currentSession.errorMessage" class="error-message">
            <strong>❌ Chyba:</strong> {{ currentSession.errorMessage }}
          </div>

          <div class="progress-bar">
            <div
              class="progress-fill"
              :style="{ width: sessionProgress + '%' }"
            ></div>
          </div>
        </div>

        <!-- Final Answer (Result from AI-1/AI-2 collaboration) -->
        <div v-if="finalAnswer" class="final-answer">
          <div class="final-answer-header">
            <h3 v-if="finalAnswer.type === 'code'">💻 Finální kód</h3>
            <h3 v-else>📝 Finální odpověď</h3>
            <div class="answer-meta">
              <span>Výsledek spolupráce AI-1 ↔ AI-2 (iterace {{ finalAnswer.iteration }})</span>
              <span>{{ formatTime(finalAnswer.timestamp) }}</span>
              <button v-if="finalAnswer.type === 'code'" @click="copyAnswerToClipboard" class="copy-code-btn">📋 Kopírovat kód</button>
              <button v-else @click="copyAnswerToClipboard" class="copy-text-btn">📋 Kopírovat text</button>
            </div>
          </div>
          <div class="final-answer-content">
            <div class="answer-note">
              <strong>🤝 Spolupráce:</strong> Toto je finální řešení vytvořené iterativní spoluprací mezi AI-1 a AI-2.
            </div>
            <pre v-if="finalAnswer.type === 'code'"><code>{{ finalAnswer.content }}</code></pre>
            <div v-else class="text-content">{{ finalAnswer.content }}</div>
          </div>
        </div>

        <!-- Final Validation (Relevance check from AI-0) -->
        <div v-if="finalValidation" class="final-validation">
          <div class="final-validation-header">
            <h3>🎯 Validace relevance</h3>
            <div class="validation-meta">
              <span>Zkontrolováno {{ finalValidation.validatedBy }}</span>
              <span v-if="finalValidation.topicAdherence" :class="getAdherenceClass(finalValidation.topicAdherence)">
                {{ getAdherenceText(finalValidation.topicAdherence) }}
              </span>
              <span>{{ formatTime(finalValidation.timestamp) }}</span>
            </div>
          </div>
          <div class="final-validation-content">
            <div class="validation-note">
              <strong>📋 Kontrola:</strong> AI-0 ověřuje, jestli AI-1 a AI-2 odpověděly na původní otázku a nezavlekly se jinam.
            </div>
            <div class="validation-result">
              {{ finalValidation.content }}
            </div>
          </div>
        </div>

        <!-- Legacy Final Code (for backward compatibility) -->
        <div v-if="finalCode && !finalAnswer" class="final-code">
          <div class="final-code-header">
            <h3>💻 Finální kód</h3>
            <div class="code-meta">
              <span>Výsledek z iterace {{ finalCode.iteration }}</span>
              <span>{{ formatTime(finalCode.timestamp) }}</span>
              <button @click="copyCodeToClipboard" class="copy-code-btn">📋 Kopírovat</button>
            </div>
          </div>
          <div class="final-code-content">
            <pre><code>{{ finalCode.content }}</code></pre>
          </div>
        </div>

        <!-- AI-0 Activity Panel -->
        <div v-if="ai0Messages.length > 0" class="ai0-activity">
          <div class="ai-panel ai0-panel">
            <div class="panel-header">
              <h3>⚡ AI-0 (Prompt Optimizer) - Aktivita</h3>
            </div>
            <div class="conversation-display">
              <div
                v-for="message in ai0Messages"
                :key="message.id"
                class="conversation-message"
                :class="message.type"
              >
                <div class="message-header">
                  <span class="message-phase">{{ message.phase }}</span>
                  <span class="message-time">{{ formatTime(message.timestamp) }}</span>
                </div>
                <div class="message-content">
                  <div class="prompt-section" v-if="message.inputPrompt">
                    <strong>📥 Vstupní prompt:</strong>
                    <div class="prompt-text">{{ message.inputPrompt }}</div>
                  </div>
                  <div class="prompt-section" v-if="message.outputPrompt">
                    <strong>📤 Výstupní prompt:</strong>
                    <div class="prompt-text">{{ message.outputPrompt }}</div>
                  </div>
                  <div v-if="message.analysis" class="analysis-section">
                    <strong>🔍 Analýza:</strong>
                    <div class="analysis-text">{{ message.analysis }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Triple Panel Layout -->
        <div class="triple-panel-layout">
          <!-- AI-1 Panel -->
          <div class="ai-panel ai1-panel">
            <div class="panel-header">
              <h3>🤖 AI-1 (Primary Processor)</h3>
              <div class="panel-controls">
                <button
                  @click="showManualInput('ai1')"
                  :disabled="!currentSession || isProcessing"
                  class="manual-input-btn"
                >
                  ✏️ Manuální input
                </button>
              </div>
            </div>
            <div class="conversation-display">
              <div
                v-for="message in ai1Messages"
                :key="message.id"
                class="conversation-message"
                :class="message.type"
              >
                <div class="message-header">
                  <span class="message-iteration">Iterace {{ message.iteration }}</span>
                  <span class="message-time">{{ formatTime(message.timestamp) }}</span>
                </div>
                <div class="message-content">
                  <div class="prompt-section" v-if="message.inputPrompt">
                    <strong>📥 Dostal prompt:</strong>
                    <div class="prompt-text">{{ message.inputPrompt }}</div>
                  </div>
                  <div class="prompt-section" v-if="message.content">
                    <strong>📤 Odpověď:</strong>
                    <div class="response-text">{{ message.content }}</div>
                  </div>
                  <div class="prompt-section" v-if="message.outputPrompt">
                    <strong>➡️ Předal AI-2:</strong>
                    <div class="prompt-text">{{ message.outputPrompt }}</div>
                  </div>
                </div>
              </div>
              <div v-if="ai1Messages.length === 0" class="empty-conversation">
                Žádné zprávy v AI-1 konverzaci
              </div>
            </div>
          </div>

          <!-- AI-2 Panel -->
          <div class="ai-panel ai2-panel">
            <div class="panel-header">
              <h3>🧠 AI-2 (Secondary Processor)</h3>
              <div class="panel-controls">
                <button
                  @click="showManualInput('ai2')"
                  :disabled="!currentSession || isProcessing"
                  class="manual-input-btn"
                >
                  ✏️ Manuální input
                </button>
              </div>
            </div>
            <div class="conversation-display">
              <div
                v-for="message in ai2Messages"
                :key="message.id"
                class="conversation-message"
                :class="message.type"
              >
                <div class="message-header">
                  <span class="message-iteration">Iterace {{ message.iteration }}</span>
                  <span class="message-time">{{ formatTime(message.timestamp) }}</span>
                </div>
                <div class="message-content">
                  <div class="prompt-section" v-if="message.inputPrompt">
                    <strong>📥 Dostal od AI-1:</strong>
                    <div class="prompt-text">{{ message.inputPrompt }}</div>
                  </div>
                  <div class="prompt-section" v-if="message.content">
                    <strong>📤 Analýza:</strong>
                    <div class="response-text">{{ message.content }}</div>
                  </div>
                  <div class="prompt-section" v-if="message.outputPrompt">
                    <strong>➡️ Předal AI-1:</strong>
                    <div class="prompt-text">{{ message.outputPrompt }}</div>
                  </div>
                </div>
              </div>
              <div v-if="ai2Messages.length === 0" class="empty-conversation">
                Žádné zprávy v AI-2 konverzaci
              </div>
            </div>
          </div>
        </div>

        <!-- Manual Input Modal -->
        <div v-if="showManualInputModal" class="modal-overlay" @click="closeManualInput">
          <div class="modal-content manual-input-modal" @click.stop>
            <div class="modal-header">
              <h3>✏️ Manuální input pro {{ manualInputTarget === 'ai1' ? 'AI-1' : 'AI-2' }}</h3>
              <button @click="closeManualInput" class="close-btn">✕</button>
            </div>
            <div class="modal-body">
              <textarea
                v-model="manualInputText"
                placeholder="Zadejte zprávu pro AI model..."
                rows="6"
                class="manual-input-textarea"
              ></textarea>
            </div>
            <div class="modal-actions">
              <button @click="closeManualInput" class="cancel-btn">Zrušit</button>
              <button
                @click="sendManualInput"
                :disabled="!manualInputText.trim() || isProcessing"
                class="send-btn"
              >
                🚀 Odeslat
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { llmDbService } from '@/services/llm_db.service';
import { chatService } from '@/services/chat.service';

export default {
  name: 'LlmManagement',

  data() {
    return {
      activeTab: 'providers',

      // Providers data
      providers: [],
      models: [],
      selectedProvider: null,
      selectedProviderModels: [],
      isTestingProviders: false,

      // Management data
      managementSubTab: 'providers',
      showProviderForm: false,
      showModelForm: false,
      editingProvider: null,
      editingModel: null,
      isSaving: false,

      // Provider form data
      providerForm: {
        name: '',
        base_url: '',
        api_key: '',
        api_version: 'v1',
        auth_type: 'api_key',
        rate_limit: null,
        api_key_required: true,
        is_active: true
      },

      // Model form data
      modelForm: {
        provider_id: '',
        model_name: '',
        model_identifier: '',
        context_length: null,
        max_tokens_output: null,
        temperature: 0.7,
        capabilities: {
          text: true,
          code: false,
          vision: false,
          function_calling: false,
          reasoning: false,
          multimodal: false
        },
        is_default: false,
        is_active: true
      },

      // Testing data
      selectedTestModel: '',
      testMessage: '',
      testMessages: [],
      isTesting: false,

      // Performance data
      performanceStats: {
        avgResponseTime: 1250,
        totalTokens: 45678,
        totalCost: 12.34,
        successRate: 94.2,
        responseTimeTrend: 'down',
        tokensTrend: 'up',
        costTrend: 'up',
        successRateTrend: 'stable'
      },
      modelPerformanceData: [],
      selectedTimeRange: '24h',

      // Communication between LLM data
      communicationForm: {
        ai0Model: '',
        ai1Model: '',
        ai2Model: '',
        middlewareModel: '',
        iterationCount: 10,
        userPrompt: ''
      },
      currentSession: null,
      isProcessing: false,
      finalAnswer: null,
      finalCode: null,
      finalValidation: null,
      ai0Messages: [],
      ai1Messages: [],
      ai2Messages: [],
      showManualInputModal: false,
      manualInputTarget: null,
      manualInputText: ''
    };
  },

  computed: {
    allModels() {
      console.log('allModels computed called, models.length:', this.models.length);
      if (this.models.length === 0) {
        console.log('No models available for mapping');
        return [];
      }

      // Vrátíme modely načtené z API endpoint /api/db/llm/models
      const mappedModels = this.models.map(model => {
        console.log('Mapping model:', model.provider_name, '-', model.model_name);
        return {
          ...model,
          // Formát zobrazení: "Poskytovatel - Model"
          display_name: `${model.provider_name} - ${model.model_name}`
        };
      });
      console.log('allModels computed result:', mappedModels.length, 'modelů');
      console.log('První 3 allModels:', mappedModels.slice(0, 3).map(m => m.display_name));
      return mappedModels;
    },

    // Communication computed properties
    canStartCommunication() {
      return this.communicationForm.ai0Model &&
             this.communicationForm.ai1Model &&
             this.communicationForm.ai2Model &&
             this.communicationForm.middlewareModel &&
             this.communicationForm.userPrompt.trim() &&
             this.communicationForm.iterationCount > 0 &&
             !this.isProcessing;
    },

    sessionProgress() {
      if (!this.currentSession) return 0;
      return Math.round((this.currentSession.currentIteration / this.currentSession.maxIterations) * 100);
    }
  },

  watch: {
    models: {
      handler(newModels) {
        console.log('Models watch triggered, new length:', newModels.length);
        console.log('allModels computed will now have:', this.allModels.length, 'items');
      },
      deep: true
    }
  },

  methods: {
    async switchTab(tabName) {
      this.activeTab = tabName;
      console.log(`Přepínám na záložku: ${tabName}`);

      // Při přepnutí na testování se ujistíme, že máme načtené modely
      if (tabName === 'testing') {
        console.log('Testování záložka - kontroluji modely');
        if (this.models.length === 0) {
          console.log('Žádné modely - načítám...');
          await this.refreshModels();
        } else {
          console.log(`Máme ${this.models.length} modelů`);
        }
      }

      // Při přepnutí na komunikaci mezi LLM se ujistíme, že máme načtené modely
      if (tabName === 'communication') {
        console.log('Komunikace mezi LLM záložka - kontroluji modely');
        if (this.models.length === 0) {
          console.log('Žádné modely - načítám...');
          await this.refreshModels();
        } else {
          console.log(`Máme ${this.models.length} modelů pro komunikaci`);
        }
      }
    },

    async refreshProviders() {
      try {
        console.log('Načítání poskytovatelů z API...');
        const response = await fetch('http://localhost:8001/api/db/llm/providers');
        console.log('API Response status:', response.status);

        if (response.ok) {
          const data = await response.json();
          console.log('Raw provider data:', data);

          this.providers = data.map(provider => ({
            // Mapování podle skutečné struktury dat z API
            id: provider.id,  // API vrací "id", ne "provider_id"
            name: provider.name,  // API vrací "name", ne "provider_name"
            status: provider.is_active ? 'active' : 'inactive',
            description: `${provider.name} LLM provider - ${provider.base_url || 'No URL'}`,
            models_count: 0, // Bude doplněno při načítání modelů
            api_version: provider.api_version || 'v1',
            api_key: provider.api_key && provider.api_key !== '********' ? '✅ Nastaven' : '❌ Chybí',
            base_url: provider.base_url,
            auth_type: provider.auth_type || 'api_key',
            rate_limit: provider.rate_limit
          }));
          console.log('Načteno poskytovatelů:', this.providers.length);
          console.log('Mapped providers:', this.providers);

          // Načteme také modely
          await this.refreshModels();

          // Spočítáme počet modelů pro každého poskytovatele
          this.providers.forEach(provider => {
            const providerModels = this.models.filter(model => model.provider_id === provider.id);
            provider.models_count = providerModels.length;
          });
        } else {
          console.error('Chyba při načítání poskytovatelů:', response.status);
          const errorText = await response.text();
          console.error('Error response:', errorText);
        }
      } catch (error) {
        console.error('Chyba při načítání poskytovatelů:', error);
      }
    },

    async refreshModels() {
      try {
        console.log('Načítání modelů z API...');
        const response = await fetch('/api/db/llm/models');
        console.log('API Response status:', response.status);

        if (response.ok) {
          const data = await response.json();
          console.log('Raw models data:', data);

          // Mapování modelů podle skutečné struktury dat z API
          this.models = data.map(model => ({
            id: model.id,
            model_id: model.model_id,
            provider_id: model.provider_id,
            model_name: model.name,  // API vrací "name"
            model_identifier: model.model_identifier,
            provider_name: model.provider_name,
            context_length: model.context_length,
            max_tokens_output: model.max_tokens,
            temperature: model.temperature,
            capabilities: model.capabilities,
            is_default: model.is_default,
            is_active: model.is_active,
            api_key_required: model.api_key_required,
            pricing_input: null, // Není v API datech
            pricing_output: null // Není v API datech
          }));

          console.log('Načteno modelů:', this.models.length);
          console.log('Mapped models:', this.models);
          console.log('Dostupné modely:', this.allModels.length);
          console.log('První 3 modely:', this.models.slice(0, 3));

          // Debug výchozích modelů
          const defaultModels = this.models.filter(m => m.is_default);
          console.log('Výchozí modely po načtení:', defaultModels.map(m => ({
            name: m.model_name,
            provider_id: m.provider_id,
            is_default: m.is_default
          })));

          // Vynucení aktualizace Vue komponenty
          this.$forceUpdate();
        } else {
          console.error('Chyba při načítání modelů:', response.status);
          const errorText = await response.text();
          console.error('Error response:', errorText);


        }
      } catch (error) {
        console.error('Chyba při načítání modelů:', error);


      }
    },

    async selectProvider(provider) {
      this.selectedProvider = provider;
      // Načteme modely pro vybraného poskytovatele
      this.selectedProviderModels = this.models.filter(
        model => model.provider_id === provider.id
      ).map(model => ({
        id: model.model_id,
        name: model.model_name,  // Už je správně mapováno
        identifier: model.model_identifier,
        available: model.is_active,
        is_default: model.is_default,
        context_length: model.context_length,
        max_tokens_output: model.max_tokens_output,
        cost_per_token: model.pricing_input ? `${model.pricing_input}/1k` : 'N/A',
        capabilities: model.capabilities,
        description: model.capabilities && Object.keys(model.capabilities).length > 0 ?
          `Model s možnostmi: ${Object.keys(model.capabilities || {}).join(', ')}` :
          'Standardní LLM model'
      }));
      console.log(`Načteno ${this.selectedProviderModels.length} modelů pro ${provider.name}`);
      console.log('Selected provider models:', this.selectedProviderModels);
    },

    async testProvider(provider) {
      provider.testing = true;
      try {
        console.log(`Testování poskytovatele ${provider.name} (ID: ${provider.id})`);

        const response = await fetch(`/api/db/llm/performance/test-provider/${provider.id}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const result = await response.json();
          console.log('Test result:', result);

          if (result.success) {
            alert(`✅ Test poskytovatele ${provider.name} byl úspěšný!\n\n` +
                  `Model: ${result.model_tested}\n` +
                  `Čas odpovědi: ${result.responseTime}ms\n` +
                  `Odpověď: ${result.response_text}`);
          } else {
            alert(`❌ Test poskytovatele ${provider.name} selhal!\n\n` +
                  `Model: ${result.model_tested}\n` +
                  `Čas: ${result.responseTime}ms\n` +
                  `Chyba: ${result.error || result.message}`);
          }
        } else {
          const errorData = await response.json();
          alert(`❌ Test poskytovatele ${provider.name} selhal: ${errorData.detail}`);
        }
      } catch (error) {
        console.error('Chyba při testování poskytovatele:', error);
        alert(`❌ Test poskytovatele ${provider.name} selhal: ${error.message}`);
      } finally {
        provider.testing = false;
      }
    },

    async testAllProviders() {
      this.isTestingProviders = true;
      try {
        for (const provider of this.providers) {
          if (provider.status === 'active') {
            await this.testProvider(provider);
          }
        }
        alert('✅ Testování všech poskytovatelů dokončeno!');
      } catch (error) {
        alert(`❌ Chyba při testování poskytovatelů: ${error.message}`);
      } finally {
        this.isTestingProviders = false;
      }
    },

    editProvider(provider) {
      // TODO: Implementovat editaci poskytovatele
      alert(`Editace poskytovatele ${provider.name} - implementace v budoucnu`);
    },

    getAverageContext() {
      if (this.selectedProviderModels.length === 0) return 'N/A';
      const total = this.selectedProviderModels.reduce((sum, model) => {
        return sum + (model.context_length || 0);
      }, 0);
      const avg = Math.round(total / this.selectedProviderModels.length);
      return this.formatNumber(avg);
    },

    getModelCapabilities(model) {
      if (!model.capabilities) return [];
      const caps = typeof model.capabilities === 'string' ?
        JSON.parse(model.capabilities) : model.capabilities;
      return Object.keys(caps).filter(key => caps[key]);
    },

    formatNumber(num) {
      if (!num) return '0';
      return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    },

    async sendTestMessage() {
      if (!this.selectedTestModel || !this.testMessage.trim()) return;

      this.isTesting = true;
      const startTime = performance.now();

      // Add user message
      const userMessage = {
        id: Date.now(),
        sender: 'user',
        text: this.testMessage,
        time: new Date()
      };
      this.testMessages.push(userMessage);

      const messageToSend = this.testMessage;
      this.testMessage = '';

      try {
        const response = await chatService.sendMessage({
          message: messageToSend,
          model_id: this.selectedTestModel
        });

        const endTime = performance.now();
        const responseTime = Math.round(endTime - startTime);

        // Add assistant message
        const assistantMessage = {
          id: Date.now() + 1,
          sender: 'assistant',
          text: response.response?.text || response.text || response.content || 'Odpověď nebyla získána',
          time: new Date(),
          metrics: {
            responseTime,
            tokens: response.response?.usage?.total_tokens || response.usage?.total_tokens || 0
          }
        };
        this.testMessages.push(assistantMessage);

      } catch (error) {
        console.error('Chyba při testování:', error);
        const errorMessage = {
          id: Date.now() + 1,
          sender: 'assistant',
          text: 'Chyba při komunikaci s modelem: ' + error.message,
          time: new Date(),
          metrics: {
            responseTime: performance.now() - startTime,
            tokens: 0
          }
        };
        this.testMessages.push(errorMessage);
      } finally {
        this.isTesting = false;
      }
    },

    formatTime(timestamp) {
      return timestamp.toLocaleTimeString('cs-CZ');
    },

    getPerformanceClass(responseTime) {
      if (responseTime < 1000) return 'fast';
      if (responseTime < 3000) return 'medium';
      return 'slow';
    },

    getSuccessRateClass(successRate) {
      if (successRate >= 95) return 'excellent';
      if (successRate >= 85) return 'good';
      if (successRate >= 70) return 'fair';
      return 'poor';
    },

    formatRelativeTime(timestamp) {
      if (!timestamp) return 'Nikdy';
      const now = new Date();
      const time = new Date(timestamp);
      const diffMs = now - time;
      const diffMins = Math.floor(diffMs / 60000);
      const diffHours = Math.floor(diffMs / 3600000);
      const diffDays = Math.floor(diffMs / 86400000);

      if (diffMins < 1) return 'Právě teď';
      if (diffMins < 60) return `${diffMins}m`;
      if (diffHours < 24) return `${diffHours}h`;
      return `${diffDays}d`;
    },

    async refreshPerformanceData() {
      try {
        console.log(`Načítání výkonových dat pro období: ${this.selectedTimeRange}`);

        // Načtení přehledu výkonu
        const overviewResponse = await fetch(`/api/db/llm/performance/overview?time_range=${this.selectedTimeRange}`);
        if (overviewResponse.ok) {
          const overviewData = await overviewResponse.json();
          this.performanceStats = {
            avgResponseTime: overviewData.avgResponseTime,
            totalTokens: overviewData.totalTokens,
            totalCost: overviewData.totalCost,
            successRate: overviewData.successRate,
            responseTimeTrend: overviewData.responseTimeTrend,
            tokensTrend: overviewData.tokensTrend,
            costTrend: overviewData.costTrend,
            successRateTrend: overviewData.successRateTrend
          };
        }

        // Načtení výkonu modelů
        const modelsResponse = await fetch(`/api/db/llm/performance/models?time_range=${this.selectedTimeRange}`);
        if (modelsResponse.ok) {
          const modelsData = await modelsResponse.json();
          this.modelPerformanceData = modelsData.map(model => ({
            ...model,
            lastUsed: new Date(model.lastUsed)
          }));
        }

        console.log('Výkonová data načtena:', this.modelPerformanceData.length, 'modelů');
      } catch (error) {
        console.error('Chyba při načítání výkonových dat:', error);
        // Fallback na simulovaná data při chybě
        this.modelPerformanceData = [
          {
            id: 1,
            name: 'GPT-4o',
            provider: 'OpenAI',
            avgResponseTime: 850,
            successRate: 98.5,
            costPerToken: '$0.03/1k',
            usageCount: 1247,
            lastUsed: new Date(Date.now() - 300000),
            isTopPerformer: true
          }
        ];
      }
    },

    async exportPerformanceData() {
      try {
        // Vytvoření CSV dat
        const headers = ['Model', 'Provider', 'Avg Response (ms)', 'Success Rate (%)', 'Cost/Token', 'Usage Count', 'Last Used'];
        const csvData = [
          headers.join(','),
          ...this.modelPerformanceData.map(model => [
            model.name,
            model.provider,
            model.avgResponseTime,
            model.successRate,
            model.costPerToken,
            model.usageCount,
            model.lastUsed.toISOString()
          ].join(','))
        ].join('\n');

        // Stažení souboru
        const blob = new Blob([csvData], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `llm-performance-${this.selectedTimeRange}-${new Date().toISOString().split('T')[0]}.csv`;
        a.click();
        window.URL.revokeObjectURL(url);

        alert('✅ Výkonová data byla exportována do CSV souboru!');
      } catch (error) {
        console.error('Chyba při exportu dat:', error);
        alert('❌ Chyba při exportu dat: ' + error.message);
      }
    },

    // === CRUD Methods for Management ===

    // Provider CRUD methods
    showCreateProviderForm() {
      this.editingProvider = null;
      this.resetProviderForm();
      this.showProviderForm = true;
    },

    editProvider(provider) {
      this.editingProvider = provider;
      this.providerForm = {
        name: provider.name,
        base_url: provider.base_url || '',
        api_key: '', // Nezobrazujeme existující klíč z bezpečnostních důvodů
        api_version: provider.api_version || 'v1',
        auth_type: provider.auth_type || 'api_key',
        rate_limit: provider.rate_limit,
        api_key_required: provider.api_key_required !== false,
        is_active: provider.status === 'active'
      };
      this.showProviderForm = true;
    },

    async deleteProvider(provider) {
      if (!confirm(`Opravdu chcete smazat poskytovatele "${provider.name}"? Tato akce smaže také všechny jeho modely a nelze ji vrátit zpět.`)) {
        return;
      }

      try {
        const response = await fetch(`/api/db/llm/providers/${provider.id}`, {
          method: 'DELETE'
        });

        if (response.ok) {
          alert(`✅ Poskytovatel "${provider.name}" byl úspěšně smazán!`);
          await this.refreshProviders();
          await this.refreshModels();
        } else {
          const errorData = await response.json();
          alert(`❌ Chyba při mazání poskytovatele: ${errorData.detail || 'Neznámá chyba'}`);
        }
      } catch (error) {
        console.error('Chyba při mazání poskytovatele:', error);
        alert('❌ Chyba při mazání poskytovatele: ' + error.message);
      }
    },

    async saveProvider() {
      this.isSaving = true;
      try {
        const url = this.editingProvider
          ? `/api/db/llm/providers/${this.editingProvider.id}`
          : '/api/db/llm/providers';

        const method = this.editingProvider ? 'PUT' : 'POST';

        const providerData = {
          name: this.providerForm.name,
          base_url: this.providerForm.base_url || null,
          api_key: this.providerForm.api_key || null,
          api_version: this.providerForm.api_version || 'v1',
          auth_type: this.providerForm.auth_type,
          rate_limit: this.providerForm.rate_limit || null,
          api_key_required: this.providerForm.api_key_required,
          is_active: this.providerForm.is_active
        };

        const response = await fetch(url, {
          method: method,
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(providerData)
        });

        if (response.ok) {
          const action = this.editingProvider ? 'aktualizován' : 'vytvořen';
          alert(`✅ Poskytovatel "${this.providerForm.name}" byl úspěšně ${action}!`);
          this.closeProviderForm();
          await this.refreshProviders();
        } else {
          const errorData = await response.json();
          alert(`❌ Chyba při ukládání poskytovatele: ${errorData.detail || 'Neznámá chyba'}`);
        }
      } catch (error) {
        console.error('Chyba při ukládání poskytovatele:', error);
        alert('❌ Chyba při ukládání poskytovatele: ' + error.message);
      } finally {
        this.isSaving = false;
      }
    },

    closeProviderForm() {
      this.showProviderForm = false;
      this.editingProvider = null;
      this.resetProviderForm();
    },

    resetProviderForm() {
      this.providerForm = {
        name: '',
        base_url: '',
        api_key: '',
        api_version: 'v1',
        auth_type: 'api_key',
        rate_limit: null,
        api_key_required: true,
        is_active: true
      };
    },

    // Model CRUD methods
    showCreateModelForm() {
      this.editingModel = null;
      this.resetModelForm();
      this.showModelForm = true;
    },

    editModel(model) {
      console.log('🔧 Editace modelu - debug info:', {
        model_id: model.model_id,
        id: model.id,
        model_name: model.model_name,
        provider_id: model.provider_id,
        provider_name: model.provider_name,
        full_model_object: model
      });

      this.editingModel = model;
      this.modelForm = {
        provider_id: model.provider_id,
        model_name: model.model_name,
        model_identifier: model.model_identifier,
        context_length: model.context_length,
        max_tokens_output: model.max_tokens_output,
        temperature: model.temperature || 0.7,
        capabilities: {
          text: model.capabilities?.text !== false,
          code: model.capabilities?.code === true,
          vision: model.capabilities?.vision === true,
          function_calling: model.capabilities?.function_calling === true,
          reasoning: model.capabilities?.reasoning === true,
          multimodal: model.capabilities?.multimodal === true
        },
        is_default: model.is_default,
        is_active: model.is_active
      };
      this.showModelForm = true;
    },

    async deleteModel(model) {
      if (!confirm(`Opravdu chcete smazat model "${model.model_name}"? Tato akce nelze vrátit zpět.`)) {
        return;
      }

      try {
        const response = await fetch(`/api/db/llm/models/${model.model_id}`, {
          method: 'DELETE'
        });

        if (response.ok) {
          alert(`✅ Model "${model.model_name}" byl úspěšně smazán!`);
          await this.refreshModels();
          await this.refreshProviders(); // Aktualizace počtu modelů u poskytovatelů
        } else {
          const errorData = await response.json();
          alert(`❌ Chyba při mazání modelu: ${errorData.detail || 'Neznámá chyba'}`);
        }
      } catch (error) {
        console.error('Chyba při mazání modelu:', error);
        alert('❌ Chyba při mazání modelu: ' + error.message);
      }
    },

    async toggleModelDefault(model) {
      try {
        console.log('🔄 Nastavování výchozího modelu:', {
          model_name: model.model_name,
          provider_id: model.provider_id,
          provider_name: model.provider_name,
          current_is_default: model.is_default,
          model_id: model.model_id
        });

        const url = `/api/db/llm/providers/${model.provider_id}/models/${model.model_name}/set-default`;
        console.log('🌐 API URL:', url);

        const response = await fetch(url, {
          method: 'PUT'
        });

        console.log('📡 API Response status:', response.status);

        if (response.ok) {
          const responseData = await response.json();
          console.log('✅ API Response data:', responseData);
          console.log(`Nastavuji model "${model.model_name}" jako výchozí pro poskytovatele ${model.provider_id}`);

          // Aktualizujeme data lokálně pro okamžitou odezvu
          this.models.forEach(m => {
            if (m.provider_id === model.provider_id) {
              m.is_default = (m.model_id === model.model_id);
              console.log(`🔄 Lokální aktualizace: ${m.model_name} → is_default: ${m.is_default}`);
            }
          });

          // Vynucení re-render Vue komponenty
          this.$forceUpdate();

          alert(`✅ Model "${model.model_name}" byl nastaven jako výchozí!`);

          // Načteme čerstvá data z API
          console.log('🔄 Načítám čerstvá data z API...');
          await this.refreshModels();
          await this.refreshProviders(); // Aktualizace poskytovatelů kvůli výchozímu modelu
        } else {
          const errorData = await response.json();
          console.error('❌ API Error:', errorData);
          alert(`❌ Chyba při nastavování výchozího modelu: ${errorData.detail || 'Neznámá chyba'}`);
        }
      } catch (error) {
        console.error('❌ Chyba při nastavování výchozího modelu:', error);
        alert('❌ Chyba při nastavování výchozího modelu: ' + error.message);
      }
    },

    async saveModel() {
      this.isSaving = true;
      try {
        console.log('💾 Ukládání modelu - debug info:', {
          editingModel: this.editingModel,
          editingModel_model_id: this.editingModel?.model_id,
          editingModel_id: this.editingModel?.id,
          modelForm: this.modelForm
        });

        const url = this.editingModel
          ? `/api/db/llm/models/${this.editingModel.model_id}`
          : '/api/db/llm/models';

        console.log('🌐 API URL pro ukládání:', url);

        const method = this.editingModel ? 'PUT' : 'POST';

        const modelData = {
          provider_id: this.modelForm.provider_id,
          model_name: this.modelForm.model_name,
          model_identifier: this.modelForm.model_identifier,
          context_length: this.modelForm.context_length || null,
          max_tokens_output: this.modelForm.max_tokens_output || null,
          temperature: this.modelForm.temperature || 0.7,
          capabilities: this.modelForm.capabilities,
          is_default: this.modelForm.is_default,
          is_active: this.modelForm.is_active
        };

        console.log('📤 Odesílaná data:', modelData);

        const response = await fetch(url, {
          method: method,
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(modelData)
        });

        console.log('📡 Response status:', response.status);

        if (response.ok) {
          const responseData = await response.json();
          console.log('✅ Response data:', responseData);
          const action = this.editingModel ? 'aktualizován' : 'vytvořen';
          alert(`✅ Model "${this.modelForm.model_name}" byl úspěšně ${action}!`);
          this.closeModelForm();
          await this.refreshModels();
          await this.refreshProviders(); // Aktualizace počtu modelů u poskytovatelů
        } else {
          const errorData = await response.json();
          console.error('❌ API Error:', errorData);
          alert(`❌ Chyba při ukládání modelu: ${errorData.detail || 'Neznámá chyba'}`);
        }
      } catch (error) {
        console.error('❌ Chyba při ukládání modelu:', error);
        alert('❌ Chyba při ukládání modelu: ' + error.message);
      } finally {
        this.isSaving = false;
      }
    },

    async addOllamaProvider() {
      try {
        console.log('🦙 Přidávání Ollama provideru...');

        const response = await fetch('/api/db/llm/providers/ollama', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            ollama_url: 'http://192.168.111.152:11434',
            provider_name: 'Ollama'
          })
        });

        if (response.ok) {
          const data = await response.json();
          console.log('✅ Ollama provider přidán:', data);

          // Zobrazíme úspěšnou zprávu
          this.$nextTick(() => {
            const message = `✅ ${data.message}\n\nAutomaticky načteny modely z Ollama serveru.`;
            alert(message);
          });

          // Obnovíme data
          await this.refreshProviders();
          await this.refreshModels();

        } else {
          const errorData = await response.json();
          console.error('❌ Chyba při přidávání Ollama:', errorData);
          alert(`❌ Chyba při přidávání Ollama provideru: ${errorData.detail || 'Neznámá chyba'}`);
        }
      } catch (error) {
        console.error('❌ Chyba při přidávání Ollama provideru:', error);
        alert('❌ Chyba při přidávání Ollama provideru: ' + error.message);
      }
    },

    async addOllama2Provider() {
      try {
        console.log('🦙 Přidávání Ollama2 provideru...');

        const response = await fetch('/api/db/llm/providers/ollama', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            ollama_url: 'http://192.168.111.106:11434',
            provider_name: 'Ollama2'
          })
        });

        if (response.ok) {
          const data = await response.json();
          console.log('✅ Ollama2 provider přidán:', data);

          // Zobrazíme úspěšnou zprávu
          this.$nextTick(() => {
            const message = `✅ ${data.message}\n\nAutomaticky načteny modely z Ollama2 serveru (192.168.111.106).`;
            alert(message);
          });

          // Obnovíme data
          await this.refreshProviders();
          await this.refreshModels();

        } else {
          const errorData = await response.json();
          console.error('❌ Chyba při přidávání Ollama2:', errorData);
          alert(`❌ Chyba při přidávání Ollama2 provideru: ${errorData.detail || 'Neznámá chyba'}`);
        }
      } catch (error) {
        console.error('❌ Chyba při přidávání Ollama2 provideru:', error);
        alert('❌ Chyba při přidávání Ollama2 provideru: ' + error.message);
      }
    },

    async addLMStudioProvider() {
      try {
        console.log('🏭 Přidávání LM Studio provideru...');

        const response = await fetch('/api/db/llm/providers/lmstudio', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            lmstudio_url: 'http://192.168.111.126:1234',
            provider_name: 'LM Studio'
          })
        });

        if (response.ok) {
          const data = await response.json();
          console.log('✅ LM Studio provider přidán:', data);

          // Zobrazíme úspěšnou zprávu
          this.$nextTick(() => {
            const message = `✅ ${data.message}\n\nAutomaticky načteny modely z LM Studio serveru (192.168.111.126).`;
            alert(message);
          });

          // Obnovíme data
          await this.refreshProviders();
          await this.refreshModels();

        } else {
          const errorData = await response.json();
          console.error('❌ Chyba při přidávání LM Studio:', errorData);
          alert(`❌ Chyba při přidávání LM Studio provideru: ${errorData.detail || 'Neznámá chyba'}`);
        }
      } catch (error) {
        console.error('❌ Chyba při přidávání LM Studio provideru:', error);
        alert('❌ Chyba při přidávání LM Studio provideru: ' + error.message);
      }
    },

    addProvider() {
      this.editingProvider = null;
      this.resetProviderForm();
      this.showProviderForm = true;
    },

    closeModelForm() {
      this.showModelForm = false;
      this.editingModel = null;
      this.resetModelForm();
    },

    resetModelForm() {
      this.modelForm = {
        provider_id: '',
        model_name: '',
        model_identifier: '',
        context_length: null,
        max_tokens_output: null,
        temperature: 0.7,
        capabilities: {
          text: true,
          code: false,
          vision: false,
          function_calling: false,
          reasoning: false,
          multimodal: false
        },
        is_default: false,
        is_active: true
      };
    },

    // Communication between LLM methods
    async startCommunication() {
      if (!this.canStartCommunication) return;

      this.isProcessing = true;
      this.finalAnswer = null;
      this.finalCode = null;
      this.finalValidation = null;
      this.ai0Messages = [];
      this.ai1Messages = [];
      this.ai2Messages = [];

      try {
        // Vytvoření nové session
        this.currentSession = {
          id: `session_${Date.now()}`,
          startTime: new Date(),
          maxIterations: this.communicationForm.iterationCount,
          currentIteration: 0,
          status: 'initializing',
          ai0Model: this.communicationForm.ai0Model,
          originalPrompt: this.communicationForm.userPrompt
        };

        console.log('🚀 Spouštím komunikaci mezi LLM:', this.currentSession);

        // Krok 1: AI-0 optimalizuje prompt
        await this.optimizePromptWithAI0();

        // Krok 2: Spustit iterativní komunikaci mezi AI-1 a AI-2
        await this.runIterativeCommunication();

        // Krok 3: AI-0 validuje finální odpověď
        await this.validateFinalAnswer();

        this.currentSession.status = 'completed';
        console.log('✅ Komunikace mezi LLM dokončena');

      } catch (error) {
        console.error('❌ Chyba při komunikaci mezi LLM:', error);
        this.currentSession.status = 'error';
        this.currentSession.errorMessage = error.message;
      } finally {
        this.isProcessing = false;
      }
    },

    async optimizePromptWithAI0() {
      this.currentSession.status = 'optimizing';
      console.log('🔧 AI-0 optimalizuje prompt...');

      try {
        // Skutečné API volání na AI-0 model
        const ai0ModelId = this.communicationForm.ai0Model;
        if (!ai0ModelId) {
          throw new Error('AI-0 model není vybrán');
        }

        // Detekce typu úkolu
        const isCodeRequest = this.currentSession.originalPrompt.toLowerCase().includes('kod') ||
                             this.currentSession.originalPrompt.toLowerCase().includes('code') ||
                             this.currentSession.originalPrompt.toLowerCase().includes('python') ||
                             this.currentSession.originalPrompt.toLowerCase().includes('javascript') ||
                             this.currentSession.originalPrompt.toLowerCase().includes('kalkulačka') ||
                             this.currentSession.originalPrompt.toLowerCase().includes('calculator');

        let optimizationPrompt;

        if (isCodeRequest) {
          optimizationPrompt = `Jsi AI-0 prompt optimizer pro programovací úkoly. Optimalizuj následující požadavek na kód:

PŮVODNÍ POŽADAVEK: "${this.currentSession.originalPrompt}"

Vytvoř jasný a strukturovaný prompt pro AI-1, který:
1. Jasně specifikuje programovací jazyk a typ aplikace
2. Definuje konkrétní funkcionalitu a požadavky
3. Specifikuje očekávaný výstup (kompletní funkční kód)
4. Obsahuje technické požadavky (jednoduchost, rychlost, čitelnost)
5. Zdůrazní, že výstupem má být POUZE FUNKČNÍ KÓD bez vysvětlení

DŮLEŽITÉ:
- Optimalizovaný prompt musí být dostatečně specifický pro daný typ aplikace
- Musí jasně říct, že AI-1 má napsat KOMPLETNÍ FUNKČNÍ KÓD
- Nesmí obsahovat hardcoded detaily, ale obecné principy

Vrať pouze optimalizovaný prompt bez dalších komentářů.`;
        } else {
          optimizationPrompt = `Jsi AI-0 prompt optimizer. Tvým úkolem je optimalizovat následující prompt pro lepší pochopení a zpracování dalšími AI modely.

Původní prompt: "${this.currentSession.originalPrompt}"

Optimalizuj tento prompt tak, aby:
1. Byl jasný a strukturovaný
2. Obsahoval všechny potřebné detaily
3. Byl vhodný pro iterativní zpracování mezi AI-1 a AI-2
4. Specifikoval očekávaný výstup

Vrať pouze optimalizovaný prompt bez dalších komentářů.`;
        }

        console.log('📤 Odesílám prompt na AI-0:', optimizationPrompt);

        const response = await chatService.sendMessage({
          message: optimizationPrompt,
          model_id: ai0ModelId
        });

        const optimizedPrompt = response.response?.text || response.text || response.content || response.message;

        if (!optimizedPrompt) {
          throw new Error('AI-0 nevrátil optimalizovaný prompt');
        }

        console.log('📥 AI-0 odpověď:', optimizedPrompt);

        // Uložení do Mem0
        await this.saveToMem0('ai0_optimization', {
          original_prompt: this.currentSession.originalPrompt,
          optimized_prompt: optimizedPrompt,
          session_id: this.currentSession.id,
          timestamp: new Date().toISOString()
        });

        // Uložení AI-0 aktivity
        const ai0Message = {
          id: `ai0_optimize_${Date.now()}`,
          type: 'optimization',
          phase: 'Optimalizace promptu',
          inputPrompt: this.currentSession.originalPrompt,
          outputPrompt: optimizedPrompt,
          analysis: `AI-0 optimalizoval prompt pomocí modelu ${ai0ModelId}`,
          timestamp: new Date()
        };

        this.ai0Messages.push(ai0Message);
        this.currentSession.optimizedPrompt = optimizedPrompt;

        console.log('✅ AI-0 optimalizace dokončena');

      } catch (error) {
        throw new Error(`Chyba při optimalizaci promptu: ${error.message}`);
      }
    },

    async runIterativeCommunication() {
      this.currentSession.status = 'processing';
      console.log('🔄 Spouštím iterativní komunikaci...');

      for (let i = 1; i <= this.currentSession.maxIterations; i++) {
        this.currentSession.currentIteration = i;
        console.log(`📝 Iterace ${i}/${this.currentSession.maxIterations}`);

        // AI-1 zpracuje prompt/předchozí odpověď AI-2
        await this.processWithAI1(i);

        // AI-2 analyzuje odpověď AI-1
        await this.processWithAI2(i);

        // Krátká pauza mezi iteracemi
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    },

    // Middleware funkce s vlastním LLM pro inteligentní analýzu
    async processMessage(message, fromAgent, toAgent, iteration, history, originalTask) {
      console.log(`🔄 Middleware LLM: ${fromAgent} → ${toAgent} (iterace ${iteration})`);

      try {
        // Volání middleware LLM pro inteligentní analýzu
        const middlewareInstructions = await this.callMiddlewareLLM(
          originalTask || this.currentSession.originalPrompt,
          history,
          toAgent,
          iteration,
          this.currentSession.maxIterations
        );

        // Sestavení obohacené zprávy s AI-generovanými instrukcemi
        const enrichedMessage = `
🚨 MIDDLEWARE INSTRUKCE (AI-GENEROVANÉ) 🚨

ITERACE: ${iteration}/${this.currentSession.maxIterations}
${middlewareInstructions}

=== PŮVODNÍ ZPRÁVA ===
${message}

🚨 DODRŽUJ VÝŠE UVEDENÉ INSTRUKCE! 🚨
`;

        console.log('🧠 Middleware LLM vygeneroval instrukce:', middlewareInstructions);
        return enrichedMessage;

      } catch (error) {
        console.error('❌ Chyba v middleware LLM:', error);

        // Fallback na statické instrukce při chybě
        return this.processMessageFallback(message, fromAgent, toAgent, iteration, history);
      }
    },

    // Volání middleware LLM pro analýzu a generování instrukcí
    async callMiddlewareLLM(originalTask, history, toAgent, iteration, maxIterations) {
      const middlewareModelId = this.communicationForm.middlewareModel;
      if (!middlewareModelId) {
        throw new Error('Middleware LLM model není vybrán');
      }

      // Příprava historie pro analýzu
      const historyText = history.length > 0
        ? history.map((h, i) => `${h.agent} (${i+1}): ${h.content}`).join('\n\n')
        : 'Žádná předchozí historie';

      // Poslední zpráva pro kontext
      const lastMessage = history.length > 0
        ? history[history.length - 1].content
        : 'Žádná předchozí zpráva';

      const middlewarePrompt = `Jsi UNIVERZÁLNÍ MIDDLEWARE pro multi-agent spolupráci. Tvým úkolem je nechat každou AI být sama sebou a využít její přirozené silné stránky.

ÚKOL: "${originalTask}"

HISTORIE KONVERZACE:
${historyText}

POSLEDNÍ PŘÍSPĚVEK:
${lastMessage}

AKTUÁLNÍ SITUACE:
- Iterace: ${iteration}/${maxIterations}
- Příští agent: ${toAgent}
- Typ úlohy: ${this.detectTaskType(originalTask)}

FILOSOFIE MULTI-AGENT SPOLUPRÁCE:
- NEDIKTUJ co má ${toAgent} dělat
- Nech ${toAgent} přinést SVOU UNIKÁTNÍ perspektivu
- Každá AI má jiné silné stránky (analytické/kreativní/pragmatické/teoretické)
- Diversita perspektiv = lepší výsledky
- Přirozená spolupráce místo nucené

TVŮJ ÚKOL:
Vytvoř jednoduchou, povzbuzující instrukci pro ${toAgent}, která:
1. Nechá ${toAgent} být sám sebou
2. Využije jeho přirozené silné stránky
3. Povzbudí k unikátnímu pohledu na úkol
4. Zabrání opakování už řečeného

POŽADOVANÝ FORMÁT:
=== TVOJE ROLE: ${toAgent} ===
ÚKOL: ${originalTask}
TVŮJ PŘÍSPĚVEK: [co má přidat ze své perspektivy]
ZAMĚŘ SE NA: [jeho silné stránky pro tento typ úkolu]
NEPIŠ ZNOVU: [stručně - co už bylo pokryto]

PŘÍKLADY DOBRÝCH INSTRUKCÍ:

Pro programování:
=== TVOJE ROLE: AI-1 ===
ÚKOL: Napiš kalkulačku
TVŮJ PŘÍSPĚVEK: Přidej svůj analytický pohled na strukturu kódu
ZAMĚŘ SE NA: Tvoje technické schopnosti a logické myšlení
NEPIŠ ZNOVU: Základní definici funkce

Pro kreativní úkol:
=== TVOJE ROLE: AI-2 ===
ÚKOL: Vymysli nový produkt
TVŮJ PŘÍSPĚVEK: Přines nečekanou, inovativní funkci
ZAMĚŘ SE NA: Tvoje kreativní myšlení a originalitu
NEPIŠ ZNOVU: Základní popis produktu

Vygeneruj instrukci pro ${toAgent}:`;

      const response = await chatService.sendMessage({
        model_id: middlewareModelId,
        message: middlewarePrompt
      });

      const middlewareResponse = response.response?.text || response.text || response.content || response.message;

      if (!middlewareResponse) {
        throw new Error('Middleware LLM nevrátil odpověď');
      }

      return middlewareResponse;
    },

    // Detekce typu úlohy
    detectTaskType(task) {
      const taskLower = task.toLowerCase();

      if (taskLower.includes('kód') || taskLower.includes('code') ||
          taskLower.includes('program') || taskLower.includes('kalkulačka') ||
          taskLower.includes('python') || taskLower.includes('javascript')) {
        return 'programování';
      } else if (taskLower.includes('příběh') || taskLower.includes('story') ||
                 taskLower.includes('kreativní') || taskLower.includes('báseň')) {
        return 'kreativní';
      } else if (taskLower.includes('analýza') || taskLower.includes('vyhodnoť') ||
                 taskLower.includes('porovnej') || taskLower.includes('zhodnoť')) {
        return 'analytická';
      } else if (taskLower.includes('postup') || taskLower.includes('návod') ||
                 taskLower.includes('jak') || taskLower.includes('kroky')) {
        return 'stavební';
      } else {
        return 'obecná';
      }
    },

    // Fallback funkce při chybě middleware LLM
    processMessageFallback(message, fromAgent, toAgent, iteration, history) {
      console.log('🔄 Fallback middleware (statické instrukce)');

      const enrichedMessage = `
🚨 MIDDLEWARE INSTRUKCE (FALLBACK) 🚨

ITERACE: ${iteration}/${this.currentSession.maxIterations}
PŮVODNÍ ÚKOL: "${this.currentSession.originalPrompt}"

⚠️ KRITICKÉ INSTRUKCE ⚠️
1. PŘIDEJ POUZE JEDNU VĚC NAVÍC k tomu, co už je napsáno
2. POKUD NAPÍŠEŠ VÍC NEŽ 2 VĚTY, SELHAL JSI
3. NEPIŠ CELÉ ŘEŠENÍ ZNOVU - jen JEDEN KROK DOPŘEDU

TVŮJ ÚKOL: Přidej jeden konkrétní detail k předchozí verzi

=== PŮVODNÍ ZPRÁVA ===
${message}

🚨 POUZE JEDEN KROK! MAXIMÁLNĚ 2 VĚTY! 🚨
`;

      return enrichedMessage;
    },

    // Detekce ZAKÁZANÝCH frází a dlouhých odpovědí
    detectRepeatedConcepts(messages) {
      const violations = [];
      const bannedPhrases = [
        'budu pokračovat', 'implementuji doporučení', 'rozvijem', 'vylepším',
        'pokračujeme', 'budeme pracovat', 'zaměříme se', 'můžeme přidat',
        'v další iteraci', 'postupně', 'krok za krokem', 'celkově',
        'komplexní řešení', 'detailní analýza', 'rozsáhlé', 'kompletní'
      ];

      messages.forEach((msg, index) => {
        // Kontrola zakázaných frází
        bannedPhrases.forEach(phrase => {
          if (msg.toLowerCase().includes(phrase.toLowerCase())) {
            violations.push(`❌ ZAKÁZÁNO: "${phrase}"`);
          }
        });

        // Kontrola délky (více než 3 věty = porušení)
        const sentences = msg.split(/[.!?]+/).filter(s => s.trim().length > 0);
        if (sentences.length > 3) {
          violations.push(`❌ PŘÍLIŠ DLOUHÉ: ${sentences.length} vět (max 2)`);
        }

        // Kontrola opakování celých bloků
        if (index > 0 && msg.length > 100 && messages[index-1].includes(msg.substring(0, 50))) {
          violations.push(`❌ OPAKOVÁNÍ: Přepisování předchozí odpovědi`);
        }
      });

      return [...new Set(violations)];
    },

    // Analýza pokroku
    analyzeProgress(history, originalTask) {
      const completed = [];
      const missing = [];

      // Základní analýza podle typu úkolu
      if (originalTask.toLowerCase().includes('kód') || originalTask.toLowerCase().includes('code')) {
        const hasCode = history.some(h => h.content.includes('def ') || h.content.includes('function') || h.content.includes('class'));
        const hasLogic = history.some(h => h.content.includes('if ') || h.content.includes('for ') || h.content.includes('while'));
        const hasComments = history.some(h => h.content.includes('#') || h.content.includes('//'));

        if (hasCode) completed.push('Základní kód napsán');
        else missing.push('Napsat funkční kód');

        if (hasLogic) completed.push('Logika implementována');
        else missing.push('Implementovat logiku');

        if (hasComments) completed.push('Komentáře přidány');
        else missing.push('Přidat komentáře a dokumentaci');
      } else {
        // Pro textové úkoly
        const hasStructure = history.some(h => h.content.length > 100);
        const hasDetails = history.some(h => h.content.includes('například') || h.content.includes('konkrétně'));

        if (hasStructure) completed.push('Základní struktura odpovědi');
        else missing.push('Vytvořit strukturovanou odpověď');

        if (hasDetails) completed.push('Konkrétní příklady');
        else missing.push('Přidat konkrétní příklady');
      }

      return { completed, missing };
    },

    // Generování OSTRÝCH konkrétních úkolů
    generateNewAspects(agent, iteration, progress) {
      const aspects = [];

      if (agent === 'AI-1') {
        if (iteration === 1) {
          aspects.push(`🎯 Napiš POUZE první řádek kódu/první větu odpovědi`);
        } else {
          if (progress.missing.length > 0) {
            aspects.push(`🎯 Přidej POUZE: ${progress.missing[0]} (jeden řádek)`);
          } else {
            aspects.push(`🎯 Vylepši POUZE jeden detail z předchozí verze`);
          }
        }
      } else if (agent === 'AI-2') {
        if (iteration === 1) {
          aspects.push(`🔍 Řekni POUZE jednu konkrétní věc, co chybí`);
        } else {
          aspects.push(`🔍 Navrhni POUZE jednu konkrétní změnu (ne seznam)`);
        }
      }

      return aspects;
    },

    async processWithAI1(iteration) {
      console.log(`🤖 AI-1 zpracovává iteraci ${iteration}...`);

      try {
        // Skutečné API volání na AI-1 model
        const ai1ModelId = this.communicationForm.ai1Model;
        if (!ai1ModelId) {
          throw new Error('AI-1 model není vybrán');
        }

        // Určení vstupního promptu
        let inputPrompt;
        let contextFromMem0 = '';

        if (iteration === 1) {
          inputPrompt = this.currentSession.optimizedPrompt;
        } else {
          // Načíst kontext z Mem0 od AI-2
          const mem0Data = await this.getFromMem0(`ai2_iteration_${iteration - 1}`);
          if (mem0Data && mem0Data.content) {
            contextFromMem0 = `\n\nKontext z předchozí iterace od AI-2:\n${mem0Data.content}`;
          }

          const lastAI2Message = this.ai2Messages[this.ai2Messages.length - 1];
          inputPrompt = (lastAI2Message?.outputPrompt || 'Pokračuj v předchozí práci...') + contextFromMem0;
        }

        // MIDDLEWARE: Obohacení zprávy před odesláním
        const history = [
          ...this.ai1Messages.map(m => ({agent: 'AI-1', content: m.content})),
          ...this.ai2Messages.map(m => ({agent: 'AI-2', content: m.content}))
        ];

        inputPrompt = await this.processMessage(
          inputPrompt,
          iteration === 1 ? 'AI-0' : 'AI-2',
          'AI-1',
          iteration,
          history,
          this.currentSession.originalPrompt
        );

        // Detekce typu úkolu pro AI-1
        const isCodeRequest = this.currentSession.originalPrompt.toLowerCase().includes('kod') ||
                             this.currentSession.originalPrompt.toLowerCase().includes('code') ||
                             this.currentSession.originalPrompt.toLowerCase().includes('python') ||
                             this.currentSession.originalPrompt.toLowerCase().includes('javascript') ||
                             this.currentSession.originalPrompt.toLowerCase().includes('kalkulačka') ||
                             this.currentSession.originalPrompt.toLowerCase().includes('calculator');

        let ai1Prompt;

        if (isCodeRequest) {
          ai1Prompt = `${inputPrompt}

Jsi AI-1 - analytický programátor s technickým zaměřením.

PŮVODNÍ ÚKOL: "${this.currentSession.originalPrompt}"
OPTIMALIZOVANÉ ZADÁNÍ: "${this.currentSession.optimizedPrompt}"

TVOJE SILNÉ STRÁNKY:
- Analytické myšlení
- Technické řešení problémů
- Strukturované programování
- Logická architektura kódu

TVŮJ PŘÍSTUP:
Využij své analytické schopnosti a přidej svůj unikátní technický pohled na řešení.
Buď sám sebou - přines to, co umíš nejlépe.`;
        } else {
          ai1Prompt = `${inputPrompt}

Jsi AI-1 - analytický myslitel se zaměřením na strukturované řešení problémů.

PŮVODNÍ ÚKOL: "${this.currentSession.originalPrompt}"
OPTIMALIZOVANÉ ZADÁNÍ: "${this.currentSession.optimizedPrompt}"

TVOJE SILNÉ STRÁNKY:
- Analytické myšlení
- Strukturované přístupy
- Logické řešení problémů
- Systematické zpracování informací

TVŮJ PŘÍSTUP:
Využij své analytické schopnosti a přidej svůj unikátní systematický pohled na řešení.
Buď sám sebou - přines to, co umíš nejlépe.`;
        }

        console.log(`📤 Odesílám prompt na AI-1 (iterace ${iteration}):`, ai1Prompt);

        const response = await chatService.sendMessage({
          message: ai1Prompt,
          model_id: ai1ModelId
        });

        const ai1Response = response.response?.text || response.text || response.content || response.message;

        if (!ai1Response) {
          throw new Error(`AI-1 nevrátil odpověď v iteraci ${iteration}`);
        }

        console.log(`📥 AI-1 odpověď (iterace ${iteration}):`, ai1Response);

        // Uložení do Mem0
        await this.saveToMem0(`ai1_iteration_${iteration}`, {
          content: ai1Response,
          input_prompt: inputPrompt,
          iteration: iteration,
          session_id: this.currentSession.id,
          timestamp: new Date().toISOString()
        });

        const outputPrompt = `Analyzuj a rozšiř tuto odpověď od AI-1 v iteraci ${iteration}.`;

        const message = {
          id: `ai1_${iteration}_${Date.now()}`,
          type: 'response',
          content: ai1Response,
          inputPrompt: inputPrompt,
          outputPrompt: outputPrompt,
          iteration: iteration,
          timestamp: new Date()
        };

        this.ai1Messages.push(message);
        console.log(`✅ AI-1 iterace ${iteration} dokončena`);

      } catch (error) {
        throw new Error(`Chyba při zpracování AI-1 v iteraci ${iteration}: ${error.message}`);
      }
    },

    async processWithAI2(iteration) {
      console.log(`🧠 AI-2 analyzuje iteraci ${iteration}...`);

      try {
        // Skutečné API volání na AI-2 model
        const ai2ModelId = this.communicationForm.ai2Model;
        if (!ai2ModelId) {
          throw new Error('AI-2 model není vybrán');
        }

        // Získání posledního výstupu od AI-1
        const lastAI1Message = this.ai1Messages[this.ai1Messages.length - 1];
        let inputFromAI1 = lastAI1Message?.content || 'Žádný vstup od AI-1';

        // Načíst kontext z Mem0 od AI-1
        const mem0Data = await this.getFromMem0(`ai1_iteration_${iteration}`);
        let contextFromMem0 = '';
        if (mem0Data && mem0Data.content) {
          contextFromMem0 = `\n\nKontext z Mem0 od AI-1:\n${mem0Data.content}`;
        }

        // MIDDLEWARE: Obohacení zprávy před odesláním
        const history = [
          ...this.ai1Messages.map(m => ({agent: 'AI-1', content: m.content})),
          ...this.ai2Messages.map(m => ({agent: 'AI-2', content: m.content}))
        ];

        inputFromAI1 = await this.processMessage(
          inputFromAI1 + contextFromMem0,
          'AI-1',
          'AI-2',
          iteration,
          history,
          this.currentSession.originalPrompt
        );

        // Detekce typu úkolu pro AI-2
        const isCodeRequest = this.currentSession.originalPrompt.toLowerCase().includes('kod') ||
                             this.currentSession.originalPrompt.toLowerCase().includes('code') ||
                             this.currentSession.originalPrompt.toLowerCase().includes('python') ||
                             this.currentSession.originalPrompt.toLowerCase().includes('javascript') ||
                             this.currentSession.originalPrompt.toLowerCase().includes('kalkulačka') ||
                             this.currentSession.originalPrompt.toLowerCase().includes('calculator');

        let ai2Prompt;

        if (isCodeRequest) {
          ai2Prompt = `${inputFromAI1}

Jsi AI-2 - kreativní a pragmatický programátor se zaměřením na inovace a praktičnost.

PŮVODNÍ ÚKOL: "${this.currentSession.originalPrompt}"
OPTIMALIZOVANÉ ZADÁNÍ: "${this.currentSession.optimizedPrompt}"

TVOJE SILNÉ STRÁNKY:
- Kreativní přístupy k řešení
- Pragmatické myšlení
- Inovativní nápady
- Praktické vylepšení
- Uživatelská perspektiva

TVŮJ PŘÍSTUP:
Podívej se na to, co vytvořil AI-1, a přidej svůj unikátní kreativní a pragmatický pohled.
Buď sám sebou - přines to, co umíš nejlépe.`;
        } else {
          ai2Prompt = `${inputFromAI1}

Jsi AI-2 - kreativní myslitel se zaměřením na inovace a praktické aplikace.

PŮVODNÍ ÚKOL: "${this.currentSession.originalPrompt}"
OPTIMALIZOVANÉ ZADÁNÍ: "${this.currentSession.optimizedPrompt}"

TVOJE SILNÉ STRÁNKY:
- Kreativní myšlení
- Inovativní přístupy
- Praktické aplikace
- Nečekané perspektivy
- Uživatelský pohled

TVŮJ PŘÍSTUP:
Podívej se na to, co vytvořil AI-1, a přidej svůj unikátní kreativní a praktický pohled.
Buď sám sebou - přines to, co umíš nejlépe.`;
        }

        console.log(`📤 Odesílám prompt na AI-2 (iterace ${iteration}):`, ai2Prompt);

        const response = await chatService.sendMessage({
          message: ai2Prompt,
          model_id: ai2ModelId
        });

        const ai2Response = response.response?.text || response.text || response.content || response.message;

        if (!ai2Response) {
          throw new Error(`AI-2 nevrátil odpověď v iteraci ${iteration}`);
        }

        console.log(`📥 AI-2 odpověď (iterace ${iteration}):`, ai2Response);

        // Uložení do Mem0
        await this.saveToMem0(`ai2_iteration_${iteration}`, {
          content: ai2Response,
          input_from_ai1: inputFromAI1,
          iteration: iteration,
          session_id: this.currentSession.id,
          timestamp: new Date().toISOString()
        });

        const outputPrompt = `Implementuj doporučení od AI-2 a pokračuj v práci na základě této analýzy.`;

        const message = {
          id: `ai2_${iteration}_${Date.now()}`,
          type: 'analysis',
          content: ai2Response,
          inputPrompt: inputFromAI1,
          outputPrompt: outputPrompt,
          iteration: iteration,
          timestamp: new Date()
        };

        this.ai2Messages.push(message);
        console.log(`✅ AI-2 iterace ${iteration} dokončena`);

      } catch (error) {
        throw new Error(`Chyba při zpracování AI-2 v iteraci ${iteration}: ${error.message}`);
      }
    },

    async validateFinalAnswer() {
      this.currentSession.status = 'validating';
      console.log('✅ AI-0 validuje finální odpověď...');

      try {
        // Skutečné API volání na AI-0 model pro validaci
        const ai0ModelId = this.communicationForm.ai0Model;
        if (!ai0ModelId) {
          throw new Error('AI-0 model není vybrán');
        }

        // Sestavení všech výsledků z iterací
        let allResults = '';
        for (let i = 1; i <= this.currentSession.maxIterations; i++) {
          const ai1Message = this.ai1Messages.find(m => m.iteration === i);
          const ai2Message = this.ai2Messages.find(m => m.iteration === i);

          if (ai1Message) {
            allResults += `\n\n=== ITERACE ${i} - AI-1 ===\n${ai1Message.content}`;
          }
          if (ai2Message) {
            allResults += `\n\n=== ITERACE ${i} - AI-2 ===\n${ai2Message.content}`;
          }
        }

        // Detekce typu úkolu pro validaci
        const isCodeRequest = this.currentSession.originalPrompt.toLowerCase().includes('kod') ||
                             this.currentSession.originalPrompt.toLowerCase().includes('code') ||
                             this.currentSession.originalPrompt.toLowerCase().includes('python') ||
                             this.currentSession.originalPrompt.toLowerCase().includes('javascript') ||
                             this.currentSession.originalPrompt.toLowerCase().includes('kalkulačka') ||
                             this.currentSession.originalPrompt.toLowerCase().includes('calculator');

        let validationPrompt;

        // AI-0 pouze validuje relevanci, NEVYTVÁŘÍ vlastní odpověď
        validationPrompt = `Jsi AI-0 validator. Tvým úkolem je pouze porovnat a ohodnotit relevanci finální odpovědi.

PŮVODNÍ OTÁZKA UŽIVATELE: "${this.currentSession.originalPrompt}"

OPTIMALIZOVANÁ OTÁZKA (kterou jsi vytvořil): "${this.currentSession.optimizedPrompt}"

FINÁLNÍ ODPOVĚĎ OD AI-1/AI-2:
${this.ai1Messages.length > 0 ? this.ai1Messages[this.ai1Messages.length - 1].content : 'Žádná odpověď'}

TVŮJ ÚKOL - POUZE VALIDACE:

1. POROVNEJ: Odpovídá finální řešení na původní otázku uživatele?
2. OHODNOŤ: Je odpověď relevantní k původní otázce?
3. ZKONTROLUJ: Nezavlekly se AI-1 a AI-2 do jiného tématu?

DŮLEŽITÉ:
- NEVYTVÁŘEJ vlastní odpověď
- POUZE ohodnoť relevanci existující odpovědi
- Buď stručný a jasný

Vrať pouze krátké hodnocení: "Je finální odpověď relevantní k původní otázce? ANO/NE a proč."`;

        console.log('📤 Odesílám validační prompt na AI-0:', validationPrompt);

        const response = await chatService.sendMessage({
          message: validationPrompt,
          model_id: ai0ModelId
        });

        const finalContent = response.response?.text || response.text || response.content || response.message;

        if (!finalContent) {
          throw new Error('AI-0 nevrátil validační odpověď');
        }

        console.log('📥 AI-0 validační odpověď:', finalContent);

        // Uložení do Mem0
        await this.saveToMem0('final_validation', {
          content: finalContent,
          original_prompt: this.currentSession.originalPrompt,
          session_id: this.currentSession.id,
          all_iterations: allResults,
          timestamp: new Date().toISOString()
        });

        // Určení adherence na základě obsahu odpovědi
        let topicAdherence = 'good';
        const lowerContent = finalContent.toLowerCase();
        if (lowerContent.includes('výborně') || lowerContent.includes('plně splňuje') || lowerContent.includes('excellent')) {
          topicAdherence = 'excellent';
        } else if (lowerContent.includes('částečně') || lowerContent.includes('fair')) {
          topicAdherence = 'fair';
        } else if (lowerContent.includes('nedostatečně') || lowerContent.includes('poor')) {
          topicAdherence = 'poor';
        }

        // Uložení AI-0 validační aktivity
        const ai0ValidationMessage = {
          id: `ai0_validation_${Date.now()}`,
          type: 'validation',
          phase: 'Finální validace',
          inputPrompt: 'Validace výsledků všech iterací',
          analysis: `AI-0 provedl validaci ${this.currentSession.maxIterations} iterací pomocí modelu ${ai0ModelId}`,
          timestamp: new Date()
        };

        this.ai0Messages.push(ai0ValidationMessage);

        // Extrakce finální odpovědi z poslední iterace AI-1 (pro všechny typy úkolů)
        if (this.ai1Messages.length > 0) {
          const lastAI1Message = this.ai1Messages[this.ai1Messages.length - 1];

          // Finální odpověď (kód nebo text)
          this.finalAnswer = {
            content: lastAI1Message.content,
            iteration: lastAI1Message.iteration,
            timestamp: lastAI1Message.timestamp,
            type: isCodeRequest ? 'code' : 'text'
          };
          console.log('💻 Finální odpověď extrahována:', this.finalAnswer);

          // Pokud je to kód, uložíme také do finalCode pro zpětnou kompatibilitu
          if (isCodeRequest) {
            this.finalCode = {
              content: lastAI1Message.content,
              iteration: lastAI1Message.iteration,
              timestamp: lastAI1Message.timestamp
            };
          }
        }

        // Hodnocení od AI-0 (oddělené od finální odpovědi)
        this.finalValidation = {
          content: finalContent,
          topicAdherence: topicAdherence,
          validatedBy: 'AI-0',
          timestamp: new Date()
        };

        console.log('✅ AI-0 validace dokončena');

      } catch (error) {
        throw new Error(`Chyba při validaci finální odpovědi: ${error.message}`);
      }
    },

    stopCommunication() {
      if (this.currentSession) {
        this.currentSession.status = 'stopped';
        this.isProcessing = false;
        console.log('🛑 Komunikace zastavena uživatelem');
      }
    },

    clearSession() {
      this.currentSession = null;
      this.finalAnswer = null;
      this.finalCode = null;
      this.finalValidation = null;
      this.ai0Messages = [];
      this.ai1Messages = [];
      this.ai2Messages = [];
      this.communicationForm.userPrompt = '';
      console.log('🗑️ Session vymazána');
    },

    showManualInput(target) {
      this.manualInputTarget = target;
      this.manualInputText = '';
      this.showManualInputModal = true;
    },

    closeManualInput() {
      this.showManualInputModal = false;
      this.manualInputTarget = null;
      this.manualInputText = '';
    },

    async sendManualInput() {
      if (!this.manualInputText.trim() || !this.manualInputTarget) return;

      try {
        const message = {
          id: `manual_${this.manualInputTarget}_${Date.now()}`,
          type: 'manual',
          content: this.manualInputText,
          iteration: this.currentSession?.currentIteration || 0,
          timestamp: new Date()
        };

        if (this.manualInputTarget === 'ai1') {
          this.ai1Messages.push(message);
        } else {
          this.ai2Messages.push(message);
        }

        console.log(`✏️ Manuální input pro ${this.manualInputTarget}:`, this.manualInputText);

        this.closeManualInput();

      } catch (error) {
        console.error('Chyba při odesílání manuálního inputu:', error);
        // Chyba se zobrazí v konzoli, bez alert dialogu
      }
    },

    getSessionStatusText(status) {
      const statusMap = {
        'initializing': '🔧 Inicializace',
        'optimizing': '⚡ Optimalizace promptu',
        'processing': '🔄 Zpracovávání',
        'validating': '✅ Validace',
        'completed': '✨ Dokončeno',
        'stopped': '🛑 Zastaveno',
        'error': '❌ Chyba'
      };
      return statusMap[status] || status;
    },

    getAdherenceClass(adherence) {
      const classMap = {
        'excellent': 'adherence-excellent',
        'good': 'adherence-good',
        'fair': 'adherence-fair',
        'poor': 'adherence-poor'
      };
      return classMap[adherence] || '';
    },

    getAdherenceText(adherence) {
      const textMap = {
        'excellent': '🟢 Výborné dodržení tématu',
        'good': '🟡 Dobré dodržení tématu',
        'fair': '🟠 Částečné dodržení tématu',
        'poor': '🔴 Slabé dodržení tématu'
      };
      return textMap[adherence] || adherence;
    },

    // Mem0 integration methods
    async saveToMem0(key, data) {
      try {
        console.log(`💾 Ukládám do Mem0 klíč: ${key}`, data);

        // Volání MCP serveru pro Mem0
        const response = await fetch('/api/mcp/test', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            server_name: 'mem0',
            tool_name: 'add_memory',
            arguments: {
              messages: [{ role: 'user', content: JSON.stringify(data) }],
              user_id: `llm_communication_${this.currentSession?.id || 'default'}`,
              metadata: {
                key: key,
                session_id: this.currentSession?.id,
                timestamp: new Date().toISOString()
              }
            }
          })
        });

        if (!response.ok) {
          throw new Error(`Mem0 API chyba: ${response.status}`);
        }

        const result = await response.json();
        console.log(`✅ Uloženo do Mem0 klíč: ${key}`, result);
        return result;

      } catch (error) {
        console.error(`❌ Chyba při ukládání do Mem0 klíč: ${key}`, error);
        // Neblokujeme proces kvůli chybě Mem0
        return null;
      }
    },

    async getFromMem0(key) {
      try {
        console.log(`📥 Načítám z Mem0 klíč: ${key}`);

        // Volání MCP serveru pro Mem0
        const response = await fetch('/api/mcp/test', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            server_name: 'mem0',
            tool_name: 'search_memory',
            arguments: {
              query: key,
              user_id: `llm_communication_${this.currentSession?.id || 'default'}`,
              limit: 1
            }
          })
        });

        if (!response.ok) {
          throw new Error(`Mem0 API chyba: ${response.status}`);
        }

        const result = await response.json();
        console.log(`✅ Načteno z Mem0 klíč: ${key}`, result);

        if (result.content && result.content.length > 0) {
          // Pokusíme se parsovat JSON obsah
          try {
            return JSON.parse(result.content[0].memory);
          } catch (parseError) {
            return { content: result.content[0].memory };
          }
        }

        return null;

      } catch (error) {
        console.error(`❌ Chyba při načítání z Mem0 klíč: ${key}`, error);
        // Neblokujeme proces kvůli chybě Mem0
        return null;
      }
    },

    formatTime(timestamp) {
      if (!timestamp) return '';
      const date = new Date(timestamp);
      return date.toLocaleTimeString('cs-CZ', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    },

    async copyCodeToClipboard() {
      if (!this.finalCode || !this.finalCode.content) return;

      try {
        await navigator.clipboard.writeText(this.finalCode.content);
        console.log('📋 Kód zkopírován do schránky');
        // Můžeme přidat vizuální feedback později
      } catch (error) {
        console.error('❌ Chyba při kopírování kódu:', error);
        // Fallback pro starší prohlížeče
        const textArea = document.createElement('textarea');
        textArea.value = this.finalCode.content;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        console.log('📋 Kód zkopírován do schránky (fallback)');
      }
    },

    async copyAnswerToClipboard() {
      if (!this.finalAnswer || !this.finalAnswer.content) return;

      try {
        await navigator.clipboard.writeText(this.finalAnswer.content);
        console.log('📋 Finální odpověď zkopírována do schránky');
        // Můžeme přidat vizuální feedback později
      } catch (error) {
        console.error('❌ Chyba při kopírování odpovědi:', error);
        // Fallback pro starší prohlížeče
        const textArea = document.createElement('textarea');
        textArea.value = this.finalAnswer.content;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        console.log('📋 Finální odpověď zkopírována do schránky (fallback)');
      }
    }
  },

  async mounted() {
    console.log('LlmManagement mounted - začínám načítání dat');
    await this.refreshProviders();
    await this.refreshModels();
    await this.refreshPerformanceData();
    console.log('LlmManagement mounted - dokončeno, modelů:', this.models.length);
  }
};
</script>

<style>
@import '@/styles/admin-common.css';
@import '@/styles/chat-test.css';

/* Ollama specific styles */
.management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background: #2a2a2a;
  border-radius: 8px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.ollama-btn {
  background: linear-gradient(135deg, #ff6b35, #f7931e);
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.ollama-btn:hover {
  background: linear-gradient(135deg, #e55a2b, #e8841a);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
}

.ollama2-btn {
  background: linear-gradient(135deg, #6b46c1, #9333ea);
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.ollama2-btn:hover {
  background: linear-gradient(135deg, #5b21b6, #7c3aed);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(107, 70, 193, 0.3);
}

.lmstudio-btn {
  background: linear-gradient(135deg, #059669, #10b981);
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.lmstudio-btn:hover {
  background: linear-gradient(135deg, #047857, #059669);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
}

.add-btn {
  background: #4CAF50;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.add-btn:hover {
  background: #45a049;
  transform: translateY(-2px);
}

/* Communication between LLM styles */
.communication-section {
  padding: 20px;
}

.section-description {
  color: #b0b0b0;
  margin-bottom: 30px;
  font-style: italic;
}

.communication-controls {
  background: #2a2a2a;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.control-row {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
  align-items: end;
}

.control-row:last-child {
  margin-bottom: 0;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.control-group.full-width {
  flex: 1;
}

.control-group label {
  color: #e0e0e0;
  font-weight: 500;
  font-size: 14px;
}

.model-select, .iteration-input {
  background: #1a1a1a;
  border: 1px solid #444;
  color: #e0e0e0;
  padding: 8px 12px;
  border-radius: 4px;
  min-width: 200px;
}

.iteration-input {
  min-width: 100px;
  max-width: 120px;
}

.prompt-textarea {
  background: #1a1a1a;
  border: 1px solid #444;
  color: #e0e0e0;
  padding: 12px;
  border-radius: 4px;
  resize: vertical;
  font-family: inherit;
  width: 100%;
}

.start-communication-btn {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.start-communication-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #45a049, #3d8b40);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.start-communication-btn:disabled {
  background: #666;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.stop-communication-btn {
  background: linear-gradient(135deg, #f44336, #d32f2f);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.stop-communication-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #d32f2f, #b71c1c);
  transform: translateY(-2px);
}

.clear-session-btn {
  background: linear-gradient(135deg, #ff9800, #f57c00);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.clear-session-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #f57c00, #ef6c00);
  transform: translateY(-2px);
}

.session-status {
  background: #2a2a2a;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  border-left: 4px solid #4CAF50;
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.session-meta {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #b0b0b0;
}

.session-status.error {
  border-left-color: #f44336;
}

.session-status.stopped {
  border-left-color: #ff9800;
}

.error-message {
  background: #2a1a1a;
  border: 1px solid #f44336;
  border-radius: 4px;
  padding: 12px;
  margin: 10px 0;
  color: #ffcdd2;
  font-size: 14px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #1a1a1a;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #45a049);
  transition: width 0.3s ease;
}

.final-code {
  background: #2a2a2a;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  border-left: 4px solid #4CAF50;
}

.final-code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.code-meta {
  display: flex;
  gap: 15px;
  font-size: 14px;
  color: #b0b0b0;
  align-items: center;
}

.copy-code-btn {
  background: #4CAF50;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.copy-code-btn:hover {
  background: #45a049;
  transform: translateY(-1px);
}

.final-code-content {
  background: #1a1a1a;
  border-radius: 4px;
  overflow-x: auto;
}

.final-code-content pre {
  margin: 0;
  padding: 15px;
  color: #e0e0e0;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.final-code-content code {
  color: #e0e0e0;
  background: none;
}

.final-answer {
  background: #2a2a2a;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  border-left: 4px solid #FFD700;
}

.final-answer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.answer-meta {
  display: flex;
  gap: 15px;
  font-size: 14px;
  color: #b0b0b0;
}

.final-answer-content {
  color: #e0e0e0;
  line-height: 1.6;
  font-size: 16px;
}

.final-answer-content .text-content {
  white-space: pre-wrap;
  word-wrap: break-word;
}

.answer-note {
  background: #3a3a3a;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 15px;
  border-left: 3px solid #FFD700;
  font-size: 14px;
  color: #b0b0b0;
}

.copy-text-btn {
  background: #2196F3;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.copy-text-btn:hover {
  background: #1976D2;
  transform: translateY(-1px);
}

.final-validation {
  background: #2a2a2a;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  border-left: 4px solid #FF9800;
}

.final-validation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.validation-meta {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 14px;
  color: #b0b0b0;
}

.final-validation-content {
  color: #e0e0e0;
  line-height: 1.6;
  font-size: 16px;
}

.validation-note {
  background: #3a3a3a;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 15px;
  border-left: 3px solid #FF9800;
  font-size: 14px;
  color: #b0b0b0;
}

.validation-result {
  white-space: pre-wrap;
  word-wrap: break-word;
  font-size: 16px;
}

.adherence-excellent { color: #4CAF50; }
.adherence-good { color: #FFC107; }
.adherence-fair { color: #FF9800; }
.adherence-poor { color: #f44336; }

.ai0-activity {
  margin-bottom: 20px;
}

.ai0-panel {
  border-left: 4px solid #FFD700;
}

.triple-panel-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.dual-panel-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.ai-panel {
  background: #2a2a2a;
  border-radius: 8px;
  overflow: hidden;
}

.ai1-panel {
  border-left: 4px solid #2196F3;
}

.ai2-panel {
  border-left: 4px solid #9C27B0;
}

.panel-header {
  background: #1a1a1a;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  margin: 0;
  color: #e0e0e0;
}

.manual-input-btn {
  background: #666;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.manual-input-btn:hover:not(:disabled) {
  background: #777;
}

.manual-input-btn:disabled {
  background: #444;
  cursor: not-allowed;
}

.conversation-display {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}

.conversation-message {
  background: #1a1a1a;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 10px;
  border-left: 3px solid #666;
}

.conversation-message.response {
  border-left-color: #2196F3;
}

.conversation-message.analysis {
  border-left-color: #9C27B0;
}

.conversation-message.manual {
  border-left-color: #FF9800;
  background: #2a2a1a;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 12px;
  color: #b0b0b0;
}

.message-content {
  color: #e0e0e0;
  line-height: 1.5;
}

.prompt-section {
  margin-bottom: 12px;
}

.prompt-section:last-child {
  margin-bottom: 0;
}

.prompt-text, .response-text, .analysis-text {
  background: #1a1a1a;
  padding: 8px 12px;
  border-radius: 4px;
  margin-top: 4px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
  border-left: 3px solid #444;
}

.prompt-text {
  border-left-color: #2196F3;
}

.response-text {
  border-left-color: #4CAF50;
}

.analysis-text {
  border-left-color: #FF9800;
}

.empty-conversation {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 40px 20px;
}

.manual-input-modal {
  max-width: 600px;
  width: 90%;
}

.manual-input-textarea {
  width: 100%;
  background: #1a1a1a;
  border: 1px solid #444;
  color: #e0e0e0;
  padding: 12px;
  border-radius: 4px;
  resize: vertical;
  font-family: inherit;
}

@media (max-width: 768px) {
  .triple-panel-layout,
  .dual-panel-layout {
    grid-template-columns: 1fr;
  }

  .control-row {
    flex-direction: column;
    align-items: stretch;
  }

  .session-meta {
    flex-direction: column;
    gap: 5px;
  }

  .model-select {
    min-width: auto;
    width: 100%;
  }
}
</style>
