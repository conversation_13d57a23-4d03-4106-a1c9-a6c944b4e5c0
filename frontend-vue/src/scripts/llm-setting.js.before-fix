import { ref, reactive, computed, onMounted } from 'vue';
import { llmDbService } from '../services/llm_db.service';
import LlmChatTest from '../components/LlmChatTest.vue';

export default {
  name: 'LlmSetting',
  
  components: {
    LlmChatTest
  },
  
  setup() {
    // Reactive state
    const isLoading = ref(false);
    const errorMessage = ref(null);
    const errorDetails = ref(null);
    const successMessage = ref(null);
    const providers = ref([]);
    const selectedProviderId = ref(null);
    const selectedProvider = ref(null);
    const checkingConnection = ref(false);
    const testingConnection = ref(false);
    const connectionResult = ref(null);
    const providersForChat = ref(null);
    
    // Stav pro modální okna a přidávání poskytovatelů
    const showNewProviderModal = ref(false);
    const showApiKey = ref(false);
    const showModelsSection = ref(true);
    
    // Nový poskytovatel
    const newProvider = reactive({
      id: null,
      name: '',
      api_key: '',
      base_url: '',
      model: '',
      timeout: 60,
      retry_count: 3,
      is_default: false,
      models: {}
    });
    
    // Model
    const showModelModal = ref(false);
    const editingModelName = ref(null);
    const modelForm = reactive({
      name: '',
      context_window: 32000,
      max_tokens: 4096,
      capabilities: ['text', 'code']
    });
    
    // Přidané proměnné pro modální okno existujícího poskytovatele
    const showExistingModelModal = ref(false);
    const editingExistingModelName = ref(null);
    const existingModelForm = reactive({
      name: '',
      context_window: 32000,
      max_tokens: 4096,
      capabilities: ['text', 'code']
    });
    
    // Seznam dostupných schopností modelu
    const availableCapabilities = [
      'text', 'images', 'code', 'reasoning', 'planning', 'search',
      'math', 'embeddings', 'function_calling', 'vision'
    ];
    
    // Computed properties
    const dbConnected = computed(() => {
      const status = llmDbService.getDbConnectionStatus();
      return status.connected;
    });
    
    const dbConnectionMessage = computed(() => {
      const status = llmDbService.getDbConnectionStatus();
      return status.message;
    });
    
    const venvStatus = computed(() => {
      const status = llmDbService.getDbConnectionStatus();
      return status.venvActive;
    });
    
    // Základní metody
    const clearMessages = () => {
      errorMessage.value = null;
      errorDetails.value = null;
      successMessage.value = null;
    };
    
    const showSuccess = (message) => {
      successMessage.value = message;
      setTimeout(() => { successMessage.value = null; }, 5000);
    };
    
    const showError = (message, details = null) => {
      errorMessage.value = message;
      errorDetails.value = details;
    };

    // Implementace metod pro práci s DB
    const checkDbConnection = async () => {
      clearMessages();
      checkingConnection.value = true;
      try {
        console.log('Kontrola připojení k databázi a stavu virtuálního prostředí...');
        
        // Přímé volání checkDbConnection z llm_db.service.js
        const result = await llmDbService.checkDbConnection();
        
        // Zobrazíme výsledek kontroly s ohledem na stav virtuálního prostředí
        if (result.connected) {
          showSuccess('Připojení k databázi je aktivní.');
          
          // Pokud i přes úspěšné připojení není aktivní virtuální prostředí, zobrazíme varování
          if (result.venvActive === false) {
            console.warn('Připojení k DB je OK, ale virtuální prostředí není aktivní');
            showError(
              'Virtuální prostředí není aktivní!', 
              'I když je připojení k databázi aktivní, virtuální prostředí není aktivováno. ' +
              'Pro plnou funkčnost aplikace je doporučeno aktivovat virtuální prostředí: ' +
              'source /opt/gent/venv/bin/activate'
            );
          }
        } else {
          // Přizpůsobíme chybovou zprávu podle stavu virtuálního prostředí
          if (result.venvActive === false) {
            console.error('Připojení k DB selhalo - virtuální prostředí není aktivní');
            showError(
              'Virtuální prostředí není aktivní!', 
              'Pro správné fungování DB je nutné aktivovat virtuální prostředí. Spusťte ' +
              'příkaz "source /opt/gent/venv/bin/activate" v terminálu a zkuste to znovu.'
            );
          } else {
            console.error('Připojení k DB selhalo - virtuální prostředí je OK');
            showError('Připojení k databázi selhalo.', result.error || 'Zkontrolujte nastavení databáze a zda služba běží.');
          }
        }
        
        console.log('Výsledek kontroly DB:', result);
        return result;
      } catch (error) {
        console.error('Neočekávaná chyba při kontrole DB připojení:', error);
        
        // Zkusíme zjistit, zda jde o chybu virtuálního prostředí
        if (error.message && (
          error.message.includes('venv') || 
          error.message.includes('virtual') || 
          error.message.includes('prostředí')
        )) {
          showError(
            'Virtuální prostředí není aktivní!', 
            `${error.message} Spusťte příkaz "source /opt/gent/venv/bin/activate" v terminálu a zkuste to znovu.`
          );
        } else {
          showError('Nepodařilo se zkontrolovat připojení k DB.', error.message);
        }
        
        return {
          connected: false,
          venvActive: false,
          error: error.message
        };
      } finally {
        checkingConnection.value = false;
      }
    };
    
    const loadLlmProviders = async () => {
      clearMessages();
      isLoading.value = true;
      try {
        console.time('loadLlmProviders');
        console.log('Načítání poskytovatelů z DB...');
        
        // Nejprve zkontrolujeme stav DB připojení
        const connectionStatus = llmDbService.getDbConnectionStatus();
        // Pokud nemáme informaci o stavu připojení nebo je připojení neaktivní, zkusíme ho obnovit
        if (!connectionStatus.connected || !connectionStatus.lastChecked) {
          console.log('DB připojení není aktivní nebo nebylo zkontrolováno, zkouším zkontrolovat...');
          
          // Zkusíme explicitně zkontrolovat připojení
          const connResult = await checkDbConnection();
          
          // Pokud se ani teď nepodařilo připojit, zobrazíme chybu a přerušíme načítání
          if (!connResult || !connResult.connected) {
            console.error('Nepodařilo se připojit k DB, načítání poskytovatelů přerušeno');
            
            // Zobrazíme speciální chybu pro virtuální prostředí, pokud víme, že není aktivní
            if (connResult && connResult.venvActive === false) {
              showError(
                'Virtuální prostředí není aktivní!', 
                `Pro správné fungování DB je nutné aktivovat virtuální prostředí. Spusťte příkaz "source /opt/gent/venv/bin/activate" v terminálu a zkuste to znovu.`
              );
            } else {
              showError(
                'Nepodařilo se připojit k databázi.', 
                connectionStatus.error || 'Zkontrolujte nastavení databáze a zda je služba spuštěna.'
              );
            }
            
            providers.value = [];
            return;
          }
        }
        
        // Nyní načteme poskytovatele
        const providersResponse = await llmDbService.getProviders();
        
        // Kontrola, zda odpověď obsahuje chybu
        if (providersResponse.error) {
          showError(providersResponse.error.message, providersResponse.error.details);
          providers.value = [];
          return;
        }
        
        providers.value = providersResponse.data || [];
        console.log('Úspěšně načteno', providers.value.length, 'poskytovatelů z DB');
        console.timeEnd('loadLlmProviders');
        
        // Připravíme data pro LlmChatTest komponentu
        providersForChat.value = prepareProvidersForChat();
        
        // Pokud máme poskytovatele, vybereme první z nich
        if (providers.value.length > 0) {
          const defaultProvider = providers.value.find(p => p.is_default) || providers.value[0];
          selectedProviderId.value = defaultProvider.id;
          await loadProviderDetail(defaultProvider.id);
        } else {
          console.warn('Žádní poskytovatelé v DB nebyli nalezeni');
        }
      } catch (error) {
        console.error('Neočekávaná chyba při načítání poskytovatelů LLM:', error);
        showError('Nepodařilo se načíst poskytovatele LLM. Neočekávaná chyba.', error.message);
      } finally {
        isLoading.value = false;
      }
    };
    
    const loadProviderDetail = async (providerId) => {
      if (!providerId) return;
      
      clearMessages();
      isLoading.value = true;
      
      try {
        console.time('loadProviderDetail');
        const response = await llmDbService.getProviderDetail(providerId);
        
        // Kontrola, zda odpověď obsahuje chybu
        if (response.error) {
          showError(response.error.message, response.error.details);
          selectedProvider.value = null;
          return;
        }
        
        selectedProvider.value = response.data;
        console.timeEnd('loadProviderDetail');
      } catch (error) {
        console.error('Chyba při načítání detailu poskytovatele:', error);
        showError('Nepodařilo se načíst detail poskytovatele.', error.message);
        selectedProvider.value = null;
      } finally {
        isLoading.value = false;
      }
    };
    
    const handleProviderSelect = async () => {
      if (selectedProviderId.value) {
        await loadProviderDetail(selectedProviderId.value);
      } else {
        selectedProvider.value = null;
      }
    };
    
    const prepareProvidersForChat = () => {
      if (!providers.value || providers.value.length === 0) {
        return null;
      }
      
      // Vytvoříme zjednodušenou verzi poskytovatelů pro chat komponentu
      return providers.value.map(provider => ({
        id: provider.id,
        name: provider.name,
        model: provider.model,
        isDefault: provider.is_default,
        apiKey: provider.api_key,
        baseUrl: provider.base_url,
        models: provider.models || {}
      }));
    };
    
    const testApiConnection = async () => {
      if (!selectedProvider.value) {
        showError('Není vybrán žádný poskytovatel pro testování.');
        return;
      }
      
      testingConnection.value = true;
      clearMessages();
      
      try {
        const response = await llmDbService.testProviderConnection(selectedProvider.value);
        
        // Kontrola, zda odpověď obsahuje chybu
        if (response.error) {
          connectionResult.value = {
            success: false,
            message: response.error.message
          };
          return;
        }
        
        connectionResult.value = {
          success: response.data.success,
          message: response.data.message
        };
      } catch (error) {
        console.error('Chyba při testování API připojení:', error);
        connectionResult.value = {
          success: false,
          message: `Chyba při testování připojení: ${error.message}`
        };
      } finally {
        testingConnection.value = false;
      }
    };
    
    const saveProvider = async () => {
      if (!selectedProvider.value) {
        showError('Není vybrán žádný poskytovatel k uložení.');
        return;
      }
      
      clearMessages();
      isLoading.value = true;
      
      try {
        const response = await llmDbService.saveProvider(selectedProvider.value);
        
        // Kontrola, zda odpověď obsahuje chybu
        if (response.error) {
          showError(response.error.message, response.error.details);
          return;
        }
        
        showSuccess('Poskytovatel byl úspěšně uložen.');
        
        // Aktualizujeme seznam poskytovatelů
        await loadLlmProviders();
      } catch (error) {
        console.error('Chyba při ukládání poskytovatele:', error);
        showError('Nepodařilo se uložit poskytovatele.', error.message);
      } finally {
        isLoading.value = false;
      }
    };
    
    const resetProvider = async () => {
      if (!selectedProviderId.value) {
        showError('Není vybrán žádný poskytovatel pro reset.');
        return;
      }
      
      // Znovu načteme detail z DB, čímž resetujeme změny
      await loadProviderDetail(selectedProviderId.value);
      showSuccess('Změny byly resetovány.');
    };
    
    // Metody pro správu modálních oken a modelů
    const openNewProviderModal = () => {
      // Reset formuláře nového poskytovatele
      Object.assign(newProvider, {
        id: null,
        name: '',
        api_key: '',
        base_url: '',
        model: '',
        timeout: 60,
        retry_count: 3,
        is_default: false,
        models: {}
      });
      showNewProviderModal.value = true;
    };
    
    const closeNewProviderModal = () => {
      showNewProviderModal.value = false;
    };
    
    const createNewProvider = async () => {
      // Validace
      if (!newProvider.name.trim()) {
        showError('Je nutné zadat název poskytovatele.');
        return;
      }
      
      if (!newProvider.base_url.trim()) {
        showError('Je nutné zadat Base URL adresu.');
        return;
      }
      
      clearMessages();
      isLoading.value = true;
      
      try {
        const response = await llmDbService.saveProvider(newProvider);
        
        // Kontrola, zda odpověď obsahuje chybu
        if (response.error) {
          showError(response.error.message, response.error.details);
          return;
        }
        
        showSuccess('Nový poskytovatel byl úspěšně vytvořen.');
        closeNewProviderModal();
        
        // Aktualizujeme seznam poskytovatelů
        await loadLlmProviders();
      } catch (error) {
        console.error('Chyba při vytváření poskytovatele:', error);
        showError('Nepodařilo se vytvořit poskytovatele.', error.message);
      } finally {
        isLoading.value = false;
      }
    };
    
    // Metody pro správu modelů existujícího poskytovatele
    const openExistingModelModal = (modelName = null) => {
      console.log("DEBUG: openExistingModelModal - Otevírám modal pro model:", modelName);
      
      if (!selectedProvider.value) {
        showError("Není vybrán žádný poskytovatel.");
        return;
      }
      
      if (modelName) {
        // Editace existujícího modelu
        editingExistingModelName.value = modelName;

        /* Bezpečná kontrola, zda models existuje */
        if (!selectedProvider.value.models) {
          selectedProvider.value.models = {};
          console.log("DEBUG: Inicializuji prázdný objekt modelů");
        }
        
        const model = selectedProvider.value.models[modelName];
        
        // Naplnění formuláře hodnotami modelu
        existingModelForm.name = modelName;
        existingModelForm.context_window = model.context_window || 32000;
        existingModelForm.max_tokens = model.max_tokens || 4096;
        existingModelForm.capabilities = [...(model.capabilities || ['text', 'code'])];
      } else {
        // Přidání nového modelu
        editingExistingModelName.value = null;
        existingModelForm.name = '';
        existingModelForm.context_window = 32000;
        existingModelForm.max_tokens = 4096;
        existingModelForm.capabilities = ['text', 'code'];
      }
      
      showExistingModelModal.value = true;
    };
    
    const closeExistingModelModal = () => {
      showExistingModelModal.value = false;
      editingExistingModelName.value = null;
    };
    
    const saveExistingModel = async () => {
      console.log("DEBUG: saveExistingModel - Začínám ukládat model");
      
      if (!selectedProvider.value) {
        showError('Není vybrán žádný poskytovatel.');
        return;
      }
      
      // Validace
      if (!existingModelForm.name.trim()) {
        showError('Je nutné zadat název modelu.');
        return;
      }
      
      if (existingModelForm.context_window <= 0) {
        showError('Velikost kontextového okna musí být kladné číslo.');
        return;
      }
      
      if (existingModelForm.max_tokens <= 0) {
        showError('Maximální počet tokenů musí být kladné číslo.');
        return;
      }
      
      // Zajistíme, že poskytovatel má definovaný objekt modelů
      if (!selectedProvider.value.models) {
        selectedProvider.value.models = {};
      }
      
      // Přidání nebo aktualizace modelu
      const modelName = editingExistingModelName.value || existingModelForm.name;

      console.log("DEBUG: saveExistingModel - Pracuji s modelem:", modelName);
      
      selectedProvider.value.models[modelName] = {
        context_window: existingModelForm.context_window,
        max_tokens: existingModelForm.max_tokens,
        capabilities: [...existingModelForm.capabilities]
      };
      
      // Pokud byl přidán nový model a poskytovatel nemá výchozí, nastavíme tento
      if (!selectedProvider.value.model || selectedProvider.value.model === '') {
        selectedProvider.value.model = modelName;
      }
      
      closeExistingModelModal();
      
      // Ukládáme změny do databáze
      isLoading.value = true;
      try {
        console.log('Ukládání modelu do databáze:', modelName);
        const response = await llmDbService.saveProvider(selectedProvider.value);
        
        if (response.error) {
          showError(response.error.message, response.error.details);
          return;
        }
        
        showSuccess('Model byl úspěšně uložen.');
        
        // Aktualizujeme seznam poskytovatelů a přenačteme data
        await loadLlmProviders();
      } catch (error) {
        console.error('Chyba při ukládání modelu:', error);
        showError('Nepodařilo se uložit model.', error.message);
      } finally {
        isLoading.value = false;
      }
    };
    
    const editExistingModel = (modelName) => {
      openExistingModelModal(modelName);
    };
    
    const deleteExistingModel = async (modelName) => {
      console.log("DEBUG: deleteExistingModel - Mažu model:", modelName);
      
      if (!selectedProvider.value) {
        showError("Není vybrán žádný poskytovatel.");
        return;
      }
      
      if (!selectedProvider.value.models) {
        showError("Poskytovatel nemá žádné modely k odstranění.");
        return;
      }
      
      // Potvrzení před smazáním
      if (!confirm(`Opravdu chcete smazat model "${modelName}"?`)) {
        return;
      }
      
      isLoading.value = true;
      try {
        // Přímé smazání modelu přes API
        const providerId = selectedProvider.value.id;
        console.log(`DEBUG: Mažu model "${modelName}" poskytovatele ${providerId}`);
        
        // Odstranění modelu z objektu modelů
        delete selectedProvider.value.models[modelName];
        console.log("DEBUG: Model odstraněn z objektu selectedProvider.value.models");
        
        // Pokud byl tento model výchozí, resetujeme výchozí model
        if (selectedProvider.value.model === modelName) {
          const remainingModels = Object.keys(selectedProvider.value.models);
          selectedProvider.value.model = remainingModels.length > 0 ? remainingModels[0] : "";
          console.log(`DEBUG: Model "${modelName}" byl výchozí, nově nastaven "${selectedProvider.value.model}"`);
        }
        
        // Uložení poskytovatele s odstraněným modelem
        const response = await llmDbService.saveProvider(selectedProvider.value);
        
        if (response.error) {
          console.error("DEBUG: Chyba při ukládání poskytovatele po smazání modelu:", response.error);
          showError(response.error.message, response.error.details);
          // Pokud se nepodařilo smazat model, vrátíme ho zpět
          await loadProviderDetail(providerId);
          return;
        }
        
        showSuccess(`Model "${modelName}" byl odstraněn.`);
        
        // Aktualizujeme seznam poskytovatelů a přenačteme data
        await loadLlmProviders();
      } catch (error) {
        console.error(`DEBUG: Chyba při mazání modelu "${modelName}":`, error, error.stack);
        showError(`Nepodařilo se smazat model "${modelName}".`, error.message);
        // Znovu načteme poskytovatele, aby se data vrátila do původního stavu
        await loadProviderDetail(selectedProvider.value.id);
      } finally {
        isLoading.value = false;
      }
    };
    
    // Metody pro modální okno přidání modelu k novému poskytovateli
    const openModelModal = (modelName = null) => {
      if (modelName) {
        // Editace existujícího modelu
        editingModelName.value = modelName;
        const model = newProvider.models[modelName];
        
        // Naplnění formuláře hodnotami modelu
        modelForm.name = modelName;
        modelForm.context_window = model.context_window || 32000;
        modelForm.max_tokens = model.max_tokens || 4096;
        modelForm.capabilities = [...(model.capabilities || ['text', 'code'])];
      } else {
        // Přidání nového modelu
        editingModelName.value = null;
        modelForm.name = '';
        modelForm.context_window = 32000;
        modelForm.max_tokens = 4096;
        modelForm.capabilities = ['text', 'code'];
      }
      
      showModelModal.value = true;
    };
    
    const closeModelModal = () => {
      showModelModal.value = false;
      editingModelName.value = null;
    };
    
    const saveModel = async () => {
      // Validace
      if (!modelForm.name.trim()) {
        showError('Je nutné zadat název modelu.');
        return;
      }
      
      if (modelForm.context_window <= 0) {
        showError('Velikost kontextového okna musí být kladné číslo.');
        return;
      }
      
      if (modelForm.max_tokens <= 0) {
        showError('Maximální počet tokenů musí být kladné číslo.');
        return;
      }
      
      // Zajistíme, že poskytovatel má definovaný objekt modelů
      if (!newProvider.models) {
        newProvider.models = {};
      }
      
      // Přidání nebo aktualizace modelu
      const modelName = editingModelName.value || modelForm.name;
      
      newProvider.models[modelName] = {
        context_window: modelForm.context_window,
        max_tokens: modelForm.max_tokens,
        capabilities: [...modelForm.capabilities]
      };
      
      // Pokud byl přidán nový model a poskytovatel nemá výchozí, nastavíme tento
      if (!newProvider.model || newProvider.model === '') {
        newProvider.model = modelName;
      }
      
      closeModelModal();
      showSuccess('Model byl úspěšně přidán k novému poskytovateli.');
      
      // Poznámka: U nového poskytovatele neukládáme do DB hned - uloží se až při vytvoření poskytovatele
    };
    
    const editModel = (modelName) => {
      openModelModal(modelName);
    };
    
    const deleteModel = async (modelName) => {
      if (!newProvider.models) {
        return;
      }
      
      // Potvrzení před smazáním
      if (!confirm(`Opravdu chcete smazat model "${modelName}"?`)) {
        return;
      }
      
      // Odstranění modelu z objektu modelů
      delete newProvider.models[modelName];
      
      // Pokud byl tento model výchozí, resetujeme výchozí model
      if (newProvider.model === modelName) {
        const remainingModels = Object.keys(newProvider.models);
        newProvider.model = remainingModels.length > 0 ? remainingModels[0] : '';
      }
      
      showSuccess(`Model "${modelName}" byl odstraněn.`);
      
      // Poznámka: U nového poskytovatele neukládáme do DB hned - uloží se až při vytvoření poskytovatele
    };
    
    onMounted(async () => {
      // Při načtení komponenty aktivujeme virtuální prostředí a zkontrolujeme připojení k DB
      isLoading.value = true;
      
      try {
        console.log('Iniciace komponenty LlmSetting, kontrola připojení k DB...');
        // Nejprve zkontrolujeme připojení k DB
        const connectionResult = await checkDbConnection();
        
        if (connectionResult && connectionResult.connected) {
          console.log('DB připojení OK, načítám poskytovatele...');
          await loadLlmProviders();
        } else {
          // Pokud připojení selže, zobrazíme chybu a nabídneme možnost aktivovat virtuální prostředí
          errorMessage.value = connectionResult.message || 'Nepodařilo se připojit k databázi';
          errorDetails.value = connectionResult.error || 'Zkontrolujte, zda je aktivní virtuální prostředí';
          
          // Pokud není aktivní virtuální prostředí, zobrazíme výrazné upozornění
          if (!connectionResult.venvActive) {
            errorDetails.value = `Virtuální prostředí není aktivní! Spusťte příkaz "source /opt/gent/venv/bin/activate" v terminálu a zkuste to znovu.`;
          }
        }
      } catch (error) {
        console.error('
