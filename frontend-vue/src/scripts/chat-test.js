/**
 * Skript pro stránku CHAT-TEST
 * Obsahuje business logiku pro výběr AI poskytovatelů, modelů a chatování
 */

import { ref, reactive, computed, onMounted, watch } from 'vue';
import axios from 'axios';

export default function useChatTest() {
  // Stav poskytovatele a modelu
  const providers = ref([]);
  // Inicializace s prázdným objektem místo null
  const selectedProvider = ref({ id: '', provider_name: '' });
  const models = ref([]);
  const selectedModel = ref({ id: '', model_name: '' });
  const isLoadingProviders = ref(false);
  const isLoadingModels = ref(false);

  // Stav chatu
  const chatMessages = ref([]);
  const newMessage = ref('');
  const isProcessing = ref(false);
  const error = ref(null);

  // Ukázková data pro poskytovatele
  const mockProviders = [
    { id: 1, provider_name: 'OpenAI', api_base_url: 'https://api.openai.com/v1', is_active: true },
    { id: 2, provider_name: 'Anthropic', api_base_url: 'https://api.anthropic.com', is_active: true },
    { id: 3, provider_name: 'Google', api_base_url: 'https://generativelanguage.googleapis.com', is_active: true }
  ];

  // Ukázková data pro modely
  const mockModels = {
    1: [ // OpenAI modely
      { id: 101, provider_id: 1, model_name: 'GPT-4 Turbo', model_identifier: 'gpt-4-1106-preview', context_length: 128000 },
      { id: 102, provider_id: 1, model_name: 'GPT-4', model_identifier: 'gpt-4', context_length: 8192 },
      { id: 103, provider_id: 1, model_name: 'GPT-3.5 Turbo', model_identifier: 'gpt-3.5-turbo', context_length: 16384 }
    ],
    2: [ // Anthropic modely
      { id: 201, provider_id: 2, model_name: 'Claude 3 Opus', model_identifier: 'claude-3-opus-20240229', context_length: 200000 },
      { id: 202, provider_id: 2, model_name: 'Claude 3 Sonnet', model_identifier: 'claude-3-sonnet-20240229', context_length: 180000 },
      { id: 203, provider_id: 2, model_name: 'Claude 3 Haiku', model_identifier: 'claude-3-haiku-20240307', context_length: 150000 }
    ],
    3: [ // Google modely
      { id: 301, provider_id: 3, model_name: 'Gemini Pro', model_identifier: 'gemini-pro', context_length: 30720 },
      { id: 302, provider_id: 3, model_name: 'Gemini Ultra', model_identifier: 'gemini-ultra', context_length: 32768 }
    ]
  };

  // Získání seznamu poskytovatelů z API
  const fetchProviders = async () => {
    isLoadingProviders.value = true;
    error.value = null;
    
    try {
      // Simulujeme prodlevu sítě
      await new Promise(resolve => setTimeout(resolve, 800));
      
      // Použijeme ukázková data místo volání API
      providers.value = mockProviders;
      
      // Pokud máme poskytovatele, vybereme první automaticky
      if (providers.value.length > 0) {
        selectedProvider.value = providers.value[0];
        fetchModels(selectedProvider.value.id);
      }
    } catch (err) {
      console.error('Chyba při načítání poskytovatelů:', err);
      error.value = 'Nepodařilo se načíst seznam poskytovatelů. Zkuste to prosím později.';
    } finally {
      isLoadingProviders.value = false;
    }
  };

  // Získání modelů zvoleného poskytovatele
  const fetchModels = async (providerId) => {
    if (!providerId) return;
    
    isLoadingModels.value = true;
    error.value = null;
    // Resetujeme na výchozí prázdný objekt místo null
    selectedModel.value = { id: '', model_name: '' };
    
    try {
      // Simulujeme prodlevu sítě
      await new Promise(resolve => setTimeout(resolve, 600));
      
      // Použijeme ukázková data místo volání API
      models.value = mockModels[providerId] || [];
      
      // Pokud máme modely, vybereme první automaticky
      if (models.value.length > 0) {
        selectedModel.value = models.value[0];
      }
    } catch (err) {
      console.error('Chyba při načítání modelů:', err);
      error.value = 'Nepodařilo se načíst seznam modelů. Zkuste to prosím později.';
    } finally {
      isLoadingModels.value = false;
    }
  };

  // Změna vybraného poskytovatele
  const changeProvider = (providerId) => {
    const provider = providers.value.find(p => p.id == providerId);
    if (provider) {
      selectedProvider.value = provider;
      fetchModels(provider.id);
    }
  };

  // Změna vybraného modelu
  const changeModel = (modelId) => {
    const model = models.value.find(m => m.id == modelId);
    if (model) {
      selectedModel.value = model;
    }
  };

  // Funkce pro odeslání zprávy
  const sendMessage = async () => {
    if (!newMessage.value.trim() || !selectedModel.value.id || isProcessing.value) {
      return;
    }
    
    const userMessage = {
      role: 'user',
      content: newMessage.value,
      timestamp: new Date().toISOString()
    };
    
    // Přidáme zprávu uživatele do chatu
    chatMessages.value.push(userMessage);
    
    // Resetujeme vstupní pole
    const messageToSend = newMessage.value;
    newMessage.value = '';
    
    isProcessing.value = true;
    error.value = null;
    
    try {
      // Tady by bylo skutečné API volání na LLM
      // Pro ukázku simulujeme odpověď s prodlevou
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const assistantMessage = {
        role: 'assistant',
        content: `Odpověď od modelu ${selectedModel.value.model_name} na zprávu: "${messageToSend}"`,
        timestamp: new Date().toISOString()
      };
      
      chatMessages.value.push(assistantMessage);
    } catch (err) {
      console.error('Chyba při komunikaci s LLM:', err);
      error.value = 'Nepodařilo se získat odpověď od LLM modelu. Zkuste to prosím později.';
    } finally {
      isProcessing.value = false;
    }
  };

  // Vyčištění chatu
  const clearChat = () => {
    chatMessages.value = [];
  };

  // Rychlý test
  const quickTest = () => {
    newMessage.value = "Ahoj, proveď rychlý test a popiš své schopnosti.";
    sendMessage();
  };

  // Nastavení sledování změn poskytovatele
  watch(selectedProvider, (newProvider) => {
    if (newProvider && newProvider.id) {
      fetchModels(newProvider.id);
    } else {
      models.value = [];
      selectedModel.value = { id: '', model_name: '' };
    }
  });

  // Načtení dat při inicializaci
  onMounted(() => {
    fetchProviders();
  });

  return {
    // Stav
    providers,
    selectedProvider,
    models,
    selectedModel,
    chatMessages,
    newMessage,
    isLoadingProviders,
    isLoadingModels,
    isProcessing,
    error,
    
    // Metody
    fetchProviders,
    fetchModels,
    changeProvider,
    changeModel,
    sendMessage,
    clearChat,
    quickTest
  };
}
