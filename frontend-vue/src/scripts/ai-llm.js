import { ref, reactive, computed, onMounted } from 'vue';
import { llmDbService } from '../services/llm_db.service';

export default {
  name: 'AiLlm',
  
  setup() {
    // Reaktivní stav
    const isLoading = ref(false);
    const errorMessage = ref(null);
    const successMessage = ref(null);
    const providers = ref([]);
    const selectedProviderId = ref(null);
    const selectedProvider = ref(null);
    
    // Stav pro formuláře
    const showAddProviderForm = ref(false);
    const showEditProviderForm = ref(false);
    const showAddModelForm = ref(false);
    const showEditModelForm = ref(false);
    
    // Nový poskytovatel
    const newProvider = reactive({
      name: '',
      api_key: '',
      base_url: '',
      model: '',
      timeout: 60,
      retry_count: 3,
      is_default: false,
      models: {}
    });
    
    // Editovaný poskytovatel
    const editedProvider = reactive({
      id: null,
      name: '',
      api_key: '',
      base_url: '',
      model: '',
      timeout: 60,
      retry_count: 3,
      is_default: false,
      models: {}
    });
    
    // Nový model
    const newModel = reactive({
      name: '',
      context_window: 32000,
      max_tokens: 4096,
      capabilities: ['text', 'code']
    });
    
    // Editovaný model
    const editedModel = reactive({
      original_name: '',
      name: '',
      context_window: 32000,
      max_tokens: 4096,
      capabilities: ['text', 'code']
    });
    
    // Seznam dostupných schopností modelu
    const availableCapabilities = [
      'text', 'images', 'code', 'reasoning', 'planning', 'search',
      'math', 'embeddings', 'function_calling', 'vision'
    ];
    
    // Základní metody
    const clearMessages = () => {
      errorMessage.value = null;
      successMessage.value = null;
    };
    
    const showSuccess = (message) => {
      successMessage.value = message;
      setTimeout(() => { successMessage.value = null; }, 5000);
    };
    
    const showError = (message) => {
      errorMessage.value = message;
    };
    
    // Implementace metod pro práci s DB
    const loadProviders = async () => {
      clearMessages();
      isLoading.value = true;
      try {
        console.log('Načítání poskytovatelů LLM...');
        const response = await llmDbService.getProviders();
        
        if (response.error) {
          showError(response.error.message);
          providers.value = [];
          return;
        }
        
        providers.value = response.data || [];
        console.log('Úspěšně načteno', providers.value.length, 'poskytovatelů');
        
        // Pokud máme poskytovatele a žádný není vybrán, vybereme první
        if (providers.value.length > 0 && !selectedProviderId.value) {
          selectedProviderId.value = providers.value[0].id;
          await loadProviderDetail(selectedProviderId.value);
        }
      } catch (error) {
        console.error('Chyba při načítání poskytovatelů LLM:', error);
        showError('Nepodařilo se načíst poskytovatele. ' + error.message);
        providers.value = [];
      } finally {
        isLoading.value = false;
      }
    };
    
    const loadProviderDetail = async (providerId) => {
      if (!providerId) return;
      
      clearMessages();
      isLoading.value = true;
      
      try {
        const response = await llmDbService.getProviderDetail(providerId);
        
        if (response.error) {
          showError(response.error.message);
          selectedProvider.value = null;
          return;
        }
        
        selectedProvider.value = response.data;
        console.log('Načten detail poskytovatele:', selectedProvider.value.name);
      } catch (error) {
        console.error('Chyba při načítání detailu poskytovatele:', error);
        showError('Nepodařilo se načíst detail poskytovatele. ' + error.message);
        selectedProvider.value = null;
      } finally {
        isLoading.value = false;
      }
    };
    
    const handleProviderSelect = async () => {
      if (selectedProviderId.value) {
        await loadProviderDetail(selectedProviderId.value);
      } else {
        selectedProvider.value = null;
      }
    };
    
    // Formuláře poskytovatelů
    const openAddProviderForm = () => {
      // Reset formuláře
      Object.assign(newProvider, {
        name: '',
        api_key: '',
        base_url: '',
        model: '',
        timeout: 60,
        retry_count: 3,
        is_default: false,
        models: {}
      });
      showAddProviderForm.value = true;
    };
    
    const closeAddProviderForm = () => {
      showAddProviderForm.value = false;
    };
    
    const openEditProviderForm = () => {
      if (!selectedProvider.value) {
        showError('Není vybrán žádný poskytovatel.');
        return;
      }
      
      // Kopírování hodnot do formuláře
      Object.assign(editedProvider, {
        id: selectedProvider.value.id,
        name: selectedProvider.value.name,
        api_key: selectedProvider.value.api_key,
        base_url: selectedProvider.value.base_url,
        model: selectedProvider.value.model,
        timeout: selectedProvider.value.timeout,
        retry_count: selectedProvider.value.retry_count,
        is_default: selectedProvider.value.is_default,
        models: JSON.parse(JSON.stringify(selectedProvider.value.models)) // Hluboká kopie
      });
      
      showEditProviderForm.value = true;
    };
    
    const closeEditProviderForm = () => {
      showEditProviderForm.value = false;
    };
    
    // Formuláře modelů
    const openAddModelForm = () => {
      if (!selectedProvider.value) {
        showError('Není vybrán žádný poskytovatel.');
        return;
      }
      
      // Reset formuláře
      Object.assign(newModel, {
        name: '',
        context_window: 32000,
        max_tokens: 4096,
        capabilities: ['text', 'code']
      });
      
      showAddModelForm.value = true;
    };
    
    const closeAddModelForm = () => {
      showAddModelForm.value = false;
    };
    
    const openEditModelForm = (modelName) => {
      if (!selectedProvider.value || !selectedProvider.value.models[modelName]) {
        showError('Vybraný model neexistuje.');
        return;
      }
      
      const model = selectedProvider.value.models[modelName];
      console.log('Otevírám formulář pro editaci modelu:', modelName);
      console.log('Data modelu k editaci:', model);
      
      // Kopírování hodnot do formuláře
      Object.assign(editedModel, {
        original_name: modelName,
        name: modelName,
        context_window: model.context_length || 32000,  // Opraveno: context_length místo context_window
        max_tokens: model.max_tokens || 4096,
        capabilities: Object.keys(model.capabilities || { text: true, code: true }) // Převedení objektu capabilities na pole
      });
      
      console.log('Upravená data pro formulář:', editedModel);
      console.log('Nastavuji showEditModelForm na true');
      
      // Důležité: Nastavení visibility formuláře
      showEditModelForm.value = true;
    };
    
    const closeEditModelForm = () => {
      showEditModelForm.value = false;
    };
    
    // CRUD operace
    const createProvider = async () => {
      // Validace
      if (!newProvider.name.trim()) {
        showError('Je nutné zadat název poskytovatele.');
        return;
      }
      
      clearMessages();
      isLoading.value = true;
      
      try {
        const response = await llmDbService.saveProvider(newProvider);
        
        if (response.error) {
          showError(response.error.message);
          return;
        }
        
        showSuccess('Nový poskytovatel byl úspěšně vytvořen.');
        closeAddProviderForm();
        
        // Aktualizujeme seznam poskytovatelů
        await loadProviders();
      } catch (error) {
        console.error('Chyba při vytváření poskytovatele:', error);
        showError('Nepodařilo se vytvořit poskytovatele. ' + error.message);
      } finally {
        isLoading.value = false;
      }
    };
    
    const updateProvider = async () => {
      // Validace
      if (!editedProvider.name.trim()) {
        showError('Je nutné zadat název poskytovatele.');
        return;
      }
      
      clearMessages();
      isLoading.value = true;
      
      try {
        const response = await llmDbService.saveProvider(editedProvider);
        
        if (response.error) {
          showError(response.error.message);
          return;
        }
        
        showSuccess('Poskytovatel byl úspěšně aktualizován.');
        closeEditProviderForm();
        
        // Aktualizujeme seznam poskytovatelů a vybraného poskytovatele
        await loadProviders();
        await loadProviderDetail(editedProvider.id);
      } catch (error) {
        console.error('Chyba při aktualizaci poskytovatele:', error);
        showError('Nepodařilo se aktualizovat poskytovatele. ' + error.message);
      } finally {
        isLoading.value = false;
      }
    };
    
    const deleteProvider = async (providerId) => {
      if (!confirm('Opravdu chcete smazat tohoto poskytovatele? Budou smazány i všechny jeho modely.')) {
        return;
      }
      
      clearMessages();
      isLoading.value = true;
      
      try {
        // Smazání poskytovatele pomocí nové implementované metody
        const response = await llmDbService.deleteProvider(providerId);
        
        if (response.error) {
          showError(response.error.message);
          return;
        }
        
        showSuccess('Poskytovatel byl úspěšně smazán včetně všech jeho modelů.');
        
        // Aktualizujeme seznam poskytovatelů
        await loadProviders();
        
        // Pokud byl smazán aktuálně vybraný poskytovatel, zrušíme výběr
        if (selectedProviderId.value === providerId) {
          selectedProviderId.value = null;
          selectedProvider.value = null;
        }
      } catch (error) {
        console.error('Chyba při mazání poskytovatele:', error);
        showError('Nepodařilo se smazat poskytovatele. ' + error.message);
      } finally {
        isLoading.value = false;
      }
    };
    
    const addModel = async () => {
      // Validace
      if (!newModel.name.trim()) {
        showError('Je nutné zadat název modelu.');
        return;
      }
      
      if (newModel.context_window <= 0) {
        showError('Velikost kontextového okna musí být kladné číslo.');
        return;
      }
      
      if (newModel.max_tokens <= 0) {
        showError('Maximální počet tokenů musí být kladné číslo.');
        return;
      }
      
      if (selectedProvider.value.models && selectedProvider.value.models[newModel.name]) {
        showError('Model s tímto názvem již existuje.');
        return;
      }
      
      clearMessages();
      isLoading.value = true;
      
      try {
        // Přidáme model do vybraného poskytovatele
        if (!selectedProvider.value.models) {
          selectedProvider.value.models = {};
        }
        
        selectedProvider.value.models[newModel.name] = {
          context_window: newModel.context_window,
          max_tokens: newModel.max_tokens,
          capabilities: [...newModel.capabilities]
        };
        
        // Uložíme změny
        const response = await llmDbService.saveProvider(selectedProvider.value);
        
        if (response.error) {
          showError(response.error.message);
          return;
        }
        
        showSuccess('Model byl úspěšně přidán.');
        closeAddModelForm();
        
        // Znovu načteme detail poskytovatele, aby se zobrazil nový model
        await loadProviderDetail(selectedProvider.value.id);
      } catch (error) {
        console.error('Chyba při přidávání modelu:', error);
        showError('Nepodařilo se přidat model. ' + error.message);
      } finally {
        isLoading.value = false;
      }
    };
    
    const updateModel = async () => {
      // Validace
      if (!editedModel.name.trim()) {
        showError('Je nutné zadat název modelu.');
        return;
      }
      
      if (editedModel.context_window <= 0) {
        showError('Velikost kontextového okna musí být kladné číslo.');
        return;
      }
      
      if (editedModel.max_tokens <= 0) {
        showError('Maximální počet tokenů musí být kladné číslo.');
        return;
      }
      
      if (editedModel.name !== editedModel.original_name && 
          selectedProvider.value.models && 
          selectedProvider.value.models[editedModel.name]) {
        showError('Model s tímto názvem již existuje.');
        return;
      }
      
      // Získání ID modelu z původního modelu
      const model = selectedProvider.value.models[editedModel.original_name];
      if (!model) {
        showError(`Model ${editedModel.original_name} neexistuje.`);
        return;
      }
      
      const modelId = model.model_id;
      if (!modelId) {
        showError(`Model ${editedModel.original_name} nemá platné ID.`);
        console.error(`Model ${editedModel.original_name} nemá ID:`, model);
        return;
      }
      
      const isDefault = model.is_default === true;
      console.log(`Aktualizace modelu ${editedModel.original_name} (ID: ${modelId}), výchozí model: ${isDefault}`);
      console.log(`Původní model:`, model);
      
      clearMessages();
      isLoading.value = true;
      
      try {
        // Zjistíme, zda se změnil název modelu
        const isNameChanged = editedModel.name !== editedModel.original_name;
        
        console.log(`Detekce změny názvu modelu: ${isNameChanged ? 'název změněn' : 'název nezměněn'}`);
        
        // Připravíme data modelu pro API a zajistíme správné mapování property
        const modelData = {
          model_name: editedModel.name,
          context_length: editedModel.context_window,
          max_tokens_output: editedModel.max_tokens,
          capabilities: {...editedModel.capabilities},
          provider_id: selectedProvider.value.id,
          is_default: isDefault,  // Důležité: zachovat informaci, zda je model výchozí
          is_name_changed: isNameChanged  // Přidáme informaci, zda se změnil název
        };
        
        // Zachováme model_identifier pouze pokud se název nezměnil
        if (!isNameChanged && model.model_identifier) {
          modelData.model_identifier = model.model_identifier;
        }
        
        console.log('Data modelu k aktualizaci (API formát):', modelData);
        
        // Použijeme novou službu pro přímou aktualizaci modelu
        const response = await llmDbService.updateModelById(modelId, modelData);
        
        if (response.error) {
          showError(response.error.message);
          console.error('Chyba při aktualizaci modelu:', response.error);
          return;
        }
        
        showSuccess(`Model byl úspěšně aktualizován na "${editedModel.name}".`);
        closeEditModelForm();
        
        // Znovu načteme detail poskytovatele, aby se zobrazily změny
        await loadProviderDetail(selectedProvider.value.id);
      } catch (error) {
        console.error('Chyba při aktualizaci modelu:', error);
        console.error('Detail chyby:', error.message);
        showError('Nepodařilo se aktualizovat model. ' + error.message);
      } finally {
        isLoading.value = false;
      }
    };
    
    const deleteModel = async (modelName) => {
      console.log(`Požadavek na smazání modelu: ${modelName}`);
      
      if (!selectedProvider.value) {
        showError('Není vybrán žádný poskytovatel.');
        console.error('Pokus o smazání modelu bez vybraného poskytovatele');
        return;
      }
      
      if (!selectedProvider.value.models) {
        showError('Poskytovatel nemá žádné modely.');
        console.error('Poskytovatel nemá property models:', selectedProvider.value);
        return;
      }
      
      if (!selectedProvider.value.models[modelName]) {
        showError(`Model ${modelName} neexistuje.`);
        console.error(`Model ${modelName} nebyl nalezen v seznamu modelů:`, Object.keys(selectedProvider.value.models));
        return;
      }
      
      // Získání ID modelu
      const modelId = selectedProvider.value.models[modelName].model_id;
      if (!modelId) {
        showError(`Model ${modelName} nemá platné ID.`);
        console.error(`Model ${modelName} nemá ID:`, selectedProvider.value.models[modelName]);
        return;
      }
      
      if (!confirm(`Opravdu chcete smazat model ${modelName} (ID: ${modelId})?`)) {
        console.log('Uživatel zrušil smazání modelu');
        return;
      }
      
      clearMessages();
      isLoading.value = true;
      console.log(`Začínám mazání modelu ${modelName} (ID: ${modelId}) poskytovatele ${selectedProvider.value.id}...`);
      
      try {
        // Zkusíme přímo smazat model podle ID
        const response = await llmDbService.deleteModelById(modelId);
        
        // Kontrola odpovědi
        if (response.error) {
          console.error('Server vrátil chybu při mazání modelu podle ID:', response.error);
          console.log('Zkouším alternativní metodu mazání modelu...');
          
          // Pokud selže přímé mazání podle ID, použijeme alternativní metodu přes název modelu
          const alternativeResponse = await llmDbService.deleteProviderModel(selectedProvider.value.id, modelName);
          
          if (alternativeResponse.error) {
            console.error('Server vrátil chybu i při alternativní metodě:', alternativeResponse.error);
            showError(alternativeResponse.error.message || 'Došlo k chybě při mazání modelu.');
            return;
          }
          
          // Pokud alternativní metoda uspěje, pokračujeme
          console.log('Alternativní metoda mazání modelu byla úspěšná');
        } else {
          console.log('Přímé mazání modelu podle ID bylo úspěšné');
        }
        
        showSuccess(`Model ${modelName} byl úspěšně smazán.`);
        console.log(`Model ${modelName} byl úspěšně smazán, aktualizuji UI...`);
        
        // Znovu načteme detail poskytovatele, aby se zobrazily změny
        console.log('Načítám aktualizovaná data z databáze...');
        await loadProviderDetail(selectedProvider.value.id);
      } catch (error) {
        console.error('Nastala výjimka při mazání modelu:', error);
        console.error('Detail chyby:', error.message);
        console.error('Stack trace:', error.stack);
        showError('Nepodařilo se smazat model. ' + error.message);
      } finally {
        isLoading.value = false;
        console.log('Operace mazání modelu dokončena');
      }
    };
    
    // Inicializace
    onMounted(async () => {
      await loadProviders();
    });
    
    return {
      // Reaktivní stav
      isLoading, errorMessage, successMessage, providers, selectedProviderId, selectedProvider,
      
      // Stav formulářů
      showAddProviderForm, showEditProviderForm, showAddModelForm, showEditModelForm,
      
      // Formulářové data
      newProvider, editedProvider, newModel, editedModel, availableCapabilities,
      
      // Základní metody
      clearMessages, showSuccess, showError,
      
      // Načítání dat
      loadProviders, loadProviderDetail, handleProviderSelect,
      
      // Formuláře poskytovatelů
      openAddProviderForm, closeAddProviderForm, openEditProviderForm, closeEditProviderForm,
      
      // Formuláře modelů
      openAddModelForm, closeAddModelForm, openEditModelForm, closeEditModelForm,
      
      // CRUD operace
      createProvider, updateProvider, deleteProvider, addModel, updateModel, deleteModel
    };
  }
};
