<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></title>
    <style>
        body {
            background-color: #121212;
            color: #ffffff;
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
        }
        
        button {
            padding: 10px 15px;
            background-color: #2563EB;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 20px 0;
        }
        
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.85);
            z-index: 1000;
            display: flex;
            justify-content: center;
            align-items: center;
            backdrop-filter: blur(3px);
        }
        
        .modal-container {
            width: 90%;
            max-width: 700px;
            max-height: 90vh;
            background-color: #1e1e1e;
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.5), 0 0 1px rgba(255, 255, 255, 0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .modal-header {
            padding: 18px 24px;
            background-color: #252525;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #444;
        }
        
        .modal-header h3 {
            margin: 0;
            font-size: 1.3rem;
            font-weight: 600;
            color: #fff;
        }
        
        .modal-close {
            background: none;
            border: none;
            font-size: 1.8rem;
            color: #fff;
            cursor: pointer;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background-color 0.2s;
        }
        
        .modal-body {
            padding: 24px 28px;
            overflow-y: auto;
            max-height: calc(90vh - 140px);
            color: #fff;
        }
        
        .modal-footer {
            padding: 18px 24px;
            background-color: #252525;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            border-top: 1px solid #444;
        }
        
        .modal-footer button {
            margin: 0;
        }
        
        .modal-section {
            margin-bottom: 28px;
            padding-bottom: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
        }
        
        input {
            width: 100%;
            padding: 12px 14px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            background-color: #2d2d2d;
            color: #fff;
            font-size: 1rem;
        }
        
        .btn-primary {
            background-color: #2563EB;
        }
        
        .btn-secondary {
            background-color: #444;
        }
        
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <h1>Test Modálního Okna pro Přidání Modelu</h1>
    
    <p>Tato stránka testuje otevírání modálního okna bez Vue.js, abychom ověřili, že samotné modální okno funguje správně.</p>
    
    <div>
        <button id="openModalBtn">Přidat model k poskytovateli</button>
    </div>
    
    <div id="modelModal" class="modal-overlay hidden">
        <div class="modal-container">
            <div class="modal-header">
                <h3>Přidat model</h3>
                <button class="modal-close" id="closeModalBtn">&times;</button>
            </div>
            <div class="modal-body">
                <p>Vyplňte parametry modelu pro poskytovatele <strong>Test LLM</strong>.</p>
                
                <div class="modal-section">
                    <h4>Základní informace</h4>
                    
                    <div class="form-group">
                        <label for="model-name">Název modelu</label>
                        <input 
                            type="text" 
                            id="model-name" 
                            placeholder="Např. gpt-4o, claude-3-sonnet, gemini-1.5-pro"
                        >
                    </div>
                </div>
                
                <div class="modal-section">
                    <h4>Parametry modelu</h4>
                    
                    <div class="form-group">
                        <label for="model-context">Velikost kontextového okna</label>
                        <input 
                            type="number" 
                            id="model-context" 
                            placeholder="Např. 128000, 200000"
                            value="32000"
                        >
                    </div>
                    
                    <div class="form-group">
                        <label for="model-tokens">Maximální tokeny v odpovědi</label>
                        <input 
                            type="number" 
                            id="model-tokens" 
                            placeholder="Např. 4096, 8192"
                            value="4096"
                        >
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-primary" id="saveModelBtn">Uložit model</button>
                <button class="btn-secondary" id="cancelBtn">Zrušit</button>
            </div>
        </div>
    </div>
    
    <script>
        // Jednoduché JavaScript funkce pro otevírání a zavírání modálního okna
        const openModalBtn = document.getElementById('openModalBtn');
        const closeModalBtn = document.getElementById('closeModalBtn');
        const cancelBtn = document.getElementById('cancelBtn');
        const saveModelBtn = document.getElementById('saveModelBtn');
        const modelModal = document.getElementById('modelModal');
        
        openModalBtn.addEventListener('click', () => {
            console.log('Otevírám modální okno');
            modelModal.classList.remove('hidden');
            
            // Přidání informací do konzole pro debugování
            console.log('Modální okno je nyní:', modelModal.classList.contains('hidden') ? 'skryté' : 'viditelné');
            console.log('Styl display:', window.getComputedStyle(modelModal).display);
        });
        
        const closeModal = () => {
            modelModal.classList.add('hidden');
        };
        
        closeModalBtn.addEventListener('click', closeModal);
        cancelBtn.addEventListener('click', closeModal);
        
        saveModelBtn.addEventListener('click', () => {
            const modelName = document.getElementById('model-name').value;
            const contextWindow = document.getElementById('model-context').value;
            const maxTokens = document.getElementById('model-tokens').value;
            
            if (!modelName) {
                alert('Prosím zadejte název modelu');
                return;
            }
            
            console.log('Ukládám model:', {
                name: modelName,
                context_window: contextWindow,
                max_tokens: maxTokens
            });
            
            alert(`Model ${modelName} byl úspěšně uložen`);
            closeModal();
        });
    </script>
</body>
</html>
