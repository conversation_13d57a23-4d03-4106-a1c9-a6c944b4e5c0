<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LLM Setting - Opravená verze</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            width: 100%;
            overflow: hidden;
        }
        
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        #fixed-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.85);
            z-index: 10000;
            display: flex;
            justify-content: center;
            align-items: center;
            backdrop-filter: blur(3px);
        }
        
        #fixed-modal-container {
            width: 90%;
            max-width: 700px;
            max-height: 90vh;
            background-color: #1e1e1e;
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.5), 0 0 1px rgba(255, 255, 255, 0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        #fixed-modal-header {
            padding: 18px 24px;
            background-color: #252525;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #444;
        }
        
        #fixed-modal-header h3 {
            margin: 0;
            font-size: 1.3rem;
            font-weight: 600;
            color: #fff;
        }
        
        #fixed-modal-close {
            background: none;
            border: none;
            font-size: 1.8rem;
            color: #fff;
            cursor: pointer;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background-color 0.2s;
        }
        
        #fixed-modal-body {
            padding: 24px 28px;
            overflow-y: auto;
            max-height: calc(90vh - 140px);
            color: #fff;
        }
        
        #fixed-modal-footer {
            padding: 18px 24px;
            background-color: #252525;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            border-top: 1px solid #444;
        }
        
        .fixed-form-group {
            margin-bottom: 20px;
        }
        
        .fixed-form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #fff;
        }
        
        .fixed-form-input {
            width: 100%;
            padding: 12px 14px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            background-color: #2d2d2d;
            color: #fff;
            font-size: 1rem;
        }
        
        .fixed-modal-section {
            margin-bottom: 28px;
            padding-bottom: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .fixed-btn {
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 5px;
            margin: 0;
        }
        
        .fixed-btn-primary {
            background-color: #2563EB;
            color: white;
        }
        
        .fixed-btn-secondary {
            background-color: #444;
            color: white;
        }
        
        .fixed-hidden {
            display: none !important;
        }
        
        #add-model-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: #2563EB;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
            cursor: pointer;
            font-weight: bold;
            z-index: 9999;
        }
    </style>
</head>
<body>
    <!-- Iframe načte původní webovou stránku -->
    <iframe src="http://192.168.14.150:8000/llm-setting" id="app-frame"></iframe>
    
    <!-- Přidáme vlastní tlačítko -->
    <button id="add-model-button">+ Přidat model k poskytovateli</button>
    
    <!-- Náš vlastní modální dialog, který může být zobrazen místo nefunkčního dialogu v aplikaci -->
    <div id="fixed-modal-overlay" class="fixed-hidden">
        <div id="fixed-modal-container">
            <div id="fixed-modal-header">
                <h3>Přidat model</h3>
                <button id="fixed-modal-close">&times;</button>
            </div>
            <div id="fixed-modal-body">
                <p>Vyplňte parametry modelu pro poskytovatele.</p>
                
                <div class="fixed-modal-section">
                    <h4>Základní informace</h4>
                    
                    <div class="fixed-form-group">
                        <label for="fixed-model-name">Název modelu</label>
                        <input 
                            type="text" 
                            id="fixed-model-name" 
                            class="fixed-form-input"
                            placeholder="Např. gpt-4o, claude-3-sonnet, gemini-1.5-pro"
                        >
                    </div>
                </div>
                
                <div class="fixed-modal-section">
                    <h4>Parametry modelu</h4>
                    
                    <div class="fixed-form-group">
                        <label for="fixed-model-context">Velikost kontextového okna</label>
                        <input 
                            type="number" 
                            id="fixed-model-context" 
                            class="fixed-form-input"
                            placeholder="Např. 128000, 200000"
                            value="32000"
                        >
                    </div>
                    
                    <div class="fixed-form-group">
                        <label for="fixed-model-tokens">Maximální tokeny v odpovědi</label>
                        <input 
                            type="number" 
                            id="fixed-model-tokens" 
                            class="fixed-form-input"
                            placeholder="Např. 4096, 8192"
                            value="4096"
                        >
                    </div>
                </div>
                
                <div class="fixed-modal-section">
                    <h4>Schopnosti modelu</h4>
                    <p>Vyberte schopnosti, které tento model podporuje:</p>
                    
                    <div class="fixed-form-group">
                        <div id="capabilities-container" style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px;">
                            <div>
                                <input type="checkbox" id="cap-text" checked>
                                <label for="cap-text" style="display: inline;">Text</label>
                            </div>
                            <div>
                                <input type="checkbox" id="cap-code" checked>
                                <label for="cap-code" style="display: inline;">Kód</label>
                            </div>
                            <div>
                                <input type="checkbox" id="cap-images">
                                <label for="cap-images" style="display: inline;">Obrázky</label>
                            </div>
                            <div>
                                <input type="checkbox" id="cap-reasoning">
                                <label for="cap-reasoning" style="display: inline;">Uvažování</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="fixed-modal-footer">
                <button class="fixed-btn fixed-btn-primary" id="fixed-save-model-btn">Uložit model</button>
                <button class="fixed-btn fixed-btn-secondary" id="fixed-cancel-btn">Zrušit</button>
            </div>
        </div>
    </div>
    
    <script>
        // Skript pro komunikaci s iframe a interakci s originální stránkou
        document.addEventListener('DOMContentLoaded', function() {
            const addModelButton = document.getElementById('add-model-button');
            const fixedModalOverlay = document.getElementById('fixed-modal-overlay');
            const fixedModalClose = document.getElementById('fixed-modal-close');
            const fixedCancelBtn = document.getElementById('fixed-cancel-btn');
            const fixedSaveModelBtn = document.getElementById('fixed-save-model-btn');
            
            // Funkce pro zobrazení modálního okna
            function showModal() {
                fixedModalOverlay.classList.remove('fixed-hidden');
            }
            
            // Funkce pro skrytí modálního okna
            function hideModal() {
                fixedModalOverlay.classList.add('fixed-hidden');
            }
            
            // Nastavení posluchačů událostí
            addModelButton.addEventListener('click', showModal);
            fixedModalClose.addEventListener('click', hideModal);
            fixedCancelBtn.addEventListener('click', hideModal);
            
            // Uložení modelu - komunikace s iframe
            fixedSaveModelBtn.addEventListener('click', function() {
                const modelName = document.getElementById('fixed-model-name').value;
                const contextWindow = document.getElementById('fixed-model-context').value;
                const maxTokens = document.getElementById('fixed-model-tokens').value;
                
                // Základní validace
                if (!modelName) {
                    alert('Prosím zadejte název modelu');
                    return;
                }
                
                // Získání vybraných schopností
                const capabilities = [];
                if (document.getElementById('cap-text').checked) capabilities.push('text');
                if (document.getElementById('cap-code').checked) capabilities.push('code');
                if (document.getElementById('cap-images').checked) capabilities.push('images');
                if (document.getElementById('cap-reasoning').checked) capabilities.push('reasoning');
                
                // Data modelu
                const modelData = {
                    name: modelName,
                    context_window: parseInt(contextWindow),
                    max_tokens: parseInt(maxTokens),
                    capabilities: capabilities
                };
                
                console.log('Ukládám model:', modelData);
                
                // Injektujeme skript do iframe pro přidání modelu
                const iframe = document.getElementById('app-frame');
                const iframeWindow = iframe.contentWindow;
                const iframeDocument = iframe.contentDocument || iframe.contentWindow.document;
                
                // Vytvoření skriptu pro injektování do iframe
                const script = iframeDocument.createElement('script');
                script.textContent = `
                    // Získání Vue instance
                    const app = document.querySelector('.llm-setting-container').__vue__;
                    if (app && app.selectedProvider && app.selectedProvider.value) {
                        console.log("Přidávám model k poskytovateli:", app.selectedProvider.value.name);
                        
                        // Ujistíme se, že existuje objekt modelů
                        if (!app.selectedProvider.value.models) {
                            app.selectedProvider.value.models = {};
                        }
                        
                        // Přidání modelu
                        app.selectedProvider.value.models["${modelData.name}"] = {
                            context_window: ${modelData.context_window},
                            max_tokens: ${modelData.max_tokens},
                            capabilities: ${JSON.stringify(modelData.capabilities)}
                        };
                        
                        // Uložení změn
                        app.saveProvider();
                        
                        console.log("Model byl přidán a uložen");
                    } else {
                        console.error("Nelze najít Vue instanci nebo vybraného poskytovatele");
                    }
                `;
                
                iframeDocument.body.appendChild(script);
                
                // Zobrazení úspěšné zprávy
                alert(`Model ${modelName} byl úspěšně přidán`);
                hideModal();
            });
        });
    </script>
</body>
</html>
