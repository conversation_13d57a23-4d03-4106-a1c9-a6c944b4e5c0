-- V<PERSON><PERSON><PERSON><PERSON><PERSON> tabulek pro agenty a skupiny agentů

-- Tabulka pro skupiny agentů
CREATE TABLE IF NOT EXISTS agent_groups (
    group_id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    metadata JSONB,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Tabulka pro agenty
CREATE TABLE IF NOT EXISTS agents (
    agent_id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    agent_type VARCHAR(50) NOT NULL,
    description TEXT,
    llm_model_id INTEGER REFERENCES llm_models(model_id) ON DELETE SET NULL,
    system_message TEXT,
    group_id INTEGER REFERENCES agent_groups(group_id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT TRUE,
    metadata JSONB,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Indexy
CREATE INDEX IF NOT EXISTS idx_agents_name ON agents(name);
CREATE INDEX IF NOT EXISTS idx_agents_agent_type ON agents(agent_type);
CREATE INDEX IF NOT EXISTS idx_agents_group_id ON agents(group_id);
CREATE INDEX IF NOT EXISTS idx_agent_groups_name ON agent_groups(name);

-- Vložení výchozích skupin agentů
INSERT INTO agent_groups (name, description, is_active)
VALUES 
    ('Vývojová skupina', 'Skupina agentů zaměřených na vývoj a testování kódu', TRUE),
    ('Výzkumná skupina', 'Skupina agentů zaměřených na výzkum a analýzu informací', TRUE),
    ('Kreativní skupina', 'Skupina agentů zaměřených na generování obsahu a nápadů', TRUE)
ON CONFLICT (name) DO NOTHING;

-- Vložení výchozích agentů
INSERT INTO agents (name, agent_type, description, system_message, group_id, is_active)
VALUES 
    (
        'CodeDeveloper', 
        'developer', 
        'Vývoj a refaktorování kódu', 
        'Jsi pokročilý vývojářský agent specializovaný na psaní, úpravu a refaktorování kódu. Tvým úkolem je vytvářet kvalitní, čistý a efektivní kód podle zadaných požadavků. Vždy dodržuješ best practices a návrhové vzory. Při psaní kódu dbáš na čitelnost, udržitelnost a testovatelnost.', 
        (SELECT group_id FROM agent_groups WHERE name = 'Vývojová skupina'), 
        TRUE
    ),
    (
        'TestEngineer', 
        'tester', 
        'Psaní a spouštění testů', 
        'Jsi pokročilý testovací agent specializovaný na psaní a spouštění testů. Tvým úkolem je vytvářet kvalitní testy, které ověří funkčnost kódu a odhalí potenciální chyby. Zaměřuješ se na unit testy, integrační testy i end-to-end testy. Při psaní testů dbáš na pokrytí kódu a testování hraničních případů.', 
        (SELECT group_id FROM agent_groups WHERE name = 'Vývojová skupina'), 
        TRUE
    ),
    (
        'DataAnalyst', 
        'analyst', 
        'Analýza dat a požadavků', 
        'Jsi pokročilý analytický agent specializovaný na analýzu dat a požadavků. Tvým úkolem je zpracovávat a analyzovat data, identifikovat vzory a trendy a poskytovat užitečné informace pro rozhodování. Při analýze dat používáš statistické metody a vizualizace.', 
        (SELECT group_id FROM agent_groups WHERE name = 'Výzkumná skupina'), 
        TRUE
    ),
    (
        'ResearchAssistant', 
        'researcher', 
        'Vyhledávání a zpracování informací', 
        'Jsi pokročilý výzkumný agent specializovaný na vyhledávání a zpracování informací. Tvým úkolem je získávat relevantní informace z různých zdrojů, ověřovat jejich správnost a poskytovat užitečné souhrny. Při výzkumu používáš různé metody a nástroje, včetně webového vyhledávání a analýzy dokumentů.', 
        (SELECT group_id FROM agent_groups WHERE name = 'Výzkumná skupina'), 
        TRUE
    ),
    (
        'ContentCreator', 
        'creative', 
        'Generování textů a nápadů', 
        'Jsi pokročilý kreativní agent specializovaný na generování textů a nápadů. Tvým úkolem je vytvářet originální a kvalitní obsah podle zadaných požadavků. Při tvorbě obsahu dbáš na kreativitu, originalitu a relevanci.', 
        (SELECT group_id FROM agent_groups WHERE name = 'Kreativní skupina'), 
        TRUE
    )
ON CONFLICT (name) DO NOTHING;
