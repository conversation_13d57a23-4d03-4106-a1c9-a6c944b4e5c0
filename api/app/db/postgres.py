"""
GENT v10 API - PostgreSQL Database Connection Module

Tento modul poskytuje funkce pro připojení k PostgreSQL databázím
používaným v systému GENT v10.
"""

import os
import json
import psycopg2
import logging
from typing import Optional

# Nastavení loggeru
logger = logging.getLogger("gent.api.db.postgres")

# Cesta ke konfiguračnímu souboru
BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
CONFIG_PATH = os.path.join(BASE_DIR, "config", "postgres_credentials.json")


class SimpleDB:
    """
    Jednoduchá třída pro práci s PostgreSQL databází.
    """
    
    def __init__(self, dbname, user="postgres", password=None, host="localhost", port=5432):
        """
        Inicializace připojení k databázi.
        
        Args:
            dbname (str): <PERSON><PERSON><PERSON><PERSON> databáze
            user (str): Uživatelské jméno
            password (str): He<PERSON>lo
            host (str): Host databáze
            port (int): Port databáze
        """
        self.conn_params = {
            "dbname": dbname,
            "user": user,
            "host": host,
            "port": port
        }
        
        # Pokud heslo není zadáno, načteme ho z konfiguračního souboru
        if not password:
            try:
                with open(CONFIG_PATH, "r") as f:
                    creds = json.load(f)
                    self.conn_params["password"] = creds["postgres_password"]
            except Exception as e:
                logger.error(f"Chyba při načítání hesla z konfiguračního souboru: {str(e)}")
                raise
    
    def connect(self):
        """
        Vytvoří a vrátí nové připojení k databázi.
        
        Returns:
            psycopg2.extensions.connection: Připojení k databázi
        """
        try:
            return psycopg2.connect(**self.conn_params)
        except Exception as e:
            logger.error(f"Chyba při připojování k databázi {self.conn_params['dbname']}: {str(e)}")
            raise


def get_postgres_connection(db_name: str) -> psycopg2.extensions.connection:
    """
    Vytvoří a vrátí připojení k PostgreSQL databázi.
    
    Args:
        db_name (str): Název databáze (gentdb, gentdb_analytics, gentdb_knowledge, gentdb_logs)
        
    Returns:
        psycopg2.extensions.connection: Připojení k databázi
        
    Raises:
        Exception: Pokud se nepodaří připojit k databázi
    """
    try:
        # Načtení přístupových údajů
        with open(CONFIG_PATH, "r") as f:
            creds = json.load(f)
        
        # Vytvoření připojení
        conn = psycopg2.connect(
            dbname=db_name,
            user="postgres",
            password=creds["postgres_password"],
            host="localhost",
            port=5432
        )
        
        logger.info(f"Úspěšně připojeno k databázi {db_name}")
        return conn
        
    except Exception as e:
        logger.error(f"Chyba při připojování k databázi {db_name}: {str(e)}")
        raise


def get_db(db_name: str) -> SimpleDB:
    """
    Vytvoří a vrátí instanci SimpleDB pro danou databázi.
    
    Args:
        db_name (str): Název databáze
    
    Returns:
        SimpleDB: Instance SimpleDB
    """
    try:
        # Načtení přístupových údajů
        with open(CONFIG_PATH, "r") as f:
            creds = json.load(f)
        
        # Vytvoření SimpleDB instance
        return SimpleDB(
            dbname=db_name,
            user="postgres",
            password=creds["postgres_password"],
            host="localhost",
            port=5432
        )
    except Exception as e:
        logger.error(f"Chyba při vytváření SimpleDB instance: {str(e)}")
        raise
