"""
GENT v10 API - Agents Routes

Tento modul implementuje API endpointy pro správu agentů.
"""

from fastapi import APIRouter, HTTPException, Depends, Query, Path
from typing import List, Dict, Any, Optional
import logging
import json
from datetime import datetime
from pydantic import BaseModel, Field

from ..db.postgres import get_postgres_connection
from ..models.agents import Agent, AgentCreate, AgentUpdate, AgentGroup, AgentGroupCreate, AgentGroupUpdate

# Konfigurace loggeru
logger = logging.getLogger("gent.api.agents")

# Vytvoření routeru
router = APIRouter(
    prefix="/api/agents",
    tags=["agents"],
    responses={404: {"description": "Not found"}},
)

# Endpointy pro agenty

@router.get("/", response_model=List[Agent])
async def get_agents():
    """
    Získá seznam všech agentů.
    
    Returns:
        List[Agent]: Seznam agentů
    """
    try:
        conn = get_postgres_connection("gentdb")
        cursor = conn.cursor()
        
        # Získání agentů z databáze
        cursor.execute("""
            SELECT agent_id, name, agent_type, description, llm_model_id, system_message, 
                   group_id, is_active, created_at, updated_at, metadata
            FROM agents
            ORDER BY name
        """)
        
        agents = []
        for row in cursor.fetchall():
            agent = {
                "agent_id": row[0],
                "name": row[1],
                "agent_type": row[2],
                "description": row[3],
                "llm_model_id": row[4],
                "system_message": row[5],
                "group_id": row[6],
                "is_active": row[7],
                "created_at": row[8],
                "updated_at": row[9],
                "metadata": row[10] if row[10] else {}
            }
            agents.append(agent)
        
        cursor.close()
        conn.close()
        
        return agents
    
    except Exception as e:
        logger.error(f"Chyba při získávání agentů: {e}")
        raise HTTPException(status_code=500, detail=f"Chyba při získávání agentů: {str(e)}")

@router.get("/{agent_id}", response_model=Agent)
async def get_agent(agent_id: int = Path(..., description="ID agenta")):
    """
    Získá detail agenta podle ID.
    
    Args:
        agent_id: ID agenta
        
    Returns:
        Agent: Detail agenta
    """
    try:
        conn = get_postgres_connection("gentdb")
        cursor = conn.cursor()
        
        # Získání agenta z databáze
        cursor.execute("""
            SELECT agent_id, name, agent_type, description, llm_model_id, system_message, 
                   group_id, is_active, created_at, updated_at, metadata
            FROM agents
            WHERE agent_id = %s
        """, (agent_id,))
        
        row = cursor.fetchone()
        if not row:
            cursor.close()
            conn.close()
            raise HTTPException(status_code=404, detail=f"Agent s ID {agent_id} nebyl nalezen")
        
        agent = {
            "agent_id": row[0],
            "name": row[1],
            "agent_type": row[2],
            "description": row[3],
            "llm_model_id": row[4],
            "system_message": row[5],
            "group_id": row[6],
            "is_active": row[7],
            "created_at": row[8],
            "updated_at": row[9],
            "metadata": row[10] if row[10] else {}
        }
        
        cursor.close()
        conn.close()
        
        return agent
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Chyba při získávání agenta: {e}")
        raise HTTPException(status_code=500, detail=f"Chyba při získávání agenta: {str(e)}")

@router.post("/", response_model=Agent)
async def create_agent(agent: AgentCreate):
    """
    Vytvoří nového agenta.
    
    Args:
        agent: Data agenta
        
    Returns:
        Agent: Vytvořený agent
    """
    try:
        conn = get_postgres_connection("gentdb")
        cursor = conn.cursor()
        
        # Kontrola, zda existuje model
        if agent.llm_model_id:
            cursor.execute("SELECT model_id FROM llm_models WHERE model_id = %s", (agent.llm_model_id,))
            if not cursor.fetchone():
                cursor.close()
                conn.close()
                raise HTTPException(status_code=404, detail=f"Model s ID {agent.llm_model_id} nebyl nalezen")
        
        # Kontrola, zda existuje skupina
        if agent.group_id:
            cursor.execute("SELECT group_id FROM agent_groups WHERE group_id = %s", (agent.group_id,))
            if not cursor.fetchone():
                cursor.close()
                conn.close()
                raise HTTPException(status_code=404, detail=f"Skupina s ID {agent.group_id} nebyla nalezena")
        
        # Vytvoření agenta v databázi
        cursor.execute("""
            INSERT INTO agents (name, agent_type, description, llm_model_id, system_message, 
                              group_id, is_active, metadata)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            RETURNING agent_id, created_at, updated_at
        """, (
            agent.name,
            agent.agent_type,
            agent.description,
            agent.llm_model_id,
            agent.system_message,
            agent.group_id,
            agent.is_active,
            json.dumps(agent.metadata) if agent.metadata else None
        ))
        
        result = cursor.fetchone()
        agent_id, created_at, updated_at = result
        
        conn.commit()
        cursor.close()
        conn.close()
        
        # Vytvoření odpovědi
        return {
            "agent_id": agent_id,
            "name": agent.name,
            "agent_type": agent.agent_type,
            "description": agent.description,
            "llm_model_id": agent.llm_model_id,
            "system_message": agent.system_message,
            "group_id": agent.group_id,
            "is_active": agent.is_active,
            "created_at": created_at,
            "updated_at": updated_at,
            "metadata": agent.metadata or {}
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Chyba při vytváření agenta: {e}")
        raise HTTPException(status_code=500, detail=f"Chyba při vytváření agenta: {str(e)}")

@router.put("/{agent_id}", response_model=Agent)
async def update_agent(agent_id: int, agent: AgentUpdate):
    """
    Aktualizuje agenta.
    
    Args:
        agent_id: ID agenta
        agent: Data agenta
        
    Returns:
        Agent: Aktualizovaný agent
    """
    try:
        conn = get_postgres_connection("gentdb")
        cursor = conn.cursor()
        
        # Kontrola, zda agent existuje
        cursor.execute("SELECT agent_id FROM agents WHERE agent_id = %s", (agent_id,))
        if not cursor.fetchone():
            cursor.close()
            conn.close()
            raise HTTPException(status_code=404, detail=f"Agent s ID {agent_id} nebyl nalezen")
        
        # Kontrola, zda existuje model
        if agent.llm_model_id:
            cursor.execute("SELECT model_id FROM llm_models WHERE model_id = %s", (agent.llm_model_id,))
            if not cursor.fetchone():
                cursor.close()
                conn.close()
                raise HTTPException(status_code=404, detail=f"Model s ID {agent.llm_model_id} nebyl nalezen")
        
        # Kontrola, zda existuje skupina
        if agent.group_id:
            cursor.execute("SELECT group_id FROM agent_groups WHERE group_id = %s", (agent.group_id,))
            if not cursor.fetchone():
                cursor.close()
                conn.close()
                raise HTTPException(status_code=404, detail=f"Skupina s ID {agent.group_id} nebyla nalezena")
        
        # Aktualizace agenta v databázi
        update_fields = []
        params = []
        
        if agent.name is not None:
            update_fields.append("name = %s")
            params.append(agent.name)
        
        if agent.agent_type is not None:
            update_fields.append("agent_type = %s")
            params.append(agent.agent_type)
        
        if agent.description is not None:
            update_fields.append("description = %s")
            params.append(agent.description)
        
        if agent.llm_model_id is not None:
            update_fields.append("llm_model_id = %s")
            params.append(agent.llm_model_id)
        
        if agent.system_message is not None:
            update_fields.append("system_message = %s")
            params.append(agent.system_message)
        
        if agent.group_id is not None:
            update_fields.append("group_id = %s")
            params.append(agent.group_id)
        
        if agent.is_active is not None:
            update_fields.append("is_active = %s")
            params.append(agent.is_active)
        
        if agent.metadata is not None:
            update_fields.append("metadata = %s")
            params.append(json.dumps(agent.metadata))
        
        update_fields.append("updated_at = CURRENT_TIMESTAMP")
        
        # Sestavení a provedení dotazu
        query = f"""
            UPDATE agents
            SET {", ".join(update_fields)}
            WHERE agent_id = %s
            RETURNING agent_id, name, agent_type, description, llm_model_id, system_message, 
                     group_id, is_active, created_at, updated_at, metadata
        """
        params.append(agent_id)
        
        cursor.execute(query, params)
        row = cursor.fetchone()
        
        conn.commit()
        cursor.close()
        conn.close()
        
        # Vytvoření odpovědi
        return {
            "agent_id": row[0],
            "name": row[1],
            "agent_type": row[2],
            "description": row[3],
            "llm_model_id": row[4],
            "system_message": row[5],
            "group_id": row[6],
            "is_active": row[7],
            "created_at": row[8],
            "updated_at": row[9],
            "metadata": row[10] if row[10] else {}
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Chyba při aktualizaci agenta: {e}")
        raise HTTPException(status_code=500, detail=f"Chyba při aktualizaci agenta: {str(e)}")

@router.delete("/{agent_id}")
async def delete_agent(agent_id: int):
    """
    Smaže agenta.
    
    Args:
        agent_id: ID agenta
        
    Returns:
        Dict: Informace o úspěchu operace
    """
    try:
        conn = get_postgres_connection("gentdb")
        cursor = conn.cursor()
        
        # Kontrola, zda agent existuje
        cursor.execute("SELECT agent_id FROM agents WHERE agent_id = %s", (agent_id,))
        if not cursor.fetchone():
            cursor.close()
            conn.close()
            raise HTTPException(status_code=404, detail=f"Agent s ID {agent_id} nebyl nalezen")
        
        # Smazání agenta z databáze
        cursor.execute("DELETE FROM agents WHERE agent_id = %s", (agent_id,))
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return {"success": True, "message": f"Agent s ID {agent_id} byl smazán"}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Chyba při mazání agenta: {e}")
        raise HTTPException(status_code=500, detail=f"Chyba při mazání agenta: {str(e)}")

# Endpointy pro skupiny agentů

@router.get("/groups", response_model=List[AgentGroup])
async def get_agent_groups():
    """
    Získá seznam všech skupin agentů.
    
    Returns:
        List[AgentGroup]: Seznam skupin agentů
    """
    try:
        conn = get_postgres_connection("gentdb")
        cursor = conn.cursor()
        
        # Získání skupin z databáze
        cursor.execute("""
            SELECT group_id, name, description, is_active, created_at, updated_at, metadata
            FROM agent_groups
            ORDER BY name
        """)
        
        groups = []
        for row in cursor.fetchall():
            group = {
                "group_id": row[0],
                "name": row[1],
                "description": row[2],
                "is_active": row[3],
                "created_at": row[4],
                "updated_at": row[5],
                "metadata": row[6] if row[6] else {}
            }
            groups.append(group)
        
        cursor.close()
        conn.close()
        
        return groups
    
    except Exception as e:
        logger.error(f"Chyba při získávání skupin agentů: {e}")
        raise HTTPException(status_code=500, detail=f"Chyba při získávání skupin agentů: {str(e)}")

@router.get("/groups/{group_id}", response_model=AgentGroup)
async def get_agent_group(group_id: int):
    """
    Získá detail skupiny agentů podle ID.
    
    Args:
        group_id: ID skupiny
        
    Returns:
        AgentGroup: Detail skupiny
    """
    try:
        conn = get_postgres_connection("gentdb")
        cursor = conn.cursor()
        
        # Získání skupiny z databáze
        cursor.execute("""
            SELECT group_id, name, description, is_active, created_at, updated_at, metadata
            FROM agent_groups
            WHERE group_id = %s
        """, (group_id,))
        
        row = cursor.fetchone()
        if not row:
            cursor.close()
            conn.close()
            raise HTTPException(status_code=404, detail=f"Skupina s ID {group_id} nebyla nalezena")
        
        group = {
            "group_id": row[0],
            "name": row[1],
            "description": row[2],
            "is_active": row[3],
            "created_at": row[4],
            "updated_at": row[5],
            "metadata": row[6] if row[6] else {}
        }
        
        # Získání agentů ve skupině
        cursor.execute("""
            SELECT agent_id, name, agent_type
            FROM agents
            WHERE group_id = %s
        """, (group_id,))
        
        agents = []
        for row in cursor.fetchall():
            agent = {
                "agent_id": row[0],
                "name": row[1],
                "agent_type": row[2]
            }
            agents.append(agent)
        
        group["agents"] = agents
        
        cursor.close()
        conn.close()
        
        return group
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Chyba při získávání skupiny agentů: {e}")
        raise HTTPException(status_code=500, detail=f"Chyba při získávání skupiny agentů: {str(e)}")

@router.post("/groups", response_model=AgentGroup)
async def create_agent_group(group: AgentGroupCreate):
    """
    Vytvoří novou skupinu agentů.
    
    Args:
        group: Data skupiny
        
    Returns:
        AgentGroup: Vytvořená skupina
    """
    try:
        conn = get_postgres_connection("gentdb")
        cursor = conn.cursor()
        
        # Vytvoření skupiny v databázi
        cursor.execute("""
            INSERT INTO agent_groups (name, description, is_active, metadata)
            VALUES (%s, %s, %s, %s)
            RETURNING group_id, created_at, updated_at
        """, (
            group.name,
            group.description,
            group.is_active,
            json.dumps(group.metadata) if group.metadata else None
        ))
        
        result = cursor.fetchone()
        group_id, created_at, updated_at = result
        
        conn.commit()
        cursor.close()
        conn.close()
        
        # Vytvoření odpovědi
        return {
            "group_id": group_id,
            "name": group.name,
            "description": group.description,
            "is_active": group.is_active,
            "created_at": created_at,
            "updated_at": updated_at,
            "metadata": group.metadata or {},
            "agents": []
        }
    
    except Exception as e:
        logger.error(f"Chyba při vytváření skupiny agentů: {e}")
        raise HTTPException(status_code=500, detail=f"Chyba při vytváření skupiny agentů: {str(e)}")

@router.put("/groups/{group_id}", response_model=AgentGroup)
async def update_agent_group(group_id: int, group: AgentGroupUpdate):
    """
    Aktualizuje skupinu agentů.
    
    Args:
        group_id: ID skupiny
        group: Data skupiny
        
    Returns:
        AgentGroup: Aktualizovaná skupina
    """
    try:
        conn = get_postgres_connection("gentdb")
        cursor = conn.cursor()
        
        # Kontrola, zda skupina existuje
        cursor.execute("SELECT group_id FROM agent_groups WHERE group_id = %s", (group_id,))
        if not cursor.fetchone():
            cursor.close()
            conn.close()
            raise HTTPException(status_code=404, detail=f"Skupina s ID {group_id} nebyla nalezena")
        
        # Aktualizace skupiny v databázi
        update_fields = []
        params = []
        
        if group.name is not None:
            update_fields.append("name = %s")
            params.append(group.name)
        
        if group.description is not None:
            update_fields.append("description = %s")
            params.append(group.description)
        
        if group.is_active is not None:
            update_fields.append("is_active = %s")
            params.append(group.is_active)
        
        if group.metadata is not None:
            update_fields.append("metadata = %s")
            params.append(json.dumps(group.metadata))
        
        update_fields.append("updated_at = CURRENT_TIMESTAMP")
        
        # Sestavení a provedení dotazu
        query = f"""
            UPDATE agent_groups
            SET {", ".join(update_fields)}
            WHERE group_id = %s
            RETURNING group_id, name, description, is_active, created_at, updated_at, metadata
        """
        params.append(group_id)
        
        cursor.execute(query, params)
        row = cursor.fetchone()
        
        conn.commit()
        
        # Získání agentů ve skupině
        cursor.execute("""
            SELECT agent_id, name, agent_type
            FROM agents
            WHERE group_id = %s
        """, (group_id,))
        
        agents = []
        for agent_row in cursor.fetchall():
            agent = {
                "agent_id": agent_row[0],
                "name": agent_row[1],
                "agent_type": agent_row[2]
            }
            agents.append(agent)
        
        cursor.close()
        conn.close()
        
        # Vytvoření odpovědi
        return {
            "group_id": row[0],
            "name": row[1],
            "description": row[2],
            "is_active": row[3],
            "created_at": row[4],
            "updated_at": row[5],
            "metadata": row[6] if row[6] else {},
            "agents": agents
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Chyba při aktualizaci skupiny agentů: {e}")
        raise HTTPException(status_code=500, detail=f"Chyba při aktualizaci skupiny agentů: {str(e)}")

@router.delete("/groups/{group_id}")
async def delete_agent_group(group_id: int):
    """
    Smaže skupinu agentů.
    
    Args:
        group_id: ID skupiny
        
    Returns:
        Dict: Informace o úspěchu operace
    """
    try:
        conn = get_postgres_connection("gentdb")
        cursor = conn.cursor()
        
        # Kontrola, zda skupina existuje
        cursor.execute("SELECT group_id FROM agent_groups WHERE group_id = %s", (group_id,))
        if not cursor.fetchone():
            cursor.close()
            conn.close()
            raise HTTPException(status_code=404, detail=f"Skupina s ID {group_id} nebyla nalezena")
        
        # Kontrola, zda skupina nemá přiřazené agenty
        cursor.execute("SELECT COUNT(*) FROM agents WHERE group_id = %s", (group_id,))
        if cursor.fetchone()[0] > 0:
            cursor.close()
            conn.close()
            raise HTTPException(status_code=400, detail=f"Skupina s ID {group_id} má přiřazené agenty a nelze ji smazat")
        
        # Smazání skupiny z databáze
        cursor.execute("DELETE FROM agent_groups WHERE group_id = %s", (group_id,))
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return {"success": True, "message": f"Skupina s ID {group_id} byla smazána"}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Chyba při mazání skupiny agentů: {e}")
        raise HTTPException(status_code=500, detail=f"Chyba při mazání skupiny agentů: {str(e)}")

@router.get("/groups/{group_id}/agents", response_model=List[Agent])
async def get_agents_in_group(group_id: int):
    """
    Získá seznam agentů ve skupině.
    
    Args:
        group_id: ID skupiny
        
    Returns:
        List[Agent]: Seznam agentů ve skupině
    """
    try:
        conn = get_postgres_connection("gentdb")
        cursor = conn.cursor()
        
        # Kontrola, zda skupina existuje
        cursor.execute("SELECT group_id FROM agent_groups WHERE group_id = %s", (group_id,))
        if not cursor.fetchone():
            cursor.close()
            conn.close()
            raise HTTPException(status_code=404, detail=f"Skupina s ID {group_id} nebyla nalezena")
        
        # Získání agentů ve skupině
        cursor.execute("""
            SELECT agent_id, name, agent_type, description, llm_model_id, system_message, 
                   group_id, is_active, created_at, updated_at, metadata
            FROM agents
            WHERE group_id = %s
            ORDER BY name
        """, (group_id,))
        
        agents = []
        for row in cursor.fetchall():
            agent = {
                "agent_id": row[0],
                "name": row[1],
                "agent_type": row[2],
                "description": row[3],
                "llm_model_id": row[4],
                "system_message": row[5],
                "group_id": row[6],
                "is_active": row[7],
                "created_at": row[8],
                "updated_at": row[9],
                "metadata": row[10] if row[10] else {}
            }
            agents.append(agent)
        
        cursor.close()
        conn.close()
        
        return agents
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Chyba při získávání agentů ve skupině: {e}")
        raise HTTPException(status_code=500, detail=f"Chyba při získávání agentů ve skupině: {str(e)}")

@router.post("/groups/{group_id}/agents/{agent_id}")
async def add_agent_to_group(group_id: int, agent_id: int):
    """
    Přidá agenta do skupiny.
    
    Args:
        group_id: ID skupiny
        agent_id: ID agenta
        
    Returns:
        Dict: Informace o úspěchu operace
    """
    try:
        conn = get_postgres_connection("gentdb")
        cursor = conn.cursor()
        
        # Kontrola, zda skupina existuje
        cursor.execute("SELECT group_id FROM agent_groups WHERE group_id = %s", (group_id,))
        if not cursor.fetchone():
            cursor.close()
            conn.close()
            raise HTTPException(status_code=404, detail=f"Skupina s ID {group_id} nebyla nalezena")
        
        # Kontrola, zda agent existuje
        cursor.execute("SELECT agent_id FROM agents WHERE agent_id = %s", (agent_id,))
        if not cursor.fetchone():
            cursor.close()
            conn.close()
            raise HTTPException(status_code=404, detail=f"Agent s ID {agent_id} nebyl nalezen")
        
        # Přidání agenta do skupiny
        cursor.execute("""
            UPDATE agents
            SET group_id = %s, updated_at = CURRENT_TIMESTAMP
            WHERE agent_id = %s
        """, (group_id, agent_id))
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return {"success": True, "message": f"Agent s ID {agent_id} byl přidán do skupiny s ID {group_id}"}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Chyba při přidávání agenta do skupiny: {e}")
        raise HTTPException(status_code=500, detail=f"Chyba při přidávání agenta do skupiny: {str(e)}")

@router.delete("/groups/{group_id}/agents/{agent_id}")
async def remove_agent_from_group(group_id: int, agent_id: int):
    """
    Odebere agenta ze skupiny.
    
    Args:
        group_id: ID skupiny
        agent_id: ID agenta
        
    Returns:
        Dict: Informace o úspěchu operace
    """
    try:
        conn = get_postgres_connection("gentdb")
        cursor = conn.cursor()
        
        # Kontrola, zda skupina existuje
        cursor.execute("SELECT group_id FROM agent_groups WHERE group_id = %s", (group_id,))
        if not cursor.fetchone():
            cursor.close()
            conn.close()
            raise HTTPException(status_code=404, detail=f"Skupina s ID {group_id} nebyla nalezena")
        
        # Kontrola, zda agent existuje a je ve skupině
        cursor.execute("SELECT agent_id FROM agents WHERE agent_id = %s AND group_id = %s", (agent_id, group_id))
        if not cursor.fetchone():
            cursor.close()
            conn.close()
            raise HTTPException(status_code=404, detail=f"Agent s ID {agent_id} nebyl nalezen ve skupině s ID {group_id}")
        
        # Odebrání agenta ze skupiny
        cursor.execute("""
            UPDATE agents
            SET group_id = NULL, updated_at = CURRENT_TIMESTAMP
            WHERE agent_id = %s
        """, (agent_id,))
        
        conn.commit()
        cursor.close()
        conn.close()
        
        return {"success": True, "message": f"Agent s ID {agent_id} byl odebrán ze skupiny s ID {group_id}"}
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Chyba při odebírání agenta ze skupiny: {e}")
        raise HTTPException(status_code=500, detail=f"Chyba při odebírání agenta ze skupiny: {str(e)}")
