from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.utils import get_openapi

# Import našich API endpointů
from api.app.routes.agents_routes import router as agents_router

# Vytvoření instance FastAPI
app = FastAPI(title="GENT v10 API Service - Agents", version="1.0")

# API dokumentace
api_description = """
# GENT v10 API dokumentace - Agents

Tato dokumentace popisuje API pro správu agentů v systému GENT v10.

## Kategorie API

* **Agents** - Správa agentů a skupin agentů
"""

# Povolíme CORS pro lokální vývoj
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Pro produkci by m<PERSON><PERSON> být omezeno pouze na konkrétní domény
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Základní endpoint pro ově<PERSON><PERSON><PERSON> běhu služby
@app.get("/")
async def read_root():
    return {"message": "Agents API Service is running"}

# Endpoint pro kontrolu zdraví služby
@app.get("/health")
async def health_check():
    return {"status": "ok"}

# Registrujeme naše API endpointy
app.include_router(agents_router)

# Vlastní funkce pro přizpůsobení OpenAPI schématu
def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema
    
    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=api_description,
        routes=app.routes,
    )
    
    # Přidání TagGroups pro lepší organizaci v Swagger UI
    openapi_schema["tags"] = [
        {
            "name": "agents",
            "description": "Správa agentů a skupin agentů",
            "externalDocs": {
                "description": "Další dokumentace",
                "url": "/docs/agents_documentation.md",
            }
        }
    ]
    
    app.openapi_schema = openapi_schema
    return app.openapi_schema

# Nastavení vlastního OpenAPI schématu
app.openapi = custom_openapi
