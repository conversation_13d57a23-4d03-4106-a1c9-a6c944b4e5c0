"""
GENT v10 API - Agent Models

Tento modul definuje Pydantic modely pro agenty a skupiny agentů.
"""

from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any
from datetime import datetime

# Modely pro agenty

class AgentBase(BaseModel):
    """Základní model pro agenta."""
    name: str = Field(..., description="Název agenta")
    agent_type: str = Field(..., description="Typ agenta (developer, tester, analyst, researcher, creative)")
    description: str = Field(..., description="Popis agenta")
    llm_model_id: Optional[int] = Field(None, description="ID LLM modelu, který agent používá")
    system_message: Optional[str] = Field(None, description="Systémová zpráva (mozek) agenta")
    group_id: Optional[int] = Field(None, description="ID skupiny, do které agent patří")
    is_active: bool = Field(True, description="Zda je agent aktivní")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Metadata agenta")

class AgentCreate(AgentBase):
    """Model pro vytvoření agenta."""
    pass

class AgentUpdate(BaseModel):
    """Model pro aktualizaci agenta."""
    name: Optional[str] = Field(None, description="Název agenta")
    agent_type: Optional[str] = Field(None, description="Typ agenta (developer, tester, analyst, researcher, creative)")
    description: Optional[str] = Field(None, description="Popis agenta")
    llm_model_id: Optional[int] = Field(None, description="ID LLM modelu, který agent používá")
    system_message: Optional[str] = Field(None, description="Systémová zpráva (mozek) agenta")
    group_id: Optional[int] = Field(None, description="ID skupiny, do které agent patří")
    is_active: Optional[bool] = Field(None, description="Zda je agent aktivní")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Metadata agenta")

class Agent(AgentBase):
    """Model pro agenta."""
    agent_id: int = Field(..., description="ID agenta")
    created_at: datetime = Field(..., description="Datum a čas vytvoření")
    updated_at: datetime = Field(..., description="Datum a čas poslední aktualizace")

    class Config:
        orm_mode = True

# Modely pro skupiny agentů

class AgentGroupBase(BaseModel):
    """Základní model pro skupinu agentů."""
    name: str = Field(..., description="Název skupiny")
    description: str = Field(..., description="Popis skupiny")
    is_active: bool = Field(True, description="Zda je skupina aktivní")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Metadata skupiny")

class AgentGroupCreate(AgentGroupBase):
    """Model pro vytvoření skupiny agentů."""
    pass

class AgentGroupUpdate(BaseModel):
    """Model pro aktualizaci skupiny agentů."""
    name: Optional[str] = Field(None, description="Název skupiny")
    description: Optional[str] = Field(None, description="Popis skupiny")
    is_active: Optional[bool] = Field(None, description="Zda je skupina aktivní")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Metadata skupiny")

class AgentInGroup(BaseModel):
    """Model pro agenta ve skupině."""
    agent_id: int = Field(..., description="ID agenta")
    name: str = Field(..., description="Název agenta")
    agent_type: str = Field(..., description="Typ agenta")

class AgentGroup(AgentGroupBase):
    """Model pro skupinu agentů."""
    group_id: int = Field(..., description="ID skupiny")
    created_at: datetime = Field(..., description="Datum a čas vytvoření")
    updated_at: datetime = Field(..., description="Datum a čas poslední aktualizace")
    agents: List[AgentInGroup] = Field(default_factory=list, description="Seznam agentů ve skupině")

    class Config:
        orm_mode = True
